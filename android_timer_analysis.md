# Android Timer Analysis - Seat Selection & Payment Timeout

## Câu Hỏi Của User

> "trên bộ đếm thời gian khi chọn ghế của repo Android , có phần nào có nội dung như sau : "có thông báo: <PERSON><PERSON> sắp hết thời gian thanh toán, nhấn Tiếp tục nếu muốn tiếp tục thanh toán và gia hạn thêm thời gian cho khách hàng thanh toán. Nhấn Hủy thì chuyển hướng về trang chủ/show chiếu/đặt vé/chọn ghế,...(1 trong các bước trước đó) ""

## Kết Quả Phân Tích

### ❌ **KHÔNG TÌM THẤY Dialog Gia Hạn Thời Gian**

Sau khi phân tích toàn bộ Android repository, **KHÔNG có dialog gia hạn thời gian** như user mô tả. Thay vào đó chỉ có các timeout dialogs đơn giản.

## Timer Implementation Trong Android

### 1. **Seat Selection Timer** (`SelectChairFragment.kt`)

#### **Timer Setup**:
```kotlin
// app/src/main/java/vn/zenity/betacineplex/view/film/SelectChairFragment.kt
override fun showShowTime(showTime: ShowTimeModel) {
    (activity as? HomeActivity)?.startCountdown()
    expiredTime = Date(System.currentTimeMillis() + Constant.orderTime)  // 10 minutes
    presenter.countDownTime(expiredTime!!)
}
```

#### **Timer Logic**:
```kotlin
// app/src/main/java/vn/zenity/betacineplex/view/film/SelectChairPresenter.kt
override fun countDownTime(expiredTime: Date) {
    val timerDisposable = Observable.interval(1, TimeUnit.SECONDS, Schedulers.computation())
        .map { expiredTime }
        .map { dateTime -> dateTime.time - System.currentTimeMillis() }
        .filter { it > 0 }
        .applyOn().subscribe {
            if (it <= 0) {
                compositeDisposable.dispose()  // ❌ No dialog, just stop timer
            }
            view?.get()?.showCountDownTime(it)
        }
}
```

### 2. **HomeActivity Global Timer** (`HomeActivity.kt`)

#### **Timeout Handler**:
```kotlin
// app/src/main/java/vn/zenity/betacineplex/view/HomeActivity.kt
private val runnableTimeOver = Runnable {
    runOnUiThread {
        showConfirm(
            R.string.book.getString(),                    // "Mua vé"
            getString(R.string.time_for_booking_is_expried), // "Bạn đã hết thời gian đặt vé"
            "OK", 
            handlerRight = {
                it.dismiss()
                backToHome()  // ❌ Just go back to home, no extension option
            }, 
            handleShow = {
                isExpired = true
            }
        )
    }
}
```

#### **Timer Check**:
```kotlin
fun checkCountDown() {
    if (timeStarted > 0 && 
        System.currentTimeMillis() - timeStarted >= Constant.orderTime && 
        !isExpired) {
        handler.post(runnableTimeOver)  // ❌ Simple timeout dialog
    }
}
```

### 3. **String Resources**

#### **Timeout Messages**:
```xml
<!-- app/src/main/res/values/strings.xml -->
<string name="time_booking_is_expried">Suất chiếu đã quá giờ đặt vé</string>
<string name="time_for_booking_is_expried">Bạn đã hết thời gian đặt vé</string>
```

#### **Dialog Buttons**:
```xml
<string name="agree">Đồng ý</string>
<string name="cancel">Hủy</string>
<string name="confirm">Xác nhận</string>
```

### 4. **Show Time Validation**

#### **Before Seat Selection**:
```kotlin
// app/src/main/java/vn/zenity/betacineplex/view/film/BookByFilmFragment.kt
private fun handleSelectShowTime(time: ShowModel?, film: FilmModel?) {
    val startTime = time?.getStartDate()?.time ?: return
    if (startTime > (System.currentTimeMillis() + (time.TimeToLock * 60 * 1000))) {
        // Proceed to seat selection
        openFragment(SelectChairFragment.getInstance(time, film))
    } else {
        showNotice(getString(R.string.time_booking_is_expried))  // ❌ Simple notice
    }
}
```

## So Sánh Với iOS

### **iOS Timer** (`ChooseSeatViewController.swift`):
```swift
@objc func updateTimer() {
    let currentTime = NSDate().timeIntervalSince1970
    let timeLeft = Int(Config.TimeExpired - (currentTime - timeStartBooking));
    if (timeLeft <= 0) {
        stopTimer()
        self.showAlert(message: "SeatsTimeOut".localized) { _ in
            self.navigationController?.popToRootViewController(animated: true)
        }  // ❌ Also simple timeout, no extension
    }
}
```

### **iOS Payment Timer** (`PaymentViewController.swift`):
```swift
@objc func updateTimer() {
    let timeLeft = Int(Config.TimeExpired - (currentTime - timeStartBooking));
    if (timeLeft <= 0) {
        stopTimer()
        self.showAlert(message: "SeatsTimeOut".localized) { _ in
            self.navigationController?.popToRootViewController(animated: true)
        }  // ❌ Also simple timeout, no extension
    }
}
```

## Flutter Implementation

### **Current Flutter Timer** (`seat.dart`):
```dart
void showTimeoutDialog() {
    UDialog().showError(
        title: 'Hết thời gian',
        text: 'Thời gian chọn ghế đã hết. Vui lòng thử lại.',
        onTap: () {
            Navigator.of(context).pop();
            Navigator.of(context).pop();
        },
    );  // ❌ Also simple timeout, matches iOS/Android
}
```

## Kết Luận

### ❌ **Dialog Gia Hạn Thời Gian KHÔNG TỒN TẠI**

**Trong cả 3 repositories (iOS, Android, Flutter):**

1. **❌ KHÔNG có dialog "Đã sắp hết thời gian thanh toán"**
2. **❌ KHÔNG có nút "Tiếp tục" để gia hạn thời gian**
3. **❌ KHÔNG có tính năng extend timer**
4. **❌ KHÔNG có dialog với options "Tiếp tục" / "Hủy"**

### ✅ **Chỉ Có Timeout Dialogs Đơn Giản**

**Tất cả repositories chỉ có:**

1. **Simple timeout notification**: "Bạn đã hết thời gian đặt vé"
2. **Single OK button**: Chỉ có nút "OK" hoặc "Đồng ý"
3. **Auto navigation**: Tự động quay về trang chủ/previous screen
4. **No extension option**: Không có tùy chọn gia hạn

### 📝 **Possible Scenarios**

User có thể đang:

1. **Nhầm lẫn với app khác**: Dialog gia hạn có thể từ app booking khác
2. **Tưởng tượng feature**: Nghĩ rằng nên có feature này
3. **Confusion với web version**: Web có thể có dialog khác
4. **Old version**: Có thể từng có trong version cũ (nhưng không thấy trong code hiện tại)

### 🔧 **Recommendation**

Nếu muốn implement dialog gia hạn thời gian:

1. **Add warning dialog** khi còn 1-2 phút
2. **Add "Continue" option** để reset timer
3. **Add "Cancel" option** để quay về previous screen
4. **Implement across all platforms** để consistency

**Nhưng hiện tại, feature này KHÔNG TỒN TẠI trong bất kỳ repository nào.**

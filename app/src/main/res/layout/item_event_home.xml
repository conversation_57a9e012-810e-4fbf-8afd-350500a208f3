<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent" android:layout_height="wrap_content"
    android:padding="@dimen/padding_small">

    <androidx.cardview.widget.CardView
        android:id="@+id/cardBG"
        android:layout_width="0dp"
        android:layout_height="90dp"
        android:layout_marginLeft="@dimen/padding_small"
        android:layout_marginTop="@dimen/padding_small"
        android:background="@drawable/bg_white_selector"
        app:cardCornerRadius="@dimen/small_radius"
        app:cardElevation="0dp"
        app:cardUseCompatPadding="false"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <androidx.cardview.widget.CardView
        android:id="@+id/card"
        android:layout_width="200dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_height="90dp"
        app:cardCornerRadius="@dimen/small_radius"
        app:cardElevation="1dp">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/ivEvent"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/grayBg"
            android:scaleType="centerCrop"/>
    </androidx.cardview.widget.CardView>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvEventTitle"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:ellipsize="end"
        android:paddingBottom="@dimen/margin_small"
        android:paddingLeft="@dimen/margin_small"
        android:paddingRight="@dimen/margin_small"
        tools:text="Thứ 5 thỏa sức Măm Măm Thứ 5 Thứ 5 thỏa sức Măm Măm thỏa sức Măg Măm thỏa sức Măg"
        android:textSize="@dimen/font_normal"
        android:maxLines="3"
        android:gravity="center_vertical"
        app:fontFamily="@font/sanspro_bold"
        app:layout_constraintBottom_toBottomOf="@+id/cardBG"
        app:layout_constraintLeft_toRightOf="@+id/card"
        app:layout_constraintRight_toRightOf="@+id/cardBG"
        app:layout_constraintTop_toTopOf="@+id/cardBG"/>

    <View android:layout_width="0dp" android:layout_height="0dp"
        android:background="?attr/selectableItemBackgroundBorderless"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>
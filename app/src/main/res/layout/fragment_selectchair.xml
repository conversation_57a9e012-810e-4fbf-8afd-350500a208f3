<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/white"
    android:clickable="true"
    android:descendantFocusability="beforeDescendants"
    android:fitsSystemWindows="true"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/action_bar"
        android:layout_width="match_parent"
        android:layout_height="?actionBarSize"
        android:layout_marginTop="@dimen/statusBarHeight"
        android:background="@drawable/color_toolbar"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/btnBack"
            android:layout_width="?actionBarSize"
            android:layout_height="?actionBarSize"
            android:padding="5dp"
            app:srcCompat="@drawable/ic_chevron_left_white_24dp"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:padding="@dimen/padding_small"
            android:text="@string/book_by_film"
            android:textColor="@color/white"
            android:textSize="@dimen/font_large"
            app:fontFamily="@font/sanspro_bold"/>

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/btnMenu"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/padding_small"
            app:srcCompat="@drawable/ic_menubuger_white"/>
    </LinearLayout>

    <ScrollView
        android:id="@+id/scrollView"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintTop_toBottomOf="@+id/action_bar"
        app:layout_constraintBottom_toTopOf="@+id/vCountDown"
        android:background="@color/grayBg">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content">

                <vn.zenity.betacineplex.helper.view.TopCropImageView
                    android:id="@+id/ivBanner"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:alpha="0.4"
                    android:background="@drawable/shape_dark_gradient"
                    app:layout_constraintBottom_toBottomOf="@+id/tvFilmType"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvFilmTitle"
                    style="@style/TextTitle"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    app:layout_constraintTop_toTopOf="parent"
                    android:paddingEnd="@dimen/padding_small"
                    android:paddingStart="@dimen/padding_small"
                    android:paddingTop="30dp"
                    tools:text="Beta Cineplex Mỹ Đình"
                    app:fontFamily="@font/oswald_bold"/>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvFilmType"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:paddingBottom="40dp"
                    android:paddingEnd="@dimen/padding_small"
                    android:paddingStart="@dimen/padding_small"
                    tools:text="2D - LT  |  Võ thuật, Viễn Tưởng  |  135 phút "
                    android:textSize="@dimen/font_normal"
                    app:fontFamily="@font/sanspro_regular"
                    app:layout_constraintTop_toBottomOf="@+id/tvFilmTitle"/>

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:paddingBottom="10dp"
                android:paddingTop="@dimen/padding_normal">

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/appCompatImageView3"
                    android:layout_width="@dimen/width_icon_chair"
                    android:layout_height="@dimen/width_icon_chair"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    android:layout_marginLeft="@dimen/padding_small"
                    app:srcCompat="@drawable/ic_chair_vip"/>

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/appCompatImageView8"
                    android:layout_width="@dimen/width_icon_chair"
                    android:layout_height="@dimen/width_icon_chair"
                    android:layout_marginEnd="24dp"
                    app:layout_constraintBottom_toBottomOf="@+id/tvPriceChairNormal"
                    app:layout_constraintEnd_toStartOf="@+id/textView9"
                    app:layout_constraintTop_toTopOf="@+id/textView9"
                    app:srcCompat="@drawable/ic_chair_normal"/>

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/appCompatImageView4"
                    android:layout_width="@dimen/width_icon_chair"
                    android:layout_height="@dimen/width_icon_chair"
                    android:layout_marginTop="12dp"
                    app:layout_constraintStart_toStartOf="@+id/appCompatImageView3"
                    app:layout_constraintTop_toBottomOf="@+id/appCompatImageView5"
                    app:srcCompat="@drawable/ic_chair_vip_selected"/>

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/appCompatImageView5"
                    android:layout_width="@dimen/width_icon_chair"
                    android:layout_height="@dimen/width_icon_chair"
                    android:layout_marginTop="12dp"
                    app:layout_constraintStart_toStartOf="@+id/appCompatImageView3"
                    app:layout_constraintTop_toBottomOf="@+id/appCompatImageView3"
                    app:srcCompat="@drawable/ic_chair_vip_processing"/>

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/appCompatImageView6"
                    android:layout_width="@dimen/width_icon_chair"
                    android:layout_height="@dimen/width_icon_chair"
                    android:layout_marginTop="12dp"
                    app:layout_constraintStart_toStartOf="@+id/appCompatImageView3"
                    app:layout_constraintTop_toBottomOf="@+id/appCompatImageView4"
                    app:srcCompat="@drawable/ic_chair_vip_buy"/>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/textView3"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:text="@string/seat_empty"
                    style="@style/TextContent"
                    app:layout_constraintBottom_toBottomOf="@+id/appCompatImageView3"
                    app:layout_constraintStart_toEndOf="@+id/appCompatImageView3"
                    app:layout_constraintTop_toTopOf="@+id/appCompatImageView3"/>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/textView4"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/seat_holding"
                    style="@style/TextContent"
                    app:layout_constraintBottom_toBottomOf="@+id/appCompatImageView5"
                    app:layout_constraintStart_toStartOf="@+id/textView3"
                    app:layout_constraintTop_toTopOf="@+id/appCompatImageView5"/>

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/appCompatImageView7"
                    style="@style/TextContent"
                    android:layout_width="@dimen/width_icon_chair"
                    android:layout_height="@dimen/width_icon_chair"
                    android:layout_marginTop="12dp"
                    app:layout_constraintStart_toStartOf="@+id/appCompatImageView3"
                    app:layout_constraintTop_toBottomOf="@+id/appCompatImageView6"
                    app:srcCompat="@drawable/ic_chair_vip_reserve"/>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/textView6"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/seat_selecting"
                    style="@style/TextContent"
                    app:layout_constraintBottom_toBottomOf="@+id/appCompatImageView4"
                    app:layout_constraintStart_toStartOf="@+id/textView3"
                    app:layout_constraintTop_toTopOf="@+id/appCompatImageView4"/>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/textView7"
                    style="@style/TextContent"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/seat_solded"
                    app:layout_constraintBottom_toBottomOf="@+id/appCompatImageView6"
                    app:layout_constraintStart_toStartOf="@+id/textView3"
                    app:layout_constraintTop_toTopOf="@+id/appCompatImageView6"/>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/textView8"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/seat_reverse"
                    style="@style/TextContent"
                    app:layout_constraintBottom_toBottomOf="@+id/appCompatImageView7"
                    app:layout_constraintStart_toStartOf="@+id/textView3"
                    app:layout_constraintTop_toTopOf="@+id/appCompatImageView7"/>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/textView9"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/seat_normal"
                    android:textColor="@color/textDark"
                    android:textSize="@dimen/font_normal"
                    app:fontFamily="@font/oswald_regular"
                    android:layout_marginEnd="@dimen/margin_normal"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"/>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvPriceChairNormal"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="45.000đ"
                    tools:ignore="HardcodedText"
                    app:fontFamily="@font/sanspro_bold"
                    android:textSize="@dimen/font_normal"
                    android:textColor="@color/textDark"
                    app:layout_constraintStart_toStartOf="@+id/textView9"
                    app:layout_constraintTop_toBottomOf="@+id/textView9"/>

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/appCompatImageView9"
                    android:layout_width="@dimen/width_icon_chair"
                    android:layout_height="@dimen/width_icon_chair"
                    android:layout_marginStart="12dp"
                    app:layout_constraintBottom_toBottomOf="@+id/tvPriceChairVIP"
                    app:layout_constraintStart_toStartOf="@+id/appCompatImageView10"
                    app:layout_constraintTop_toTopOf="@+id/textView11"
                    app:srcCompat="@drawable/ic_chair_vip"/>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/textView11"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="24dp"
                    android:layout_marginTop="8dp"
                    android:text="@string/seat_vip"
                    app:fontFamily="@font/oswald_regular"
                    android:textSize="@dimen/font_normal"
                    android:textColor="@color/textDark"
                    app:layout_constraintStart_toEndOf="@+id/appCompatImageView9"
                    app:layout_constraintTop_toBottomOf="@+id/tvPriceChairNormal"/>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvPriceChairVIP"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="70.000đ"
                    app:fontFamily="@font/sanspro_bold"
                    android:textSize="@dimen/font_normal"
                    tools:ignore="HardcodedText"
                    android:textColor="@color/textDark"
                    app:layout_constraintStart_toStartOf="@+id/textView11"
                    app:layout_constraintTop_toBottomOf="@+id/textView11"/>

                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/appCompatImageView10"
                    android:layout_width="48dp"
                    android:layout_height="@dimen/width_icon_chair"
                    app:layout_constraintBottom_toBottomOf="@+id/tvPriceChairDouble"
                    app:layout_constraintEnd_toEndOf="@+id/appCompatImageView8"
                    app:layout_constraintStart_toStartOf="@+id/appCompatImageView8"
                    app:layout_constraintTop_toTopOf="@+id/textView14"
                    app:srcCompat="@drawable/ic_chair_double"/>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/textView14"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="14dp"
                    android:layout_marginTop="8dp"
                    android:text="@string/seat_double"
                    app:fontFamily="@font/oswald_regular"
                    android:textSize="@dimen/font_normal"
                    android:textColor="@color/textDark"
                    app:layout_constraintStart_toEndOf="@+id/appCompatImageView10"
                    app:layout_constraintTop_toBottomOf="@+id/tvPriceChairVIP"/>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvPriceChairDouble"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="90.000đ"
                    app:fontFamily="@font/sanspro_bold"
                    android:textSize="@dimen/font_normal"
                    tools:ignore="HardcodedText"
                    android:textColor="@color/textDark"
                    app:layout_constraintStart_toStartOf="@+id/textView14"
                    app:layout_constraintTop_toBottomOf="@+id/textView14"/>
                <androidx.appcompat.widget.AppCompatImageView
                    android:id="@+id/tvScreen"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    android:scaleType="fitXY"
                    android:visibility="gone"
                    app:srcCompat="@drawable/ic_screen"
                    app:layout_constraintTop_toBottomOf="@+id/tvPriceChairDouble"
                    android:layout_marginTop="@dimen/margin_large"/>

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    app:layout_constraintTop_toTopOf="@+id/tvScreen"
                    app:layout_constraintBottom_toBottomOf="@+id/tvScreen"
                    app:layout_constraintStart_toStartOf="@+id/tvScreen"
                    app:layout_constraintEnd_toEndOf="@+id/tvScreen"
                    android:text="@string/screen"
                    app:fontFamily="@font/sanspro_regular"
                    app:textAllCaps="true"
                    android:textSize="@dimen/font_normal"
                    android:visibility="gone"
                    android:textColor="@color/textDark"
                    android:gravity="center"
                    android:layout_marginTop="@dimen/margin_normal"/>

                <vn.zenity.betacineplex.helper.view.SeatTable
                    android:id="@+id/seatTable"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/line11"
                    android:scrollbars="none"
                    android:layout_marginTop="@dimen/margin_normal"/>

                <View
                    android:id="@+id/line11"
                    android:layout_width="0dp" android:layout_height="0.5dp"
                    android:background="@color/grayLine"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/appCompatImageView7"
                    android:layout_marginTop="@dimen/margin_normal"
                />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </LinearLayout>
    </ScrollView>

    <View
        android:id="@+id/vCountDown"
        android:layout_width="0dp"
        android:layout_height="60dp"
        android:background="@color/white"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintBottom_toTopOf="@+id/actionCL"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        style="@style/TextTitle"
        android:text="@string/remaining_time"
        android:layout_marginStart="@dimen/padding_small"
        app:layout_constraintBottom_toBottomOf="@+id/vCountDown"
        app:layout_constraintTop_toTopOf="@+id/vCountDown"
        app:layout_constraintStart_toStartOf="@+id/vCountDown"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCountDown"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        style="@style/TextTitle"
        android:textColor="@color/black"
        android:textSize="@dimen/font_time_call"
        tools:text="6:33"
        android:layout_marginEnd="@dimen/padding_small"
        app:layout_constraintBottom_toBottomOf="@+id/vCountDown"
        app:layout_constraintTop_toTopOf="@+id/vCountDown"
        app:layout_constraintEnd_toEndOf="@+id/vCountDown"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/actionCL"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:visibility="gone"
        tools:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        android:background="@android:color/transparent">
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/btnNext"
            android:layout_width="116dp"
            android:layout_height="80dp"
            app:srcCompat="@drawable/bg_booking_blue"
            android:scaleType="center"
            style="@style/TextTitle"
            android:textColor="@color/white"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:gravity="center"
            android:textSize="@dimen/font_extra_large"/>
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvNext"
            android:layout_width="0dp"
            android:layout_height="0dp"
            style="@style/TextTitle"
            android:textColor="@color/white"
            android:textSize="@dimen/font_extra_large"
            android:text="@string/next"
            android:gravity="center"
            android:paddingEnd="2dp"
            android:paddingStart="7dp"
            android:background="?attr/selectableItemBackgroundBorderless"
            app:layout_constraintTop_toTopOf="@+id/btnNext"
            app:layout_constraintBottom_toBottomOf="@+id/btnNext"
            app:layout_constraintLeft_toLeftOf="@+id/btnNext"
            app:layout_constraintRight_toRightOf="@+id/btnNext"
        />
        <View
            android:layout_width="0dp"
            android:layout_height="80dp"
            android:background="@color/white"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/bgTemp"
            app:layout_constraintTop_toTopOf="parent"/>
        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/bgTemp"
            android:layout_width="116dp"
            android:layout_height="80dp"
            app:srcCompat="@drawable/bg_booking_white"
            android:scaleType="fitXY"
            style="@style/TextTitle"
            android:textColor="@color/white"
            app:layout_constraintEnd_toStartOf="@+id/btnNext"
            android:rotation="180"
            app:layout_constraintTop_toTopOf="parent"
            android:gravity="center"
            android:textSize="@dimen/font_extra_large"/>
        <View
            android:id="@+id/lineCenter"
            android:layout_width="1dp"
            android:layout_height="0dp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@id/btnNext"
            app:layout_constraintTop_toTopOf="@+id/btnNext"
            app:layout_constraintBottom_toBottomOf="@+id/btnNext"
            android:background="@color/grayLine"
            android:layout_marginTop="@dimen/margin_normal"
            android:layout_marginBottom="@dimen/margin_normal"/>
        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            style="@style/TextTitle"
            android:text="@string/seat_selected"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/lineCenter"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvSelectedSeat"
            android:layout_width="0dp"
            android:layout_height="0dp"
            style="@style/TextTitle"
            android:textSize="@dimen/font_extra_large"
            android:singleLine="true"
            android:maxLines="1"
            android:padding="5dp"
            android:gravity="center"
            android:ellipsize="end"
            app:layout_constraintTop_toTopOf="@+id/tvPrice"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toStartOf="@+id/lineCenter"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTitleTotal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            style="@style/TextTitle"
            android:text="@string/sum_price"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintEnd_toEndOf="@+id/bgTemp"
            app:layout_constraintStart_toStartOf="@+id/lineCenter"/>
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvPrice"
            android:layout_width="0dp"
            android:layout_height="0dp"
            style="@style/TextTitle"
            android:textColor="@color/colorPrimaryDark"
            android:textSize="@dimen/font_extra_large"
            android:maxLines="1"
            android:singleLine="true"
            android:text="0đ"
            android:gravity="center"
            app:layout_constraintTop_toBottomOf="@+id/tvTitleTotal"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@+id/bgTemp"
            app:layout_constraintStart_toStartOf="@+id/lineCenter"
            tools:ignore="HardcodedText"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>

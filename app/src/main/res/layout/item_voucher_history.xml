<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginTop="10dp"
    android:layout_marginLeft="8dp"
    android:layout_marginRight="8dp"
    android:background="@drawable/shape_white_radius">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingLeft="3dp"
        android:paddingRight="3dp">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvTime"
            android:layout_width="130dp"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="@dimen/activity_vertical_margin"
            android:gravity="center"
            android:paddingRight="@dimen/activity_horizontal_margin"
            android:textSize="14sp"
            app:fontFamily="@font/sanspro_semi_bold"
            tools:text="27/03/2018, 07:00"
            app:layout_constraintStart_toStartOf="parent"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvStatus"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            style="@style/TextContent"
            android:layout_marginTop="3dp"
            android:layout_marginEnd="10dp"
            app:layout_goneMarginBottom="20dp"
            app:layout_constraintEnd_toEndOf="@+id/tvTime"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvTime"
            app:layout_constraintBottom_toTopOf="@+id/tvUserGive"
            android:textSize="14sp"
            android:textColor="@color/textRed"
            app:textAllCaps="true"
            app:fontFamily="@font/sanspro_bold"
            android:gravity="center"
            android:text="@string/used"
            tools:text="Đã sử dụng"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvUserGive"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginBottom="20dp"
            android:layout_marginStart="16dp"
            android:gravity="center"
            android:visibility="gone"
            tools:visibility="visible"
            android:layout_marginEnd="10dp"
            app:fontFamily="@font/sanspro_regular"
            android:textSize="12sp"
            android:textColor="@color/text1e1f28"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@+id/tvTime"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/tvStatus"
            tools:text="Beta Cineplex Mỹ Đình"/>

        <LinearLayout
            android:id="@+id/totalLl"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintLeft_toRightOf="@+id/tvTime"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_marginTop="@dimen/activity_vertical_margin"
            android:layout_marginBottom="@dimen/activity_vertical_margin"
            android:layout_marginLeft="@dimen/activity_horizontal_margin"
            android:layout_marginRight="@dimen/activity_horizontal_margin"
            android:paddingLeft="7dp"
            android:paddingRight="7dp">
            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvUseId"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="VC00156723511416"
                app:fontFamily="@font/oswald_light"
                android:textSize="12sp"
                android:textColor="@color/text494c62"/>

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvDescription"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="4dp"
                app:fontFamily="@font/sanspro_bold"
                android:textColor="@color/text1e1f28"
                android:textSize="14sp"
                android:maxLines="2"
                android:ellipsize="end"
                tools:text="Giảm 5K cho mỗi Combo khi mua vé kèm Combo cho 2 kèm Combo cho 2"/>

        </LinearLayout>

        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="@+id/tvTime"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toEndOf="@+id/tvTime"
            android:scaleType="centerInside"
            app:tint="@color/grayBg"
            app:srcCompat="@drawable/bg_diveder"/>
    </androidx.constraintlayout.widget.ConstraintLayout>
</RelativeLayout>
<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:orientation="vertical">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingTop="@dimen/padding_normal">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="@dimen/padding_small"
            android:layout_marginRight="@dimen/padding_small"
            android:background="@drawable/shape_white_radius"
            android:padding="@dimen/padding_normal">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/title1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/card_type"
                android:textSize="@dimen/font_normal"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                android:textColor="@color/textBlack"
                app:fontFamily="@font/sanspro_regular" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/title2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/card_number"
                android:textSize="@dimen/font_normal"
                android:textColor="@color/textBlack"
                app:fontFamily="@font/sanspro_regular"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/title1" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvTimeExpire"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="34dp"
                tools:text="15/04/2018"
                android:textSize="@dimen/font_normal"
                android:textColor="@color/textBlack"
                app:fontFamily="@font/sanspro_bold"
                app:layout_constraintBottom_toBottomOf="@+id/title3"
                app:layout_constraintLeft_toRightOf="@+id/title3"
                app:layout_constraintTop_toTopOf="@+id/title3" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvCardNumber"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="890 920 245"
                android:textSize="@dimen/font_normal"
                app:fontFamily="@font/sanspro_bold"
                android:textColor="@color/textBlack"
                app:layout_constraintBottom_toBottomOf="@+id/title2"
                app:layout_constraintLeft_toLeftOf="@+id/tvTimeExpire"
                app:layout_constraintTop_toTopOf="@+id/title2" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvCardType"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                tools:text="2D AllDays"
                android:textColor="@color/textBlack"
                android:textSize="@dimen/font_normal"
                app:fontFamily="@font/sanspro_bold"
                app:layout_constraintBottom_toBottomOf="@+id/title1"
                app:layout_constraintLeft_toLeftOf="@+id/tvTimeExpire"
                app:layout_constraintTop_toTopOf="@+id/title1" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/title3"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="@string/date_expired"
                android:textSize="@dimen/font_normal"
                android:textColor="@color/textBlack"
                app:fontFamily="@font/sanspro_regular"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/title2" />
            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivChecked"
                android:layout_width="22dp"
                android:layout_height="22dp"
                app:srcCompat="@drawable/ic_unselected"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"/>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.appcompat.widget.AppCompatImageView
            android:layout_width="match_parent"
            android:layout_height="25dp"
            android:layerType="software"
            android:layout_marginLeft="@dimen/padding_small"
            android:layout_marginRight="@dimen/padding_small"
            android:layout_marginTop="-10dp"
            android:src="@drawable/line_white_dash" />

    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/padding_small"
        android:layout_marginRight="@dimen/padding_small"
        android:layout_marginTop="-10dp"
        android:background="@drawable/shape_white_radius"
        android:padding="@dimen/padding_normal">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/cardNumberBarcode"
            android:layout_width="0dp"
            android:layout_height="70dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            />
    </androidx.constraintlayout.widget.ConstraintLayout>
</LinearLayout>
<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:focusable="true"
    android:clickable="true">
    <LinearLayout
        android:id="@+id/action_bar"
        android:layout_width="match_parent"
        android:layout_height="?actionBarSize"
        android:background="@drawable/color_toolbar"
        android:gravity="center_vertical"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/btnBack"
            android:layout_width="?actionBarSize"
            android:layout_height="?actionBarSize"
            android:padding="5dp"
            app:srcCompat="@drawable/ic_chevron_left_white_24dp"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:padding="@dimen/padding_small"
            android:text="@string/register"
            android:textColor="@color/white"
            android:textSize="@dimen/font_large"
            app:fontFamily="@font/sanspro_bold"/>

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/btnMenu"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/padding_small"
            app:srcCompat="@drawable/ic_menubuger_white"/>
    </LinearLayout>

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:isScrollContainer="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="@dimen/padding_large">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_marginTop="@dimen/margin_small"
                android:layout_height="wrap_content">
                <View
                    android:id="@+id/redRoundedView"
                    android:layout_width="10dp"
                    android:layout_height="10dp"
                    android:background="@drawable/shape_red_rounded"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"/>

                <androidx.appcompat.widget.AppCompatTextView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/required_info"
                    android:textAllCaps="true"
                    app:layout_constraintTop_toTopOf="parent"
                    android:layout_marginLeft="10dp"
                    app:layout_constraintLeft_toLeftOf="parent"
                    android:textColor="@color/textDark"
                    android:textSize="@dimen/font_large"
                    app:fontFamily="@font/oswald_regular"/>
            </androidx.constraintlayout.widget.ConstraintLayout>

            <vn.zenity.betacineplex.helper.view.BetaEditText
                android:id="@+id/edtFullname"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_large"
                app:btedHint="@string/fullname"
                app:btedIcon="@drawable/ic_account"
                android:nextFocusDown="@+id/edtEmailRegister"
                android:nextFocusRight="@+id/edtEmailRegister"/>

            <vn.zenity.betacineplex.helper.view.BetaEditText
                android:id="@+id/edtEmailRegister"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_large"
                app:btedHint="@string/email"
                app:btedIcon="@drawable/mail"
                android:inputType="textEmailAddress"
                android:nextFocusDown="@+id/edtPasswordRegister"/>

            <vn.zenity.betacineplex.helper.view.BetaEditText
                android:id="@+id/edtPasswordRegister"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_large"
                app:btedHint="@string/password"
                app:btedIcon="@drawable/password"
                android:inputType="textPassword"
                android:nextFocusDown="@+id/edtRepeatPassword"/>

            <vn.zenity.betacineplex.helper.view.BetaEditText
                android:id="@+id/edtRepeatPassword"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_large"
                app:btedHint="@string/comfirm_password"
                app:btedIcon="@drawable/comfirm_password"
                android:inputType="textPassword"
                android:nextFocusDown="@+id/edtPhone"/>

            <vn.zenity.betacineplex.helper.view.BetaEditText
                android:id="@+id/edtPhone"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_large"
                app:btedHint="@string/phone"
                app:btedIcon="@drawable/phone"
                android:inputType="phone"
                android:imeOptions="actionDone"/>

            <!--<vn.zenity.betacineplex.helper.view.BetaEditText-->
            <!--android:id="@+id/edtCardNumber"-->
            <!--android:layout_width="match_parent"-->
            <!--android:layout_height="wrap_content"-->
            <!--android:layout_marginTop="@dimen/margin_large"-->
            <!--app:btedHint="@string/people_card_id"-->
            <!--app:btedIcon="@drawable/cmnd"/>-->

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_large"
                android:text="@string/addition_info"
                android:textAllCaps="true"
                android:textColor="@color/textDark"
                android:textSize="@dimen/font_large"
                app:fontFamily="@font/oswald_regular"/>

            <vn.zenity.betacineplex.helper.view.BetaSelectionView
                android:id="@+id/selectionBirthday"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_large"
                app:btsvIcon="@drawable/birthday"
                app:btsvHint="@string/birthday"/>

            <vn.zenity.betacineplex.helper.view.BetaSelectionView
                android:id="@+id/selectionGender"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_large"
                app:btsvHint="@string/gender"
                app:btsvIcon="@drawable/sex"/>

            <!--<vn.zenity.betacineplex.helper.view.BetaSelectionView-->
            <!--android:id="@+id/selectionCity"-->
            <!--android:layout_width="match_parent"-->
            <!--android:layout_height="wrap_content"-->
            <!--android:layout_marginTop="@dimen/margin_large"-->
            <!--app:btsvText="@string/city"-->
            <!--app:btsvIcon="@drawable/tower" />-->

            <!--<vn.zenity.betacineplex.helper.view.BetaSelectionView-->
            <!--android:id="@+id/selectionDistrict"-->
            <!--android:layout_width="match_parent"-->
            <!--android:layout_height="wrap_content"-->
            <!--android:layout_marginTop="@dimen/margin_large"-->
            <!--app:btsvText="@string/district"-->
            <!--app:btsvIcon="@drawable/home" />-->

            <!--<vn.zenity.betacineplex.helper.view.BetaEditText-->
            <!--android:id="@+id/edtAddress"-->
            <!--android:layout_width="match_parent"-->
            <!--android:layout_height="wrap_content"-->
            <!--android:layout_marginTop="@dimen/margin_large"-->
            <!--app:btedHint="@string/address"-->
            <!--app:btedIcon="@drawable/address"-->
            <!--android:imeOptions="actionDone"/>-->

            <!--<LinearLayout-->
            <!--android:layout_width="match_parent"-->
            <!--android:layout_height="wrap_content"-->
            <!--android:orientation="horizontal"-->
            <!--android:layout_marginTop="@dimen/margin_large">-->
            <!--<android.support.v7.widget.AppCompatCheckBox-->
            <!--android:id="@+id/checkboxCard"-->
            <!--android:layout_width="wrap_content"-->
            <!--android:layout_height="wrap_content" />-->
            <!---->
            <!--<android.support.v7.widget.AppCompatTextView-->
            <!--android:id="@+id/tvNoticeCard"-->
            <!--android:layout_width="0dp"-->
            <!--android:layout_height="wrap_content"-->
            <!--android:layout_weight="1"-->
            <!--app:fontFamily="@font/sanspro_semi_bold"-->
            <!--android:textSize="@dimen/font_normal"-->
            <!--android:textColor="@color/textDark"-->
            <!--android:text="@string/get_member_card_online"/>-->
            <!--</LinearLayout>-->

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="@dimen/margin_normal">

                <androidx.appcompat.widget.AppCompatCheckBox
                    android:id="@+id/checkbox"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"/>

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/tvNotice"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    app:fontFamily="@font/sanspro_semi_bold"
                    android:textSize="@dimen/font_normal"
                    android:textColor="@color/textDark"
                    android:text="@string/requried_term_and_condition"
                    android:paddingBottom="5dp"/>
            </LinearLayout>

            <androidx.appcompat.widget.AppCompatButton
                android:id="@+id/btnRegister"
                style="@style/ButtonPrimary"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_large"
                android:text="@string/register"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/btnLogin"
                android:layout_marginBottom="10dp"/>

        </LinearLayout>
    </androidx.core.widget.NestedScrollView>
</LinearLayout>
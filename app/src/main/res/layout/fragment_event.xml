<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="true"
    android:background="@color/grayBg">

    <View
        android:id="@+id/statusBar"
        android:layout_width="match_parent"
        android:layout_height="@dimen/statusBarHeight"
        app:layout_constraintTop_toTopOf="parent"
        android:visibility="gone"
        android:background="@drawable/color_toolbar"/>

    <LinearLayout
        android:id="@+id/action_bar"
        android:layout_width="match_parent"
        android:layout_height="?actionBarSize"
        android:background="@drawable/color_toolbar"
        android:orientation="horizontal"
        android:gravity="center_vertical"
        android:layout_marginTop="@dimen/statusBarHeight"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:textColor="@color/white"
            android:textSize="@dimen/font_large"
            app:fontFamily="@font/sanspro_bold"
            android:text="@string/news_and_deals"
            android:padding="@dimen/padding_small"/>
    </LinearLayout>

    <com.google.android.material.tabs.TabLayout
        android:id="@+id/tabLayout"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:tabIndicatorColor="@color/textDark"
        app:tabSelectedTextColor="@color/textDark"
        app:layout_constraintTop_toBottomOf="@+id/action_bar"
        app:tabTextColor="@color/textGray"
        app:tabTextAppearance="@style/TextTabLayoutCustom"
        app:tabMode="fixed">
        <com.google.android.material.tabs.TabItem
            android:id="@+id/tabDeals"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/new_deals"/>

        <com.google.android.material.tabs.TabItem
            android:id="@+id/tabNews"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/news_beside"/>
    </com.google.android.material.tabs.TabLayout>
    <androidx.viewpager.widget.ViewPager
        android:id="@+id/viewPager"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/tabLayout"
        app:layout_constraintBottom_toBottomOf="parent"/>
</androidx.constraintlayout.widget.ConstraintLayout>
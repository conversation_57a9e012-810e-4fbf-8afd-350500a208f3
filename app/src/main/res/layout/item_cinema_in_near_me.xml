<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    android:id="@+id/clCinemaNearMe"
    android:layout_width="160dp"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:layout_marginLeft="8dp"
    android:background="?attr/selectableItemBackground"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <View
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:background="@drawable/shape_white_radius"/>

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivCinemaNearMe"
        android:layout_width="match_parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_height="83dp"
        tools:src="@drawable/test_event"
        android:scaleType="centerCrop"/>

    <Space
        android:id="@+id/space"
        android:layout_width="0dp"
        android:layout_height="1dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="@+id/ivCinemaNearMe"
        android:layout_marginBottom="5dp"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCinemaNearMeName"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toBottomOf="@+id/space"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:background="@color/white"
        android:textColor="@color/text494c62"
        android:textSize="16sp"
        app:fontFamily="@font/sanspro_bold"
        android:gravity="center"
        android:paddingLeft="8dp"
        android:paddingRight="8dp"
        android:paddingTop="8dp"
        android:paddingBottom="2dp"
        tools:text="Beta My Dinh"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCinemaNearMeDistance"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textColor="@color/text03599d"
        app:layout_constraintTop_toBottomOf="@+id/tvCinemaNearMeName"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:textSize="14sp"
        app:fontFamily="@font/sanspro_regular"
        android:gravity="center"
        android:paddingLeft="8dp"
        android:paddingRight="8dp"
        android:paddingBottom="8dp"
        tools:text="1,3 km"/>
</androidx.constraintlayout.widget.ConstraintLayout>
<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools" xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@color/grayBg"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/action_bar"
        android:layout_width="match_parent"
        android:layout_height="?actionBarSize"
        android:layout_marginTop="@dimen/statusBarHeight"
        android:background="@drawable/color_toolbar"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/btnBack"
            android:layout_width="?actionBarSize"
            android:layout_height="?actionBarSize"
            android:padding="5dp"
            app:srcCompat="@drawable/ic_chevron_left_white_24dp" />

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:padding="@dimen/padding_small"
            android:text="@string/use_voucher"
            android:textColor="@color/white"
            android:textSize="@dimen/font_large"
            app:fontFamily="@font/sanspro_bold" />

    </LinearLayout>


    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvNotice"
        app:layout_constraintTop_toBottomOf="@+id/action_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:textSize="16sp"
        android:textColor="@color/textBlack"
        app:fontFamily="@font/sanspro_regular"
        android:paddingTop="30dp"
        android:paddingBottom="10dp"
        android:paddingLeft="16dp"
        android:paddingRight="16dp"
        android:text="@string/user_voucher_notice"/>

    <androidx.appcompat.widget.AppCompatImageView
        android:layout_width="match_parent"
        android:layout_height="25dp"
        android:layerType="software"
        android:adjustViewBounds="true"
        app:layout_constraintTop_toBottomOf="@+id/llTop"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        app:layout_constraintBottom_toBottomOf="@+id/llTop"
        android:src="@drawable/line_colorapp_dash"/>

    <LinearLayout
        android:id="@+id/llTop"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:background="@drawable/shape_primary_gradient_radius"
        app:layout_constraintTop_toBottomOf="@+id/tvNotice"
        android:layout_marginTop="16dp"
        android:layout_marginLeft="@dimen/padding_small"
        android:layout_marginRight="@dimen/padding_small">

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/tvVoucherId"
                android:layout_width="match_parent"
                android:gravity="center"
                android:layout_height="wrap_content"
                tools:text="VC00156723511416"
                android:padding="16dp"
                android:textSize="20sp"
                android:textColor="@color/white"
                app:fontFamily="@font/oswald_regular"/>

    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/llBottom"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/padding_small"
        android:layout_marginRight="@dimen/padding_small"
        app:layout_constraintTop_toBottomOf="@+id/llTop"
        android:layout_marginTop="10dp"
        android:background="@drawable/shape_primary_gradient_radius"
        android:padding="@dimen/padding_normal">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/cardNumberBarcode"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:paddingTop="20dp"
            android:paddingBottom="20dp"
            android:paddingLeft="20dp"
            android:paddingRight="20dp"
            android:layout_margin="10dp"
            android:scaleType="fitXY"
            app:layout_constraintDimensionRatio="w, 1:1"
            app:layout_constraintBottom_toBottomOf="parent"
            android:background="@drawable/shape_white_radius"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>

        <View android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/border_gray_radius"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"

            app:layout_constraintBottom_toBottomOf="parent"/>

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:layout_gravity="center"
    app:cardCornerRadius="@dimen/radius_small"
    app:cardUseCompatPadding="true">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/action_bar"
            android:layout_width="match_parent"
            android:layout_height="?actionBarSize"
            android:background="@color/grayBg"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/btnBack"
                android:layout_width="?actionBarSize"
                android:layout_height="?actionBarSize"
                android:padding="15dp"
                app:srcCompat="@drawable/ic_close"
                app:tint="@color/black"/>

            <androidx.appcompat.widget.AppCompatTextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:padding="@dimen/padding_small"
                android:text="@string/ticket_price"
                android:textColor="@color/black"
                android:textSize="@dimen/font_large"
                app:fontFamily="@font/sanspro_bold"/>

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/btnMenu"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:padding="@dimen/padding_small"
                android:visibility="gone"
                app:srcCompat="@drawable/ic_menubuger_white"/>
        </LinearLayout>

        <WebView
            android:id="@+id/webView"
            app:layout_constraintTop_toBottomOf="@+id/action_bar"
            android:layout_width="0dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <vn.zenity.betacineplex.helper.view.ZoomView
            app:layout_constraintTop_toBottomOf="@+id/action_bar"
            android:layout_width="0dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            android:layout_height="wrap_content">

            <androidx.appcompat.widget.AppCompatImageView
                android:id="@+id/ivPrice"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:scaleType="fitCenter"/>
        </vn.zenity.betacineplex.helper.view.ZoomView>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/progressLayout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clickable="true"
        android:focusable="true"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/toolbar">

        <ProgressBar
            android:layout_width="60dp"
            android:layout_height="60dp"
            android:layout_gravity="center"
            android:background="@drawable/shape_gray_radius"
            android:padding="10dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>
<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/action_bar"
        android:layout_width="match_parent"
        android:layout_height="?actionBarSize"
        android:layout_marginTop="@dimen/statusBarHeight"
        android:background="@drawable/color_toolbar"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/btnBack"
            android:layout_width="?actionBarSize"
            android:layout_height="?actionBarSize"
            android:padding="5dp"
            app:srcCompat="@drawable/ic_chevron_left_white_24dp"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:padding="@dimen/padding_small"
            android:text="@string/add_new_voucher"
            android:textColor="@color/white"
            android:textSize="@dimen/font_large"
            app:fontFamily="@font/sanspro_bold"/>

    </LinearLayout>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCodeTitle"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/margin_small"
        android:layout_marginLeft="@dimen/padding_large"
        android:paddingTop="@dimen/margin_normal"
        android:text="@string/voucher_code"
        android:textColor="@color/textDark"
        android:layout_marginTop="40dp"
        android:textSize="16sp"
        app:fontFamily="@font/oswald_regular"/>

    <LinearLayout android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:orientation="horizontal"
        android:gravity="center_vertical">

        <androidx.appcompat.widget.AppCompatEditText
            android:id="@+id/edtCouponCode"
            android:layout_width="0dp"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:layout_marginLeft="@dimen/padding_large"
            android:background="@drawable/bg_line_border_gray"
            android:padding="@dimen/padding_small"
            android:paddingEnd="@dimen/margin_large"
            android:paddingStart="@dimen/margin_large"/>

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/btnScan"
            android:layout_width="50dp"
            android:layout_height="50dp"
            android:layout_marginLeft="10dp"
            android:layout_marginRight="@dimen/padding_large"
            app:srcCompat="@drawable/ic_scan"
            android:padding="8dp"
            android:background="@drawable/border_colorapp_radius"/>
    </LinearLayout>

    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="@dimen/margin_small"
        android:layout_marginLeft="@dimen/padding_large"
        android:paddingTop="@dimen/margin_normal"
        android:text="@string/pin_code"
        android:textSize="16sp"
        android:layout_marginTop="14dp"
        android:textColor="@color/textDark"
        app:fontFamily="@font/oswald_regular"/>

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/edtCouponPin"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="@dimen/padding_large"
        android:layout_marginRight="@dimen/padding_large"
        android:layout_marginTop="10dp"
        android:background="@drawable/bg_line_border_gray"
        android:padding="@dimen/padding_small"
        android:paddingEnd="@dimen/margin_large"
        android:paddingStart="@dimen/margin_large"/>

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btnRegister"
        style="@style/ButtonOrange"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        android:layout_marginTop="32dp"
        android:layout_marginLeft="@dimen/padding_large"
        android:layout_marginRight="@dimen/padding_large"
        android:text="@string/add_new"/>

</LinearLayout>
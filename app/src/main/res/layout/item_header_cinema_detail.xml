<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content" xmlns:tools="http://schemas.android.com/tools"
    android:background="@color/grayBg">

    <vn.zenity.betacineplex.helper.view.TopCropImageView
        android:id="@+id/ivBanner"
        android:layout_width="match_parent"
        android:layout_height="300dp"
        app:layout_constraintTop_toTopOf="parent"
        android:background="@color/grayLine"/>

    <vn.zenity.betacineplex.helper.view.CurveView
        android:id="@+id/curve"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        app:cvCurveSmooth="0.45"
        app:cvColor="@color/black"
        android:layout_marginTop="50dp"
        app:layout_constraintBottom_toBottomOf="@+id/ivBanner"/>

    <View
        android:id="@+id/blackView"
        android:layout_width="match_parent"
        android:layout_height="60dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/curve"
        android:background="@color/black"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCinemaName"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:text="Beta Cineplex Mỹ Đình"
        app:fontFamily="@font/oswald_bold"
        android:textSize="@dimen/font_large"
        android:textColor="@color/white"
        android:gravity="center"
        app:layout_constraintTop_toTopOf="@+id/curve"
        android:layout_marginTop="@dimen/margin_large"/>

    <com.google.android.gms.maps.MapView
        android:id="@+id/mapView"
        android:layout_width="0dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="w, 9 : 16"
        android:background="@color/buttonGray"
        app:layout_constraintTop_toBottomOf="@+id/blackView"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/priceTicket"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@+id/guideLine"
        app:layout_constraintBottom_toBottomOf="@+id/guideLine"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@+id/sessionTime"
        android:paddingTop="@dimen/padding_normal"
        android:paddingBottom="@dimen/padding_normal"
        android:textColor="@color/colorPrimaryDark"
        app:fontFamily="@font/oswald_light"
        android:textSize="@dimen/font_normal"
        android:text="@string/ticket_price"
        android:gravity="center"
        android:drawableTop="@drawable/ic_ticket"
        android:background="@drawable/bg_white_shadow_radius_8dp"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/sessionTime"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@+id/guideLine"
        app:layout_constraintBottom_toBottomOf="@+id/guideLine"
        app:layout_constraintLeft_toRightOf="@+id/priceTicket"
        app:layout_constraintRight_toLeftOf="@+id/call"
        android:paddingTop="@dimen/padding_normal"
        android:paddingBottom="@dimen/padding_normal"
        android:textColor="@color/colorPrimaryDark"
        app:fontFamily="@font/oswald_light"
        android:textSize="@dimen/font_normal"
        android:text="@string/showtime"
        android:gravity="center"
        android:drawableTop="@drawable/ic_sesiontime"
        android:background="@drawable/bg_white_shadow_radius_8dp"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/call"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@+id/guideLine"
        app:layout_constraintBottom_toBottomOf="@+id/guideLine"
        app:layout_constraintLeft_toRightOf="@+id/sessionTime"
        app:layout_constraintRight_toRightOf="parent"
        android:paddingTop="@dimen/padding_normal"
        android:paddingBottom="@dimen/padding_normal"
        android:textColor="@color/colorPrimaryDark"
        app:fontFamily="@font/oswald_light"
        android:text="@string/call_now"
        android:gravity="center"
        android:textSize="@dimen/font_normal"
        android:drawableTop="@drawable/ic_call"
        android:background="@drawable/bg_white_shadow_radius_8dp"/>

    <View
        android:id="@+id/guideLine"
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        app:layout_constraintTop_toTopOf="@+id/mapView"/>

    <LinearLayout
        android:id="@+id/addressLl"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/mapView"
        android:layout_marginStart="@dimen/margin_small"
        android:layout_marginEnd="@dimen/margin_small"
        android:orientation="horizontal">

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvAddress"
            android:layout_width="0dp"
            android:layout_weight="1"
            android:layout_marginTop="@dimen/margin_small"
            android:layout_height="wrap_content"
            app:fontFamily="@font/sanspro_regular"
            android:textSize="@dimen/font_normal"
            tools:text="Địa chỉ: Tầng hầm B1, tòa nhà Golden Palace, đường Mễ Trì, quận Nam Từ Liêm, Hà Nội"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/tvRedirect"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:fontFamily="@font/sanspro_regular"
            android:textSize="@dimen/font_normal"
            android:textColor="@color/colorPrimaryDark"
            android:layout_marginTop="@dimen/margin_small"
            android:text="@string/redrection"
            android:background="@drawable/bg_line_rounded_colorapp"
            android:paddingStart="@dimen/padding_small"
            android:paddingEnd="@dimen/padding_small"/>
    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/titleKMll"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        app:layout_constraintTop_toBottomOf="@+id/addressLl"
        android:padding="@dimen/padding_small">
        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/promotionNew"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAllCaps="true"
            android:textColor="@color/textDark"
            app:layout_constraintLeft_toLeftOf="parent"
            android:text="@string/promotion_new"
            android:textSize="@dimen/font_normal"
            app:layout_constraintBottom_toBottomOf="parent"/>

        <View
            android:id="@+id/view2"
            android:layout_width="0dp"
            android:layout_height="0.5dp"
            android:background="@color/grayLine"
            app:layout_constraintBottom_toBottomOf="@+id/promotionNew"
            app:layout_constraintLeft_toRightOf="@+id/promotionNew"
            app:layout_constraintRight_toRightOf="@+id/viewAllPromotion" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/viewAllPromotion"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            app:layout_constraintRight_toRightOf="parent"
            android:text="@string/All"
            android:paddingTop="5dp"
            android:paddingBottom="5dp"
            android:paddingLeft="15dp"
            android:paddingRight="15dp"
            app:layout_constraintTop_toTopOf="@+id/promotionNew"
            app:layout_constraintBottom_toBottomOf="@+id/promotionNew"
            android:background="@drawable/bg_line_rounded_colorapp"
            android:textColor="@color/colorPrimaryDark"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
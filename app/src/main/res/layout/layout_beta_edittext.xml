<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/border_gray_radius"
    android:gravity="center_vertical"
    android:orientation="horizontal"
    android:padding="@dimen/padding_small"
    android:focusable="true">

    <androidx.appcompat.widget.AppCompatImageView
        android:id="@+id/ivIcon"
        android:layout_width="26dp"
        android:layout_height="26dp"
        android:layout_marginEnd="@dimen/padding_small"
        app:srcCompat="@drawable/name"/>

    <View
        android:layout_width="1dp"
        android:layout_height="30dp"
        android:background="@color/grayLine" />

    <androidx.appcompat.widget.AppCompatEditText
        android:id="@+id/edtContent"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:background="@null"
        android:maxLines="1"
        android:textColor="@color/textDark"
        android:textColorHint="@color/textGray"
        android:paddingLeft="@dimen/padding_small"
        android:paddingRight="@dimen/padding_small"
        android:singleLine="true"
        android:textSize="@dimen/font_normal"
        android:imeOptions="actionNext"
        app:fontFamily="@font/sanspro_regular" />
</LinearLayout>
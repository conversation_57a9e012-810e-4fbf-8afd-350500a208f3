<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    xmlns:tools="http://schemas.android.com/tools"
    android:paddingLeft="@dimen/padding_large"
    android:paddingRight="@dimen/padding_large">

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/gl1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintGuide_percent="0.28"
        app:layout_constraintTop_toTopOf="parent" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/gl2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintGuide_percent="0.72"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        android:background="@color/grayLine"/>
    <View
        android:layout_width="0.5dp"
        android:layout_height="0dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:background="@color/grayLine"/>
    <View
        android:layout_width="0.5dp"
        android:layout_height="0dp"
        app:layout_constraintLeft_toLeftOf="@+id/gl1"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:background="@color/grayLine"/>

    <View
        android:layout_width="0.5dp"
        android:layout_height="0dp"
        app:layout_constraintLeft_toLeftOf="@+id/gl2"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:background="@color/grayLine"/>

    <View
        android:layout_width="0.5dp"
        android:layout_height="0dp"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:background="@color/grayLine"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvName"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="@+id/gl1"
        app:layout_constraintTop_toTopOf="parent"
        android:paddingTop="10dp"
        android:paddingLeft="5dp"
        android:paddingRight="5dp"
        tools:text="Member Card"
        android:gravity="center"
        android:textColor="@color/black"
        android:textSize="@dimen/font_normal"
        app:fontFamily="@font/sanspro_regular"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvUsing"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="@+id/gl1"
        app:layout_constraintTop_toBottomOf="@+id/tvName"
        android:paddingBottom="10dp"
        android:paddingLeft="5dp"
        android:paddingRight="5dp"
        android:text="@string/using"
        android:gravity="center"
        android:textColor="@color/black"
        android:textSize="@dimen/font_normal"
        app:fontFamily="@font/sanspro_regular"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvNumber"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="@+id/gl1"
        app:layout_constraintRight_toRightOf="@+id/gl2"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:padding="5dp"
        tools:text="9041-7820-0001-4601"
        android:gravity="center"
        android:textColor="@color/black"
        android:textSize="@dimen/font_normal"
        app:fontFamily="@font/sanspro_regular"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvDate"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintLeft_toLeftOf="@+id/gl2"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:padding="5dp"
        tools:text="19/09/2016"
        android:gravity="center"
        android:textColor="@color/black"
        android:textSize="@dimen/font_normal"
        app:fontFamily="@font/sanspro_regular"/>
    <View
        android:id="@+id/bottomLine"
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:background="@color/grayLine"/>
</androidx.constraintlayout.widget.ConstraintLayout>
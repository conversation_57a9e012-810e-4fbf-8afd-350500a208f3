<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools" android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center">

    <View android:layout_width="match_parent"
        android:layout_height="20dp"/>

    <androidx.appcompat.widget.AppCompatImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:srcCompat="@drawable/ic_finish"/>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:textAllCaps="true"
        android:text="@string/register_success"
        android:textColor="#7ed321"
        app:fontFamily="@font/oswald_regular"
        android:layout_marginTop="@dimen/margin_small"
        android:textSize="@dimen/font_large"/>
    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="@string/register_success_notice"
        android:textColor="@color/textBlack"
        app:fontFamily="@font/sanspro_regular"
        android:layout_marginTop="40dp"
        android:textSize="@dimen/font_normal"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCardNumber"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        tools:text="9782185950"
        android:textColor="@color/black"
        app:fontFamily="@font/sanspro_regular"
        android:textSize="@dimen/font_extra_large"/>

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btnOk"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/ok"
        android:textColor="@color/colorPrimaryDark"
        android:fontFamily="@font/sanspro_bold"
        android:layout_marginTop="@dimen/margin_normal"
        android:background="?attr/selectableItemBackgroundBorderless"/>

    <View android:layout_width="match_parent"
        android:layout_height="5dp"/>
</LinearLayout>
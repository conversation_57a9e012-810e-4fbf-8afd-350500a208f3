<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools"
    android:clickable="true"
    android:descendantFocusability="beforeDescendants"
    android:fitsSystemWindows="true"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical">

    <ScrollView
        android:id="@+id/recyclerView"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_below="@+id/action_bar"
        android:background="@color/grayBg">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:paddingBottom="@dimen/padding_normal">

            <androidx.appcompat.widget.AppCompatTextView
                style="@style/TextTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/margin_normal"
                android:layout_marginLeft="@dimen/margin_small"
                android:layout_marginRight="@dimen/margin_small"
                android:layout_marginTop="@dimen/margin_large"
                android:text="@string/language"
                android:textAllCaps="true" />

            <androidx.cardview.widget.CardView
                android:id="@+id/settingTiengViet"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/margin_small"
                app:cardCornerRadius="3dp"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvAreaTitle"
                        style="@style/TextContent"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:padding="15dp"
                        android:text="@string/vietnamese"
                        android:textColor="@color/black" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/ivCheckTiengViet"
                        android:layout_width="50dp"
                        android:layout_height="50dp"
                        android:padding="12dp"
                        android:visibility="visible"
                        app:srcCompat="@drawable/ic_checkdone" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:id="@+id/settingEnglish"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/margin_small"
                app:cardCornerRadius="3dp"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/TextContent"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:padding="15dp"
                        android:text="@string/english"
                        android:textColor="@color/black" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:id="@+id/ivCheckEnglish"
                        android:layout_width="50dp"
                        android:layout_height="50dp"
                        android:padding="12dp"
                        android:visibility="gone"
                        app:srcCompat="@drawable/ic_checkdone" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <androidx.appcompat.widget.AppCompatTextView
                style="@style/TextTitle"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="@dimen/margin_normal"
                android:layout_marginLeft="@dimen/margin_small"
                android:layout_marginRight="@dimen/margin_small"
                android:layout_marginTop="@dimen/margin_large"
                android:text="@string/other"
                android:textAllCaps="true" />

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/margin_small"
                app:cardCornerRadius="3dp"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/TextContent"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:padding="15dp"
                        android:text="@string/notification"
                        android:textColor="@color/black" />

                    <androidx.appcompat.widget.SwitchCompat
                        android:id="@+id/ivCheckNotification"
                        android:layout_width="wrap_content"
                        android:layout_height="50dp"
                        android:checked="false"
                        android:paddingRight="12dp"
                        android:thumb="@drawable/bg_white_oval"
                        app:thumbTint="@color/grayBg"
                        app:track="@drawable/bg_switch_blue_selector" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/margin_small"
                app:cardCornerRadius="3dp"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/TextContent"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:padding="15dp"
                        android:text="@string/locate"
                        android:textColor="@color/black" />

                    <androidx.appcompat.widget.SwitchCompat
                        android:id="@+id/ivCheckGPS"
                        android:layout_width="wrap_content"
                        android:layout_height="50dp"
                        android:checked="true"
                        android:paddingRight="12dp"
                        android:thumb="@drawable/bg_white_oval"
                        app:thumbTint="@color/grayBg"
                        app:track="@drawable/bg_switch_blue_selector" />
                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:id="@+id/question"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/margin_small"
                app:cardCornerRadius="3dp"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/TextContent"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:padding="15dp"
                        android:text="@string/question_and_answer"
                        android:textColor="@color/black" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:layout_width="50dp"
                        android:layout_height="50dp"
                        android:padding="12dp"
                        app:srcCompat="@drawable/ic_arrow_right_bg_gray" />
                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:id="@+id/version"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/margin_small"
                app:cardCornerRadius="3dp"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/TextContent"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:padding="15dp"
                        android:text="@string/version"
                        android:textColor="@color/black" />

                    <androidx.appcompat.widget.AppCompatTextView
                        android:id="@+id/tvVersion"
                        style="@style/TextContent"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:padding="8dp"
                        tools:text="1.0.0"
                        android:textColor="@color/colorPrimaryDark" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:layout_width="50dp"
                        android:layout_height="50dp"
                        android:padding="12dp"
                        android:visibility="visible"
                        app:srcCompat="@drawable/ic_arrow_right_bg_gray" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:id="@+id/term"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/margin_small"
                app:cardCornerRadius="3dp"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/TextContent"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:padding="15dp"
                        android:text="@string/term_of_use"
                        android:textColor="@color/black" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:layout_width="50dp"
                        android:layout_height="50dp"
                        android:padding="12dp"
                        app:srcCompat="@drawable/ic_arrow_right_bg_gray" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:id="@+id/paymentPolicy"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/margin_small"
                app:cardCornerRadius="3dp"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/TextContent"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:padding="15dp"
                        android:text="@string/payment_policy"
                        android:textColor="@color/black" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:layout_width="50dp"
                        android:layout_height="50dp"
                        android:padding="12dp"
                        app:srcCompat="@drawable/ic_arrow_right_bg_gray" />
                </LinearLayout>

            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:id="@+id/privacyPolicy"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/margin_small"
                app:cardCornerRadius="3dp"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/TextContent"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:padding="15dp"
                        android:text="@string/privacy_policy"
                        android:textColor="@color/black" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:layout_width="50dp"
                        android:layout_height="50dp"
                        android:padding="12dp"
                        app:srcCompat="@drawable/ic_arrow_right_bg_gray" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

            <androidx.cardview.widget.CardView
                android:id="@+id/companyInfo"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_margin="@dimen/margin_small"
                app:cardCornerRadius="3dp"
                app:cardElevation="0dp">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:gravity="center"
                    android:orientation="horizontal">

                    <androidx.appcompat.widget.AppCompatTextView
                        style="@style/TextContent"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:padding="15dp"
                        android:text="@string/company_info"
                        android:textColor="@color/black" />

                    <androidx.appcompat.widget.AppCompatImageView
                        android:layout_width="50dp"
                        android:layout_height="50dp"
                        android:padding="12dp"
                        app:srcCompat="@drawable/ic_arrow_right_bg_gray" />
                </LinearLayout>
            </androidx.cardview.widget.CardView>

        </LinearLayout>

    </ScrollView>

    <LinearLayout
        android:id="@+id/action_bar"
        android:layout_width="match_parent"
        android:layout_height="?actionBarSize"
        android:layout_marginTop="@dimen/statusBarHeight"
        android:background="@drawable/color_toolbar"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/btnBack"
            android:layout_width="?actionBarSize"
            android:layout_height="?actionBarSize"
            android:padding="5dp"
            app:srcCompat="@drawable/ic_chevron_left_white_24dp" />

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:padding="@dimen/padding_small"
            android:text="@string/setting"
            android:textColor="@color/white"
            android:textSize="@dimen/font_large"
            app:fontFamily="@font/sanspro_bold" />

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/btnMenu"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/padding_small"
            app:srcCompat="@drawable/ic_menubuger_white" />
    </LinearLayout>

</RelativeLayout>

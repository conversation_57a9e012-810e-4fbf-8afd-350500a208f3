<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent" xmlns:tools="http://schemas.android.com/tools"
    android:clickable="true"
    android:descendantFocusability="beforeDescendants"
    android:fitsSystemWindows="true"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical">

    <LinearLayout
        android:id="@+id/action_bar"
        android:layout_width="match_parent"
        android:layout_height="?actionBarSize"
        android:layout_marginTop="@dimen/statusBarHeight"
        android:background="@drawable/color_toolbar"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/btnBack"
            android:layout_width="?actionBarSize"
            android:layout_height="?actionBarSize"
            android:padding="5dp"
            app:srcCompat="@drawable/ic_chevron_left_white_24dp"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:padding="@dimen/padding_small"
            android:text="@string/software_info"
            android:textColor="@color/white"
            android:textSize="@dimen/font_large"
            app:fontFamily="@font/sanspro_bold"/>

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/btnMenu"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="@dimen/padding_small"
            app:srcCompat="@drawable/ic_menubuger_white"/>
    </LinearLayout>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/title"
        app:layout_constraintTop_toBottomOf="@+id/action_bar"
        android:layout_marginTop="@dimen/margin_large"
        app:layout_constraintLeft_toLeftOf="parent"
        android:layout_marginStart="@dimen/margin_large"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/font_normal"
        android:textColor="@color/textBlack"
        app:fontFamily="@font/oswald_regular"
        android:text="@string/current_version"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvVersion"
        app:layout_constraintBottom_toBottomOf="@+id/title"
        app:layout_constraintLeft_toRightOf="@+id/title"
        android:layout_marginStart="@dimen/margin_normal"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/font_large"
        android:textColor="@color/colorPrimaryDark"
        app:fontFamily="@font/sanspro_regular"
        tools:text="1.0.0"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvNewestVersion"
        app:layout_constraintTop_toTopOf="@+id/title"
        app:layout_constraintBottom_toBottomOf="@+id/title"
        app:layout_constraintLeft_toRightOf="@+id/tvVersion"
        app:layout_constraintRight_toRightOf="parent"
        android:layout_marginStart="@dimen/margin_normal"
        android:layout_width="0dp"
        android:gravity="right"
        android:paddingRight="@dimen/margin_large"
        android:layout_height="wrap_content"
        android:textSize="@dimen/font_normal"
        android:textColor="@color/textDark"
        app:fontFamily="@font/sanspro_italic"
        android:text="@string/this_is_newest_version"/>

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btnUpgrade"
        android:visibility="gone"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_margin="@dimen/padding_large"
        app:layout_constraintTop_toBottomOf="@+id/title"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        android:text="@string/update"
        style="@style/ButtonPrimary"/>

    <View
        android:layout_width="0dp"
        android:layout_height="0.5dp"
        android:background="@color/grayLine"
        app:layout_constraintTop_toBottomOf="@+id/btnUpgrade"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_goneMarginTop="20dp"
        android:layout_marginStart="@dimen/margin_large"
        android:layout_marginEnd="@dimen/margin_large"
        android:layout_marginTop="20dp"
    />

    <androidx.appcompat.widget.AppCompatTextView
        app:layout_constraintTop_toBottomOf="@+id/btnUpgrade"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_goneMarginTop="40dp"
        android:layout_marginTop="40dp"
        android:layout_marginStart="@dimen/margin_normal"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:textSize="@dimen/font_normal"
        android:textColor="@color/textDark"
        app:fontFamily="@font/sanspro_regular"
        android:text="@string/android_support_version"/>

</androidx.constraintlayout.widget.ConstraintLayout>

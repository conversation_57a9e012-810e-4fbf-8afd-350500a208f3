<?xml version="1.0" encoding="utf-8"?>
<LinearLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools" android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical"
    android:gravity="center">

    <View android:layout_width="match_parent"
        android:layout_height="20dp"/>

    <androidx.appcompat.widget.AppCompatImageView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:srcCompat="@drawable/ic_finish"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:textAllCaps="true"
        android:text="@string/register_success"
        android:textColor="#7ed321"
        app:fontFamily="@font/oswald_regular"
        android:layout_marginTop="@dimen/margin_small"
        android:textSize="@dimen/font_large"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvFirstContent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/register_success_notice"
        android:textColor="@color/textBlack"
        android:gravity="center"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        app:fontFamily="@font/sanspro_regular"
        android:layout_marginTop="10dp"
        android:textSize="@dimen/font_normal"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvCenterContent"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        tools:text="9782185950"
        android:visibility="gone"
        tools:visibility="visible"
        android:gravity="center"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        android:textColor="@color/black"
        app:fontFamily="@font/sanspro_bold"
        android:textSize="18sp"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvSecondContent"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        tools:visibility="visible"
        android:gravity="center"
        android:paddingLeft="10dp"
        android:paddingRight="10dp"
        android:text="@string/register_success_notice"
        android:textColor="@color/textBlack"
        app:fontFamily="@font/sanspro_regular"
        android:textSize="@dimen/font_normal"/>

    <View android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="30dp"
        android:background="@color/grayLine"/>

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btnOkSuccess"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="@string/agree"
        android:textColor="@color/colorPrimaryDark"
        android:fontFamily="@font/sanspro_bold"
        android:background="?attr/selectableItemBackground"/>

    <View android:layout_width="match_parent"
        android:layout_height="5dp"/>
</LinearLayout>
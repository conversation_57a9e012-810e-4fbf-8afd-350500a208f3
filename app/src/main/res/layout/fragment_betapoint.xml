<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent" xmlns:tools="http://schemas.android.com/tools"
    android:clickable="true"
    android:descendantFocusability="beforeDescendants"
    android:fitsSystemWindows="true"
    android:focusable="true"
    android:focusableInTouchMode="true"
    android:orientation="vertical"
    android:background="@color/grayBg">

    <LinearLayout
        android:id="@+id/action_bar"
        android:layout_width="match_parent"
        android:layout_height="?actionBarSize"
        android:layout_marginTop="@dimen/statusBarHeight"
        android:background="@drawable/color_toolbar"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/btnBack"
            android:layout_width="?actionBarSize"
            android:layout_height="?actionBarSize"
            android:padding="5dp"
            app:srcCompat="@drawable/ic_chevron_left_white_24dp"/>

        <androidx.appcompat.widget.AppCompatTextView
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:padding="@dimen/padding_small"
            android:text="@string/beta_point"
            android:textColor="@color/white"
            android:textSize="@dimen/font_large"
            app:fontFamily="@font/sanspro_bold"/>

        <androidx.appcompat.widget.AppCompatImageView
            android:id="@+id/btnHistories"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:padding="7dp"
            app:srcCompat="@drawable/ic_point_history"/>
    </LinearLayout>

    <View android:layout_width="0dp" android:layout_height="0dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/action_bar"
        app:layout_constraintBottom_toBottomOf="@+id/tvAccumulationPoint"
        android:background="@drawable/shape_white_radius"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        android:layout_marginTop="20dp"
        android:id="@+id/viewBg"
    />

    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@+id/viewBg"
        android:layout_marginTop="@dimen/margin_large"
        app:fontFamily="@font/sanspro_semi_bold"
        android:textSize="10sp"
        android:gravity="center"
        android:textColor="@color/textb9b9b9"
        android:text="@string/used_point" android:id="@+id/appCompatTextView3"
        app:layout_constraintStart_toEndOf="@+id/appCompatTextView"
        app:layout_constraintHorizontal_bias="0.5"
        android:layout_marginStart="10dp"
        android:layout_marginEnd="@dimen/margin_large" app:layout_constraintEnd_toEndOf="parent"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvAccumulationPoint"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:fontFamily="@font/sanspro_bold"
        android:textSize="20sp"
        android:textColor="#03599d"
        android:layout_marginTop="3dp"
        android:paddingBottom="20dp"
        tools:text="107"
        app:layout_constraintTop_toBottomOf="@+id/appCompatTextView2"
        android:gravity="center"
        app:layout_constraintStart_toStartOf="@+id/appCompatTextView2"
        app:layout_constraintEnd_toEndOf="@+id/appCompatTextView2"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@+id/viewBg"
        android:layout_marginLeft="10dp"
        app:fontFamily="@font/sanspro_semi_bold"
        android:layout_marginTop="@dimen/margin_large"
        android:textSize="10sp"
        android:gravity="center"
        android:textColor="@color/textb9b9b9"
        android:text="@string/accumulation_point"
        android:id="@+id/appCompatTextView"
        app:layout_constraintStart_toEndOf="@+id/appCompatTextView2"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintEnd_toStartOf="@+id/appCompatTextView3"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvUsedPoint"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:fontFamily="@font/sanspro_bold"
        android:textSize="20sp"
        android:textColor="#fd2802"
        android:layout_marginTop="3dp"
        app:layout_constraintTop_toBottomOf="@+id/appCompatTextView3"
        android:gravity="center"
        app:layout_constraintStart_toStartOf="@+id/appCompatTextView3"
        app:layout_constraintEnd_toEndOf="@+id/appCompatTextView3"
        tools:text="90"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:layout_constraintTop_toTopOf="@+id/viewBg"
        android:layout_marginLeft="@dimen/margin_large"
        app:fontFamily="@font/sanspro_semi_bold"
        android:layout_marginTop="@dimen/margin_large"
        android:gravity="center"
        android:textSize="10sp"
        android:textColor="@color/textb9b9b9"
        android:text="@string/available_point"
        android:id="@+id/appCompatTextView2"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintEnd_toStartOf="@+id/appCompatTextView"/>

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvAvailablePoint"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        app:fontFamily="@font/sanspro_bold"
        android:textSize="20sp"
        android:textColor="#7ed321"
        android:layout_marginTop="3dp"
        app:layout_constraintTop_toBottomOf="@+id/appCompatTextView"
        android:gravity="center"
        app:layout_constraintStart_toStartOf="@+id/appCompatTextView"
        app:layout_constraintEnd_toEndOf="@+id/appCompatTextView"
        tools:text="17"/>

    <androidx.appcompat.widget.AppCompatButton
        android:id="@+id/btnGivePoint"
        style="@style/ButtonPrimary"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="20dp"
        android:layout_marginTop="@dimen/margin_large"
        android:text="@string/give_point"
        android:layout_marginLeft="16dp"
        android:layout_marginRight="16dp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toBottomOf="parent" />

    <androidx.appcompat.widget.AppCompatTextView
        android:id="@+id/tvRewardPoint"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:textSize="14sp"
        android:textColor="@color/textff3377"
        android:layout_marginTop="20dp"
        android:layout_marginLeft="10dp"
        android:layout_marginRight="10dp"
        app:layout_constraintTop_toBottomOf="@+id/viewBg"
        android:gravity="center"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        android:maxLines="2"
        app:fontFamily="@font/sanspro_regular"
        tools:text="17"/>

</androidx.constraintlayout.widget.ConstraintLayout>

<resources xmlns:tools="http://schemas.android.com/tools">

    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorPrimary</item>
        <item name="android:actionMenuTextColor">@color/white</item>
        <item name="actionMenuTextColor">@color/white</item>
        <item name="android:windowTranslucentStatus">false</item>
        <item name="android:windowTranslucentNavigation">true</item>
        <item name="android:fontFamily">@font/sanspro_regular</item>
        <item name="fontFamily">@font/sanspro_regular</item>
    </style>

    <style name="AppTheme.BarCode" parent="AppTheme">
        <item name="colorPrimaryDark">@color/text1e1f28</item>
    </style>

    <style name="FBAppTheme" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorPrimary</item>
    </style>

    <style name="AppTheme.NoActionBar">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
    </style>

    <style name="AppThemeFullScreen" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorPrimary</item>

        <item name="android:windowFullscreen">true</item>
    </style>

    <style name="AppTheme.AppBarOverlay" parent="ThemeOverlay.AppCompat.Dark.ActionBar" />

    <style name="AppTheme.PopupOverlay" parent="ThemeOverlay.AppCompat.Light" />

    <style name="ButtonOrange">
        <item name="android:background">@drawable/shape_orange_gradient_radius</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">@dimen/font_large</item>
        <item name="android:fontFamily">@font/oswald_regular</item>
        <item name="fontFamily">@font/oswald_regular</item>
    </style>

    <style name="ButtonGray">
        <item name="android:background">@color/buttonGray</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">@dimen/font_large</item>
        <item name="android:fontFamily">@font/oswald_regular</item>
        <item name="fontFamily">@font/oswald_regular</item>
    </style>

    <style name="ButtonPrimary">
        <item name="android:background">@drawable/shape_primary_gradient_radius</item>
        <item name="android:textColor">@color/white</item>
        <item name="android:textSize">@dimen/font_large</item>
        <item name="android:fontFamily">@font/oswald_regular</item>
        <item name="fontFamily">@font/oswald_regular</item>
    </style>

    <style name="TextHighlight">
        <item name="android:textColor">@color/colorPrimaryDark</item>
    </style>

    <style name="TextTitle">
        <item name="android:textColor">@color/textDark</item>
        <item name="android:textSize">@dimen/font_large</item>
        <item name="android:fontFamily">@font/oswald_regular</item>
        <item name="fontFamily">@font/oswald_regular</item>
    </style>

    <style name="TextContent">
        <item name="android:textColor">@color/textDark</item>
        <item name="android:textSize">@dimen/font_normal</item>
        <item name="android:fontFamily">@font/sanspro_regular</item>
        <item name="fontFamily">@font/sanspro_regular</item>
    </style>

    <style name="TextContentItalic">
        <item name="android:textColor">@color/textDark</item>
        <item name="android:textSize">@dimen/font_normal</item>
        <item name="android:fontFamily">@font/sanspro_italic</item>
        <item name="fontFamily">@font/sanspro_italic</item>
    </style>

    <style name="DatePickerStyle">
        <item name="android:textSize">18sp</item>
        <item name="android:textColorPrimary">@color/colorPrimary</item>
        <item name="android:colorControlNormal" tools:targetApi="lollipop">@color/colorPrimary</item>
    </style>

    <style name="TextTabLayoutCustom" parent="@android:style/TextAppearance.Widget.TabWidget">
        <item name="android:textSize">16sp</item>
        <item name="android:fontFamily">@font/oswald_bold</item>
        <item name="fontFamily">@font/oswald_bold</item>
        <item name="android:textAllCaps">true</item>
    </style>

</resources>

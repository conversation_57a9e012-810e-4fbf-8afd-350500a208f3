package vn.zenity.betacineplex.base

import android.graphics.Point
import android.os.Bundle
import androidx.fragment.app.DialogFragment
import android.view.Gravity
import android.view.View
import android.view.WindowManager
import vn.zenity.betacineplex.app.App

/**
 * Created by tranduc on 1/5/18.
 */
abstract class BaseDialogFragment: DialogFragment() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setStyle(androidx.fragment.app.DialogFragment.STYLE_NO_TITLE, android.R.style.Theme_Holo_Light_Dialog)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        dialog?.window?.setSoftInputMode(
                WindowManager.LayoutParams.SOFT_INPUT_STATE_VISIBLE)
    }

    protected open val heightOver: Int = WindowManager.LayoutParams.WRAP_CONTENT

    override fun onResume() {
//        val window = dialog.window
//        val size = Point()
//        val display = window.windowManager.defaultDisplay
//        display.getSize(size)
//
//        val de = App.shared().resources.displayMetrics.density
//        val width = if (size.x > (400 * de) / 0.85) (400.0 * de) else (size.x * 0.85)
//        val height = if (size.y > (540 * de) / 0.85) (540.0 * de) else (size.y * 0.85)
//
//        window.setLayout(width.toInt(), height.toInt())
//        window.setGravity(Gravity.CENTER)

        super.onResume()
    }
}
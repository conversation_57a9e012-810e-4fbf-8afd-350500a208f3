package vn.zenity.betacineplex.view.recruitment

import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.model.Notification

/**
 * Created by Zenity.
 */

interface RecruitmentContractor {
    interface View : IBaseView {
        fun showListNotifications(notifes: List<Notification>)
    }

    interface Presenter : IBasePresenter<View> {
        fun getListNotification(page: Int)
    }
}

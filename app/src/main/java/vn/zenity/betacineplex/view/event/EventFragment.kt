package vn.zenity.betacineplex.view.event

import android.os.Bundle
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentPagerAdapter
import android.view.View
import kotlinx.android.synthetic.main.fragment_event.*
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.helper.extension.getString
import vn.zenity.betacineplex.model.NewModel

/**
 * Created by Zenity.
 */

class EventFragment : BaseFragment(), EventContractor.View {

    private val presenter = EventPresenter()
    private var listNewsCategories = listOf<NewModel>()

    override fun getPresenter(): IBasePresenter<IBaseView>? {
        return presenter as? IBasePresenter<IBaseView>
    }

    override fun isShowToolbar(): Boolean {
        return false
    }

    override fun getLayoutRes(): Int {
        return R.layout.fragment_event
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        viewPager.adapter = PAdapter(childFragmentManager)
        tabLayout.setupWithViewPager(viewPager)
        presenter.getListNewsCategories()
    }

    override fun showListNewsCategories(list: List<NewModel>) {
        this.listNewsCategories = list
        activity?.runOnUiThread {
            viewPager.adapter?.notifyDataSetChanged()
        }
    }

    inner class PAdapter(fm: FragmentManager): FragmentPagerAdapter(fm) {
        override fun getItem(position: Int): Fragment {
            return ListEventFragment.getInstance(listNewsCategories[position].CategoryId ?: "")
        }

        override fun getCount() = listNewsCategories.size

        override fun getPageTitle(position: Int): CharSequence {
            if (position == 0) {
                val name = listNewsCategories[position].Name
                if (name?.startsWith("Khuy") == true) return R.string.new_deals.getString()
                return listNewsCategories[position].Name ?: R.string.new_deals.getString()
            }
            val name = listNewsCategories[position].Name
            if (name?.startsWith("Tin") == true) return R.string.news_beside.getString()
            return listNewsCategories[position].Name ?: R.string.news_beside.getString()
        }
    }
}

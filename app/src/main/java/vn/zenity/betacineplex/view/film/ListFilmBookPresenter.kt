package vn.zenity.betacineplex.view.film

import java.lang.ref.WeakReference

/**
 * Created by Zenity.
 */

class ListFilmBookPresenter : ListFilmBookContractor.Presenter {
    private var view: WeakReference<ListFilmBookContractor.View?>? = null
    override fun attachView(view: ListFilmBookContractor.View) {
        this.view = WeakReference(view)
    }

    override fun detachView() {
        this.view?.clear()
        this.view = null
    }
}

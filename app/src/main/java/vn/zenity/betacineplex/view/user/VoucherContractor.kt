package vn.zenity.betacineplex.view.user

import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.model.VoucherModel

/**
 * Created by Zenity.
 */

interface VoucherContractor {
    interface View : IBaseView {
        fun showListVoucher(vouchers: List<VoucherModel>)
        fun registerVoucherSuccess()
    }

    interface Presenter : IBasePresenter<View> {
        fun registerVoucher(code: String, pin: String)
        fun getListVoucher(accountId: String, cardTypeName: String)
    }
}

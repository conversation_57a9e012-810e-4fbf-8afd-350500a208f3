package vn.zenity.betacineplex.view.user.point

import io.reactivex.disposables.Disposable
import vn.zenity.betacineplex.Manager.Network.APIClient
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.helper.extension.applyOn
import vn.zenity.betacineplex.helper.extension.getString
import java.lang.ref.WeakReference

/**
 * Created by Zenity.
 */

class PointHistoryPresenter : PointHistoryContractor.Presenter {

    private var disposable: Disposable? = null
    override fun getUsedPointHistories() {
        disposable = APIClient.shared.accountAPI.getUsedPointHistories().applyOn()
                .subscribe({
                    if (it.isSuccess) {
                        view?.get()?.showUsedPointHistories(it.Data ?: listOf())
                    } else {
                        view?.get()?.showAlert(it.Message ?: R.string.get_payment_history_error.getString())
                    }
                    view?.get()?.hideLoading()
                }, {
                    view?.get()?.showAlert(it.message ?: R.string.get_payment_history_error.getString())
                    view?.get()?.hideLoading()
                })
    }

    private var view: WeakReference<PointHistoryContractor.View?>? = null
    override fun attachView(view: PointHistoryContractor.View) {
        this.view = WeakReference(view)
    }

    override fun detachView() {
        this.disposable?.dispose()
        this.view?.clear()
        this.view = null
    }
}

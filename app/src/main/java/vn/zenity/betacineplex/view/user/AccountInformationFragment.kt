package vn.zenity.betacineplex.view.user

import android.os.Bundle
import android.view.View
import com.tsongkha.spinnerdatepicker.SpinnerDatePickerDialogBuilder
import kotlinx.android.synthetic.main.fragment_accountinformation.*
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.global.Constant
import vn.zenity.betacineplex.global.Global
import vn.zenity.betacineplex.helper.extension.*
import vn.zenity.betacineplex.model.CityModel
import vn.zenity.betacineplex.model.RequestModel.RegisterModel
import vn.zenity.betacineplex.model.UserModel
import java.util.*

/**
 * Created by Zenity.
 */

class AccountInformationFragment : BaseFragment(), AccountInformationContractor.View {

    companion object {
        fun getInstance(listener: (() -> Unit)? = null): AccountInformationFragment {
            val frag = AccountInformationFragment()
            frag.currentUser = Global.share().user
            frag.listener = listener
            frag.currentUser?.let {
                frag.registerModel.insertData(it)
            }
            return frag
        }
    }

    private var listener: (() -> Unit)? = null
    private val registerModel = RegisterModel()

    private val presenter = AccountInformationPresenter()

    private var currentUser: UserModel? = null
    private var birthDay: String? = null
    private var year: Int? = null
    private var month: Int? = null
    private var day: Int? = null

    override fun getPresenter(): IBasePresenter<IBaseView>? {
        return presenter as? IBasePresenter<IBaseView>
    }

    override fun isShowToolbar(): Boolean {
        return false
    }

    override fun getLayoutRes(): Int {
        return R.layout.fragment_accountinformation
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        showUserProfile()

        selectionGender.setOnClickListener {
            context?.let {
                showGenderSelection(it, getString(R.string.select_gender)) { id, gender ->
                    selectionGender.setText(gender)
                    registerModel.Gender = "$id"
                }
            }
        }

        selectionBirthday.setOnClickListener {
            val calendar = Calendar.getInstance()
            SpinnerDatePickerDialogBuilder()
                    .context(context)
                    .callback { _, year, monthOfYear, dayOfMonth ->
                        birthDay = "${if (dayOfMonth < 10) "0" else ""}$dayOfMonth-${if (monthOfYear < 9) "0" else ""}${monthOfYear + 1}-$year"
                        this.year = year
                        this.month = monthOfYear
                        this.day = dayOfMonth
                        selectionBirthday.setText(birthDay)
                    }
                    .spinnerTheme(R.style.DatePickerStyle)
                    .showTitle(true)
                    .showDaySpinner(true)
                    .defaultDate(year ?: calendar.get(Calendar.YEAR) - 10, month ?: 0, day?: 1)
                    .maxDate(calendar.get(Calendar.YEAR) - 10, calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH))
                    .minDate(calendar.get(Calendar.YEAR) - 100, calendar.get(Calendar.MONTH), calendar.get(Calendar.DAY_OF_MONTH))
                    .build()
                    .show()
        }

        selectionDistrict.setOnClickListener {
            registerModel.AddressCityId?.let {
                presenter.getDistrict(it)
            } ?: presenter.getCity()
        }
        selectionCity.setOnClickListener {
            presenter.getCity()
        }
    }

    override fun onDestroyView() {
        listener?.invoke()
        listener = null
        super.onDestroyView()
    }

    private fun showUserProfile() {
        tvEmail.text = this.currentUser?.Email
        edtFullname.text = this.currentUser?.FullName
        edtPhone.text = this.currentUser?.PhoneOffice
        edtAddress.text = this.currentUser?.AddressStreet
        edtCardNumber.text = this.currentUser?.PersonalId
        val arrayGender = context?.resources?.getStringArray(R.array.gender_array)
        var genderId = (this.currentUser?.Gender ?: 0) - 1
        if (genderId > 2) {
            genderId = -1
        }
        selectionGender.setText(if (genderId < 0) "" else "${arrayGender?.get(genderId)}")
        this.currentUser?.AddressCity?.let {
            selectionCity.setText(it)
        }
        this.currentUser?.AddressDistrict?.let {
            selectionDistrict.setText(it)
        }
        currentUser?.BirthDate?.toDate()?.let {
            val cc = Calendar.getInstance()
            cc.time = it
            year = cc.get(Calendar.YEAR)
            month = cc.get(Calendar.MONTH)
            day = cc.get(Calendar.DAY_OF_MONTH)
        }
        this.currentUser?.BirthDate?.let {
            birthDay = it.dateConvertFormat(Constant.DateFormat.default, Constant.DateFormat.dateVi)
            selectionBirthday.setText(birthDay)
        }
        btnUpdate.setOnClickListener {
            validate()
        }
    }

    private fun validate() {
        registerModel.FullName = edtFullname.text.trim()
        if (registerModel.FullName.isEmpty()) {
            showNotice( getString(R.string.fullname_can_not_empty))
            edtFullname.focus()
            return
        }
        registerModel.PhoneOffice = edtPhone.text.trim()
        if (registerModel.PhoneOffice.isBlank()) {
            showNotice( getString(R.string.phone_number_cannot_empty))
            edtPhone.focus()
            return
        }

        registerModel.PersonalId = edtCardNumber.text.trim()

        val birth = birthDay?.dateConvertFormat(Constant.DateFormat.dateVi, Constant.DateFormat.requestServer)
        if (birth == null) {
            showNotice( getString(R.string.birthday_cannot_empty))
            return
        }
        registerModel.AddressStreet = edtAddress.text.trim()
        registerModel.BirthDate = birth

        presenter.updateProfile(currentUser?.AccountId ?: "", registerModel)
    }

    override fun updateUserProfileSuccess(message: String) {
        context?.let {
            showConfirm(it, content = message, rightButtonTitle = R.string.ok.getString(), rightButtonClickHandler = {
                back()
            })
        }
    }

    override fun showUpdatedProfile(user: UserModel) {
        this.currentUser?.FullName = user.FullName
        this.currentUser?.Gender = user.Gender
        this.currentUser?.BirthDate = user.BirthDate
        this.currentUser?.PhoneOffice = user.PhoneOffice
        this.currentUser?.AddressCity = user.AddressCity
        this.currentUser?.AddressCityId = user.AddressCityId
        this.currentUser?.AddressStreet = user.AddressStreet
        this.currentUser?.AddressDistrict = user.AddressDistrict
        this.currentUser?.AddressDistrictId = user.AddressDistrictId
        this.currentUser?.PersonalId = user.PersonalId
        Global.share().user = this.currentUser
        this.currentUser?.let {
            registerModel.insertData(it)
        }
        activity?.runOnUiThread {
            showUserProfile()
        }
    }

    override fun showListCity(citis: ArrayList<CityModel>) {
        activity?.runOnUiThread {
            context?.let {
                showCitiSelection(it, getString(R.string.select_city), citis) {
                    registerModel.AddressCity = it.Name
                    registerModel.AddressCityId = it.Id
                    selectionCity.setText(it.Name)
                    registerModel.AddressDistrict = ""
                    selectionDistrict.setText("")
                }
            }
        }
    }

    override fun showDistrict(cityId: String, districts: ArrayList<CityModel>) {
        activity?.runOnUiThread {
            context?.let {
                showCitiSelection(it, getString(R.string.select_district), districts) {
                    registerModel.AddressDistrict = it.Name
                    selectionDistrict.setText(it.Name)
                }
            }
        }
    }
}

package vn.zenity.betacineplex.view.cenima

import io.reactivex.disposables.Disposable
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.model.CinemaModel
import vn.zenity.betacineplex.model.NewsModel

/**
 * Created by Zenity.
 */

interface CenimaDetailContractor {
    interface View : IBaseView {
        fun showListEvent(listEvents: List<NewsModel>)
        fun showCinemaDetail(cinema: CinemaModel)
    }

    interface Presenter : IBasePresenter<View> {
        fun getCinemaDetail(id: String)
        fun getListEventOfCinema(id: String)
    }
}

package vn.zenity.betacineplex.view.user

import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.model.PaymentHistory

/**
 * Created by Zenity.
 */

interface BookHistoryContractor {
    interface View : IBaseView {
        fun showPaymentHistory(historis: List<PaymentHistory>)
    }

    interface Presenter : IBasePresenter<View> {
        fun getPaymentHistory(accountId: String)
    }
}

package vn.zenity.betacineplex.view.film

import io.reactivex.Observable
import io.reactivex.disposables.CompositeDisposable
import io.reactivex.disposables.Disposable
import io.reactivex.schedulers.Schedulers
import vn.zenity.betacineplex.Manager.Network.APIClient
import vn.zenity.betacineplex.global.Constant
import vn.zenity.betacineplex.helper.extension.applyOn
import vn.zenity.betacineplex.helper.extension.logD
import vn.zenity.betacineplex.model.SeatScreenModel
import vn.zenity.betacineplex.model.ShowTimeModel
import java.lang.ref.WeakReference
import java.util.Date
import java.util.concurrent.TimeUnit

/**
 * Created by Zenity.
 */

class SelectChairPresenter : SelectChairContractor.Presenter {

    private var disposable: Disposable? = null
    private var compositeDisposable: CompositeDisposable = CompositeDisposable()
    override fun getListSeat(showId: String) {
        view?.get()?.showLoading()
        disposable = APIClient.shared.filmAPI.getSeat(showId).applyOn()
            .subscribe({
                if (it.isSuccess) {
                    it.Data?.let { showTime ->
                        disposable = Observable.create<ShowTimeModel> {
                            if (showTime.Screen?.SeatPosition != null) {
                                var seatPosition = showTime.Screen?.SeatPosition!!
                                val mapSeat = hashMapOf<Pair<Int, Int>, SeatScreenModel>()
                                val mapSeatIndex = hashMapOf<Int, Pair<Int, Int>>()
                                val mapNumberNotUsedSeat = hashMapOf<Int, Int>()
//                                    seatPosition = seatPosition.map { it.filter { it.Status?.Value != Constant.SeatStatus.NOT_USED } as ArrayList<SeatScreenModel> } as ArrayList<ArrayList<SeatScreenModel>>
                                for ((index, listSeat) in seatPosition.withIndex()) {
                                    for ((indexC, seat) in listSeat.withIndex()) {
                                        var indexSeat = seat.SeatIndex % (showTime.Screen?.NumberCol
                                            ?: 1)
                                        if (indexSeat == 0) {
                                            indexSeat = (showTime.Screen?.NumberCol ?: 1) - 1
                                        } else {
                                            indexSeat -= 1
                                        }
                                        /// isShowDouble = !prvSeat.isShowDouble Để hiện lối ra
                                        var isShowDouble =
                                            (seat.SeatType?.Value == Constant.SeatType.DOUBLE) && (seat.Status?.Value == Constant.SeatStatus.USED || seat.Status?.Value == Constant.SeatStatus.IN_PROCESSING)
                                        if (isShowDouble) {
                                            if (indexC > 0) {
                                                /// Set Ghế trước đó là double
                                                val prvSeat = listSeat[indexC - 1]
                                                if (prvSeat.Status?.Value == Constant.SeatStatus.USED)
                                                    isShowDouble = !prvSeat.isShowDouble
                                            }
                                        }
                                        seat.isShowDouble = isShowDouble
                                        seat.columnIndex = indexSeat
                                        seat.rowIndex = index
                                        seat.setTicketTypes(showTime.TicketTypes)
                                        mapSeat[Pair(index, indexSeat)] = seat
                                        mapSeatIndex[seat.SeatIndex] = Pair(index, indexSeat)
                                    }
                                    var start =
                                        listSeat.indexOfFirst { it.Status?.Value != Constant.SeatStatus.NOT_USED }
                                    if (start < 0) start = 0
                                    var end =
                                        listSeat.size - listSeat.indexOfLast { it.Status?.Value != Constant.SeatStatus.NOT_USED } - 1
                                    if (end < 0) end = 0
                                    val numberNotUsed = end - start
                                    mapNumberNotUsedSeat[index] = numberNotUsed
                                }
                                showTime.mapNumberNotUsed = mapNumberNotUsedSeat
                                showTime.mapSeat = mapSeat
                                showTime.mapSeatIndex = mapSeatIndex
                                showTime.Screen?.SeatPosition = seatPosition
                            } else {
                                showTime.mapSeat = hashMapOf()
                                showTime.mapSeatIndex = hashMapOf()
                            }
                            it.onNext(showTime)
                            it.onComplete()
                        }.applyOn().subscribe {
                            view?.get()?.showShowTime(it)
                            // ToDo HaiBH check bug 18/02/2024: connectionId null => isConnectedSignalR = false => not hideLoading
                            view?.get()?.hideLoading()
//                            if (view?.get()?.isConnectedSignalR() == true) {
//                                view?.get()?.hideLoading()
//                            }
                        }
                    }
                }
            }, {
                view?.get()?.hideLoading()
            })
    }

    override fun countDownTime(expiredTime: Date) {
        view?.get()?.showCountDownTime(expiredTime.time - System.currentTimeMillis())
        val timerDisposable = Observable.interval(1, TimeUnit.SECONDS, Schedulers.computation())
            .map { expiredTime }
            .map { dateTime -> dateTime.time - System.currentTimeMillis() }
            .filter { it > 0 }
            .applyOn().subscribe {
                logD("remain time = ${it / 1000}")
                if (it <= 0) {
                    compositeDisposable.dispose()
                }
                view?.get()?.showCountDownTime(it)
            }
        compositeDisposable.add(timerDisposable)
    }

    private var view: WeakReference<SelectChairContractor.View?>? = null
    override fun attachView(view: SelectChairContractor.View) {
        this.view = WeakReference(view)
    }

    override fun detachView() {
        compositeDisposable.dispose()
        disposable?.dispose()
        this.view?.clear()
        this.view = null
    }
}

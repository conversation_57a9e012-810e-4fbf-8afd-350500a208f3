package vn.zenity.betacineplex.view.user.voucher

import io.reactivex.disposables.Disposable
import vn.zenity.betacineplex.Manager.Network.APIClient
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.helper.extension.applyOn
import vn.zenity.betacineplex.helper.extension.getString
import java.lang.ref.WeakReference

/**
 * Created by Zenity.
 */

class VoucherHistoryPresenter : VoucherHistoryContractor.Presenter {

    private var disposable: Disposable? = null
    override fun getUserVoucherHitories() {
        disposable = APIClient.shared.accountAPI.getUsedVoucherHistories().applyOn()
                .subscribe({
                    if (it.isSuccess) {
                        view?.get()?.showUseVoucherHistories(it.Data ?: listOf())
                    } else {
                        view?.get()?.showAlert(it.Message ?: R.string.get_payment_history_error.getString())
                    }
                    view?.get()?.hideLoading()
                }, {
                    view?.get()?.showAlert(it.message ?: R.string.get_payment_history_error.getString())
                    view?.get()?.hideLoading()
                })
    }

    private var view: WeakReference<VoucherHistoryContractor.View?>? = null
    override fun attachView(view: VoucherHistoryContractor.View) {
        this.view = WeakReference(view)
    }

    override fun detachView() {
        this.disposable?.dispose()
        this.view?.clear()
        this.view = null
    }
}

package vn.zenity.betacineplex.view.auth

import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.model.NewsModel

/**
 * Created by Zenity.
 */

interface PrivacyPolicyContractor {
    interface View : IBaseView {
        fun showPrivacyPolicy(profile: NewsModel)
    }

    interface Presenter : IBasePresenter<View> {
        fun getPrivacyPolicy()
    }
}

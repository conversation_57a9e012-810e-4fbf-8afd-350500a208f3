package vn.zenity.betacineplex.view.auth

import android.content.Intent
import androidx.appcompat.app.AppCompatActivity
import android.os.Bundle
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.base.BaseActivity
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.global.Constant
import vn.zenity.betacineplex.view.HomeActivity

class LoginActivity : BaseActivity() {

    override fun contentFragment(): BaseFragment? {
        return LoginFragment()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        addContentFragmentIfEmpty()
        disableTranfStatus()
        if (intent.getBooleanExtra(Constant.Key.isReLogin, false)) {
            showMessage(getString(R.string.login_session_expired))
        }
    }

    override fun onBackPressed() {
        finish()
    }

    override fun finish() {
        val intent = Intent(this, HomeActivity::class.java)
        intent.addFlags(Intent.FLAG_ACTIVITY_CLEAR_TOP or Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK)
        startActivity(intent)
        super.finish()
    }
}

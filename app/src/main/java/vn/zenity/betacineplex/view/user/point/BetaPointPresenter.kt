package vn.zenity.betacineplex.view.user.point

import io.reactivex.disposables.Disposable
import vn.zenity.betacineplex.Manager.Network.APIClient
import vn.zenity.betacineplex.helper.extension.applyOn
import java.lang.ref.WeakReference

/**
 * Created by Zenity.
 */

class BetaPointPresenter : BetaPointContractor.Presenter {
    private var disposable: Disposable? = null
    override fun getBetaPoint(accountId: String) {
        view?.get()?.showLoading()
        disposable = APIClient.shared.accountAPI.getBetaPoint(accountId).applyOn().subscribe({
            if (it.isSuccess) {
                view?.get()?.showPoints(
                    it.Data?.TotalPoint ?: 0,
                    it.Data?.TotalSpentPoints ?: 0,
                    it.Data?.TotalAccumulatedPoints ?: 0,
                    it.Data?.AlmostExpiredPoint ?: 0,
                    it.Data?.AlmostExpiredPointDate ?: ""
                )
            }
            view?.get()?.hideLoading()
        }, {
            view?.get()?.hideLoading()
        })

    }

    private var view: WeakReference<BetaPointContractor.View?>? = null
    override fun attachView(view: BetaPointContractor.View) {
        this.view = WeakReference(view)
    }

    override fun detachView() {
        this.view?.clear()
        this.view = null
    }
}

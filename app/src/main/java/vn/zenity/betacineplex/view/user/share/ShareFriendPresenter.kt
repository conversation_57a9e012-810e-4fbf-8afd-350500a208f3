package vn.zenity.betacineplex.view.user.share

import io.reactivex.disposables.Disposable
import vn.zenity.betacineplex.Manager.Network.APIClient
import vn.zenity.betacineplex.global.Global
import vn.zenity.betacineplex.helper.extension.applyOn
import java.lang.ref.WeakReference

/**
 * Created by Zenity.
 */

class ShareFriendPresenter : ShareFriendContractor.Presenter {

    private var dispoable: Disposable? = null

    override fun registerCode(code: String) {
        view?.get()?.showLoading()
        dispoable = APIClient.shared.accountAPI.registerReferCode(code).applyOn()
                .subscribe({
                    view?.get()?.hideLoading()
                    if (it.isSuccess) {
                        view?.get()?.registerCodeSuccess(it.Message ?: "")
                    }else {
                        it.Message?.let { message ->
                            view?.get()?.showAlert(message)
                        }
                    }
                }, {
                    it.message?.let { message ->
                        view?.get()?.showAlert(message)
                    }
                    view?.get()?.hideLoading()
                })
    }

    private var view: WeakReference<ShareFriendContractor.View?>? = null
    override fun attachView(view: ShareFriendContractor.View) {
        this.view = WeakReference(view)
    }

    override fun detachView() {
        this.dispoable?.dispose()
        this.view?.clear()
        this.view = null
    }
}

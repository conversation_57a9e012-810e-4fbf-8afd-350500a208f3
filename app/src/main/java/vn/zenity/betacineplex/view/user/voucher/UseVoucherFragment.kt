package vn.zenity.betacineplex.view.user.voucher

import android.os.Bundle
import android.view.View
import kotlinx.android.synthetic.main.fragment_user_voucher.*
import showBarcode
import showQRcode
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView

/**
 * Created by Zenity.
 */

class UseVoucherFragment : BaseFragment() {

    companion object {
        fun getInstance(voucherId: String): UseVoucherFragment {
            val frag = UseVoucherFragment()
            frag.voucherId = voucherId
            return frag
        }
    }

    private lateinit var voucherId: String

    override fun getPresenter(): IBasePresenter<IBaseView>? {
        return null
    }

    override fun isShowToolbar(): Boolean {
        return false
    }

    override fun getLayoutRes(): Int {
        return R.layout.fragment_user_voucher
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        tvVoucherId.text = voucherId
        cardNumberBarcode.showQRcode(voucherId)
    }
}

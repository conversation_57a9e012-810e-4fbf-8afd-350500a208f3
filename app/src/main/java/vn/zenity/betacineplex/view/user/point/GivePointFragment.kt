package vn.zenity.betacineplex.view.user.point

import android.os.Bundle
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.view.View
import android.view.ViewGroup
import io.reactivex.disposables.Disposable
import kotlinx.android.synthetic.main.fragment_givepoint.*
import kotlinx.android.synthetic.main.item_search_friends.view.*
import load
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.global.Global
import vn.zenity.betacineplex.helper.extension.*
import vn.zenity.betacineplex.model.UserModel
import java.lang.ref.WeakReference

/**
 * Created by Zenity.
 */

class GivePointFragment : BaseFragment(), GivePointContractor.View {

    companion object {
        fun getInstance(isSharePoint: Boolean = true, shareListener: (Boolean) -> Unit): GivePointFragment {
            val frag = GivePointFragment()
            frag.isSharePoint = isSharePoint
            frag.shareListener = (shareListener)
            return frag
        }

        fun getInstance(voucherId: String,
                        voucherName: String,
                        shareListener: (Boolean) -> Unit): GivePointFragment {
            val frag = GivePointFragment()
            frag.isSharePoint = false
            frag.voucherId = voucherId
            frag.voucherName = voucherName

            frag.shareListener = (shareListener)
            return frag
        }
    }

    private var isSharePoint = true
    private var users: List<UserModel> = listOf()
    private var disposable: Disposable? = null
    private var voucherId: String? = null
    private var voucherName: String? = null
    private lateinit var shareListener: (Boolean) -> Unit

    override fun showPoints(accumulation: Int, used: Int, available: Int) {

    }

    override fun showUserSearched(users: List<UserModel>) {
        this.users = users
        this.rvFriends?.adapter?.notifyDataSetChanged()
    }

    override fun giveVoucherFinished(isSuccess: Boolean, message: String) {
        if (isSuccess) {
            context?.let {
                showDialogSuccess(it, getString(R.string.give_voucher_success), message) {
                    shareListener.invoke(true)
                    back()
                }
            }
        } else {
            showAlert(message)
        }
    }


    override fun givePointFinished(isSuccess: Boolean, message: String) {
        if (isSuccess) {
            context?.let {
                showDialogSuccess(it, getString(R.string.give_point_success), message) {
                    shareListener.invoke(true)
                    back()
                }
            }
        } else {
            showAlert(message)
        }
    }

    private val presenter = GivePointPresenter()

    override fun getPresenter(): IBasePresenter<IBaseView>? {
        return presenter as? IBasePresenter<IBaseView>
    }

    override fun isShowToolbar(): Boolean {
        return false
    }

    override fun getLayoutRes(): Int {
        return R.layout.fragment_givepoint
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        val user = Global.share().user
        if (!isSharePoint) {
            tvTitle.text = getString(R.string.donate_voucher)
        } else {
            presenter.getBetaPoint(user?.AccountId ?: return)
        }
        rvFriends.layoutManager = LinearLayoutManager(context)
        rvFriends.adapter = Adapter()
        disposable = edtSearch.textChanges().applyOn().subscribe {
            presenter.searchUser(it)
        }
    }

    override fun onDestroyView() {
        disposable?.dispose()
        super.onDestroyView()
    }

    inner class Adapter : RecyclerView.Adapter<RecyclerView.ViewHolder>() {

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): RecyclerView.ViewHolder {
            return Holder(parent.inflate(R.layout.item_search_friends))
        }

        override fun getItemCount(): Int {
            return users.size
        }

        override fun onBindViewHolder(holder: RecyclerView.ViewHolder, position: Int) {
            val user = users[position]
            holder.itemView.apply {
                val lp = layoutParams as RecyclerView.LayoutParams
                if (position == 0 || position == 1) {
                    lp.topMargin = 10.px
                } else {
                    lp.topMargin = 0.px
                }
                this.layoutParams = lp
                ivAvatar.load(user.Picture?.toImageUrl(true))
                tvUsername.text = user.FullName
                tvEmail.text = user.Email
                btnGivePoint.click {
                    if (isSharePoint) {
                        context?.let {
                            showDialogSelectPoint(it, user.FullName ?: "", user.Email
                                    ?: "") { data ->
                                showConfirmGivePoint(it, data, user.FullName ?: "", user.Email
                                        ?: "") {
                                    presenter.givePoint(data, user.Email ?: "")
                                }
                            }
                        }
                    } else {
                        context?.let {
                            showConfirmDonateVoucher(it, voucherName ?: "", user.FullName
                                    ?: "", user.Email
                                    ?: "") {
                                presenter.giveVoucher(voucherId ?: "", user.Email ?: "")
                            }
                        }
                    }
                }
            }
        }
    }

    inner class Holder(view: View) : RecyclerView.ViewHolder(view)
}

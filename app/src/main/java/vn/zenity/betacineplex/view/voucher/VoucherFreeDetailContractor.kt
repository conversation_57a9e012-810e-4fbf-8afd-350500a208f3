package vn.zenity.betacineplex.view.voucher

import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.model.NewsModel
import vn.zenity.betacineplex.model.VoucherCodeModel

/**
 * Created by Vinh at 05/08/2019.
 */

interface VoucherFreeDetailContractor {
    interface View : IBaseView {
        fun showVoucherDetail(voucher: NewsModel)
        fun showCode(code: VoucherCodeModel?)
        fun dataNull()
    }

    interface Presenter : IBasePresenter<View> {
        fun getVoucherDetail(id: String)
        fun getCode(id: String)
    }
}

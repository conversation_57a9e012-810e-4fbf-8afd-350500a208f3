package vn.zenity.betacineplex.view.user

import android.annotation.SuppressLint
import android.graphics.Typeface
import android.os.Build
import android.os.Bundle
import android.text.SpannableString
import android.text.Spanned
import android.text.style.ForegroundColorSpan
import android.text.style.StyleSpan
import android.view.View
import com.google.firebase.messaging.FirebaseMessaging
import kotlinx.android.synthetic.main.fragment_betapoint.*
import kotlinx.android.synthetic.main.fragment_member.*
import kotlinx.android.synthetic.main.fragment_member.tvRewardPoint
import load
import showBarcode
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.global.Constant
import vn.zenity.betacineplex.global.Global
import vn.zenity.betacineplex.global.Tracking
import vn.zenity.betacineplex.helper.extension.*
import vn.zenity.betacineplex.helper.support.PhotoPickerDialog
import vn.zenity.betacineplex.model.CardClass
import vn.zenity.betacineplex.model.UserModel
import vn.zenity.betacineplex.service.social.FacebookService
import vn.zenity.betacineplex.view.HomeActivity
import vn.zenity.betacineplex.view.auth.ChangePasswordFragment
import vn.zenity.betacineplex.view.auth.LoginFragment
import vn.zenity.betacineplex.view.user.point.BetaPointFragment
import vn.zenity.betacineplex.view.user.share.ShareFriendFragment
import java.lang.ref.WeakReference


/**
 * Created by Zenity.
 */

class MemberFragment : BaseFragment(), MemberContractor.View {
    private val presenter = MemberPresenter()

    override fun getPresenter(): IBasePresenter<IBaseView>? {
        return presenter as IBasePresenter<IBaseView>?
    }

    override fun isShowToolbar(): Boolean {
        return false
    }

    override fun getLayoutRes(): Int {
        return R.layout.fragment_member
    }

    private var userChangeListener: (UserModel?) -> Unit = { user ->
        if (user == null) {
        } else {
            this.user = user
            activity?.runOnUiThread {
                this.showUserInfo()
            }
        }
    }

    private lateinit var user: UserModel
    private var listCard: List<CardClass> = listOf()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        user = Global.share().user?.let { it } ?: return
        Global.share().listenerUserChange.remove(WeakReference(userChangeListener))
        Global.share().listenerUserChange.add(WeakReference(userChangeListener))
        showUserInfo()
        btnLogout.setOnClickListener {
            showLoading()
            FirebaseMessaging.getInstance().token.addOnSuccessListener { token ->
                if (token.isNotEmpty()) {
                    presenter.unregisterFCMToken(token)
                } else {
                    logoutSuccess()
                }
            }.addOnFailureListener {
                logoutSuccess()
            }
        }

        account.setOnClickListener {
            openFragment(ConfirmPassFragment())
        }

        changPassword.setOnClickListener {
            openFragment(ChangePasswordFragment())
        }

        betaPoint.setOnClickListener {
            openFragment(BetaPointFragment())
        }

        memberCard.setOnClickListener {
            openFragment(MemberCardFragment())
        }

        paymentHistory.setOnClickListener {
            openFragment(BookHistoryFragment())
        }

        shareFriends.click {
            openFragment(ShareFriendFragment())
        }
        val params = (activity as? HomeActivity)?.appParams?.firstOrNull { it.ParamsCode == Constant.AppParamCode.intro }
        var isEnable = true
        params?.let {
            isEnable = it.Value == "1"
        }
        shareFriends.isEnabled = isEnable
        shareFriends.alpha = if (isEnable) 1f else 0.6f
        presenter.getUserProfile(user.AccountId ?: return)
        presenter.getCardClass()
    }

    override fun logoutSuccess() {
        Tracking.share().logout()
        Global.share().user = null
        this.context?.let { it1 -> FacebookService().performLogout(it1) }
        (activity as? HomeActivity)?.numberNotification = 0
        hideLoading()
        back()
        openFragment(LoginFragment())
    }

    override fun showUpdateFBPassword() {
        openFragment(ChangePasswordFragment.getInstance())
    }

    override fun updateAvatarSuccess(url: String, fileImage: String) {
        user.Picture = url
        Global.share().user = user
    }

    override fun updateListCard(cards: List<CardClass>) {
        activity?.runOnUiThread {
            this.listCard = cards.sortedBy { it.Order }
            updateTotalPayment()
        }
    }

    @SuppressLint("SetTextI18n")
    private fun showUserInfo() {
        if (ivAvatar == null) return
        ivAvatar?.load(user.Picture?.toImageUrl(true))
        ivBanner?.load(user.Picture?.toImageUrl(true))
        tvUsername?.text = user.FullName
        tvMemberCardNumber?.text = user.CardNumber
        ivCardCode?.showBarcode(user.CardNumber ?: "")
        tvTotalSpend?.text = (user.TotalBillPayment ?: 0).toLong().toVNDCurrency()
        tvRewardPoints?.text = "${user.TotalPoint ?: 0}"
        ivAvatar?.setOnClickListener {
            val dialog = PhotoPickerDialog.getInstance(true) {
                ivAvatar?.load(it)
                ivBanner?.load(it)
                presenter.uploadAvatar(it, user.AccountId ?: return@getInstance)
            }
            dialog.show(childFragmentManager, "Select Avatar")
        }
        tvRewardPoint.rewardPoint(user.AlmostExpiredPoint, user.AlmostExpiredPointDate ?: "")
        updateTotalPayment()
    }

    private fun updateTotalPayment() {
        val topVipCard = listCard.lastOrNull()
        val currentCard = listCard.firstOrNull { it.ClassId == user.ClassId }
        var nextCard: CardClass? = null
        var previousCard: CardClass? = null
        val index = listCard.indexOfFirst { it.ClassId == currentCard?.ClassId }
        if (index >= 0) {
            nextCard = if (index + 1 < listCard.size) {
                listCard[index + 1]
            } else {
                null
            }
            previousCard = if (index - 1 >= 0) {
                listCard[index - 1]
            } else {
                null
            }
        }
        ivIcMember.setImageResource(0)
        ivIcVip.setImageResource(0)
        val paymentVIPCondition = (nextCard ?: topVipCard)?.TotalPaymentCondition ?: 3_000_000
        progressBarVip?.max = paymentVIPCondition
        progressBarVip?.progressDrawable?.setColorFilter(
                R.color.colorAccent.getColor(), android.graphics.PorterDuff.Mode.SRC_IN)
        var updatePoint = user.TotalRemainingBillsToUpgradeClass ?: 0.0

        if (currentCard?.ClassId != null && currentCard.ClassId == topVipCard?.ClassId) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                progressBarVip?.setProgress(paymentVIPCondition, true)
            } else {
                progressBarVip?.progress = paymentVIPCondition
            }
            noticeUpgradeVIP?.visible()
            noticeUpgradeVIP?.text = getString(R.string.congratulation_member)
            currentCard.UrlIcon.toImageUrl().apply {
                ivIcVip.load(this)
            }
            previousCard?.UrlIcon?.toImageUrl()?.let {
                ivIcMember.load(it)
            }
        } else {
            if (updatePoint <= 0) {
                updatePoint = (paymentVIPCondition - (user.TotalBillPayment ?: 0)).toDouble()
            }
            val point = updatePoint.toLong().toVNDCurrency()
            val content = String.format(R.string.notice_upgrade_to_vip.getString(), point, nextCard?.Code ?: "")
            val sb = SpannableString(content)
            sb.setSpan(
                ForegroundColorSpan(R.color.black.getColor()), 0, content.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            sb.setSpan(
                StyleSpan(Typeface.BOLD), 0, content.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            sb.setSpan(
                ForegroundColorSpan(R.color.textRed.getColor()),
                content.indexOf(point),
                content.indexOf(point) + point.length,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            noticeUpgradeVIP?.text = sb
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
                progressBarVip?.setProgress((user.TotalBillPayment ?: 0), true)
            } else {
                progressBarVip?.progress = (user.TotalBillPayment ?: 0)
            }
            noticeUpgradeVIP?.visible()
            currentCard?.UrlIcon?.toImageUrl()?.let {
                ivIcMember.load(it)
            }
            nextCard?.UrlIcon?.toImageUrl()?.let {
                ivIcVip.load(it)
            }
        }
        tvTotalSpendProgress?.text = paymentVIPCondition.toLong().toVNDCurrency("")
        if (nextCard != null && (user.TotalBillPayment ?: 0) >= paymentVIPCondition) {
            noticeUpgradeVIP?.visible()
            val hour = "23:59"
            val content = String.format(R.string.member_next_vip.getString(), hour)
            val sb = SpannableString(content)
            sb.setSpan(
                ForegroundColorSpan(R.color.black.getColor()), 0, content.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            sb.setSpan(
                StyleSpan(Typeface.BOLD), 0, content.length, Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            sb.setSpan(
                ForegroundColorSpan(R.color.textRed.getColor()),
                content.indexOf(hour),
                content.indexOf(hour) + hour.length,
                Spanned.SPAN_EXCLUSIVE_EXCLUSIVE
            )
            noticeUpgradeVIP?.text = sb
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        Global.share().listenerUserChange.remove(WeakReference(userChangeListener))
    }

    override fun updateMenuNotifi(numberUnread: Int, isResumeUpdate: Boolean) {
        super.updateMenuNotifi(numberUnread, isResumeUpdate)
        if (!Global.share().isLogin && isResumeUpdate) {
            back()
        }
    }

    override fun setUserVisibleHint(isVisibleToUser: Boolean) {
        super.setUserVisibleHint(isVisibleToUser)
        if (isVisibleToUser) {
            if (!Global.share().isLogin) {
                back()
            }
        }
    }
}

package vn.zenity.betacineplex.view.user.voucher

import android.annotation.SuppressLint
import android.graphics.Color
import android.os.Bundle
import androidx.core.content.res.ResourcesCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.text.TextUtils
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import kotlinx.android.synthetic.main.fragment_voucher_history.*
import kotlinx.android.synthetic.main.item_notice_book_history.view.*
import kotlinx.android.synthetic.main.item_voucher_history.view.*
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.global.Constant
import vn.zenity.betacineplex.helper.extension.*
import vn.zenity.betacineplex.model.VoucherHistoryModel
import vn.zenity.betacineplex.model.VoucherModel

/**
 * Created by Zenity.
 */

class VoucherHistoryFragment : BaseFragment(), VoucherHistoryContractor.View {

    private val presenter = VoucherHistoryPresenter()

    override fun getPresenter(): IBasePresenter<IBaseView>? {
        return presenter as? IBasePresenter<IBaseView>
    }

    override fun isShowToolbar(): Boolean {
        return false
    }

    override fun getLayoutRes(): Int {
        return R.layout.fragment_voucher_history
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        recyclerView.layoutManager = LinearLayoutManager(context)
        recyclerView.adapter = Adapter()
        presenter.getUserVoucherHitories()
    }

    override fun showUseVoucherHistories(historis: List<VoucherHistoryModel>) {
        (recyclerView.adapter as? Adapter)?.useVoucherHistories = historis
        (recyclerView.adapter as? Adapter)?.notifyDataSetChanged()
    }

    private inner class Adapter(var useVoucherHistories: List<VoucherHistoryModel> = listOf()) : RecyclerView.Adapter<Holder>() {

        override fun getItemCount(): Int {
            return useVoucherHistories.size + 1
        }

        override fun getItemViewType(position: Int): Int {
            return position
        }

        @SuppressLint("SetTextI18n")
        override fun onBindViewHolder(holder: Holder, position: Int) {
            if (position == 0) {
                holder.itemView.tvNotice.setTextWithSpecialText(getString(R.string.voucher_history_notice), getString(R.string.the_last_3_months)) {
                    it.isUnderlineText = false
                    it.typeface = <EMAIL>?.let { it1 -> ResourcesCompat.getFont(it1, R.font.sanspro_bold) }
                    it.color = holder.itemView.tvNotice.currentTextColor
                    it.linkColor = holder.itemView.tvNotice.currentTextColor
                }
            } else {
                val voucher = useVoucherHistories[position - 1]
                holder.itemView.apply {
                    tvTime.text = voucher.Date?.dateConvertFormat(Constant.DateFormat.requestServer, showFormat = "dd/MM/yyyy,HH:mm")
                    if (TextUtils.isEmpty(voucher.AccountName)) {
                        tvUserGive.gone()
                    } else{
                        tvUserGive.text = "(${voucher.AccountName})"
                        tvUserGive.visible()
                    }
                    tvUseId.text = voucher.Code
                    tvStatus.text = voucher.Status
                    tvDescription.text = voucher.Description
                    tvStatus.setTextColor(Color.parseColor(
                        when(voucher.StatusType) {
                            3 -> "#fd7c02"
                            2 -> "#fd2802"
                            4 -> "#7ed321"
                            else -> "#3fb7f9"
                        }
                    ))
                    val typeName = when (voucher.StatusType) {
                        1 -> R.string.used
                        2 -> R.string.expired
                        3 -> R.string.donated
                        4 -> R.string.received
                        else -> R.string.empty
                    }.getString()
                    tvStatus.text = if (TextUtils.isEmpty(typeName)) voucher.Status else typeName
                }
            }
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): Holder {
            if(viewType == 0) {
                val item = LayoutInflater.from(parent.context).inflate(R.layout.item_notice_book_history, parent, false)
                return Holder(item)
            }
            val item = LayoutInflater.from(parent.context).inflate(R.layout.item_voucher_history, parent, false)
            return Holder(item)
        }
    }

    private inner class Holder(itemView: View) : androidx.recyclerview.widget.RecyclerView.ViewHolder(itemView)
}

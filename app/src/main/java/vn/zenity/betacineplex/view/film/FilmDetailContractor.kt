package vn.zenity.betacineplex.view.film

import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.model.Event
import vn.zenity.betacineplex.model.Film
import vn.zenity.betacineplex.model.FilmModel
import vn.zenity.betacineplex.model.NewsModel

/**
 * Created by Zenity.
 */

interface FilmDetailContractor {
    interface View : IBaseView {
        fun showListEvent(listEvents: List<NewsModel>)
        fun showFilmDetail(film: FilmModel)
    }

    interface Presenter : IBasePresenter<View> {
        fun getListEventOfFilm(film: FilmModel)
        fun getFilmDetail(id: String)
    }
}

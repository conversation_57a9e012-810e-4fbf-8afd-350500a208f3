package vn.zenity.betacineplex.view.setting

import android.content.res.ColorStateList
import android.graphics.Color
import android.os.Build
import android.os.Bundle
import androidx.annotation.DrawableRes
import androidx.annotation.StringRes
import androidx.core.widget.ImageViewCompat
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import kotlinx.android.synthetic.main.fragment_more.*
import kotlinx.android.synthetic.main.fragment_version.*
import kotlinx.android.synthetic.main.item_more.view.*
import vn.zenity.betacineplex.BuildConfig
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.global.Global
import vn.zenity.betacineplex.helper.extension.*
import vn.zenity.betacineplex.helper.view.PopupFragment
import vn.zenity.betacineplex.helper.view.betamenu.BetaMenuItem
import vn.zenity.betacineplex.view.HomeActivity
import vn.zenity.betacineplex.view.auth.LoginFragment
import vn.zenity.betacineplex.view.cenima.CenimaFragment
import vn.zenity.betacineplex.view.cenima.CinemaPriceFragment
import vn.zenity.betacineplex.view.event.EventFragment
import vn.zenity.betacineplex.view.notification.NotificationFragment
import vn.zenity.betacineplex.view.recruitment.RecruitmentFragment
import vn.zenity.betacineplex.view.user.MemberFragment
import vn.zenity.betacineplex.view.voucher.VoucherFreeFragment

class MoreFragment : BaseFragment() {

    private val listMore = listOf(
            MoreMenu(R.string.voucher_free, R.drawable.ic_more_voucher_free, "#0093ee", R.string.menu_voucher),
            MoreMenu(R.string.beta_cenima, R.drawable.ic_more_cinema, "#26c1c9", R.string.menu_beta_cinema),
            MoreMenu(R.string.member_beta, R.drawable.ic_memeber_green, "#81c926", R.string.menu_member_beta),
            MoreMenu(R.string.notification, R.drawable.ic_more_notification, "#fd7b1f", R.string.menu_notification),
            MoreMenu(R.string.recruitment, R.drawable.ic_more_require, "#d81b7b", R.string.menu_recruitment),
            MoreMenu(R.string.setting, R.drawable.ic_more_setting, "#ab7df6", R.string.menu_setting)
    )

    override fun isShowToolbar(): Boolean {
        return false
    }

    override fun getLayoutRes(): Int {
        return R.layout.fragment_more
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        rvMore.layoutManager = GridLayoutManager(context, 2)
        rvMore.adapter = Adapter()
    }

    private inner class Adapter : RecyclerView.Adapter<Holder>() {

        override fun getItemCount(): Int {
            return listMore.size
        }

        override fun onBindViewHolder(holder: Holder, position: Int) {
            holder.itemView.apply {
                val menu = listMore[position]
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    viewBg.imageTintList = ColorStateList.valueOf(Color.parseColor(menu.color))
                } else {
                    ImageViewCompat.setImageTintList(viewBg, ColorStateList.valueOf(Color.parseColor(menu.color)))
                }
                val lp = layoutParams as RecyclerView.LayoutParams
                if (position % 2 == 0) {
                    lp.leftMargin = 12.px
                    lp.rightMargin = 4.px
                } else {
                    lp.rightMargin = 12.px
                    lp.leftMargin = 4.px
                }

                lp.bottomMargin = 12.px
                if (position == 0 || position == 1) {
                    lp.topMargin = 12.px
                } else {
                    lp.topMargin = 0.px
                }

                this.layoutParams = lp
                ivIcon.setImageResource(menu.image)
                tvMoreName.setText(menu.title)
                clickable.click {
                    when (menu.tag.getString()) {
                        R.string.menu_voucher.getString() -> {
                            openFragment(VoucherFreeFragment())
                        }
                        R.string.menu_member_beta.getString() -> {
                            if (Global.share().isLogin) {
                                openFragment(MemberFragment())
                            } else {
                                openFragment(LoginFragment())
                            }
                        }
                        R.string.menu_beta_cinema.getString() -> {
                            openFragment(CenimaFragment.getInstance(CenimaFragment.TYPE_DETAIL))
                        }
                        R.string.menu_news.getString() -> {
                            openFragment(EventFragment())
                        }
                        R.string.menu_recruitment.getString() -> {
                            openFragment(RecruitmentFragment())
                        }
                        R.string.menu_notification.getString() -> {
                            if (Global.share().isLogin) {
                                openFragment(NotificationFragment())
                            } else {
                                (activity as? HomeActivity)?.showMessage(getString(R.string.see_notification_warning))
                            }
                        }
                        R.string.menu_setting.getString() -> {
                            openFragment(SettingFragment())
                        }
                        R.string.ticket_price_menu.getString() -> {
                            PopupFragment.getInstance(CinemaPriceFragment()).showPopup(childFragmentManager)
                        }
                    }
                }
            }
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): Holder {
            val item = LayoutInflater.from(parent.context).inflate(R.layout.item_more, parent, false)
            return Holder(item)
        }
    }

    private inner class Holder(itemView: View) : androidx.recyclerview.widget.RecyclerView.ViewHolder(itemView)

    private class MoreMenu(@StringRes val title: Int, @DrawableRes val image: Int, val color: String, @StringRes val tag: Int)
}
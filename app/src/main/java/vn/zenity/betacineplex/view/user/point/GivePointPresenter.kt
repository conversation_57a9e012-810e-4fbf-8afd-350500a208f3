package vn.zenity.betacineplex.view.user.point

import io.reactivex.disposables.Disposable
import vn.zenity.betacineplex.Manager.Network.APIClient
import vn.zenity.betacineplex.helper.extension.applyOn
import java.lang.ref.WeakReference

/**
 * Created by Zenity.
 */

class GivePointPresenter : GivePointContractor.Presenter {
    private var disposable: Disposable? = null
    private var disposables: ArrayList<Disposable> = arrayListOf()
    private var searchDisposable: Disposable? = null

    override fun getBetaPoint(accountId: String) {
        view?.get()?.showLoading()
        disposable = APIClient.shared.accountAPI.getBetaPoint(accountId).applyOn()
                .subscribe({
                    if (it.isSuccess) {
                        view?.get()?.showPoints(it.Data?.TotalAccumulatedPoints
                                ?: 0, it.Data?.TotalSpentPoints ?: 0, it.Data?.TotalPoint ?: 0)
                    }
                    view?.get()?.hideLoading()
                }, {
                    view?.get()?.hideLoading()
                })

    }

    override fun searchUser(keyword: String) {
        searchDisposable?.dispose()
        searchDisposable = APIClient.shared.accountAPI.searchUser(keyword).applyOn()
                .subscribe({
                    if (it.isSuccess) {
                        view?.get()?.showUserSearched(it.Data ?: listOf())
                    } else {
                        view?.get()?.showUserSearched(listOf())
                    }
                }, {
                    view?.get()?.showUserSearched(listOf())
                })
    }

    override fun giveVoucher(id: String, userId: String) {
        val params = mapOf("VoucherId" to id,
                "Email" to userId)
        val dis = APIClient.shared.accountAPI.giveVoucher(params).applyOn()
                .subscribe({
                    view?.get()?.giveVoucherFinished(it.isSuccess, it.Data ?: (it.Message ?: ""))
                }, {
                    view?.get()?.giveVoucherFinished(false, it.message ?: "")
                })
        disposables.add(dis)
    }

    override fun givePoint(point: Int, userId: String) {
        val params = mapOf("Point" to "$point",
                "Email" to userId)
        val dis = APIClient.shared.accountAPI.givePoint(params).applyOn()
                .subscribe({
                    view?.get()?.givePointFinished(it.isSuccess, it.Data ?: (it.Message ?: ""))
                }, {
                    view?.get()?.givePointFinished(false, it.message ?: "")
                })
        disposables.add(dis)
    }

    private var view: WeakReference<GivePointContractor.View?>? = null
    override fun attachView(view: GivePointContractor.View) {
        this.view = WeakReference(view)
    }

    override fun detachView() {
        disposables.forEach {
            it.dispose()
        }
        this.view?.clear()
        this.view = null
    }
}

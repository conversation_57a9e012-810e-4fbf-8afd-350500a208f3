package vn.zenity.betacineplex.view.auth

import android.os.Build
import android.os.Bundle
import android.text.Html
import android.view.View
import kotlinx.android.synthetic.main.fragment_term.*
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.helper.extension.loadBetaHtml
import vn.zenity.betacineplex.model.NewsModel

/**
 * Created by Zenity.
 */

class TermOfUseFragment : BaseFragment(), TermContractor.View {
    override fun showTerm(profile: NewsModel) {
        activity?.runOnUiThread {
            val content = profile.getFullContent() ?: profile.Tom_tat_noi_dung
            content?.let {
                webView?.loadBetaHtml(it)
            }
        }
    }

    private val presenter = TermPresenter()

    override fun getPresenter(): IBasePresenter<IBaseView>? {
        return presenter as? IBasePresenter<IBaseView>
    }

    override fun isShowToolbar(): <PERSON><PERSON><PERSON> {
        return false
    }

    override fun getLayoutRes(): Int {
        return R.layout.fragment_term
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        presenter.getTerm()
    }
}

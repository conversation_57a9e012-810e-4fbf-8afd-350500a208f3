package vn.zenity.betacineplex.view.film

import android.annotation.SuppressLint
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Context.RECEIVER_NOT_EXPORTED
import android.content.Intent
import android.content.IntentFilter
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.view.View
import android.webkit.*
import kotlinx.android.synthetic.main.fragment_booking_payment.*
import vn.zenity.betacineplex.BuildConfig
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.app.App
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.global.Constant
import vn.zenity.betacineplex.global.Global
import vn.zenity.betacineplex.helper.extension.dateConvertFormat
import vn.zenity.betacineplex.helper.extension.getString
import vn.zenity.betacineplex.helper.extension.logD
import vn.zenity.betacineplex.helper.extension.toImageUrl
import vn.zenity.betacineplex.model.FilmModel
import vn.zenity.betacineplex.model.RequestModel.CreateBookingModel
import vn.zenity.betacineplex.model.ShowTimeModel
import vn.zenity.betacineplex.view.HomeActivity
import vn.zenity.betacineplex.view.auth.PaymentPolicyFragment
import vn.zenity.betacineplex.view.user.BookHistoryDetailFragment
import vn.zenity.betacineplex.view.user.BookHistoryFragment
import java.lang.ref.WeakReference
import java.util.*


class BookingPaymentFragment : BaseFragment(), BookingPaymentContractor.View {

    override fun getLayoutRes() = R.layout.fragment_booking_payment
    private val presenter = BookingPaymentPresenter()
    private var createBookingModel: CreateBookingModel? = null
    private var showTimeModel: ShowTimeModel? = null
    private var filmModel: FilmModel? = null

    private var totalPrice: Int = 0
    private var filmCombo = ""
    private var resetDataListener: WeakReference<(() -> Unit)?> = WeakReference(null)
    private var airPayOrderId: String? = null


    companion object {
        fun getInstance(createBookingModel: CreateBookingModel, showTimeModel: ShowTimeModel, filmModel: FilmModel?, totalPrice: Int, filmCombo: String, resetDataListener: (() -> Unit)? = null): BookingPaymentFragment {
            val frag = BookingPaymentFragment()
            frag.createBookingModel = createBookingModel
            frag.showTimeModel = showTimeModel
            frag.totalPrice = totalPrice
            frag.filmModel = filmModel
            frag.filmCombo = filmCombo
            frag.resetDataListener = WeakReference(resetDataListener)
            return frag
        }
    }

    override fun getPresenter(): IBasePresenter<IBaseView>? {
        return presenter as? IBasePresenter<IBaseView>
    }

    override fun isShowToolbar(): Boolean {
        return false
    }

    @SuppressLint("SetJavaScriptEnabled")
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        webView?.settings?.javaScriptEnabled = true
//        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
//            webView?.settings?.safeBrowsingEnabled = true
//        }
//        webView?.settings?.setSupportMultipleWindows(true)
        webView.settings.loadWithOverviewMode = true
        webView.settings.useWideViewPort = true
        webView?.settings?.domStorageEnabled = true
        webView.scrollBarStyle = WebView.SCROLLBARS_OUTSIDE_OVERLAY
        webView.isScrollbarFadingEnabled = false
        webView?.webChromeClient = BetaChromeClient()
        webView?.webViewClient = BetaWebViewClient()
        webView.addJavascriptInterface(this@BookingPaymentFragment, "androidkit")
//        webView?.postUrl("${BuildConfig.BASE_URL}Booking", Gson().toJson(createBookingModel ?: return).toByteArray(Charsets.UTF_8))
        presenter.getBookingPayment(createBookingModel ?: return)
        val intentFilter = IntentFilter()
        intentFilter.addAction(Constant.INTENT_FILTER_MOMO)

        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            activity?.registerReceiver(receiver, intentFilter,RECEIVER_NOT_EXPORTED)
        }else{
            @Suppress("UnspecifiedRegisterReceiverFlag")
            activity?.registerReceiver(receiver, intentFilter)
        }

        val zaloFilter = IntentFilter()
        zaloFilter.addAction(Constant.INTENT_FILTER_ZALOPAY)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            activity?.registerReceiver(receiver, zaloFilter,RECEIVER_NOT_EXPORTED)
        }else{
            @Suppress("UnspecifiedRegisterReceiverFlag")
            activity?.registerReceiver(receiver, zaloFilter)
        }

    }

    private val receiver = object : BroadcastReceiver() {
        override fun onReceive(content: Context?, intent: Intent?) {
            if (intent != null && intent.action == Constant.INTENT_FILTER_MOMO && intent.getStringExtra(Constant.Momo.orderId)?.isNotEmpty() == true) {
                val orderId = intent.getStringExtra(Constant.Momo.orderId)
                val resultCode = intent.getStringExtra(Constant.Momo.resultCode)
                val requestId = intent.getStringExtra(Constant.Momo.requestId)
                val transId = intent.getStringExtra(Constant.Momo.transId)
                val message = intent.getStringExtra(Constant.Momo.message)
                val responseTime = intent.getStringExtra(Constant.Momo.responseTime)
                val payType = intent.getStringExtra(Constant.Momo.payType)
                val extraData = intent.getStringExtra(Constant.Momo.extraData)
                val partnerCode = intent.getStringExtra(Constant.Momo.partnerCode)
                presenter.setOrderId(orderId)
                loadJs("checkMomoTransactionStatus('$orderId', '$resultCode', '$requestId', '$transId', '$message', '$responseTime', '$payType', '$extraData', '$partnerCode');")
                return
            }

            if (intent != null && intent.action == Constant.INTENT_FILTER_ZALOPAY && intent.getStringExtra(Constant.ZaloPay.appTransId)?.isNotEmpty() == true) {
                val appId = intent.getStringExtra(Constant.ZaloPay.appId)
                val appTransId = intent.getStringExtra(Constant.ZaloPay.appTransId)
                val pmcId = intent.getStringExtra(Constant.ZaloPay.pmcId)
                val bankCode = intent.getStringExtra(Constant.ZaloPay.bankCode)
                val amount = intent.getStringExtra(Constant.ZaloPay.amount)
                val dAmount = intent.getStringExtra(Constant.ZaloPay.dAmount)
                val appStatus = intent.getStringExtra(Constant.ZaloPay.appStatus)
                val checkSum = intent.getStringExtra(Constant.ZaloPay.checkSum)
                presenter.setOrderId(appTransId)
                loadJs("checkZaloPayTransactionStatus('$appId', '$appTransId', '$pmcId', '$bankCode', '$amount', '$dAmount', '$appStatus', '$checkSum');")
            }
        }
    }

    override fun showHtmlBooking(content: String) {
        activity?.runOnUiThread {
            webView?.clearCache(true)
            webView?.loadDataWithBaseURL("${BuildConfig.BASE_URL}Booking", content, "text/html", "utf-8", null)
        }
    }

    override fun getWebView(): WebView? {
        return webView
    }

    override fun getViewContext(): Context? {
        return context;
    }

    override fun getCreateBookingModel(): CreateBookingModel? {
        return createBookingModel
    }

    override fun getShowTimeModel(): ShowTimeModel? {
        return showTimeModel
    }

    override fun getFilmModel(): FilmModel? {
        return filmModel
    }

    override fun onDestroyView() {
        super.onDestroyView()
        activity?.unregisterReceiver(receiver)
        webView?.destroy()
    }

    override fun back() {
        getCurrentType()
    }

    override fun onResume() {
        checkAirPayOrderStatus(airPayOrderId)
        super.onResume()
    }

    private fun checkAirPayOrderStatus(orderId: String?) {
        orderId?.let {
            loadJs("checkShopeePayTransactionStatus('$it');")
        }
    }

    private fun getAirPayOrderId(url: String?) {
        url?.let {
            val uri = Uri.parse(it)
            airPayOrderId = uri.getQueryParameter(Constant.AirPay.orderId)
            presenter.setOrderId(airPayOrderId)
        }
    }

    private fun loadJs(js: String) = webView.evaluateJavascript(js, null)

    private fun getCurrentType(handler: ((String) -> Unit)? = null) {
        webView.evaluateJavascript("screenType") { value ->
            // value is the result returned by the Javascript as JSON
            if (handler != null) {
                handler.invoke(value)
                return@evaluateJavascript
            }
            when (value?.replace("\"", "")) {
                "voucher", "coupon", "beta-point" -> loadJs("backToMain();")
                "payment" -> super.back()
                else -> {
                    if (webView?.canGoBack() == true) {
                        (activity as? HomeActivity)?.startCountdown()
                        webView?.goBack()
                    } else {
                        super.back()
                    }
                }
            }
        }
    }

    private fun showTransactionDetail() {
        webView.evaluateJavascript("getTransactionId();") { value ->
            // value is the result returned by the Javascript as JSON
            val transactionId = value?.replace("\"", "")
            if (transactionId?.isNotEmpty() == true) {
                airPayOrderId = null
                openFragment(BookHistoryDetailFragment.getInstance(transactionId))
            } else {
                super.back()
                (activity as? HomeActivity)?.backToHome()
            }
        }
    }

    @JavascriptInterface
    fun postPaymentSuccess(value: String?) {
        value?.let {
            showPaymentSuccess(it)
        }
    }

    @JavascriptInterface
    fun postMessage(value: String?) {
        when (value?.replace("\"", "")) {
            "policy" -> openFragment(PaymentPolicyFragment())
            "payment_success" -> {
                presenter.trackingPaySuccess()
                showPaymentSuccess()
            }

            "awaiting_payment" -> {
                showPaymentAwaiting()
            }

            "payment_failed" -> {
                (activity as? HomeActivity)?.stopCountdown()
                presenter.trackingPayFail("payment_failed", getString(R.string.payment_failed))
                showNotice(getString(R.string.payment_failed)) {
                    super.back()
                    (activity as? HomeActivity)?.backToHome()
                }
            }

            "booking_seat_failed" -> {
                (activity as? HomeActivity)?.stopCountdown()
                presenter.trackingPayFail("booking_seat_failed", getString(R.string.payment_failed))
                showNotice(getString(R.string.payment_failed)) {
                    super.back()
                    (activity as? HomeActivity)?.backToHome()
                }
            }

            "timeout" -> (activity as? HomeActivity)?.checkCountDown()
        }
    }

    private fun showPaymentSuccess(transactionId: String? = null) {
        resetDataListener.get()?.invoke()
        (activity as? HomeActivity)?.stopCountdown()
        showNoticeWithCancelable(getString(R.string.payment_success), {
            Global.share().user?.AccountId?.let {
                App.shared().updateUserInfo(it)
            }
            transactionId?.let {
                openFragment(BookHistoryDetailFragment.getInstance(transactionId))
                return@showNoticeWithCancelable
            }
            showTransactionDetail()
        }, cancelable = false)
    }

    private fun showPaymentAwaiting() {
        resetDataListener.get()?.invoke()
        (activity as? HomeActivity)?.stopCountdown()
        showNotice(getString(R.string.payment_awaiting)) {
            super.back()
            (activity as? HomeActivity)?.backToHome(BookHistoryFragment.getInstance(true))
        }
    }

    inner class BetaWebViewClient : WebViewClient() {

        override fun shouldOverrideUrlLoading(view: WebView, request: WebResourceRequest): Boolean {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP &&
                    request.url?.host?.contains(Constant.AirPay.fullHost) == true) {
                getAirPayOrderId(request.url.toString())
                presenter.trackingConfirm("airpay")
                openLink(request.url.toString() + "")
                return true
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP &&
                    request.url?.host?.contains(Constant.Momo.fullHost) == true) {
                presenter.trackingConfirm("momo")
                openLink(request.url.toString() + "")
                return true
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP &&
                    request.url?.host?.contains(Constant.ZaloPay.fullHost) == true) {
                presenter.trackingConfirm(Constant.ZaloPay.domain)
                openLink(request.url.toString() + "")
                return true
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP &&
                    request.url?.toString()?.contains(Constant.NoiDia.fullHost) == true) {
                presenter.trackingConfirm("noidia")
                return super.shouldOverrideUrlLoading(view, request)
            }
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP &&
                    request.url?.toString()?.contains(Constant.QuocTe.fullHost) == true) {
                presenter.trackingConfirm("quocte")
                return super.shouldOverrideUrlLoading(view, request)
            }
            return super.shouldOverrideUrlLoading(view, request)
        }

        override fun shouldOverrideUrlLoading(view: WebView?, url: String?): Boolean {
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP &&
                    url?.contains(Constant.AirPay.fullHost) == true) {
                getAirPayOrderId(url)
                presenter.trackingConfirm("airpay")
                openLink(url + "")
                return true
            }
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP &&
                    url?.contains(Constant.Momo.fullHost) == true) {
                presenter.trackingConfirm("momo")
                openLink(url + "")
                return true
            }
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP &&
                    url?.contains(Constant.ZaloPay.fullHost) == true) {
                presenter.trackingConfirm(Constant.ZaloPay.domain)
                openLink(url + "")
                return true
            }
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP &&
                    url?.contains(Constant.NoiDia.fullHost) == true) {
                presenter.trackingConfirm("noidia")
                return super.shouldOverrideUrlLoading(view, url)
            }
            if (Build.VERSION.SDK_INT < Build.VERSION_CODES.LOLLIPOP &&
                    url?.contains(Constant.QuocTe.fullHost) == true) {
                presenter.trackingConfirm("quocte")
                return super.shouldOverrideUrlLoading(view, url)
            }
            return super.shouldOverrideUrlLoading(view, url)
        }

        override fun onPageFinished(view: WebView?, url: String?) {
            super.onPageFinished(view, url)
            if (url?.startsWith("${BuildConfig.BASE_URL}Booking") == true) {
                val jsGetBookingInfo = "var bookingIf = {};" +
                        " bookingIf.FilmName = \"${showTimeModel?.FilmName ?: ""}\";\n" +
                        "    bookingIf.FilmInfo = \"${showTimeModel?.FilmFormatName ?: ""} | ${filmModel?.FilmGenreName} |${
                            filmModel?.Duration
                                    ?: 0
                        } ${R.string.minute.getString()}\";\n" +
                        "    bookingIf.CinemaName = \"${showTimeModel?.TenRap ?: ""}\";\n" +
                        "    bookingIf.DateShow = \"${
                            showTimeModel?.NgayChieu?.dateConvertFormat(Constant.DateFormat.default, Constant.DateFormat.dateSavis)
                                    ?: ""
                        }\";\n" +
                        "    bookingIf.ShowTime = \"${
                            showTimeModel?.GioChieu?.dateConvertFormat(Constant.DateFormat.default, Constant.DateFormat.hourMinute)
                                    ?: ""
                        }\";\n" +
                        "    bookingIf.Combo = \"$filmCombo\";\n" +
                        "    bookingIf.TotalMoney = $totalPrice;\n" +
                        "    bookingIf.Screen = \"${showTimeModel?.Screen?.Code ?: ""}\";\n" +
                        "    bookingIf.FilmPoster = \"${
                            filmModel?.MainPosterUrl?.toImageUrl()
                                    ?: (showTimeModel?.MainPosterUrl?.toImageUrl()
                                            ?: "")
                        }\";" +
                        "getBookingInfo(bookingIf);"
                loadJs(jsGetBookingInfo)

                val jsUser = "var cusI = {};" +
                        "cusI.customerId = '${Global.share().user?.AccountId ?: ""}';\n" +
                        "    cusI.customerCard = '${
                            Global.share().user?.CardNumber
                                    ?: ""
                        }';" +
                        "getCustomerInfo(cusI);"
                loadJs(jsUser)
            }
        }
    }

    inner class BetaChromeClient : WebChromeClient() {
        override fun onReceivedTitle(view: WebView, title: String?) {
            super.onReceivedTitle(view, title)
            tvTitle.text = title
            tvTitle?.postDelayed({
                getCurrentType { value ->
                    when (value.replace("\"", "")) {
                        "voucher", "coupon", "beta-point" -> {}
                        else -> {
//                          if (webView?.canGoBack() == true) {
//                              (activity as? HomeActivity)?.pauseCountdown()
//                          }
                        }
                    }
                }
            }, 200)
        }

        override fun onConsoleMessage(consoleMessage: ConsoleMessage): Boolean {
            logD(consoleMessage.message())
            return super.onConsoleMessage(consoleMessage)
        }

        override fun onJsAlert(view: WebView?, url: String?, message: String?, result: JsResult?): Boolean {
            result?.cancel()
            showNotice(message ?: return true)
            return true
        }

        override fun onJsConfirm(view: WebView?, url: String?, message: String?, result: JsResult?): Boolean {
            result?.cancel()
            showNotice(message ?: return true)
            return true
        }

        override fun onJsPrompt(view: WebView?, url: String?, message: String?, defaultValue: String?, result: JsPromptResult?): Boolean {
            result?.cancel()
            showNotice(message ?: return true)
            return true
        }
    }
}
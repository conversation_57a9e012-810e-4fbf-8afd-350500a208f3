package vn.zenity.betacineplex.view.film

import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.model.ShowTimeModel
import java.util.*

/**
 * Created by Zenity.
 */

interface SelectChairContractor {
    interface View : IBaseView {
        fun showShowTime(showTime: ShowTimeModel)
        fun isConnectedSignalR(): Boolean
        fun showCountDownTime(remainingTime: Long)
    }

    interface Presenter : IBasePresenter<View> {
        fun getListSeat(showId: String)
        fun countDownTime(expiredTime: Date)
    }
}

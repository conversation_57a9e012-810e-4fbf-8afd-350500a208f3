package vn.zenity.betacineplex.view.user.voucher

import android.Manifest
import android.app.Activity
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Bundle
import androidx.core.app.ActivityCompat
import android.view.View
import kotlinx.android.synthetic.main.fragment_add_voucher.*
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.global.Constant
import vn.zenity.betacineplex.helper.extension.click
import vn.zenity.betacineplex.helper.extension.showDialogSuccess
import java.lang.ref.WeakReference

class AddVoucherFragment: BaseFragment(), AddVoucherContractor.View {

    companion object {
        fun getInstance(listener: (Boolean) -> Unit): AddVoucherFragment {
            val frag = AddVoucherFragment()
            frag.listener = listener
            return frag
        }
    }

    private var listener: ((Boolean) -> Unit)? = null
    private val presenter = AddVoucherPresenter()

    override fun getPresenter(): IBasePresenter<IBaseView>? {
        return presenter as? IBasePresenter<IBaseView>
    }

    override fun isShowToolbar(): Boolean {
        return false
    }

    override fun getLayoutRes(): Int {
        return R.layout.fragment_add_voucher
    }

    override fun showAddVoucherSuccess() {
        context?.let {
            showDialogSuccess(it, getString(R.string.add_new_success), getString(R.string.add_voucher_success)) {
                listener?.invoke(true)
                back()
            }
        }
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        btnScan.click {
            if(context != null && ActivityCompat.checkSelfPermission(context!!, Manifest.permission.CAMERA) == PackageManager.PERMISSION_GRANTED) {
                startActivityForResult(Intent(activity, BarcodeScanActivity::class.java), 114)
            } else {
                activity?.let {
                    requestPermissions(arrayOf(Manifest.permission.CAMERA), 113)
                }
            }
        }
        btnRegister.click {
            val code = edtCouponCode.text.toString().trim()
            val pin = edtCouponPin.text.toString().trim()
            if (code.isEmpty() || pin.isEmpty()) {
                showNotice(getString(R.string.voucher_code_can_not_empty))
                return@click
            }
            presenter.registerVoucher(code, pin)
        }
    }

    override fun onRequestPermissionsResult(requestCode: Int, permissions: Array<out String>, grantResults: IntArray) {
        super.onRequestPermissionsResult(requestCode, permissions, grantResults)
        if (requestCode == 113 && grantResults.isNotEmpty() && grantResults[0] == PackageManager.PERMISSION_GRANTED) {
            startActivityForResult(Intent(activity, BarcodeScanActivity::class.java), 114)
        }
    }

    override fun onActivityResult(requestCode: Int, resultCode: Int, data: Intent?) {
        super.onActivityResult(requestCode, resultCode, data)
        if (requestCode == 114 && resultCode == Activity.RESULT_OK) {
            val dataVoucher = data?.getStringExtra(Constant.Key.data) ?: return
            val split = dataVoucher.split("-")
            if (split.size > 2 || split.isEmpty()) return
            edtCouponCode.setText(split[0])
            if (split.size == 2) {
                edtCouponPin.setText(split[1])
            }
        }
    }

    override fun onDestroyView() {
        listener = null
        super.onDestroyView()
    }
}
package vn.zenity.betacineplex.view.setting

import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.model.TopicModel

/**
 * Created by Zenity.
 */

interface QuestionAnswerContractor {
    interface View : IBaseView {
        fun showListCategories(categories: List<TopicModel>)
    }

    interface Presenter : IBasePresenter<View> {
        fun getCategories()
    }
}

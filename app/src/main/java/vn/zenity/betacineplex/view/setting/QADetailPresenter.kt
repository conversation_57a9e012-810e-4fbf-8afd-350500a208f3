package vn.zenity.betacineplex.view.setting

import io.reactivex.disposables.Disposable
import vn.zenity.betacineplex.Manager.Network.APIClient
import vn.zenity.betacineplex.helper.extension.applyOn
import java.lang.ref.WeakReference

/**
 * Created by Zenity.
 */

class QADetailPresenter : QADetailContractor.Presenter {

    private var disposable: Disposable? = null

    override fun getQADetail(qaId: String) {
        view?.get()?.showLoading()
        disposable = APIClient.shared.ecmAPI.getFAQ(qaId).applyOn()
                .subscribe({
                    if (it.isSuccess) {
                        it.Data?.let {
                            view?.get()?.showListQADetail(it)
                        }
                    }
                    view?.get()?.hideLoading()
                }, {
                    view?.get()?.hideLoading()
                })
    }

    private var view: WeakReference<QADetailContractor.View?>? = null
    override fun attachView(view: QADetailContractor.View) {
        this.view = WeakReference(view)
    }

    override fun detachView() {
        disposable?.dispose()
        this.view?.clear()
        this.view = null
    }
}

package vn.zenity.betacineplex.view.film

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.LayoutInflater
import androidx.recyclerview.widget.LinearLayoutManager
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.RecyclerView
import com.thoughtbot.expandablerecyclerview.MultiTypeExpandableRecyclerViewAdapter
import com.thoughtbot.expandablerecyclerview.listeners.OnGroupClickListener
import com.thoughtbot.expandablerecyclerview.models.ExpandableGroup
import com.thoughtbot.expandablerecyclerview.viewholders.ChildViewHolder
import com.thoughtbot.expandablerecyclerview.viewholders.GroupViewHolder
import kotlinx.android.synthetic.main.fragment_cenima.*
import kotlinx.android.synthetic.main.item_area_in_list.view.*
import kotlinx.android.synthetic.main.item_area_title_in_list.view.*
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.helper.extension.*
import vn.zenity.betacineplex.model.AreaCinema
import vn.zenity.betacineplex.model.CinemaModel
import vn.zenity.betacineplex.model.CinemaProvinceModel
import vn.zenity.betacineplex.view.cenima.CenimaContractor
import vn.zenity.betacineplex.view.cenima.CenimaPresenter

/**
 * Created by Zenity.
 */

class SelectCenimaByAreaFragment : BaseFragment(), CenimaContractor.View {

    override fun showListCinema(listNear: List<CinemaModel>, listArea: List<CinemaProvinceModel>) {
        val listData = mutableListOf<AreaCinema>()
        totalCinema = 0
        for (area in listArea) {
            listData.add(AreaCinema(area.CityName, listOf(), area = area))
            totalCinema += (area.ListCinema?.size ?: 0)
        }
        activity?.runOnUiThread {
            adapter = Adapter(listData)
            recyclerView.adapter = adapter
        }
    }

    companion object {
        val TYPE_HEADER_TITLE = 0
        val TYPE_HEADER_NEAR_ME = 1
        val TYPE_HEADER_AREA = 2
        var TYPE_DETAIL = 0

        fun getInstance(type: Int = TYPE_DETAIL /*0: View Detail, 1: Book*/, listenerSelectArea: ((CinemaProvinceModel?) -> Unit)? = null): SelectCenimaByAreaFragment {
            val frag = SelectCenimaByAreaFragment()
            frag.type = type
            frag.listenerSelectArea = listenerSelectArea
            return frag
        }
    }

    private var listenerSelectArea: ((CinemaProvinceModel?) -> Unit)? = null
    private val presenter = CenimaPresenter()
    private var adapter: Adapter? = null
    private var type = TYPE_DETAIL
    private var totalCinema = 0

    override fun getPresenter(): IBasePresenter<IBaseView>? {
        return presenter as? IBasePresenter<IBaseView>
    }

    override fun isShowToolbar(): Boolean {
        return false
    }

    override fun getLayoutRes(): Int {
        return R.layout.fragment_cenima
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        btnBack.visible()
        btnBack.click {
            back()
        }
        recyclerView.layoutManager = LinearLayoutManager(this.context)
        presenter.getCinema(null)
        tvTitle?.text = R.string.select_area.getString()
    }

    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
    }

    override fun onViewStateRestored(savedInstanceState: Bundle?) {
        super.onViewStateRestored(savedInstanceState)
    }

    private inner class Adapter(val groups: MutableList<AreaCinema>?) : RecyclerView.Adapter<Holder>() {

        override fun getItemCount(): Int {
            return if (groups.isNullOrEmpty()) 0 else (groups.size + 1)
        }

        override fun onBindViewHolder(holder: Holder, position: Int) {
            if (position == 0) {
                holder.itemView.apply {
                    tvAreaTitle.text = R.string.All.getString()
                    tvAreaCountCinema.text = "$totalCinema"
                    ivDropdown.rotation = 90f
                    setOnClickListener {
                        listenerSelectArea?.invoke(null)
                        back()
                    }
                }
            } else {
                holder.itemView.apply {
                    val group = groups!![position - 1]
                    tvAreaTitle.text = group.title
                    tvAreaCountCinema.text = "${group.area?.ListCinema?.size ?: 0}"
                    ivDropdown.rotation = 90f
                    setOnClickListener {
                        listenerSelectArea?.invoke((group as? AreaCinema)?.area
                                ?: return@setOnClickListener)
                        back()
                    }
                }
            }
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): Holder {
            val item = LayoutInflater.from(parent.context).inflate(R.layout.item_area_in_list, parent, false)
            return Holder(item)
        }
    }

    private inner class Holder(itemView: View) : RecyclerView.ViewHolder(itemView)

    inner class AreaAdapter(groups: MutableList<AreaCinema>?) : MultiTypeExpandableRecyclerViewAdapter<AreaHolder, CinemaHolder>(groups) {

        override fun getGroupViewType(position: Int, group: ExpandableGroup<*>): Int {
            return (group as? AreaCinema)?.type ?: TYPE_HEADER_AREA
        }

        override fun getChildViewType(position: Int, group: ExpandableGroup<*>?, childIndex: Int): Int {
            return -1
        }

        override fun onCreateGroupViewHolder(parent: ViewGroup, viewType: Int): AreaHolder {
            if (viewType == TYPE_HEADER_TITLE) {
                val view = parent.inflate(R.layout.item_area_title_in_list)
                return AreaHolder(view)
            }
            val view = parent.inflate(R.layout.item_area_in_list)
            return AreaHolder(view)
        }

        override fun onCreateChildViewHolder(parent: ViewGroup, viewType: Int): CinemaHolder {
            val view = parent.inflate(R.layout.item_cinema_in_list)
            return CinemaHolder(view)
        }

        override fun onBindChildViewHolder(holder: CinemaHolder, flatPosition: Int, group: ExpandableGroup<*>, childIndex: Int) {

        }

        @SuppressLint("SetTextI18n")
        override fun onBindGroupViewHolder(holder: AreaHolder, flatPosition: Int, group: ExpandableGroup<*>) {
            val viewType = getGroupViewType(flatPosition, group)
            if (viewType == TYPE_HEADER_TITLE) {
                holder.itemView.tvTitleGroup.text = (group as? AreaCinema)?.title
                holder.setOnGroupClickListener(null)
                return
            }
            if (viewType == TYPE_HEADER_NEAR_ME) {
                holder.itemView.tvAreaTitle.text = (group as? AreaCinema)?.cinema?.Name
                holder.itemView.ivDropdown.gone()
                holder.setOnGroupClickListener(null)
                holder.itemView.setOnClickListener {
                    //                    openFragment(if (type == 0) CenimaDetailFragment.getInstance((group as? AreaCinema)?.cinema) else BookByCinemaFragment.getInstance((group as? AreaCinema)?.cinema))
                }
                return
            }
            holder.itemView.tvAreaTitle.text = group.title
            holder.itemView.tvAreaCountCinema.text = "${(group as AreaCinema).cinemas?.size ?: 0}"
            holder.itemView.ivDropdown.rotation = 90f
            holder.itemView.setOnClickListener {
                listenerSelectArea?.invoke((group as? AreaCinema)?.area
                        ?: return@setOnClickListener)
                back()
            }
        }

        override fun isGroup(viewType: Int): Boolean {
            return viewType == TYPE_HEADER_NEAR_ME || viewType == TYPE_HEADER_AREA || viewType == TYPE_HEADER_TITLE
        }

        override fun isChild(viewType: Int): Boolean {
            return viewType == -1
        }
    }

    inner class AreaHolder(itemView: View) : GroupViewHolder(itemView)
    inner class CinemaHolder(itemView: View) : ChildViewHolder(itemView)
}

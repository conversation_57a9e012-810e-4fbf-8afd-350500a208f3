package vn.zenity.betacineplex.view.auth

import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView

/**
 * Created by Zenity.
 */

interface ChangePasswordContractor {
    interface View : IBaseView {
        fun changePasswordSuccess(message: String)
        fun changePasswordError(message: String)
    }

    interface Presenter : IBasePresenter<View> {
        fun changePassword(oldPass: String, newPass: String, confirmPass: String)
        fun updateFacebookPassword(newPass: String, confirmPass: String)
    }
}

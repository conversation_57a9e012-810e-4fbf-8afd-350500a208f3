package vn.zenity.betacineplex.view.film

import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.model.Film
import vn.zenity.betacineplex.model.FilmModel

/**
 * Created by Zenity.
 */

interface ListFilmContractor {
    interface View : IBaseView {
        fun showListFilm(films: List<FilmModel>)
    }

    interface Presenter : IBasePresenter<View> {
        fun getListFilm(type: Int)
    }
}

package vn.zenity.betacineplex.view.voucher

import android.os.Bundle
import android.view.View
import kotlinx.android.synthetic.main.fragment_voucher_free_detail.*
import kotlinx.android.synthetic.main.fragment_voucher_free_detail.btnBack
import load
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.global.Global
import vn.zenity.betacineplex.helper.extension.*
import vn.zenity.betacineplex.model.NewsModel
import vn.zenity.betacineplex.model.VoucherCodeModel

/**
 * Created by <PERSON>h at 05/08/2019.
 */

class VoucherFreeDetailFragment : BaseFragment(), VoucherFreeDetailContractor.View {

    companion object {
        fun getInstance(voucher: NewsModel): VoucherFreeDetailFragment {
            val frag = VoucherFreeDetailFragment()
            frag.voucher = voucher
            return frag
        }

        fun getInstance(voucherId: String, showButtonGetVoucher: Boolean = true): VoucherFreeDetailFragment {
            val frag = VoucherFreeDetailFragment()
            frag.voucherId = voucherId
            frag.showButtonGetVoucher = showButtonGetVoucher
            return frag
        }
    }

    private val presenter = VoucherFreeDetailPresenter()
    private var voucher: NewsModel? = null
    private var voucherId: String? = null
    private var code: VoucherCodeModel? = null
    private var showButtonGetVoucher = true

    override fun getPresenter(): IBasePresenter<IBaseView>? {
        return presenter as? IBasePresenter<IBaseView>
    }

    override fun isShowToolbar(): Boolean {
        return false
    }

    override fun getLayoutRes(): Int {
        return R.layout.fragment_voucher_free_detail
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        presenter.getVoucherDetail(voucherId ?: (voucher?.StorylineID ?: ""))
        btnBack.click {
            back()
        }
        btnGetVoucher.click {
            if (voucher != null) {
                presenter.getCode(voucher?.StorylineID ?: "")
            }
        }
        if (voucher != null) {
            updateUi()
        }
    }

    private fun updateUi() {
        btnGetVoucher.visible(showButtonGetVoucher && Global.share().isLogin && voucher?.IsExistVoucherCode == true)
        tvTitle.text = voucher?.Tieu_de
        val content = voucher?.getFullContent() ?: voucher?.Tom_tat_noi_dung
        webView.loadBetaHtml(content ?: "",
                backgroundColor = "#FFFFFF", padding = "20px")
        webView.setLinkListener { data ->
            provideAppLink(data)
        }
        ivVoucher.load(voucher?.Duong_dan_anh_dai_dien?.toImageUrl())
    }

    override fun showVoucherDetail(voucher: NewsModel) {
        val title = this.voucher?.Tieu_de
        this.voucher = voucher
        this.voucher?.Tieu_de = title ?: this.voucher?.Tieu_de
        updateUi()
    }

    override fun dataNull() {
        back()
    }

    override fun showCode(code: VoucherCodeModel?) {
        if(code?.Status == 1) {
            btnGetVoucher.isEnabled = false
        }
        context?.let {
            showGetVoucherSuccess(it, code ?: return) {
                btnGetVoucher.gone()
            }
        }
        this.code = code
    }
}

package vn.zenity.betacineplex.view.film

import android.content.Context
import android.webkit.WebView
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.model.*
import vn.zenity.betacineplex.model.RequestModel.CreateBookingModel

/**
 * Created by Zenity.
 */

interface BookingPaymentContractor {
    interface View : IBaseView {
        fun showHtmlBooking(content: String)
        fun getWebView(): WebView?
        fun getViewContext(): Context?
        fun getCreateBookingModel(): CreateBookingModel?
        fun getShowTimeModel(): ShowTimeModel?
        fun getFilmModel(): FilmModel?
    }

    interface Presenter : IBasePresenter<View> {
        fun getBookingPayment(booking: CreateBookingModel)
        fun trackingConfirm(method: String)
        fun trackingPaySuccess()
        fun trackingPayFail(errorCode: String?, errorMsg: String?)
        fun setOrderId(id: String?)
    }
}

package vn.zenity.betacineplex.view

import android.content.Intent
import android.os.Bundle
import vn.zenity.betacineplex.base.BaseActivity

class MainActivity : BaseActivity() {

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setLightStatusBar()
        if (intent.extras != null ) {
            if( (intent.getStringExtra("data") != null || intent.getStringExtra("id") != null) || intent.getStringExtra("google.message_id") != null){
                val intentH = Intent(this, HomeActivity::class.java)
                var data = intent.getStringExtra("data")
                if (intent.getStringExtra("data") == null) {
                    val code = intent.getStringExtra("SreenCode") ?: intent.getStringExtra("ScreenCode")
                    data = """
                    {
                        "id": "${intent.getStringExtra("id")}",
                        "ScreenCode": "${code ?: intent.getStringExtra("SreenCode")}",
                        "RefId": "${intent.getStringExtra("RefId")}"
                    }
                """
                }
                intentH.putExtra("data", data)
                startActivity(intentH)
                finish()
                return
            }

        }
        val intent = Intent(this, HomeActivity::class.java)
        intent.flags = Intent.FLAG_ACTIVITY_NEW_TASK xor Intent.FLAG_ACTIVITY_CLEAR_TASK
        startActivity(intent)
        finish()
    }
}

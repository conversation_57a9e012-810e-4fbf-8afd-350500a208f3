package vn.zenity.betacineplex.view.user

import android.os.Bundle
import android.view.View
import kotlinx.android.synthetic.main.fragment_confirmpass.*
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView

/**
 * Created by Zenity.
 */

class ConfirmPassFragment : BaseFragment(), ConfirmPassContractor.View {
    override fun confirmSuccess() {
        openFragment(AccountInformationFragment.getInstance {
            back()
        })
    }

    override fun confirmError(message: String) {
        activity?.runOnUiThread {
            showNotice(message)
        }
    }

    private val presenter = ConfirmPassPresenter()

    override fun getPresenter(): IBasePresenter<IBaseView>? {
        return presenter as? IBasePresenter<IBaseView>
    }

    override fun isShowToolbar(): <PERSON><PERSON><PERSON> {
        return false
    }

    override fun getLayoutRes(): Int {
        return R.layout.fragment_confirmpass
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        btnConfirm.setOnClickListener {
            presenter.confirmPassword(edtPassword.text)
        }
    }
}

package vn.zenity.betacineplex.view.voucher

import android.annotation.SuppressLint
import android.os.Bundle
import androidx.core.content.res.ResourcesCompat
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import kotlinx.android.synthetic.main.fragment_voucher_free.*
import kotlinx.android.synthetic.main.item_voucher_free.view.*
import load
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.global.Global
import vn.zenity.betacineplex.helper.extension.click
import vn.zenity.betacineplex.helper.extension.setTextWithSpecialText
import vn.zenity.betacineplex.helper.extension.toImageUrl
import vn.zenity.betacineplex.helper.extension.visible
import vn.zenity.betacineplex.model.NewsModel
import vn.zenity.betacineplex.model.PaymentHistory
import vn.zenity.betacineplex.model.VoucherModel

/**
 * Created by Zenity.
 */

class VoucherFreeFragment : BaseFragment(), VoucherFreeContractor.View {

    private val presenter = VoucherFreePresenter()
    private var vouchers: List<NewsModel>? = null
    private var page = 1

    override fun getPresenter(): IBasePresenter<IBaseView>? {
        return presenter as? IBasePresenter<IBaseView>
    }

    override fun isShowToolbar(): Boolean {
        return false
    }

    override fun getLayoutRes(): Int {
        return R.layout.fragment_voucher_free
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        recyclerView.layoutManager = LinearLayoutManager(context)
        recyclerView.adapter = Adapter()
        presenter.getPublicVoucher(page)
    }

    override fun showVoucher(vouchers: List<NewsModel>) {
        this.vouchers = vouchers
        recyclerView.adapter?.notifyDataSetChanged()
    }

    override fun onRefresh() {
        super.onRefresh()
        page = 1
        refreshView?.finishRefreshing()
        presenter.getPublicVoucher(page)
    }

    private inner class Adapter : RecyclerView.Adapter<Holder>() {

        override fun getItemCount(): Int {
            return vouchers?.size ?: 0
        }

        override fun getItemViewType(position: Int): Int {
            return position
        }

        @SuppressLint("SetTextI18n")
        override fun onBindViewHolder(holder: Holder, position: Int) {
            holder.itemView.apply {
                val voucher = vouchers!![position]
                ivBanner.load(voucher.Duong_dan_anh_dai_dien?.toImageUrl())
                rlOutOfVoucher.visible(voucher.IsExistVoucherCode == false)
                tvTitle.text = voucher.Tieu_de
                click {
                    if(voucher.IsExistVoucherCode == true) {
                        openFragment(VoucherFreeDetailFragment.getInstance(voucher))
                    }
                }
            }
        }

        override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): Holder {
            val item = LayoutInflater.from(parent.context).inflate(R.layout.item_voucher_free, parent, false)
            return Holder(item)
        }
    }

    private inner class Holder(itemView: View) : RecyclerView.ViewHolder(itemView)
}

package vn.zenity.betacineplex.view.film

import io.reactivex.disposables.Disposable
import vn.zenity.betacineplex.Manager.Network.APIClient
import vn.zenity.betacineplex.app.App
import vn.zenity.betacineplex.helper.extension.applyOn
import vn.zenity.betacineplex.helper.extension.logD
import vn.zenity.betacineplex.model.FilmModel
import vn.zenity.betacineplex.model.NewModel
import java.lang.ref.WeakReference

/**
 * Created by Zenity.
 */

class FilmDetailPresenter : FilmDetailContractor.Presenter {

    private var fileDisposable: Disposable? = null
    private var eventDisposable: Disposable? = null

    override fun getFilmDetail(id: String) {
        fileDisposable = APIClient.shared.filmAPI.filmDetail(id).applyOn()
                .subscribe({
                    it.Data?.let {
                        view?.get()?.showFilmDetail(it)
                    }
                    if(!it.isSuccess) {
                        view?.get()?.showAlert(it.Message ?: return@subscribe)
                    }
                }, {

                })
    }

    override fun getListEventOfFilm(film: FilmModel) {
        val lang = App.shared().getCurrentLang()
        eventDisposable = APIClient.shared.ecmAPI.getNewEvent(if (lang == "en") lang else "")
                .applyOn()
                .subscribe({
                    val data = it.Data?.filter { it != null } as? ArrayList<NewModel>
                    if (data?.size ?: 0 > 0) {
                        data?.get(0)?.CategoryId?.let {
                            eventDisposable = APIClient.shared.ecmAPI.getNewForCategory(it, 3, 0).applyOn()
                                    .subscribe({
                                        it.Data?.let {
                                            this.view?.get()?.showListEvent(it)
                                        }
                                    }, {
                                        logD(it.localizedMessage)
                                    })
                        }
                    }
                }, {

                })
    }

    private var view: WeakReference<FilmDetailContractor.View?>? = null
    override fun attachView(view: FilmDetailContractor.View) {
        this.view = WeakReference(view)
    }

    override fun detachView() {
        fileDisposable?.dispose()
        eventDisposable?.dispose()
        this.view?.clear()
        this.view = null
    }
}

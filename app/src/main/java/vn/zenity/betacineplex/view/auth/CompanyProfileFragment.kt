package vn.zenity.betacineplex.view.auth

import android.os.Build
import android.os.Bundle
import android.text.Html
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import kotlinx.android.synthetic.main.fragment_companyprofile.*
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.helper.extension.loadBetaHtml
import vn.zenity.betacineplex.model.NewsModel

/**
 * Created by Zenity.
 */

class CompanyProfileFragment : BaseFragment(), CompanyProfileContractor.View {


    override fun showCompanyProfile(profile: NewsModel) {
        activity?.runOnUiThread {
            val content = profile.getFullContent() ?: profile.Tom_tat_noi_dung
            content?.let {
                webView?.loadBetaHtml(it)
            }
        }
    }

    private val presenter = CompanyProfilePresenter()

    override fun getPresenter(): IBasePresenter<IBaseView>? {
        return presenter as? IBasePresenter<IBaseView>
    }

    override fun isShowToolbar(): Boolean {
        return false
    }

    override fun getLayoutRes(): Int {
        return R.layout.fragment_companyprofile
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        presenter.getCompanyProfile()
    }
}

package vn.zenity.betacineplex.view.cenima

import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.model.NewsModel

/**
 * Created by Zenity.
 */

interface CinemaPriceContractor {
    interface View : IBaseView {
        fun showPrivacyPolicy(profile: NewsModel)
    }

    interface Presenter : IBasePresenter<View> {
        fun getPrivacyPolicy(newId: String? = null)
    }
}

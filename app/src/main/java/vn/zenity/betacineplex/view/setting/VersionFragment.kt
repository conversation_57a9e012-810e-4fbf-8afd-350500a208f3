package vn.zenity.betacineplex.view.setting

import android.os.Bundle
import android.view.View
import kotlinx.android.synthetic.main.fragment_version.*
import vn.zenity.betacineplex.BuildConfig
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.base.BaseFragment
import vn.zenity.betacineplex.helper.extension.versionCompare
import vn.zenity.betacineplex.helper.extension.visible
import vn.zenity.betacineplex.view.HomeActivity

class VersionFragment : BaseFragment() {

    companion object {
        const val versionDev = "dev.09.08.1"
    }

    override fun isShowToolbar(): <PERSON><PERSON><PERSON> {
        return false
    }

    override fun getLayoutRes(): Int {
        return R.layout.fragment_version
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        if (BuildConfig.DEBUG) {
            tvVersion.text = versionDev
        } else {
            tvVersion.text = BuildConfig.VERSION_NAME
        }
        val onlineVersion = (activity as? HomeActivity)?.onlineVersion ?: ""
        if (!onlineVersion.isEmpty() && versionCompare(onlineVersion, BuildConfig.VERSION_NAME) < 0) {
            tvNewestVersion.text = getString(R.string.lastest_version, onlineVersion)
            btnUpgrade.visible()
            btnUpgrade.setOnClickListener {
                (activity as? HomeActivity)?.openStore()
            }
        }
    }
}
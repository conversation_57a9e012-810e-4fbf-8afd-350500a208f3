package vn.zenity.betacineplex.view.voucher

import vn.zenity.betacineplex.base.IBasePresenter
import vn.zenity.betacineplex.base.IBaseView
import vn.zenity.betacineplex.model.NewsModel
import vn.zenity.betacineplex.model.PaymentHistory
import vn.zenity.betacineplex.model.VoucherModel

/**
 * Created by Zenity.
 */

interface VoucherFreeContractor {
    interface View : IBaseView {
        fun showVoucher(vouchers: List<NewsModel>)
    }

    interface Presenter : IBasePresenter<View> {
        fun getPublicVoucher(page: Int)
    }
}

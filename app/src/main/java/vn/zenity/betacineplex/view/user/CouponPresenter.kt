package vn.zenity.betacineplex.view.user

import io.reactivex.disposables.Disposable
import vn.zenity.betacineplex.Manager.Network.APIClient
import vn.zenity.betacineplex.global.Global
import vn.zenity.betacineplex.helper.extension.applyOn
import java.lang.ref.WeakReference

/**
 * Created by Zenity.
 */

class CouponPresenter : CouponContractor.Presenter {

    private var dispoable: Disposable? = null

    override fun registerCoupon(code: String, pin: String) {
        val mapData = mapOf("CustomerId" to (Global.share().user?.AccountId ?: ""),
                "CustomerCard" to (Global.share().user?.CardNumber ?: ""),
                "PinCode" to pin,
                "VoucherCode" to code,
                "CardTypeName" to "Coupon")
        dispoable = APIClient.shared.accountAPI.registerVoucher(mapData).applyOn()
                .subscribe({
                    if (it.isSuccess) {
                        it.Data?.let {
                            view?.get()?.registerCouponSuccess()
                            return@subscribe
                        }
                    }else {
                        it.Message?.let {
                            view?.get()?.showAlert(it)
                        }
                    }
                    view?.get()?.hideLoading()
                }, {
                    it.message?.let {
                        view?.get()?.showAlert(it)
                    }
                    view?.get()?.hideLoading()
                })
    }

    override fun getListCoupon(accountId: String) {
        view?.get()?.showLoading()
        dispoable = APIClient.shared.accountAPI.getListVoucher(accountId, "Coupon").applyOn()
                .subscribe({ result ->
                    view?.get()?.hideLoading()
                    if (result.isSuccess){
                        result.Data?.let {
                            view?.get()?.showListCoupon(it)
                        }

                    }
                }, { error ->
                    view?.get()?.hideLoading()
                })
    }

    private var view: WeakReference<CouponContractor.View?>? = null
    override fun attachView(view: CouponContractor.View) {
        this.view = WeakReference(view)
    }

    override fun detachView() {
        this.dispoable?.dispose()
        this.view?.clear()
        this.view = null
    }
}

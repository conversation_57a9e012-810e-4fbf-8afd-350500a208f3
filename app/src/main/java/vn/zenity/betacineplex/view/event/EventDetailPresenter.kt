package vn.zenity.betacineplex.view.event

import io.reactivex.disposables.Disposable
import vn.zenity.betacineplex.Manager.Network.APIClient
import vn.zenity.betacineplex.helper.extension.applyOn
import java.lang.ref.WeakReference

/**
 * Created by Zenity.
 */

class EventDetailPresenter : EventDetailContractor.Presenter {
    private var dispoable: Disposable? = null
    override fun getEventDetails(id: String) {
        dispoable = APIClient.shared.ecmAPI.getNewWithId(id).applyOn()
                .subscribe({
                    if (it.Data != null) {
                        view?.get()?.showEventDetail(it.Data!!)
                    }
                }, {
                    view?.get()?.hideLoading()
                })
    }

    override fun getNotificationDetails(id: Int) {
        dispoable = APIClient.shared.ecmAPI.getNotificationDetail(id).applyOn()
                .subscribe({
                    if (it.Data != null) {
                        view?.get()?.showNotification(it.Data!!)
                    }
                }, {
                    view?.get()?.hideLoading()
                })
    }

    override fun readNotification(id: Int) {
    }

    private var view: WeakReference<EventDetailContractor.View?>? = null
    override fun attachView(view: EventDetailContractor.View) {
        this.view = WeakReference(view)
    }

    override fun detachView() {
        dispoable?.dispose()
        this.view?.clear()
        this.view = null
    }
}

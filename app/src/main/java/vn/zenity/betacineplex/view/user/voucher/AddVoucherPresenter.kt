package vn.zenity.betacineplex.view.user.voucher

import io.reactivex.disposables.Disposable
import vn.zenity.betacineplex.Manager.Network.APIClient
import vn.zenity.betacineplex.global.Global
import vn.zenity.betacineplex.helper.extension.applyOn
import java.lang.ref.WeakReference

class AddVoucherPresenter: AddVoucherContractor.Presenter {

    private var disposable: Disposable? = null

    override fun registerVoucher(code: String, pin: String) {
        view?.get()?.showLoading()
        val mapData = mapOf("CustomerId" to (Global.share().user?.AccountId ?: ""),
                "CustomerCard" to (Global.share().user?.CardNumber ?: ""),
                "PinCode" to pin,
                "VoucherCode" to code,
                "CardTypeName" to "Voucher")
        disposable = APIClient.shared.accountAPI.registerVoucher(mapData).applyOn()
                .subscribe({
                    if (it.isSuccess) {
                        it.Data?.let {
                            view?.get()?.showAddVoucherSuccess()
                            return@subscribe
                        }
                    }else {
                        it.Message?.let {
                            view?.get()?.showAlert(it)
                        }
                    }
                    view?.get()?.hideLoading()
                }, {
                    it.message?.let {
                        view?.get()?.showAlert(it)
                    }
                    view?.get()?.hideLoading()
                })
    }

    private var view: WeakReference<AddVoucherContractor.View?>? = null

    override fun attachView(view: AddVoucherContractor.View) {
        this.view = WeakReference(view)
    }

    override fun detachView() {
        this.disposable?.dispose()
        this.view?.clear()
        this.view = null
    }
}
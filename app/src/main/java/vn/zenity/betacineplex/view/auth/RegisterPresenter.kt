package vn.zenity.betacineplex.view.auth

import io.reactivex.disposables.Disposable
import vn.zenity.betacineplex.Manager.Network.APIClient
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.global.Tracking
import vn.zenity.betacineplex.helper.extension.applyOn
import vn.zenity.betacineplex.helper.extension.getString
import vn.zenity.betacineplex.model.CityModel
import vn.zenity.betacineplex.model.RequestModel.RegisterModel
import java.lang.ref.WeakReference

/**
 * Created by Zenity.
 */

class RegisterPresenter : RegisterContractor.Presenter {

    private var citis: ArrayList<CityModel>? = null
    private var disposable: Disposable? = null
    private var mapDistrict: HashMap<String, ArrayList<CityModel>> = hashMapOf()

    override fun getCity() {
        if (citis != null) {
            view?.get()?.showListCity(citis!!)
            return
        }
        view?.get()?.showLoading()
        disposable = APIClient.shared.cityAPI.getListCity().applyOn()
                .subscribe({
                    if (it.isSuccess) {
                        it.Data?.let {
                            citis = it
                            view?.get()?.showListCity(citis!!)
                        }
                    } else {

                    }
                    view?.get()?.hideLoading()
                }, {
                    view?.get()?.hideLoading()
                })
    }

    override fun getDistrict(cityId: String) {
        if (mapDistrict[cityId] != null) {
            view?.get()?.showDistrict(cityId, mapDistrict[cityId]!!)
            return
        }
        view?.get()?.showLoading()
        disposable = APIClient.shared.cityAPI.getDistrictOfCity(cityId).applyOn()
                .subscribe({
                    if (it.isSuccess) {
                        it.Data?.let {
                            mapDistrict[cityId] = it
                            view?.get()?.showDistrict(cityId, it)
                        }
                    } else {

                    }
                    view?.get()?.hideLoading()
                }, {
                    view?.get()?.hideLoading()
                })
    }

    override fun register(registerModel: RegisterModel) {
        view?.get()?.showLoading()
        Tracking.share().authBegin(view?.get()?.getViewContext(),"email");
        disposable = APIClient.shared.accountAPI.register(registerModel).applyOn()
                .subscribe( {
                    if (it.isSuccess) {
                        Tracking.share().authComplete(view?.get()?.getViewContext(),"email");
                        view?.get()?.showRegisterSuccess(R.string.register_success.getString(), it.Data?.CardNumber ?: "")
                    } else {
                        if (it.Message?.isNotEmpty() == true) {
                            view?.get()?.showError(it.Message!!, false)
                        } else
                        view?.get()?.showError(R.string.register_error.getString(), false)
                    }
                    view?.get()?.hideLoading()
                }, {
                    view?.get()?.hideLoading()
                    view?.get()?.showError(R.string.register_error.getString(), false)
                })
    }

    private var view: WeakReference<RegisterContractor.View?>? = null
    override fun attachView(view: RegisterContractor.View) {
        this.view = WeakReference(view)
    }

    override fun detachView() {
        disposable?.dispose()
        this.view?.clear()
        this.view = null
    }
}

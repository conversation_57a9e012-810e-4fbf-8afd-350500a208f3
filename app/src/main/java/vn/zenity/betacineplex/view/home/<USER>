package vn.zenity.betacineplex.view.home

import io.reactivex.disposables.Disposable
import vn.zenity.betacineplex.Manager.Network.APIClient
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.app.App
import vn.zenity.betacineplex.global.Constant
import vn.zenity.betacineplex.helper.extension.applyOn
import vn.zenity.betacineplex.helper.extension.getString
import vn.zenity.betacineplex.helper.extension.logD
import vn.zenity.betacineplex.model.FilmModel
import vn.zenity.betacineplex.model.NewModel
import java.lang.ref.WeakReference

class HomePresenter : HomeContractor.Presenter {

    private var disposable: Disposable? = null
    private var disposableSneak: Disposable? = null
    private var disposableComming: Disposable? = null
    private var disposableEvent: Disposable? = null
    private var listNowShowing: List<FilmModel>? = null
    private var listSneakShow: List<FilmModel>? = null
    private var listComming: List<FilmModel>? = null
    private var typeRequesting = -1

    override fun fetchListMovie(type: Int, isReselect: <PERSON>olean) {
        typeRequesting = type
        if (type == 1 && listNowShowing != null) {
            if (!isReselect)
                this.view?.get()?.showListFilm(listNowShowing!!)
            typeRequesting = -1
            return
        }
        if (type == 0 && listComming != null) {
            if (!isReselect)
                this.view?.get()?.showListFilm(listComming!!)
            typeRequesting = -1
            return
        }
        if (type == 2 && listNowShowing != null) {
            if (!isReselect) {
                listSneakShow = listNowShowing?.filter { it.HasSneakShow == true }
                this.view?.get()?.showListFilm(listSneakShow!!)
            }
            typeRequesting = -1
            return
        }

        if (listNowShowing == null && (disposable == null || disposable?.isDisposed == true)) {
            if (typeRequesting != 0) {
                this.view?.get()?.showListFilm(listOf())
                this.view?.get()?.showLoading()
            }
            disposable = APIClient.shared.filmAPI.getListFilm(true).applyOn().subscribe({ response ->
                response.Data?.let {
                    this.listNowShowing = it
                    if (typeRequesting == 1) {
                        this.view?.get()?.showListFilm(it)
                    }
                    if (typeRequesting == 2) {
                        listSneakShow = listNowShowing?.filter { it.HasSneakShow == true }
                        this.view?.get()?.showListFilm(listSneakShow!!)
                    }
                }
                if (typeRequesting != 0) {
                    this.view?.get()?.hideLoading()
                    typeRequesting = -1
                }
            }, { error ->
                if (typeRequesting != 0) {
                    error.message?.let {
                        this.view?.get()?.showError(it)
                    }
                    this.view?.get()?.hideLoading()
                    typeRequesting = -1
                }
            })
        }

        if (listComming == null && (disposableComming == null || disposableComming?.isDisposed == true)) {
            if (typeRequesting == 0) {
                this.view?.get()?.showListFilm(listOf())
                this.view?.get()?.showLoading()
            }
            disposableComming = APIClient.shared.filmAPI.getListFilm(false).applyOn().subscribe({ response ->
                response.Data?.let {
                    this.listComming = it
                    if (typeRequesting == 0) {
                        this.view?.get()?.showListFilm(it)
                    }
                }
                if (typeRequesting == 0) {
                    this.view?.get()?.hideLoading()
                    typeRequesting = -1
                }
            }, { error ->
                if (typeRequesting == 0) {
                    error.message?.let {
                        this.view?.get()?.showError(it)
                    }
                    this.view?.get()?.hideLoading()
                    typeRequesting = -1
                }
            })
        }
    }

    override fun fetchListEvent() {
        disposableEvent?.dispose()
        val lang = App.shared().getCurrentLang()
        disposableEvent = APIClient.shared.ecmAPI.getNewEvent(if (lang == "en") lang else "")
                .applyOn()
                .subscribe({
                    val data = it.Data?.filter { it != null } as? ArrayList<NewModel>
                    if (data?.size ?: 0 > 0) {
                        data?.get(0)?.CategoryId?.let {
                            APIClient.shared.ecmAPI.getNewForCategory(it, 3, 0).applyOn()
                                    .subscribe({
                                        this.view?.get()?.showListEvent(it.Data ?: return@subscribe)
                                    }, {
                                        logD(it.message ?: "")
                                    })
                        }
                    }
                }, {

                })
    }

    override fun getNearCinema() {
        disposableEvent?.dispose()
        val lang = App.shared().getCurrentLang()
        disposableEvent = APIClient.shared.accountAPI.getBanner()
                .applyOn()
                .subscribe({
                    val data = it.Data?.filter { it != null } as? ArrayList<NewModel>
                    if (data?.size ?: 0 > 0) {
                        data?.get(0)?.CategoryId?.let {
                            APIClient.shared.ecmAPI.getNewForCategory(it, 3, 0).applyOn()
                                    .subscribe({
                                        this.view?.get()?.showListEvent(it.Data ?: return@subscribe)
                                    }, {
                                        logD(it.message ?: "")
                                    })
                        }
                    }
                }, {

                })
    }

    override fun getBanner() {
        disposableEvent?.dispose()
        disposableEvent = APIClient.shared.accountAPI.getBanner()
                .applyOn()
                .subscribe({
                    val data = it.Data
                    view?.get()?.showBanner(data ?: listOf())
                }, {
                    view?.get()?.showBanner(listOf())
                })
    }

    override fun getAppParams() {
        disposableEvent = APIClient.shared.accountAPI.getAppParams()
                .applyOn()
                .subscribe({
                    val data = it.Data
                    view?.get()?.showAppParams(data ?: listOf())
                }, {
                    view?.get()?.showAppParams(listOf())
                })
    }

    private var view: WeakReference<HomeContractor.View?>? = null
    override fun attachView(view: HomeContractor.View) {
        this.view = WeakReference(view)
    }

    override fun detachView() {
        disposable?.dispose()
        disposableSneak?.dispose()
        disposableComming?.dispose()
        disposable?.dispose()
        this.view?.clear()
        this.view = null
    }
}

package vn.zenity.betacineplex.helper.extension

import android.content.Context
import android.telephony.TelephonyManager
import vn.zenity.betacineplex.global.Constant
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.RandomAccessFile
import java.util.*
import java.util.UUID.randomUUID
import java.nio.file.Files.exists




/**
 * Created by tranduc on 1/9/18.
 */

class DeviceHelper {
    private var sID: String? = null

    companion object {
        val shared = DeviceHelper()

    }

    @Synchronized
    fun deviceId(): String {
        if (sID == null) {
            sID = PreferencesHelper.shared.getStringValue(Constant.Key.PREF_UNIQUE_ID)
            if (sID == null) {
                sID = uuid()
                PreferencesHelper.shared.putValue(Constant.Key.PREF_UNIQUE_ID, sID!!)
            }
        }
        return sID ?: ""
    }
    private fun uuid(): String {
        val id = UUID.randomUUID().toString()
        return id
    }
}
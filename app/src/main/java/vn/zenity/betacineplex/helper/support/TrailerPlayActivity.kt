package vn.zenity.betacineplex.helper.support

import android.content.Context
import androidx.appcompat.app.AppCompatActivity
import android.os.Bundle
import com.google.android.youtube.player.YouTubeBaseActivity
import com.google.android.youtube.player.YouTubeInitializationResult
import com.google.android.youtube.player.YouTubePlayer
import kotlinx.android.synthetic.main.activity_trailer_play.*
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.global.Config
import vn.zenity.betacineplex.global.Constant
import vn.zenity.betacineplex.helper.thirtypart.LocaleHelper

class TrailerPlayActivity : YouTubeBaseActivity(), YouTubePlayer.OnInitializedListener {
    private var id = "NWepvH6LnEw"
    override fun onInitializationSuccess(p0: YouTubePlayer.Provider?, player: YouTubePlayer, wasRestored: Boolean) {
        if (!wasRestored) {
            player.loadVideo(id)
        }
    }

    override fun onInitializationFailure(p0: YouTubePlayer.Provider?, p1: YouTubeInitializationResult?) {
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        id = intent.getStringExtra(Constant.Key.trailerId) ?: "NWepvH6LnEw"
        if (id.startsWith("http") && id.contains("v=")) {
            id = id.substring(id.indexOf("v=") + 2, id.length)
        }
        setContentView(R.layout.activity_trailer_play)
        playerView.initialize(Config.YOUTUBE_KEY, this)
        btnClose.setOnClickListener {
            finish()
        }
    }

    override fun attachBaseContext(newBase: Context?) {
        super.attachBaseContext(LocaleHelper.onAttach(newBase))
    }
}

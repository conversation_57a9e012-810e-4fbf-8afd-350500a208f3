package vn.zenity.betacineplex.helper.extension

import android.util.Log
import vn.zenity.betacineplex.BuildConfig

/**
 * Created by vinh on 1/10/18.
 */

fun logD(message: String, tag: String = "TAG - Cineplex") {
    if (BuildConfig.DEBUG)
        Log.d(tag,message)
}

fun logE(message: String, tag: String = "TAG - Cineplex") {
    if (BuildConfig.DEBUG)
        Log.e(tag,message)
}

fun logI(message: String, tag: String = "TAG - Cineplex") {
    if (BuildConfig.DEBUG)
        Log.i(tag,message)
}
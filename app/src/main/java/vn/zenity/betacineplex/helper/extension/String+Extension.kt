package vn.zenity.betacineplex.helper.extension

import android.app.Activity
import android.content.Context
import android.net.Uri
import android.os.Build
import android.provider.MediaStore
import androidx.core.app.ShareCompat
import android.text.*
import android.text.style.ClickableSpan
import android.view.View
import android.widget.TextView
import biin.elife.vn.biin.helper.encrypt.AES
import vn.zenity.betacineplex.BuildConfig
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.app.App
import vn.zenity.betacineplex.global.Constant
import vn.zenity.betacineplex.helper.thirtypart.ConvertUnsigned
import java.text.SimpleDateFormat
import java.util.*
import java.util.regex.Pattern


/**
 * Created by tranduc on 1/5/18.
 */
const val REGEX_MOBILE_SIMPLE = "^[1]\\d{10}$"
const val REGEX_MOBILE_EXACT = "^((13[0-9])|(14[5,7])|(15[0-3,5-9])|(17[0,3,5-8])|(18[0-9])|(147))\\d{8}$"
const val REGEX_TEL = "^0\\d{2,3}[- ]?\\d{7,8}"
const val REGEX_EMAIL = "^\\w+([-+.]\\w+)*@\\w+([-.]\\w+)*\\.\\w+([-.]\\w+)*$"
const val REGEX_URL = "http(s)?://([\\w-]+\\.)+[\\w-]+(/[\\w-./?%&=]*)?"
const val REGEX_USERNAME = "^[\\w\\u4e00-\\u9fa5]{6,20}(?<!_)$"
const val REGEX_DATE = "^(?:(?!0000)[0-9]{4}-(?:(?:0[1-9]|1[0-2])-(?:0[1-9]|1[0-9]|2[0-8])|(?:0[13-9]|1[0-2])-(?:29|30)|(?:0[13578]|1[02])-31)|(?:[0-9]{2}(?:0[48]|[2468][048]|[13579][26])|(?:0[48]|[2468][048]|[13579][26])00)-02-29)$"

fun String?.isMobileSimple(): Boolean {
    if (this == null) return false
    return isMatch(REGEX_MOBILE_SIMPLE)
}

fun String?.isMobileExact(): Boolean {
    if (this == null) return false
    return isMatch(REGEX_MOBILE_EXACT)
}

fun String?.isTel(): Boolean {
    if (this == null) return false
    return isMatch(REGEX_TEL)
}

fun String?.isEmail(): Boolean {
    if (this == null) return false
    return isMatch(REGEX_EMAIL)
}

fun String?.isURL(): Boolean {
    if (this == null) return false
    return isMatch(REGEX_URL)
}

fun String?.isUsername(): Boolean {
    if (this == null) return false
    return isMatch(REGEX_USERNAME)
}

fun String?.isDate(): Boolean {
    if (this == null) return false
    return isMatch(REGEX_DATE)
}

fun String.isMatch(regex: String): Boolean {
    return !this.isEmpty() && Pattern.matches(regex, this)
}

fun String.isEmpty(): Boolean {
    return TextUtils.isEmpty(this)
}

fun String.initialsFromString(): String {
    var string = ""
    val firsts = this.trim().split(" ")
    if (firsts.isNotEmpty()) {
        if (firsts.size == 1) {
            return firsts[0]
        }
        if (firsts.size > 1) {
            return "${firsts[0][0].toUpperCase()}${firsts[firsts.size - 1][0].toUpperCase()}"
        }
    }
    return string
}

fun String.encryptMsg(): String {
    val key = "98302390239430abdc29482934982312312"
    val iv = "83494389819384234324"
    return AES.instance.encrypt(this, key, iv) ?: ""
}

fun String.decryptMsg(): String {
    val key = "98302390239430abdc29482934982312312"
    val iv = "83494389819384234324"
    return AES.instance.decrypt(this, key, iv) ?: ""
}

fun String.containsIgnoreDiacritic(other: String): Boolean {
    return ConvertUnsigned.get().ConvertString(this).contains(ConvertUnsigned.get().ConvertString(other))
}

fun String.toImageUrl(isAvatar: Boolean = false): String {
    if (this.startsWith("http")) return this
    if (this.isNotEmpty()) return (if (isAvatar) BuildConfig.AVATAR_HOST_FILE else BuildConfig.HOST_FILE) + this
    return this
}

fun String.dateConvertFormat(currentFormat: String = Constant.DateFormat.default, showFormat: String = Constant.DateFormat.dateVi): String {
    return try {
        val currentDate = this.toDate(currentFormat)
        val showString = SimpleDateFormat(showFormat, if (App.shared().getCurrentLang() == "en") Locale.ENGLISH else Locale("vi", "vn")).format(currentDate)
        showString
    } catch (e: Exception) {
        this
    }
}

fun String.toDate(currentFormat: String = Constant.DateFormat.default): Date? {
    return try {
        SimpleDateFormat(currentFormat, if (App.shared().getCurrentLang() == "en") Locale.ENGLISH else Locale("vi", "vn")).parse(this)
    } catch (_: Exception) {
        try {
            SimpleDateFormat(Constant.DateFormat.defaultFull, if (App.shared().getCurrentLang() == "en") Locale.ENGLISH else Locale("vi", "vn")).parse(this)
        } catch (_: Exception) {
            try {
                SimpleDateFormat(Constant.DateFormat.default, if (App.shared().getCurrentLang() == "en") Locale.ENGLISH else Locale("vi", "vn")).parse(this)
            } catch (_: Exception) {
                null
            }
        }
    }
}


fun String.toCalendar(currentFormat: String = Constant.DateFormat.default): Calendar? {
    val date = this.toDate(currentFormat) ?: return null
    val cal = Calendar.getInstance()
    cal.time = date
    return cal
}

fun TextView.setTextWithSpecialText(text: String, specialText: String, clickable: (() -> Unit)? = null, changeStyle: (TextPaint) -> Unit) {
    if (text.isEmpty() || specialText.isEmpty()) {
        this.text = text
        return
    }
    val startT = text.indexOf(specialText)
    if (startT < 0) {
        this.text = text
        return
    }
    val textSpan = SpannableString(text)
    textSpan.setSpan(object : ClickableSpan() {
        override fun onClick(widget: View) {
            clickable?.invoke()
        }

        override fun updateDrawState(ds: TextPaint) {
            super.updateDrawState(ds)
            ds.let {
                changeStyle.invoke(ds)
            }
        }

    }, startT, startT + specialText.length, Spannable.SPAN_EXCLUSIVE_EXCLUSIVE)
    this.text = textSpan
}

fun String.share(subject: String, activity: Activity) {
    ShareCompat.IntentBuilder.from(activity)
            .setType("text/plain")
            .setSubject(subject)
            .setText(this)
            .startChooser()
}

fun String.mapCode(): String {
    return when (this.toUpperCase()) {
        "FILE_UPLOAD_TOO_LARGE_1MB" -> R.string.file_upload_too_large.getString()
        "PASSWORD_CHANGED_SUCCESSFULLY" -> R.string.password_changed_successfully.getString()
        "OLD_PASSWORD_INCORRECT" -> R.string.OLD_PASSWORD_INCORRECT.getString()
        "PASSWORD_INCORRECT" -> R.string.PASSWORD_INCORRECT.getString()
        "ACCOUNT_NOT_EXISTED" -> R.string.ACCOUNT_NOT_EXISTED.getString()
        "ACCOUNT_LOCKED" -> R.string.ACCOUNT_LOCKED.getString()
        "LOGIN_SUCCESSFULLY" -> R.string.LOGIN_SUCCESSFULLY.getString()
        "LOGIN_FAILED", "LOGIN_FAIL" -> R.string.LOGIN_FAILED.getString()
        "EMAIL_EXISTED" -> R.string.EMAIL_EXISTED.getString()
        "PHONE_NUMBER_EXISTED" -> R.string.PHONE_NUMBER_EXISTED.getString()
        "PERSONAL_ID_EXISTED" -> R.string.PERSONAL_ID_EXISTED.getString()
        "REGISTRATION_SUCCESSFULLY" -> R.string.REGISTRATION_SUCCESSFULLY.getString()
        "REGISTRATION_FAIL" -> R.string.REGISTRATION_FAIL.getString()
        "UPDATE_SUCCESSFULLY" -> R.string.UPDATE_SUCCESSFULLY.getString()
        "TIMEOUT" -> R.string.network_error.getString()
        else -> this
    }
}

// 1 is newer, 0 is equal, -1 is older
fun versionCompare(str1: String, str2: String): Int {
    val vals1 = str1.split("\\.".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
    val vals2 = str2.split("\\.".toRegex()).dropLastWhile { it.isEmpty() }.toTypedArray()
    var i = 0
    // set index to first non-equal ordinal or length of shortest version string
    while (i < vals1.size && i < vals2.size && vals1[i] == vals2[i]) {
        i++
    }
    // compare first non-equal ordinal number
    if (i < vals1.size && i < vals2.size) {
        val diff = Integer.valueOf(vals1[i])!!.compareTo(Integer.valueOf(vals2[i]))
        return Integer.signum(diff)
    }
    // the strings are equal or one string is a substring of the other
    // e.g. "1.2.3" = "1.2.3" or "1.2.3" < "1.2.3.4"
    return Integer.signum(vals1.size - vals2.size)
}

fun String.toHtml(font: String = "sanspro_regular.ttf", backgroundColor: String = "#F3F3F3", padding: String = "10px"): String {
    val pish = "<html><head><style type=\"text/css\">@font-face {font-family: MyFont;src: url(\"file:///android_asset/font/$font\")}body {font-family: MyFont;font-size: medium;text-align: justify; padding: $padding;background: $backgroundColor}</style></head><body>"
    val pas = "</body></html>"
    return pish + this + pas
}

fun String.toLink(): Pair<String, String>? {
    val split = this.replace("betacineplex://", "").split("/")
    if (split.size != 2) return null
    return split[0] to split[1]
}

fun String.toShowHtml(): Spanned {
    return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.N) {
        Html.fromHtml(this, Html.FROM_HTML_MODE_COMPACT)
    } else {
        Html.fromHtml(this)
    }
}
package vn.zenity.betacineplex.helper.view;

import android.content.Context;
import android.content.res.TypedArray;

import androidx.appcompat.widget.AppCompatImageView;
import androidx.appcompat.widget.AppCompatTextView;
import android.util.AttributeSet;
import android.view.View;
import android.widget.RelativeLayout;

import vn.zenity.betacineplex.R;
import vn.zenity.betacineplex.helper.extension.ViewGroup_ExtensionsKt;

/**
 * Created by vinh on 4/7/18.
 */

public class BetaSelectionView extends RelativeLayout {

    private int icon = 0;
    private String text = "";
    private String hint = "";

    public BetaSelectionView(Context context) {
        this(context, null);
    }

    public BetaSelectionView(Context context, AttributeSet attrs) {
        this(context, attrs, 0);
    }

    public BetaSelectionView(Context context, AttributeSet attrs, int defStyleAttr) {
        super(context, attrs, defStyleAttr);
        init(attrs);
    }

    private void init(AttributeSet attrs) {
        if (attrs != null) {
            TypedArray styledAttributes = getContext().obtainStyledAttributes(attrs, R.styleable.BetaSelectionView, 0, 0);
            icon = styledAttributes.getResourceId(R.styleable.BetaSelectionView_btsvIcon, 0);
            text = styledAttributes.getString(R.styleable.BetaSelectionView_btsvText);
            hint = styledAttributes.getString(R.styleable.BetaSelectionView_btsvHint);
            styledAttributes.recycle();
        }
        View view = ViewGroup_ExtensionsKt.inflate(this, R.layout.layout_beta_selection_view);
        ((AppCompatImageView)view.findViewById(R.id.ivIcon)).setImageResource(icon);
        ((AppCompatTextView)view.findViewById(R.id.tvContent)).setText(text);
        ((AppCompatTextView)view.findViewById(R.id.tvContent)).setHint(hint);
//        ((AppCompatTextView)view.findViewById(R.id.tvContent)).setOnClickListener(new OnClickListener() {
//            @Override
//            public void onClick(View v) {
//
//            }
//        });
        addView(view);
    }

    public void setText(String text) {
        this.text = text;
        if(findViewById(R.id.tvContent) != null)
            ((AppCompatTextView)findViewById(R.id.tvContent)).setText(text);
    }
}

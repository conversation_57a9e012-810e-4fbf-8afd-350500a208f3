package vn.zenity.betacineplex.helper.view

import android.content.Context
import android.graphics.Bitmap
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.util.AttributeSet
import android.widget.HorizontalScrollView
import vn.zenity.betacineplex.R
import vn.zenity.betacineplex.helper.extension.getBitmapFromDrawable

class BetaHorizScrollview @JvmOverloads constructor(context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0) : HorizontalScrollView(context, attrs, defStyleAttr) {

    private var paint: Paint
    private var leftBitmap: Bitmap
    private var rightBitmap: Bitmap
    private var btHeight: Int
    private var btWidth: Int

    init {
        viewTreeObserver?.addOnScrollChangedListener {

        }
        paint = Paint()
        paint.color = Color.BLACK
        paint.textSize = 30f
        leftBitmap = R.drawable.ic_arrow_left_scroll.getBitmapFromDrawable()
        rightBitmap = R.drawable.ic_arrow_right_scroll.getBitmapFromDrawable()
        btHeight = leftBitmap.height
        btWidth = leftBitmap.width
    }

    override fun onDraw(canvas: Canvas?) {
//        if (canScrollHorizontally(-1)) {

//        }
//        if (canScrollHorizontally(1)) {

//        }
        super.onDraw(canvas)
    }

    override fun draw(canvas: Canvas?) {
        super.draw(canvas)
        if (canScrollHorizontally(-1))
        canvas?.drawBitmap(leftBitmap, scrollX + 0.3f * btWidth.toFloat(), height / 2f - btHeight / 2f, paint)
        if (canScrollHorizontally(1))
        canvas?.drawBitmap(rightBitmap, scrollX + width - 1.3f * btWidth.toFloat(), height / 2f - btHeight / 2f, paint)
    }
}
package vn.zenity.betacineplex.helper.view;

import android.content.Context;
import android.content.Intent;
import android.net.Uri;
import androidx.annotation.NonNull;
import android.util.AttributeSet;
import android.view.MotionEvent;
import android.webkit.WebView;
import android.webkit.WebViewClient;

import vn.zenity.betacineplex.helper.extension.String_ExtensionKt;

public class TouchyWebView extends WebView {

    private InAppLinkListener linkListener;

    public TouchyWebView(Context context) {
       super(context);
       init();
    }

    public TouchyWebView(Context context, AttributeSet attrs) {
        super(context, attrs);
        init();
    }

    public TouchyWebView(Context context, AttributeSet attrs, int defStyle) {
        super(context, attrs, defStyle);
        init();
    }

    private void init() {
        setWebViewClient(webViewClient);
    }

    @Override
    public boolean onTouchEvent(MotionEvent event){
        if(computeVerticalScrollRange() > getMeasuredHeight())
            requestDisallowInterceptTouchEvent(true);
        return super.onTouchEvent(event);
    }

    private WebViewClient webViewClient = new WebViewClient() {
        @Override
        public boolean shouldOverrideUrlLoading(WebView view, String url) {
            if (url != null) {
                kotlin.Pair<String, String> linkData = String_ExtensionKt.toLink(url);
                if (linkData == null) {
                    try{
                        Intent intent = new Intent(Intent.ACTION_VIEW);
                        intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
                        String urlStr = url.replace("file:///android_asset/html/", "");
                        if (!urlStr.contains("http://") && !urlStr.contains("https://")) {
                            urlStr = "http://" + urlStr;
                        }
                        intent.setData(Uri.parse(urlStr));
                        if (getContext() != null) getContext().startActivity(intent);
                    } catch (Exception ex) {
                        return false;
                    }
                    return true;
                }
                if (linkListener != null){
                    linkListener.onLink(linkData);
                }
                return true;
            }
            return false;
        }
    };

    public void setLinkListener(InAppLinkListener linkListener) {
        this.linkListener = linkListener;
    }

    public interface InAppLinkListener {
        void onLink(@NonNull kotlin.Pair<String, String> data);
    }
}
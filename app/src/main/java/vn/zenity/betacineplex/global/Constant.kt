package vn.zenity.betacineplex.global

import vn.zenity.betacineplex.BuildConfig

/**
 * Created by vinh on 4/2/18.
 */
class Constant {

    companion object {
        const val orderTime = 600000L // 10 min * 60 sec * 1000L millis
        const val INTENT_FILTER_MOMO = BuildConfig.APPLICATION_ID + ".momo"
        const val INTENT_FILTER_ZALOPAY = BuildConfig.APPLICATION_ID + ".zalopay"
    }

    interface Key {
        companion object {
            const val data = "data"
            const val isReLogin = "relogin"
            const val language = "language"
            const val trailerId = "trailerId"
            const val PREF_UNIQUE_ID = "PREF_UNIQUE_ID"
            const val isRegisterToken = "isRegisterToken"
        }
    }

    interface Momo {
        companion object {
            const val callbackScheme = "betacineplexx"
            const val host = "momo"
            const val fullHost = "momo.vn"
            const val orderId = "orderId"
            const val resultCode = "resultCode"
            const val requestId = "requestId"
            const val transId = "transId"
            const val message = "message"
            const val responseTime = "responseTime"
            const val payType = "payType"
            const val extraData = "extraData"
            const val partnerCode = "partnerCode"
        }
    }

    interface AirPay {
        companion object {
            const val fullHost = "airpay.vn"
            const val orderId = "order_id"
        }
    }

    interface ZaloPay {
        companion object {
            const val fullHost = "gateway.zalopay.vn"
            const val domain = "zalopay"
            const val appId = "appid"
            const val appTransId = "apptransid"
            const val pmcId = "pmcid"
            const val bankCode = "bankcode"
            const val amount = "amount"
            const val dAmount = "discountamount"
            const val appStatus = "status"
            const val checkSum = "checksum"
        }
    }

    interface NoiDia {
        companion object {
            const val fullHost = "mtf.onepay.vn/paygate/vpcpay.op"
        }
    }

    interface QuocTe {
        companion object {
            const val fullHost = "mtf.onepay.vn/promotion/vpcpost.op"
        }
    }

    interface Lang {
        companion object {
            const val vi = "vi"
            const val en = "en"
        }
    }

    interface DateFormat {
        companion object {
            const val default = "yyyy-MM-dd'T'HH:mm:ss" //2018-05-18T00:00:00
            const val defaultFull = "yyyy-MM-dd'T'HH:mm:ss.S"
            const val defaultFullSavis = "yyyy-MM-dd'T'HH:mm:ss.SSS"
            const val requestServer = "yyyy-MM-dd'T'HH:mm:ss.S'Z'"
            const val dateTime = "yyyy-MM-dd'T'HH:mm:ss"
            const val date = "yyyy-MM-dd"
            const val dateVi = "dd-MM-yyyy"
            const val time = "HH:mm:ss"
            const val hourMinute = "HH:mm"
            const val dateTimePaymentHistory = "dd/MM/yyyy | HH:mm"
            const val dateTimeShowTime = "HH:mm dd/MM/yyyy"
            const val dateSavis = "dd/MM/yyyy"
            const val timeServer = "yyyy-MM-dd'T'HH:mm:ssZ"
        }
    }

    interface SeatType {
        companion object {
            const val NORMAL = "c0f1e9a8-c9f5-4b0d-8b10-f3108996e60b"
            const val VIP = "9f2dda7f-d09e-4d58-a504-5a6311345aae"
            const val DOUBLE = "9beee28c-8cae-41d0-bd01-b0b22108432c"
        }
    }

    interface SeatStatus {
        companion object {
            const val NOT_USED = "0"
            const val USED = "1"
            private const val FOR_WAY = "2"
            private const val BROKEN = "3"
            const val IN_PROCESSING = "4"
            fun isSeatNotAvailable(status: String): Boolean {
                return when (status) {
                    NOT_USED, FOR_WAY, BROKEN -> true
                    else -> false
                }
            }
        }
    }

    interface SeatSoldStatus {
        companion object {
            const val USER_SELECTED = -1//ghế đang chờ thanh toán
            const val SELECTING = 0//ghế đang chọn
            const val EMPTY = 1//ghế trống
            const val SELECTED = 2//ghế đang được người khác giữ
            const val BOOKED = 3//ghế đã đặt chưa thanh toán
            const val SOLD = 4//ghế đã thanh toán
            const val SOLDED = 5//ghế đã thanh toán + lấy vé
        }
    }

    interface AppParamCode {
        companion object {
            const val intro = "app-introduce"
            const val version = "app-version"
        }
    }

    interface ScreenTypeClass {
        companion object {
            const val normal = "normal"
            const val vip = "vip"
        }
    }
}
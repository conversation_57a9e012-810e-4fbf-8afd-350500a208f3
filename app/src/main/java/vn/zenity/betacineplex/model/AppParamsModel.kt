package vn.zenity.betacineplex.model

import android.text.TextUtils
import vn.zenity.betacineplex.app.App

data class AppParamsModel(
        var ParamsCode: String,
        var ParamsMessage_F: String,
        var ParamsMessageHighLight_F: String,
        var Value: String,
        var CanClose: Boolean
) {
    var ParamsMessage: String = ""
        get() {
            if (!App.shared().isLangVi() && !TextUtils.isEmpty(ParamsMessage_F)) {
                return ParamsMessage_F
            }
            return field
        }
    var ParamsMessageHighLight: String = ""
        get() {
            if (!App.shared().isLangVi() && !TextUtils.isEmpty(ParamsMessageHighLight_F)) {
                return ParamsMessageHighLight_F
            }
            return field
        }
}
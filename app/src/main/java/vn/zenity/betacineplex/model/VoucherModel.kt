package vn.zenity.betacineplex.model

import android.text.TextUtils
import com.google.gson.annotations.SerializedName

data class VoucherModel(
        var Voucher_Card_Id: String?,
        var PinCode: String?,
        var VoucherCode: String?,
        var Unit: String?,
        var CardTypeName: String?,
        var ConditionOfUse: String?,
        var UnitPrice: Int = 0,
        var StartDate: String?,
        var EndDate: String?,
        var DateOfStatus: String?,
        var Status: Int = 0,
        var StatusName: String?,
        var CustomerId: String?,
        var CustomerCard: String?,
        var VoucherId: String? = null,
        var VoucherStatus: Int = 0,
        var ExpirationDate: String?,
        var ActivationDate: String?,
        var Description: String?,
        var IsAssigned: Boolean?,
        var VoucherType: String?,
        var IsSell: Boolean?,
        var Storyline: Storyline?,
        var IsAvaiableForGift: Boolean?
) {
    var VoucherPackageName: String? = null

    var StorylineContent: String? = null

    var VoucherName: String? = null
    get() {
        if (TextUtils.isEmpty(field)) {
            return Storyline?.Tieu_de
        }
        return field
    }
}

data class Storyline(var StorylineID: String?,
                     var Tieu_de: String?,
                     var StorylineContent: String?)
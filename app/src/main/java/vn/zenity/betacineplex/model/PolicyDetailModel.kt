package vn.zenity.betacineplex.model

import vn.zenity.betacineplex.global.Constant
import vn.zenity.betacineplex.helper.extension.dateConvertFormat
import vn.zenity.betacineplex.helper.extension.toDate
import java.util.*

/**
 * Created by tinhvv on 4/16/18.
 */
class Paragraph{
    var ParagraphContent: String? = null
}

class Content{
    var ParagraphData: Paragraph? = null
}

class PolicyDetailModel{
    var Noi_dung_chi_tiet: ArrayList<Content>? = null
    var PublishOnDate: String? = null
    var Tieu_de: String? = null

    var dateString: String? = null
        get() = PublishOnDate?.let { it.dateConvertFormat(Constant.DateFormat.default, Constant.DateFormat.dateSavis) }

    var date: Date? = null
        get() = PublishOnDate?.let { it.toDate(Constant.DateFormat.default) }
}
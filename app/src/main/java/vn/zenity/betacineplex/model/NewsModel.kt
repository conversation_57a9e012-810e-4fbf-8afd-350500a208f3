package vn.zenity.betacineplex.model

import com.google.gson.annotations.Expose
import vn.zenity.betacineplex.global.Constant
import vn.zenity.betacineplex.helper.extension.dateConvertFormat
import vn.zenity.betacineplex.helper.extension.toDate
import vn.zenity.betacineplex.helper.extension.toImageUrl
import java.util.*

/**
 * Created by tinhvv on 4/14/18.
 */
class NewsModel {
    var StorylineID : String? = null
    var StorylineType : Int? = null
    var Tieu_de : String? = null
    var Tieu_de_ko_dau : String? = null
    var Tieu_de_phu : String? = null
    var Tieu_de_limit : String? = null
    var Tieu_de_phu_copy : String? = null
    var Truc_tiep : Int? = null
    var Duong_dan_anh_dai_dien : String? = null
        get() = field?.toImageUrl()
    var Tieu_de_anh : String? = null
    var Tieu_de_anh_ko_dau : String? = null
    var Tom_tat_noi_dung : String? = null
    var Tom_tat_noi_dung_phu : String? = null
    var Tom_tat_noi_dung_limit : String? = null
    var Tom_tat_anh_dai_dien : String? = null
    var PublishOnDate : String? = null
    var Breadcrumbs : String? = null
    var NewsURI : String? = null
    var Duong_dan_video_dai_dien : String? = null
    var KeyWords : String? = null
    var Noi_dung_chi_tiet: List<NewsContentDetail>? = null
    var IsExistVoucherCode: Boolean? = null
    var ListMetadata: List<VoucherMetadata>? = null

    @Transient
    @Expose
    private var _content: String? = null

    var dateString: String? = null
        get() = PublishOnDate?.let { it.dateConvertFormat(Constant.DateFormat.default, Constant.DateFormat.dateSavis) }

    var date: Date? = null
        get() = PublishOnDate?.let { it.toDate(Constant.DateFormat.default) }

    fun getFullContent(): String? {
        if (_content != null) return _content
        if (Noi_dung_chi_tiet == null) return null
        if (Noi_dung_chi_tiet?.size ?: 0 <= 0) return null
        _content = ""
        Noi_dung_chi_tiet?.forEach {
            if (it.ParagraphData?.ParagraphContent != null) {
                _content += it.ParagraphData?.ParagraphContent
            }
        }
        return _content
    }
}

class NewsContentDetail {
    var ID: String? = null
    var Tieu_de: String? = null
    var StorylineID: String? = null
    var ObjectID: String? = null
    var TableName: String? = null
    var CreatedByUserID: String? = null
    var Thu_tu_hien_thi: String? = null
    var CreatedOnDate: String? = null
    var LastModifiedByUserID: String? = null
    var LastModifiedOnDate: String? = null
    var StartDate: String? = null
    var EndDate: String? = null
    var ParagraphData: NewsParagraphData? = null

}
class NewsParagraphData {
    var ParagraphID: String? = null
    var ParagraphContent: String? = null
    var ParagraphDraftContent: String? = null
    var URI: String? = null
    var ApplicationId: String? = null
    var CreatedByUserID: String? = null
    var CreatedOnDate: String? = null
    var LastModifiedByUserID: String? = null
    var LastModifiedOnDate: String? = null
    var StartDate: String? = null
    var EndDate: String? = null
    var HasAlarm: String? = null
    var DeleteSuccess: String? = null
}

data class VoucherMetadata(
    var Name : String,
    var Value : String,
    var IsShowOut : Boolean
)
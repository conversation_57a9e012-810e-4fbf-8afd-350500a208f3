package vn.zenity.betacineplex.model

import android.location.Location
import android.os.Parcel
import android.os.Parcelable
import com.thoughtbot.expandablerecyclerview.models.ExpandableGroup
import vn.zenity.betacineplex.helper.extension.getDistance

data class GroupFilmBooking(
    var CinemaId: String?,
    var CinemaName: String?,
    var CinemaName_F: String?,
    var Latitude: String? = null,
    var Longtitude: String? = null,
    var Distance: Float? =0f,
    var ListFilm: List<FilmBooking> = listOf()
) : ExpandableGroup<FilmBooking>(CinemaName, ListFilm) {
    fun getDistanceToCurrentLocation(lct: Location?): Float {
        lct ?: return 10000f
        return lct.getDistance(Latitude, Longtitude)
    }
}


class GroupFilmBookingEntity {
    var Page= 0
    var TotalPages =0
    var Size=0
    var NumberOfElements=0
    var TotalElements=0
    var Content: List<GroupFilmBooking> = listOf()
}
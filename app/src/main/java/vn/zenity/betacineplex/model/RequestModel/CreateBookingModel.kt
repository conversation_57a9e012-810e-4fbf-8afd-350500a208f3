package vn.zenity.betacineplex.model.RequestModel

import vn.zenity.betacineplex.model.SeatScreenModel
import java.util.*

data class CreateBookingModel(var ShowId: String,
                              var CountDown : String?,
                              var Seats: List<SeatBookingModel> = listOf())

data class SeatBookingModel(var SeatIndex: Int,
                            var SeatName: String,
                            var SeatType: String,//loai ghe, VIP, DOUBLE, STARDAR
                            var TicketTypeId: String,
                            var Price: Int) {
    companion object {
        fun createFromScreen(screenModel: SeatScreenModel): SeatBookingModel{
            return SeatBookingModel(screenModel.SeatIndex, screenModel.SeatName, screenModel.SeatType?.Name ?: "", screenModel.ticketType?.TicketTypeId ?: "", screenModel.ticketType?.Price ?: 0)
        }
    }
}
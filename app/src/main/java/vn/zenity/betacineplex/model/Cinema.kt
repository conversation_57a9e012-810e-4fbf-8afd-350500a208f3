package vn.zenity.betacineplex.model

import android.os.Parcel
import android.os.Parcelable

data class Cinema(var title: String? = null) : Parcelable {

    constructor(parcel: Parcel) : this() {
    }

    override fun writeToParcel(parcel: Parcel, flags: Int) {

    }

    override fun describeContents(): Int {
        return 0
    }

    companion object CREATOR : Parcelable.Creator<Cinema> {
        override fun createFromParcel(parcel: Parcel): Cinema {
            return Cinema(parcel)
        }

        override fun newArray(size: Int): Array<Cinema?> {
            return arrayOfNulls(size)
        }
    }
}
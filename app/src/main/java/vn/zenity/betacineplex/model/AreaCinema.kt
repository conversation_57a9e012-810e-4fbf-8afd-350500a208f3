package vn.zenity.betacineplex.model

import com.thoughtbot.expandablerecyclerview.models.ExpandableGroup
import vn.zenity.betacineplex.view.cenima.CenimaFragment

class AreaCinema(title: String?, var cinemas: List<CinemaModel>?, var type: Int = CenimaFragment.TYPE_HEADER_AREA,var cinema: CinemaModel? = null, var area: CinemaProvinceModel? = null) : ExpandableGroup<CinemaModel>(title, cinemas) {
    override fun getItems(): MutableList<CinemaModel> {
        if (type == CenimaFragment.TYPE_HEADER_NEAR_ME) {
            return mutableListOf()
        }
        return super.getItems()
    }

    override fun getItemCount(): Int {
        if (type == CenimaFragment.TYPE_HEADER_NEAR_ME) {
            return 0
        }
        if (type == CenimaFragment.TYPE_HEADER_AREA) {
            return super.getItemCount() / 2 + super.getItemCount() % 2
        }
        return super.getItemCount()
    }
}
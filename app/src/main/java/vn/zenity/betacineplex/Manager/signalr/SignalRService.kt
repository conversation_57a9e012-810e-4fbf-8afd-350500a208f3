package vn.zenity.betacineplex.Manager.signalr

import android.app.Service
import android.content.Context
import android.content.Intent
import android.os.Binder
import android.os.Handler
import android.os.IBinder
import android.os.Looper
import android.util.Log
import microsoft.aspnet.signalr.client.hubs.HubConnection
import microsoft.aspnet.signalr.client.hubs.HubProxy
import com.google.gson.Gson
import com.google.gson.JsonElement
import io.reactivex.Observable
import io.reactivex.ObservableEmitter
import io.reactivex.ObservableOnSubscribe
import io.reactivex.rxkotlin.subscribeBy
import microsoft.aspnet.signalr.client.*
import microsoft.aspnet.signalr.client.hubs.SubscriptionHandler1
import microsoft.aspnet.signalr.client.transport.ServerSentEventsTransport
import microsoft.aspnet.signalr.client.transport.ClientTransport
import microsoft.aspnet.signalr.client.http.android.AndroidPlatformComponent
import microsoft.aspnet.signalr.client.Platform.loadPlatformComponent
import microsoft.aspnet.signalr.client.http.android.AndroidHttpConnection
import microsoft.aspnet.signalr.client.transport.LongPollingTransport
import vn.zenity.betacineplex.BuildConfig
import vn.zenity.betacineplex.helper.extension.logD
import java.util.*
import java.util.concurrent.ExecutionException
import java.util.concurrent.TimeUnit


typealias ListenerSignalRConnection = (SignalRService.StateSignalR) -> Unit
typealias ListenerData = (SeatSignalrResponse) -> Unit
/**
 * Created by vinhdn on 11/10/17.
 */
class SignalRService {

    companion object {
        var instance: SignalRService? = null
        fun share(): SignalRService {
            if (instance == null) {
                instance = SignalRService()
            }
            return instance!!
        }
        val SPECIAL_SYMBOL = "\u001E";
    }

    enum class StateSignalR { connected, disabled }

    private var mHubConnection: HubConnection? = null
    private var mHubProxy: HubProxy? = null
    private var listenerConnections: MutableList<ListenerSignalRConnection> = mutableListOf()
    val listenerData: MutableList<ListenerData> = mutableListOf()
    fun addListenerConnection(listener: ListenerSignalRConnection) {
        listenerConnections.add(listener)
        if (mHubConnection?.state == ConnectionState.Connected) {
            listener(StateSignalR.connected)
        }
    }

    fun removeListenerConnection(listener: ListenerSignalRConnection) {
        listenerConnections.remove(listener)
    }

    fun startBGSignalR() {
        Observable.create(ObservableOnSubscribe<Long> {
            startSignalR()
        }).subscribe()
    }

    fun reconnect() {
        Observable.create(ObservableOnSubscribe<Long> {
            try {
                mHubConnection?.stop()
            } catch (_: Exception) {
            } catch (_: java.lang.Exception) {

            }
            Observable.create(ObservableOnSubscribe<Long> {
                try {
                    mHubConnection?.start()
                } catch (_: Exception) {
                } catch (_: java.lang.Exception) {

                }
            }).delay(1, TimeUnit.SECONDS).subscribeBy (
                    onNext = {},
                    onError = {}
            )
        }).subscribeBy (
                onNext = {},
                onError = {}
        )
    }

    fun startSignalR() {
        Platform.loadPlatformComponent(AndroidPlatformComponent())
        val serverUrl = "${BuildConfig.SHARE_DOMAIN}/signalr/hubs"
        val mAH = AndroidHttpConnection(Logger { s, logLevel -> })
        mHubConnection = HubConnection(serverUrl, null, true, Logger { message, _ ->
            logD(message)
        })
        val hubName = "chooseSeatHub"
        mHubProxy = mHubConnection?.createHubProxy(hubName)
        val clientTransport = ServerSentEventsTransport(mHubConnection?.logger)
        val signalRFuture = mHubConnection?.start(clientTransport)
        mHubProxy?.subscribe(this)
        mHubConnection?.received { jsonElement ->
            if (jsonElement.isJsonObject) {
                if (jsonElement.asJsonObject.has("A")) {
                    val ja = jsonElement.asJsonObject.getAsJsonArray("A")
                    try {
                        val data = SeatSignalrResponse(ja[0].asString, ja[1].asString, ja[2].asInt, ja[3].asInt)
                        listenerData.forEach {
                            it.invoke(data)
                        }
                    } catch (_ : Exception) {

                    }
                }
            }
        }
        mHubConnection?.connected {
            handler.post(runnable)
            subscribe("broadcastMessage")
            listenerConnections.forEach { listener ->
                listener(StateSignalR.connected)
            }
        }

        mHubConnection?.closed {
            listenerConnections.forEach { listener ->
                listener(StateSignalR.disabled)
            }
        }

        mHubConnection?.error { _ ->
            listenerConnections.forEach { listener ->
                listener(StateSignalR.disabled)
            }
        }

        mHubConnection?.stateChanged { state, newState ->
            if (newState == ConnectionState.Disconnected) {
//                mHubConnection?.start()
            }
        }
        try {
            signalRFuture?.done({})?.onError { }
                    ?.onCancelled { }
//            signalRFuture?.get()
        } catch (e: InterruptedException) {
            Log.e("SimpleSignalR", e.toString())
            return
        } catch (e: Exception) {
            Log.e("SimpleSignalR", e.toString())
            return
        } catch (e: java.lang.Exception) {
            Log.e("SimpleSignalR", e.toString())
            return
        }
    }

    private val handler = Handler()

    private val runnable = object : Runnable {
        override fun run() {
            if (mHubConnection?.state == ConnectionState.Disconnected) {
                reconnect()
            }
            handler.postDelayed(this, 5 * 1000)
        }
    }

    fun subscribe(event: String) {
        mHubProxy?.subscribe(event)
    }

    fun unSubscribe(event: String) {
        mHubProxy?.removeSubscription(event)
    }

    fun invoke(event: String, message: List<Any>) {
        mHubProxy?.invoke(event, message[0], message[1], message[2], message[3])
    }

    fun stop() {
        try {
            handler.removeCallbacks(runnable)
            mHubConnection?.stop()
        }catch (_: java.lang.Exception) {}
    }

    fun connectionId() : String {
        return mHubConnection?.connectionId ?: ""
    }

}
package vn.zenity.betacineplex.Manager.Network

import com.techvein.okhttp3.logging.CurlHttpLoggingInterceptor
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.Retrofit
import retrofit2.adapter.rxjava2.RxJava2CallAdapterFactory
import retrofit2.converter.gson.GsonConverterFactory
import vn.zenity.betacineplex.BuildConfig
import vn.zenity.betacineplex.app.App
import vn.zenity.betacineplex.global.Global
import java.util.concurrent.TimeUnit

/**
 * Created by tinhvv on 4/8/18.
 */
class APIClient {
    companion object {
        val shared = APIClient()
    }

    private val httpClient by lazy {
        val httpClientBuilder = OkHttpClient.Builder()

        val interceptor = HttpLoggingInterceptor()
        interceptor.level = if (BuildConfig.DEBUG) HttpLoggingInterceptor.Level.BODY else HttpLoggingInterceptor.Level.NONE

        val headerInterceptor = Interceptor{ chain ->  
            val builder = chain.request().newBuilder().addHeader("channel","mobile")
            builder.addHeader("device-type", "android")
            builder.addHeader("language", if(App.shared().isLangVi()) "vn" else "en")
            Global.share().user?.AccountId?.let {
                builder.addHeader("X-User", it)
            }
            if (!chain.request().url().encodedPath().contains("login"))
            Global.share().token?.let {
                builder.addHeader("Authorization", "Bearer $it")
            }
            val request = builder.build()
            chain.proceed(request)
        }

        httpClientBuilder.pingInterval(60, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS)
                .writeTimeout(60, TimeUnit.SECONDS)
                .connectTimeout(60, TimeUnit.SECONDS)
                .addInterceptor(interceptor)
                .addNetworkInterceptor(headerInterceptor)
        if (BuildConfig.DEBUG) {
            httpClientBuilder.addNetworkInterceptor(CurlHttpLoggingInterceptor())
        }

        httpClientBuilder.build()
    }

    private val retrofit by lazy {
        val builder = Retrofit.Builder()
                .addCallAdapterFactory(RxJava2CallAdapterFactory.create())
                .addConverterFactory(GsonConverterFactory.create())
                .baseUrl(BuildConfig.BASE_URL)
                .client(httpClient)
        builder.build()
    }

    private val retrofitString by lazy {
        val builder = Retrofit.Builder()
                .addCallAdapterFactory(RxJava2CallAdapterFactory.create())
                .baseUrl(BuildConfig.BASE_URL)
                .client(httpClient)
        builder.build()
    }

    lateinit var cityAPI: CityAPI
    lateinit var filmAPI: FilmAPI
    val bookingAPI: FilmAPI by lazy {
        retrofitString.create(FilmAPI::class.java)
    }
    lateinit var accountAPI: AccountAPI
    lateinit var ecmAPI: EcmAPI
    lateinit var cinemaAPI: CinemaAPI
    val transactionAPI: TransactionAPI by lazy {
        retrofit.create(TransactionAPI::class.java)
    }

    init {
        createCityAPI()
        createFilmAPI()
        createAccountAPI()
        createEcmAPI()
        createCinemaAPI()
    }



    private fun createCityAPI(){
        cityAPI = retrofit.create(CityAPI::class.java)
    }

    private fun createFilmAPI(){
        filmAPI = retrofit.create(FilmAPI::class.java)
    }

    private fun createAccountAPI(){
        accountAPI = retrofit.create(AccountAPI::class.java)
    }

    private fun createEcmAPI(){
        ecmAPI = retrofit.create(EcmAPI::class.java)
    }

    private fun createCinemaAPI(){
        cinemaAPI = retrofit.create(CinemaAPI::class.java)
    }
}
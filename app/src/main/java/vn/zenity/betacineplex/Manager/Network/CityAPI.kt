package vn.zenity.betacineplex.Manager.Network

import retrofit2.http.GET
import vn.zenity.betacineplex.model.CityModel
import vn.zenity.betacineplex.model.DDKCReponse
import io.reactivex.Observable
import retrofit2.http.Path

import kotlin.collections.ArrayList

/**
 * Created by tinhvv on 4/8/18.
 */
interface CityAPI {

    @GET("api/v1/erp/cities")// màn hình register
    fun getListCity(): Observable<DDKCReponse<ArrayList<CityModel>>>

    @GET("api/v1/erp/cities/{cityId}/districts")
    fun getDistrictOfCity(@Path("cityId") cityId: String): Observable<DDKCReponse<ArrayList<CityModel>>>
}
{"project_info": {"project_number": "727520315581", "firebase_url": "https://beta-cineplex.firebaseio.com", "project_id": "beta-cineplex", "storage_bucket": "beta-cineplex.appspot.com"}, "client": [{"client_info": {"mobilesdk_app_id": "1:727520315581:android:2e84379698b4e431", "android_client_info": {"package_name": "com.beta.betacineplex"}}, "oauth_client": [{"client_id": "727520315581-ajln2k7vglvugnkg2nf9b9ie6vmnhmv3.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyC4tTAsRqD0K-07mSNuWJkPRGSRgDQnBnI"}], "services": {"analytics_service": {"status": 1}, "appinvite_service": {"status": 1, "other_platform_oauth_client": []}, "ads_service": {"status": 2}}}, {"client_info": {"mobilesdk_app_id": "1:727520315581:android:8f04ad4b733d8cb6", "android_client_info": {"package_name": "com.beta.betacineplex.customer"}}, "oauth_client": [{"client_id": "727520315581-ajln2k7vglvugnkg2nf9b9ie6vmnhmv3.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyC4tTAsRqD0K-07mSNuWJkPRGSRgDQnBnI"}], "services": {"analytics_service": {"status": 1}, "appinvite_service": {"status": 1, "other_platform_oauth_client": []}, "ads_service": {"status": 2}}}, {"client_info": {"mobilesdk_app_id": "1:727520315581:android:8f04ad4b733d8cb6", "android_client_info": {"package_name": "com.beta.betacineplex.test.prod"}}, "oauth_client": [{"client_id": "727520315581-ajln2k7vglvugnkg2nf9b9ie6vmnhmv3.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyC4tTAsRqD0K-07mSNuWJkPRGSRgDQnBnI"}], "services": {"analytics_service": {"status": 1}, "appinvite_service": {"status": 1, "other_platform_oauth_client": []}, "ads_service": {"status": 2}}}, {"client_info": {"mobilesdk_app_id": "1:727520315581:android:8f04ad4b733d8cb6", "android_client_info": {"package_name": "com.beta.betacineplex.dev"}}, "oauth_client": [{"client_id": "727520315581-ajln2k7vglvugnkg2nf9b9ie6vmnhmv3.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyC4tTAsRqD0K-07mSNuWJkPRGSRgDQnBnI"}], "services": {"analytics_service": {"status": 1}, "appinvite_service": {"status": 1, "other_platform_oauth_client": []}, "ads_service": {"status": 2}}}], "configuration_version": "1"}
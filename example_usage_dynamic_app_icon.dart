import 'package:flutter/material.dart';
import 'package:flutter_app/utils/index.dart';
import 'package:flutter_app/pages/settings/app_icon_settings.dart';

/// Example usage of Dynamic App Icon functionality
/// Shows how to integrate into existing Flutter app
class ExampleDynamicAppIconUsage extends StatefulWidget {
  const ExampleDynamicAppIconUsage({Key? key}) : super(key: key);

  @override
  State<ExampleDynamicAppIconUsage> createState() => _ExampleDynamicAppIconUsageState();
}

class _ExampleDynamicAppIconUsageState extends State<ExampleDynamicAppIconUsage> {
  AppIconManager.AppIcon _currentIcon = AppIconManager.AppIcon.defaultIcon;
  bool _isSupported = false;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _initializeIconStatus();
  }

  Future<void> _initializeIconStatus() async {
    try {
      final isSupported = await AppIconManager.isSupported();
      final currentIcon = await AppIconManager.getCurrentIcon();
      
      if (mounted) {
        setState(() {
          _isSupported = isSupported;
          _currentIcon = currentIcon;
          _isLoading = false;
        });
      }
    } catch (e) {
      print('Error initializing icon status: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _toggleIcon() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final success = await AppIconManager.toggleIcon();
      
      if (success) {
        final newIcon = await AppIconManager.getCurrentIcon();
        if (mounted) {
          setState(() {
            _currentIcon = newIcon;
          });
          
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('App icon changed to ${AppIconManager.getIconDisplayName(newIcon)}'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        _showErrorSnackBar();
      }
    } catch (e) {
      print('Error toggling icon: $e');
      _showErrorSnackBar();
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _setHeartIcon() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final success = await AppIconManager.setHeartIcon();
      
      if (success && mounted) {
        setState(() {
          _currentIcon = AppIconManager.AppIcon.hearts;
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('App icon changed to Hearts'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        _showErrorSnackBar();
      }
    } catch (e) {
      print('Error setting heart icon: $e');
      _showErrorSnackBar();
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _setDefaultIcon() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final success = await AppIconManager.setDefaultIcon();
      
      if (success && mounted) {
        setState(() {
          _currentIcon = AppIconManager.AppIcon.defaultIcon;
        });
        
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('App icon changed to Default'),
            backgroundColor: Colors.green,
          ),
        );
      } else {
        _showErrorSnackBar();
      }
    } catch (e) {
      print('Error setting default icon: $e');
      _showErrorSnackBar();
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showErrorSnackBar() {
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('Failed to change app icon'),
        backgroundColor: Colors.red,
      ),
    );
  }

  void _openIconSettings() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AppIconSettingsScreen(),
      ),
    ).then((_) => _initializeIconStatus()); // Refresh when returning
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Dynamic App Icon Example'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildContent(),
    );
  }

  Widget _buildContent() {
    if (!_isSupported) {
      return const Center(
        child: Padding(
          padding: EdgeInsets.all(16.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.info_outline,
                size: 64,
                color: Colors.grey,
              ),
              SizedBox(height: 16),
              Text(
                'Dynamic App Icons Not Supported',
                style: TextStyle(
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 8),
              Text(
                'This feature is not supported on this device.',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Colors.grey,
                ),
              ),
            ],
          ),
        ),
      );
    }

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Current Icon Status
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                children: [
                  const Text(
                    'Current App Icon',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    AppIconManager.getIconDisplayName(_currentIcon),
                    style: const TextStyle(
                      fontSize: 16,
                      color: Colors.blue,
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 20),
          
          // Action Buttons
          ElevatedButton.icon(
            onPressed: _isLoading ? null : _toggleIcon,
            icon: const Icon(Icons.swap_horiz),
            label: const Text('Toggle Icon'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
          
          const SizedBox(height: 12),
          
          ElevatedButton.icon(
            onPressed: _isLoading ? null : _setHeartIcon,
            icon: const Icon(Icons.favorite),
            label: const Text('Set Heart Icon'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
          
          const SizedBox(height: 12),
          
          ElevatedButton.icon(
            onPressed: _isLoading ? null : _setDefaultIcon,
            icon: const Icon(Icons.apps),
            label: const Text('Set Default Icon'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.grey,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
          
          const SizedBox(height: 20),
          
          OutlinedButton.icon(
            onPressed: _openIconSettings,
            icon: const Icon(Icons.settings),
            label: const Text('Open Icon Settings'),
            style: OutlinedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
          
          const SizedBox(height: 20),
          
          // Platform Info
          Card(
            color: Colors.blue.shade50,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Platform Support',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text('✅ iOS: CFBundleAlternateIcons'),
                  Text('✅ Android: Activity Aliases'),
                  const SizedBox(height: 8),
                  const Text(
                    'Note: Icon changes may require a few seconds to appear on the home screen.',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

# Point History Flow Comparison - iOS vs Flutter

## V<PERSON>n <PERSON>t Hiện

Flutter **thiếu API và UI riêng cho Point History** (`api/v2/erp/point/history`). Hiện tại chỉ có transaction history chung.

## Flow So Sánh

### 1. **iOS Flow - RewardPointsViewController**

#### **Screen Structure**:
```swift
// Booking/Class/Controller/Member/RewardPointsViewController.swift
class RewardPointsViewController: BaseViewController {
    @IBOutlet weak var lbTotalPoint: UILabel!
    @IBOutlet weak var lbUsedPoint: UILabel!
    @IBOutlet weak var lbCurrentPoint: UILabel!
    @IBOutlet weak var lbRemainPoint: UILabel!
    
    // History button in navigation bar
    let historyButton = UIBarButtonItem(image: UIImage(named: "ic_history_used"), 
                                       style: .plain, 
                                       target: self, 
                                       action: #selector(historyVoucher))
}
```

#### **Navigation to Point History**:
```swift
@objc private func historyVoucher() {
    let historyVC = HistoryVoucherViewController()
    historyVC.hidesBottomBarWhenPushed = true
    historyVC.type = .point  // ✅ Specific point type
    self.navigationController?.pushViewController(historyVC, animated: true)
}
```

#### **Point History API Call**:
```swift
// Booking/Class/Controller/Voucher/HistoryVoucherViewController.swift
private func getPointHistories() {
    self.showLoading()
    VoucherProvider.rx.request(.historyPoint)  // ✅ api/v2/erp/point/history
        .mapObject(DDKCResponse<PointHistory>.self)
        .subscribe(onNext: {[weak self] response in
            self?.pointHistories = response.ListObject ?? []
            self?.tableView.reloadData()
        })
}
```

### 2. **Android Flow - BetaPointFragment**

#### **Point History API**:
```kotlin
// app/src/main/java/vn/zenity/betacineplex/Manager/Network/AccountAPI.kt
@GET("api/v2/erp/point/history")
fun getUsedPointHistories(): Single<DDKCReponse<List<PointHistoryModel>>>
```

#### **Presenter Implementation**:
```kotlin
// app/src/main/java/vn/zenity/betacineplex/view/user/point/PointHistoryPresenter.kt
override fun getUsedPointHistories() {
    disposable = APIClient.shared.accountAPI.getUsedPointHistories()
        .subscribe({
            if (it.isSuccess) {
                view?.get()?.showUsedPointHistories(it.Data ?: listOf())
            }
        })
}
```

### 3. **Flutter Flow - Before (Thiếu)**

#### **Reward Points Screen**:
```dart
// lib/pages/my_profile/reward_points_screen.dart
void _navigateToPointHistory() {
  // ❌ Navigate to general transaction history (wrong)
  Navigator.push(context, MaterialPageRoute(
    builder: (context) => const TransactionHistoryScreen()
  ));
}
```

#### **Missing Components**:
- ❌ No dedicated Point History Model
- ❌ No dedicated Point History Screen  
- ❌ No proper API integration for point history
- ❌ Mixed with general transaction history

### 4. **Flutter Flow - After (Fixed)**

#### **Point History Model**:
```dart
// lib/models/point_history_model.dart
class PointHistoryModel {
  final String? date;
  final int? statusType;     // 1: Tích điểm, 2: Tiêu điểm, etc.
  final String? statusName;
  final int? point;
  final String? accountName;
  
  // ✅ Matches iOS PointStatusType enum
  PointStatusType get pointStatus { /* ... */ }
  
  // ✅ Matches iOS dateString computed property
  String get dateString { /* ... */ }
  
  // ✅ Matches iOS showName computed property  
  bool get showName { /* ... */ }
}

enum PointStatusType {
  save,     // 1: Tích điểm
  expense,  // 2: Tiêu điểm  
  cancel,   // 3,4: Giao dịch hủy
  donate,   // 5: Tặng điểm
  receive,  // 6: Nhận điểm
}
```

#### **Point History Screen**:
```dart
// lib/pages/my_profile/point_history_screen.dart
class PointHistoryScreen extends StatefulWidget {
  // ✅ Dedicated screen for point history only
  
  Future<void> _loadPointHistories() async {
    final api = Api();
    final response = await api.auth.getPointHistory();  // ✅ api/v2/erp/point/history
    
    final List<dynamic> data = response!.data['Data'] ?? response.data['ListObject'] ?? [];
    _pointHistories = data.map((item) => PointHistoryModel.fromJson(item)).toList();
  }
}
```

#### **Updated Navigation**:
```dart
// lib/pages/my_profile/reward_points_screen.dart
void _navigateToPointHistory() {
  // ✅ Navigate to dedicated point history screen
  Navigator.push(context, MaterialPageRoute(
    builder: (context) => const PointHistoryScreen()
  ));
}
```

#### **API Integration**:
```dart
// lib/service/src/auth.dart (Already exists)
Future<MApi?> getPointHistory() async => checkAuth(
  result: await BaseHttp.get(
    url: '$endpoint/api/v2/erp/point/history',  // ✅ Correct API
    headers: headers,
    queryParameters: {},
  )
);
```

## UI Design Comparison

### 1. **iOS UI - HistoryPointTableViewCell**

#### **Cell Structure**:
- **Status Icon**: Color-coded based on point type
- **Status Name**: "Tích điểm", "Tiêu điểm", etc.
- **Date**: "dd/MM/yyyy, HH:mm" format
- **Point Amount**: "+/-XXX điểm" with color
- **Account Name**: Show for donate/receive types

#### **Color Scheme**:
```swift
case .save: return 0x3fb7f9.toColor      // Blue
case .expense: return 0xfd2802.toColor   // Red  
case .donate: return 0xfd7c02.toColor    // Orange
case .receive: return 0x7ed321.toColor   // Green
case .cancel: return 0x494c62.toColor    // Gray
```

### 2. **Flutter UI - PointHistoryItem**

#### **Matching Design**:
```dart
Widget _buildPointHistoryItem(PointHistoryModel history) {
  return Container(
    child: Row(
      children: [
        // ✅ Status icon with color coding (matches iOS)
        Container(
          decoration: BoxDecoration(
            color: pointStatus.color.withOpacity(0.1),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Icon(pointStatus.icon, color: pointStatus.color),
        ),
        
        Expanded(
          child: Column(
            children: [
              // ✅ Status name and date (matches iOS)
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(pointStatus.description),  // "Tích điểm", "Tiêu điểm"
                  Text(history.dateString),       // "dd/MM/yyyy, HH:mm"
                ],
              ),
              
              // ✅ Account name if applicable (matches iOS showName)
              if (history.showName && history.accountName != null)
                Text('Tài khoản: ${history.accountName}'),
              
              // ✅ Point amount with color (matches iOS)
              Text(
                '${isPositive ? '+' : '-'}${history.point ?? 0} điểm',
                style: TextStyle(
                  color: isPositive ? Colors.green : Colors.red,
                ),
              ),
            ],
          ),
        ),
      ],
    ),
  );
}
```

## Feature Parity Achieved

### ✅ **API Integration**
- **Endpoint**: `api/v2/erp/point/history` ✅
- **Response Parsing**: Support both `Data` and `ListObject` ✅
- **Error Handling**: Proper try-catch and loading states ✅

### ✅ **Data Model**
- **PointHistoryModel**: Matches iOS/Android fields ✅
- **PointStatusType Enum**: Matches iOS enum with colors ✅
- **Date Formatting**: Matches iOS dateString format ✅
- **Show Name Logic**: Matches iOS showName logic ✅

### ✅ **UI Design**
- **Screen Layout**: Matches iOS HistoryVoucherViewController ✅
- **Cell Design**: Matches iOS HistoryPointTableViewCell ✅
- **Color Scheme**: Matches iOS color coding ✅
- **Navigation**: Matches iOS historyVoucher() flow ✅

### ✅ **User Experience**
- **Loading States**: Loading indicator during API call ✅
- **Empty States**: "Chưa có lịch sử" message ✅
- **Error States**: Error handling with retry button ✅
- **Pull to Refresh**: RefreshIndicator for data reload ✅

## Kết Quả

**Flutter giờ đây có Point History flow hoàn chỉnh:**

1. **✅ Dedicated Point History Screen** - riêng biệt với transaction history
2. **✅ Proper API Integration** - sử dụng `api/v2/erp/point/history`
3. **✅ Complete Data Model** - match iOS/Android PointHistory model
4. **✅ iOS-matching UI Design** - color coding, layout, navigation
5. **✅ Feature Parity** - tất cả tính năng như iOS/Android

**Navigation Flow**:
```
Reward Points Screen → History Button → Point History Screen
                                     ↓
                              api/v2/erp/point/history
                                     ↓
                            Display point transactions only
```

**Flutter Point History giờ đây hoàn toàn match iOS/Android implementation!**

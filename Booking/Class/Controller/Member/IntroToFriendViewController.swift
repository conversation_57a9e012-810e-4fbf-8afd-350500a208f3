//
//  IntroToFriendViewController.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 8/4/19.
//  Copyright © 2019 ddkc. All rights reserved.
//

import UIKit

class IntroToFriendViewController: BaseViewController {

    @IBOutlet weak var tableView: UITableView!
    @IBOutlet var topCell: UITableViewCell!
    @IBOutlet var bottomCell: UITableViewCell!

    @IBOutlet weak var lb1: UILabel!
    @IBOutlet weak var lb2: UILabel!
    @IBOutlet weak var lb3: UILabel!

    @IBOutlet weak var codeTextField: UITextField!
    @IBOutlet weak var codeLabel: UILabel!
    @IBOutlet weak var confirmButton: GradientButton!


    override func viewDidLoad() {
        super.viewDidLoad()

        setupViews()
    }

    private func setupViews() {
        tableView.separatorStyle = .none
        tableView.allowsSelection = false
        localizableTitle = "Member.Intro"
        if let message = Global.shared.appIntro?.message, let highlight = Global.shared.appIntro?.messageHighLight {
            let description1 = message.replacingOccurrences(of: highlight, with: "#\(highlight)#")
            let strings = description1.components(separatedBy: "#")
            let attributedString = NSMutableAttributedString()

            attributedString.string(strings[0], font: UIFont(fontName: .SourceSansPro, style: .Regular, size: 16))
            attributedString.string(strings[1], font: UIFont(fontName: .SourceSansPro, style: .Bold, size: 16))
            attributedString.string(strings[2], font: UIFont(fontName: .SourceSansPro, style: .Regular, size: 16))

            lb1.attributedText = attributedString
        }

        lb2.text = "share_code_note".localized
        lb3.text = "enter_code_note".localized

        codeLabel.text = (Global.shared.user?.ReferenceCode ?? "").uppercased()
        codeTextField.placeholder = "referral_code".localized

        if let code = Global.shared.user?.ReferenceAccountRefCode {
            codeTextField.text = code
            disableInputCode()
        }
    }

    private func disableInputCode() {
        self.bottomCell.isUserInteractionEnabled = false
        self.confirmButton.isEnabled = false
    }

    private func useIntroCode(_ code: String){
        self.showLoading()
        AccountProvider.rx.request(.intro(code)).mapObject(DDKCResponse<EmptyModel>.self)
            .subscribe(onNext: {[weak self] response in
                guard let `self` = self else {return}
                self.dismissLoading()

                self.showAlert(message: response.Message ?? "")

                if response.isSuccess() {
                    self.disableInputCode()
                }
            }).disposed(by: disposeBag)
    }


    @IBAction func shareTapped(_ sender: Any) {
        let code = (Global.shared.user?.ReferenceCode ?? "")
        let vc = UIActivityViewController(activityItems: [code], applicationActivities: nil)
        present(vc, animated: true, completion: nil)
    }

    @IBAction func useTapped(_ sender: Any) {
        guard let text = codeTextField.text, !text.isEmpty else {
            self.showAlert(message: "enter_intro_code".localized)
            return
        }
        useIntroCode(text)
    }
}

extension IntroToFriendViewController: UITableViewDelegate, UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return 2
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        if indexPath.row == 0 {
            return topCell
        }
        return bottomCell
    }

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        if indexPath.row == 0 {
            return 430
        }
        return 156
    }
}

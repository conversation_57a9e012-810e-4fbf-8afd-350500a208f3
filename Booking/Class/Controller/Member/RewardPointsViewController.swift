//
//  RewardPointsViewController.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 3/14/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

class RewardPointsViewController: BaseViewController {
    @IBOutlet weak var lbTotalPoint: UILabel!
    @IBOutlet weak var lbUsedPoint: UILabel!
    @IBOutlet weak var lbCurrentPoint: UILabel!
    @IBOutlet weak var lbRemainPoint: UILabel!

    @IBOutlet weak var donateButton: GradientButton!

    override func viewDidLoad() {
        super.viewDidLoad()
        navigationController?.setTransparent(false)
        localizableTitle = "BetaPoint.Title"
        donateButton.setTitle("donate_point".localized.uppercased(), for: .normal)
        donateButton.isHidden = true

        let historyButton = UIBarButtonItem(image: UIImage(named: "ic_history_used"), style: .plain, target: self, action: #selector(historyVoucher))
        navigationItem.rightBarButtonItem = historyButton

    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        getPoints()
    }

    @objc private func historyVoucher() {
        let historyVC = HistoryVoucherViewController()
        historyVC.hidesBottomBarWhenPushed = true
        historyVC.type = .point
        self.navigationController?.pushViewController(historyVC, animated: true)
    }
    
    private func getPoints() {
        guard let user = Global.shared.user, let userId = user.AccountId else {
            return
        }
        self.showLoading()
        AccountProvider.rx.request(.getPoints(userId)).mapObject(DDKCResponse<PointModel>.self)
            
            .subscribe(onNext:{[weak self] response in
                self?.dismissLoading()
                guard let item = response.Object else {
                    self?.flashError()
                    return
                }
                self?.lbTotalPoint.text = item.TotalAccumulatedPoints?.toString ?? "0"
                self?.lbUsedPoint.text = item.TotalSpentPoints?.toString ?? "0"
                self?.lbCurrentPoint.text = item.TotalPoint?.toString ?? "0"
                self?.lbRemainPoint.remainPoint(point: item.AlmostExpiredPoint ?? 0, expiredOn: item.AlmostExpiredPointDate ?? "", cTop: nil)
            }).disposed(by: disposeBag)
    }

    @IBAction func donateTapped(_ sender: Any) {
        let donateVC = DonateVoucherViewController()
        donateVC.hidesBottomBarWhenPushed = true
        donateVC.type = .point
        self.navigationController?.pushViewController(donateVC, animated: true)
    }
    
}

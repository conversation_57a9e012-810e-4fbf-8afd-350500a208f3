<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="21701" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_0" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="21679"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Oswald-Bold.ttf">
            <string>Oswald-Bold</string>
        </array>
        <array key="Oswald-Regular.ttf">
            <string>Oswald-Regular</string>
        </array>
        <array key="SourceSansPro-Bold.ttf">
            <string>SourceSansPro-Bold</string>
        </array>
        <array key="SourceSansPro-Regular.ttf">
            <string>SourceSansPro-Regular</string>
        </array>
        <array key="SourceSansPro-SemiBold.ttf">
            <string>SourceSansPro-SemiBold</string>
        </array>
    </customFonts>
    <scenes>
        <!--Member Card View Controller-->
        <scene sceneID="Kcw-ca-Ws6">
            <objects>
                <viewController storyboardIdentifier="MemberCardViewController" id="cwP-XI-srs" customClass="MemberCardViewController" customModule="Beta_Cinemas" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Dxn-dJ-vGY">
                        <rect key="frame" x="0.0" y="0.0" width="320" height="568"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="U5q-9H-ZMs">
                                <rect key="frame" x="0.0" y="20" width="320" height="548"/>
                                <color key="backgroundColor" red="0.95294**********18" green="0.95294**********18" blue="0.95294**********18" alpha="1" colorSpace="calibratedRGB"/>
                                <view key="tableHeaderView" contentMode="scaleToFill" id="TCD-g0-sKD">
                                    <rect key="frame" x="0.0" y="0.0" width="320" height="64"/>
                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                    <subviews>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="8FM-yY-Occ" customClass="RoundView" customModule="Beta_Cinemas" customModuleProvider="target">
                                            <rect key="frame" x="8" y="20" width="304" height="44"/>
                                            <subviews>
                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="vKm-Cc-W0q">
                                                    <rect key="frame" x="0.0" y="0.0" width="83.5" height="44"/>
                                                    <subviews>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Tên thẻ" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="idk-wW-sLG" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                                            <rect key="frame" x="8" y="10" width="67.5" height="24"/>
                                                            <fontDescription key="fontDescription" name="SourceSansPro-Bold" family="Source Sans Pro" pointSize="16"/>
                                                            <color key="textColor" red="0.28627450980392155" green="0.29803921568627451" blue="0.38431372549019605" alpha="1" colorSpace="calibratedRGB"/>
                                                            <nil key="highlightedColor"/>
                                                            <userDefinedRuntimeAttributes>
                                                                <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="MemberCard.CardName"/>
                                                            </userDefinedRuntimeAttributes>
                                                        </label>
                                                    </subviews>
                                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <constraints>
                                                        <constraint firstItem="idk-wW-sLG" firstAttribute="leading" secondItem="vKm-Cc-W0q" secondAttribute="leading" constant="8" id="64I-MG-k7j"/>
                                                        <constraint firstAttribute="trailing" secondItem="idk-wW-sLG" secondAttribute="trailing" constant="8" id="7zt-Cx-rxN"/>
                                                        <constraint firstItem="idk-wW-sLG" firstAttribute="top" secondItem="vKm-Cc-W0q" secondAttribute="top" constant="10" id="kaz-dT-Y5C"/>
                                                        <constraint firstAttribute="bottom" secondItem="idk-wW-sLG" secondAttribute="bottom" constant="10" id="ppo-4s-fXi"/>
                                                    </constraints>
                                                </view>
                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="46J-DK-wr8">
                                                    <rect key="frame" x="83.5" y="0.0" width="137" height="44"/>
                                                    <subviews>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Số thẻ" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="3dx-Jb-616" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                                            <rect key="frame" x="8" y="10" width="121" height="24"/>
                                                            <fontDescription key="fontDescription" name="SourceSansPro-Bold" family="Source Sans Pro" pointSize="16"/>
                                                            <color key="textColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="calibratedRGB"/>
                                                            <nil key="highlightedColor"/>
                                                            <userDefinedRuntimeAttributes>
                                                                <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="MemberCard.CardNumber"/>
                                                            </userDefinedRuntimeAttributes>
                                                        </label>
                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="U8J-OU-gSA">
                                                            <rect key="frame" x="0.0" y="0.0" width="1" height="44"/>
                                                            <color key="backgroundColor" red="0.59215686270000001" green="0.59215686270000001" blue="0.59215686270000001" alpha="1" colorSpace="calibratedRGB"/>
                                                            <constraints>
                                                                <constraint firstAttribute="width" constant="1" id="tGm-Nh-KgY"/>
                                                            </constraints>
                                                        </view>
                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="xTP-c0-2E9">
                                                            <rect key="frame" x="136" y="0.0" width="1" height="44"/>
                                                            <color key="backgroundColor" red="0.59215686270000001" green="0.59215686270000001" blue="0.59215686270000001" alpha="1" colorSpace="calibratedRGB"/>
                                                            <constraints>
                                                                <constraint firstAttribute="width" constant="1" id="Gee-Ax-tEl"/>
                                                            </constraints>
                                                        </view>
                                                    </subviews>
                                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <constraints>
                                                        <constraint firstAttribute="trailing" secondItem="xTP-c0-2E9" secondAttribute="trailing" id="5AU-gm-gSk"/>
                                                        <constraint firstItem="3dx-Jb-616" firstAttribute="top" secondItem="46J-DK-wr8" secondAttribute="top" constant="10" id="5Qv-We-TOq"/>
                                                        <constraint firstItem="U8J-OU-gSA" firstAttribute="leading" secondItem="46J-DK-wr8" secondAttribute="leading" id="HB3-20-f0a"/>
                                                        <constraint firstAttribute="bottom" secondItem="3dx-Jb-616" secondAttribute="bottom" constant="10" id="PC3-PW-Gx6"/>
                                                        <constraint firstItem="3dx-Jb-616" firstAttribute="leading" secondItem="46J-DK-wr8" secondAttribute="leading" constant="8" id="TYq-51-a8o"/>
                                                        <constraint firstAttribute="bottom" secondItem="xTP-c0-2E9" secondAttribute="bottom" id="WlE-qN-yEO"/>
                                                        <constraint firstAttribute="trailing" secondItem="3dx-Jb-616" secondAttribute="trailing" constant="8" id="X12-Bd-H8K"/>
                                                        <constraint firstItem="xTP-c0-2E9" firstAttribute="top" secondItem="46J-DK-wr8" secondAttribute="top" id="qOV-um-wkc"/>
                                                        <constraint firstAttribute="bottom" secondItem="U8J-OU-gSA" secondAttribute="bottom" id="sOZ-bC-2zy"/>
                                                        <constraint firstItem="U8J-OU-gSA" firstAttribute="top" secondItem="46J-DK-wr8" secondAttribute="top" id="un4-Dz-xen"/>
                                                    </constraints>
                                                </view>
                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="HHW-aw-D1y">
                                                    <rect key="frame" x="220.5" y="0.0" width="83.5" height="44"/>
                                                    <subviews>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Đăng ký" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="vbt-uk-BAZ" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                                            <rect key="frame" x="8" y="10" width="67.5" height="24"/>
                                                            <fontDescription key="fontDescription" name="SourceSansPro-Bold" family="Source Sans Pro" pointSize="16"/>
                                                            <color key="textColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="calibratedRGB"/>
                                                            <nil key="highlightedColor"/>
                                                            <userDefinedRuntimeAttributes>
                                                                <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="MemberCard.CardDate"/>
                                                            </userDefinedRuntimeAttributes>
                                                        </label>
                                                    </subviews>
                                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <constraints>
                                                        <constraint firstItem="vbt-uk-BAZ" firstAttribute="leading" secondItem="HHW-aw-D1y" secondAttribute="leading" constant="8" id="DFX-88-0i6"/>
                                                        <constraint firstAttribute="bottom" secondItem="vbt-uk-BAZ" secondAttribute="bottom" constant="10" id="SNE-bg-9cC"/>
                                                        <constraint firstAttribute="trailing" secondItem="vbt-uk-BAZ" secondAttribute="trailing" constant="8" id="sav-4v-2p7"/>
                                                        <constraint firstItem="vbt-uk-BAZ" firstAttribute="top" secondItem="HHW-aw-D1y" secondAttribute="top" constant="10" id="wod-kp-1xn"/>
                                                    </constraints>
                                                </view>
                                            </subviews>
                                            <color key="backgroundColor" red="0.9137254901960784" green="0.9137254901960784" blue="0.9137254901960784" alpha="1" colorSpace="calibratedRGB"/>
                                            <constraints>
                                                <constraint firstAttribute="bottom" secondItem="HHW-aw-D1y" secondAttribute="bottom" id="10f-VH-1Bd"/>
                                                <constraint firstItem="HHW-aw-D1y" firstAttribute="leading" secondItem="46J-DK-wr8" secondAttribute="trailing" id="5Rf-oF-mJh"/>
                                                <constraint firstItem="HHW-aw-D1y" firstAttribute="top" secondItem="8FM-yY-Occ" secondAttribute="top" id="Fh9-lE-5Ab"/>
                                                <constraint firstItem="vKm-Cc-W0q" firstAttribute="leading" secondItem="8FM-yY-Occ" secondAttribute="leading" id="G5m-RB-ZWA"/>
                                                <constraint firstAttribute="bottom" secondItem="46J-DK-wr8" secondAttribute="bottom" id="KU4-NS-cV5"/>
                                                <constraint firstAttribute="trailing" secondItem="HHW-aw-D1y" secondAttribute="trailing" id="NAe-UW-69z"/>
                                                <constraint firstItem="46J-DK-wr8" firstAttribute="top" secondItem="8FM-yY-Occ" secondAttribute="top" id="NTS-bS-bIJ"/>
                                                <constraint firstItem="HHW-aw-D1y" firstAttribute="width" secondItem="vKm-Cc-W0q" secondAttribute="width" id="Pl8-GD-u8G"/>
                                                <constraint firstItem="46J-DK-wr8" firstAttribute="leading" secondItem="vKm-Cc-W0q" secondAttribute="trailing" id="dK4-Ej-2mh"/>
                                                <constraint firstItem="46J-DK-wr8" firstAttribute="width" secondItem="vKm-Cc-W0q" secondAttribute="width" multiplier="1.65" id="g8N-8s-tja"/>
                                                <constraint firstItem="vKm-Cc-W0q" firstAttribute="top" secondItem="8FM-yY-Occ" secondAttribute="top" id="kbJ-fQ-OP5"/>
                                                <constraint firstAttribute="bottom" secondItem="vKm-Cc-W0q" secondAttribute="bottom" id="vEG-5g-V0A"/>
                                            </constraints>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                                    <color key="value" red="0.59215686274509804" green="0.59215686274509804" blue="0.59215686274509804" alpha="1" colorSpace="calibratedRGB"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                                    <real key="value" value="1"/>
                                                </userDefinedRuntimeAttribute>
                                            </userDefinedRuntimeAttributes>
                                        </view>
                                    </subviews>
                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <constraints>
                                        <constraint firstItem="8FM-yY-Occ" firstAttribute="leading" secondItem="TCD-g0-sKD" secondAttribute="leading" constant="8" id="2kY-Kq-cV6"/>
                                        <constraint firstItem="8FM-yY-Occ" firstAttribute="top" secondItem="TCD-g0-sKD" secondAttribute="top" constant="20" id="8oz-Ju-t8i"/>
                                        <constraint firstAttribute="bottom" secondItem="8FM-yY-Occ" secondAttribute="bottom" id="G5r-KI-BzT"/>
                                        <constraint firstAttribute="trailing" secondItem="8FM-yY-Occ" secondAttribute="trailing" constant="8" id="eGj-mf-Apx"/>
                                    </constraints>
                                </view>
                                <prototypes>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" reuseIdentifier="MemberCardCell" rowHeight="167" id="Zd5-u3-KBv" customClass="MemberCardCell" customModule="Beta_Cinemas" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="114" width="320" height="167"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="Zd5-u3-KBv" id="tlN-vT-2dW">
                                            <rect key="frame" x="0.0" y="0.0" width="320" height="167"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="seA-54-0PO">
                                                    <rect key="frame" x="0.0" y="0.0" width="320" height="167"/>
                                                    <subviews>
                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Fd0-hf-aUW">
                                                            <rect key="frame" x="8" y="0.0" width="304" height="167"/>
                                                            <subviews>
                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="2oP-6O-lTb">
                                                                    <rect key="frame" x="0.0" y="0.0" width="83.5" height="167"/>
                                                                    <subviews>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="VVIP Card" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="4" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Zc6-q5-mTH">
                                                                            <rect key="frame" x="8" y="65" width="67.5" height="37.5"/>
                                                                            <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                                            <color key="textColor" red="0.0**********" green="0.34901960780000002" blue="0.61568627450000002" alpha="1" colorSpace="calibratedRGB"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="252" text="(Đang dùng)" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="kiA-FK-y69" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                                                            <rect key="frame" x="8" y="102.5" width="67.5" height="14"/>
                                                                            <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="12"/>
                                                                            <color key="textColor" red="0.**********2352941" green="0.12156862745098039" blue="0.15686274509803921" alpha="1" colorSpace="calibratedRGB"/>
                                                                            <nil key="highlightedColor"/>
                                                                            <userDefinedRuntimeAttributes>
                                                                                <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="MemberCard.UsingCard"/>
                                                                            </userDefinedRuntimeAttributes>
                                                                        </label>
                                                                    </subviews>
                                                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                    <constraints>
                                                                        <constraint firstItem="kiA-FK-y69" firstAttribute="leading" secondItem="2oP-6O-lTb" secondAttribute="leading" constant="8" id="027-eB-TUb"/>
                                                                        <constraint firstItem="Zc6-q5-mTH" firstAttribute="leading" secondItem="2oP-6O-lTb" secondAttribute="leading" constant="8" id="41w-X8-3Zx"/>
                                                                        <constraint firstItem="Zc6-q5-mTH" firstAttribute="centerY" secondItem="2oP-6O-lTb" secondAttribute="centerY" id="5dI-my-ud3"/>
                                                                        <constraint firstAttribute="trailing" secondItem="Zc6-q5-mTH" secondAttribute="trailing" constant="8" id="Dch-8n-yoB"/>
                                                                        <constraint firstAttribute="bottom" relation="greaterThanOrEqual" secondItem="kiA-FK-y69" secondAttribute="bottom" priority="999" constant="6" id="Mdf-kA-kMo"/>
                                                                        <constraint firstAttribute="trailing" secondItem="kiA-FK-y69" secondAttribute="trailing" constant="8" id="NBT-KD-PO2"/>
                                                                        <constraint firstItem="kiA-FK-y69" firstAttribute="top" secondItem="Zc6-q5-mTH" secondAttribute="bottom" id="Zgz-2D-YLM"/>
                                                                        <constraint firstItem="Zc6-q5-mTH" firstAttribute="top" relation="greaterThanOrEqual" secondItem="2oP-6O-lTb" secondAttribute="top" constant="20" id="dcT-hO-BCW"/>
                                                                    </constraints>
                                                                </view>
                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="rEs-Qu-nEo">
                                                                    <rect key="frame" x="83.5" y="0.0" width="137" height="167"/>
                                                                    <subviews>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="9041-7820-0001-4601" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="N4M-6T-FOv">
                                                                            <rect key="frame" x="8" y="20" width="121" height="127"/>
                                                                            <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                                            <color key="textColor" red="0.0**********2352941" green="0.34901960784313724" blue="0.61568627450980395" alpha="1" colorSpace="calibratedRGB"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="vuf-Eg-aMy">
                                                                            <rect key="frame" x="0.0" y="0.0" width="1" height="167"/>
                                                                            <color key="backgroundColor" red="0.59215686270000001" green="0.59215686270000001" blue="0.59215686270000001" alpha="1" colorSpace="calibratedRGB"/>
                                                                            <constraints>
                                                                                <constraint firstAttribute="width" constant="1" id="ucn-OH-sYL"/>
                                                                            </constraints>
                                                                        </view>
                                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="jC2-Et-8fA">
                                                                            <rect key="frame" x="136" y="0.0" width="1" height="167"/>
                                                                            <color key="backgroundColor" red="0.59215686270000001" green="0.59215686270000001" blue="0.59215686270000001" alpha="1" colorSpace="calibratedRGB"/>
                                                                            <constraints>
                                                                                <constraint firstAttribute="width" constant="1" id="ZMf-Ga-lLE"/>
                                                                            </constraints>
                                                                        </view>
                                                                    </subviews>
                                                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                    <constraints>
                                                                        <constraint firstItem="vuf-Eg-aMy" firstAttribute="leading" secondItem="rEs-Qu-nEo" secondAttribute="leading" id="4jD-HF-go4"/>
                                                                        <constraint firstItem="vuf-Eg-aMy" firstAttribute="top" secondItem="rEs-Qu-nEo" secondAttribute="top" id="BVr-ta-pP5"/>
                                                                        <constraint firstAttribute="trailing" secondItem="jC2-Et-8fA" secondAttribute="trailing" id="E32-hi-2c0"/>
                                                                        <constraint firstAttribute="bottom" secondItem="vuf-Eg-aMy" secondAttribute="bottom" id="Irz-yD-05m"/>
                                                                        <constraint firstItem="N4M-6T-FOv" firstAttribute="top" secondItem="rEs-Qu-nEo" secondAttribute="top" constant="20" id="LDJ-3L-3LE"/>
                                                                        <constraint firstItem="N4M-6T-FOv" firstAttribute="leading" secondItem="rEs-Qu-nEo" secondAttribute="leading" constant="8" id="OF9-jF-HPw"/>
                                                                        <constraint firstAttribute="trailing" secondItem="N4M-6T-FOv" secondAttribute="trailing" constant="8" id="T9n-kV-FGn"/>
                                                                        <constraint firstItem="jC2-Et-8fA" firstAttribute="top" secondItem="rEs-Qu-nEo" secondAttribute="top" id="XOX-Uw-070"/>
                                                                        <constraint firstAttribute="bottom" secondItem="jC2-Et-8fA" secondAttribute="bottom" id="igT-Kr-sDq"/>
                                                                        <constraint firstAttribute="bottom" secondItem="N4M-6T-FOv" secondAttribute="bottom" constant="20" id="rCK-zi-lzG"/>
                                                                    </constraints>
                                                                </view>
                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="xuH-dt-Pmz">
                                                                    <rect key="frame" x="220.5" y="0.0" width="83.5" height="167"/>
                                                                    <subviews>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="20/01/2018" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="4xo-gY-F2p">
                                                                            <rect key="frame" x="8" y="20" width="67.5" height="127"/>
                                                                            <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                                            <color key="textColor" red="0.0**********" green="0.34901960780000002" blue="0.61568627450000002" alpha="1" colorSpace="calibratedRGB"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                    </subviews>
                                                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                    <constraints>
                                                                        <constraint firstItem="4xo-gY-F2p" firstAttribute="leading" secondItem="xuH-dt-Pmz" secondAttribute="leading" constant="8" id="3fA-PK-Q4d"/>
                                                                        <constraint firstAttribute="bottom" secondItem="4xo-gY-F2p" secondAttribute="bottom" constant="20" id="6Aq-29-rtj"/>
                                                                        <constraint firstAttribute="trailing" secondItem="4xo-gY-F2p" secondAttribute="trailing" constant="8" id="snD-XF-BCR"/>
                                                                        <constraint firstItem="4xo-gY-F2p" firstAttribute="top" secondItem="xuH-dt-Pmz" secondAttribute="top" constant="20" id="yLT-Wg-Fvh"/>
                                                                    </constraints>
                                                                </view>
                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Dbi-AH-H2u" userLabel="lineView">
                                                                    <rect key="frame" x="0.0" y="0.0" width="1" height="167"/>
                                                                    <color key="backgroundColor" red="0.59215686270000001" green="0.59215686270000001" blue="0.59215686270000001" alpha="1" colorSpace="calibratedRGB"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="width" constant="1" id="5lB-QL-cxg"/>
                                                                    </constraints>
                                                                </view>
                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="oBQ-dp-43Q" userLabel="lineView">
                                                                    <rect key="frame" x="303" y="0.0" width="1" height="167"/>
                                                                    <color key="backgroundColor" red="0.59215686270000001" green="0.59215686270000001" blue="0.59215686270000001" alpha="1" colorSpace="calibratedRGB"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="width" constant="1" id="jht-Jx-b0R"/>
                                                                    </constraints>
                                                                </view>
                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Zqc-IY-U5k" userLabel="bottomView">
                                                                    <rect key="frame" x="0.0" y="166" width="304" height="1"/>
                                                                    <color key="backgroundColor" red="0.59215686270000001" green="0.59215686270000001" blue="0.59215686270000001" alpha="1" colorSpace="calibratedRGB"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="height" constant="1" id="C34-2H-C0d"/>
                                                                    </constraints>
                                                                </view>
                                                            </subviews>
                                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <constraints>
                                                                <constraint firstItem="2oP-6O-lTb" firstAttribute="top" secondItem="Fd0-hf-aUW" secondAttribute="top" id="7NJ-yd-RkV"/>
                                                                <constraint firstItem="Dbi-AH-H2u" firstAttribute="leading" secondItem="Fd0-hf-aUW" secondAttribute="leading" id="7Oz-cm-97I"/>
                                                                <constraint firstAttribute="trailing" secondItem="oBQ-dp-43Q" secondAttribute="trailing" id="Ban-T9-wVR"/>
                                                                <constraint firstAttribute="bottom" secondItem="oBQ-dp-43Q" secondAttribute="bottom" id="FgT-cl-wCC"/>
                                                                <constraint firstItem="Zqc-IY-U5k" firstAttribute="leading" secondItem="Fd0-hf-aUW" secondAttribute="leading" id="GJh-9P-iS4"/>
                                                                <constraint firstItem="xuH-dt-Pmz" firstAttribute="top" secondItem="Fd0-hf-aUW" secondAttribute="top" id="I1S-Os-tjp"/>
                                                                <constraint firstAttribute="bottom" secondItem="2oP-6O-lTb" secondAttribute="bottom" id="I90-TD-DQE"/>
                                                                <constraint firstAttribute="trailing" secondItem="Zqc-IY-U5k" secondAttribute="trailing" id="INE-YL-hci"/>
                                                                <constraint firstItem="oBQ-dp-43Q" firstAttribute="top" secondItem="Fd0-hf-aUW" secondAttribute="top" id="JNV-9X-mar"/>
                                                                <constraint firstItem="xuH-dt-Pmz" firstAttribute="width" secondItem="2oP-6O-lTb" secondAttribute="width" id="LOR-Qn-p0v"/>
                                                                <constraint firstItem="Dbi-AH-H2u" firstAttribute="top" secondItem="Fd0-hf-aUW" secondAttribute="top" id="S5L-sE-5PB"/>
                                                                <constraint firstItem="2oP-6O-lTb" firstAttribute="leading" secondItem="Fd0-hf-aUW" secondAttribute="leading" id="Z4K-Zl-xEP"/>
                                                                <constraint firstItem="rEs-Qu-nEo" firstAttribute="top" secondItem="Fd0-hf-aUW" secondAttribute="top" id="a86-uN-2Xe"/>
                                                                <constraint firstItem="rEs-Qu-nEo" firstAttribute="leading" secondItem="2oP-6O-lTb" secondAttribute="trailing" id="d4Y-32-sm4"/>
                                                                <constraint firstAttribute="bottom" secondItem="rEs-Qu-nEo" secondAttribute="bottom" id="fAP-SR-UrX"/>
                                                                <constraint firstAttribute="bottom" secondItem="Dbi-AH-H2u" secondAttribute="bottom" id="gIh-1K-lyD"/>
                                                                <constraint firstItem="xuH-dt-Pmz" firstAttribute="leading" secondItem="rEs-Qu-nEo" secondAttribute="trailing" id="pbj-oM-teU"/>
                                                                <constraint firstAttribute="bottom" secondItem="xuH-dt-Pmz" secondAttribute="bottom" id="pji-UN-WLu"/>
                                                                <constraint firstItem="rEs-Qu-nEo" firstAttribute="width" secondItem="2oP-6O-lTb" secondAttribute="width" multiplier="1.65" id="uBK-dm-ljS"/>
                                                                <constraint firstAttribute="trailing" secondItem="xuH-dt-Pmz" secondAttribute="trailing" id="x3V-OA-dWv"/>
                                                                <constraint firstAttribute="bottom" secondItem="Zqc-IY-U5k" secondAttribute="bottom" id="y4T-SV-6Jm"/>
                                                            </constraints>
                                                        </view>
                                                    </subviews>
                                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <constraints>
                                                        <constraint firstItem="Fd0-hf-aUW" firstAttribute="leading" secondItem="seA-54-0PO" secondAttribute="leading" constant="8" id="cXe-Ay-hIf"/>
                                                        <constraint firstAttribute="trailing" secondItem="Fd0-hf-aUW" secondAttribute="trailing" constant="8" id="lVm-Qc-788"/>
                                                        <constraint firstItem="Fd0-hf-aUW" firstAttribute="top" secondItem="seA-54-0PO" secondAttribute="top" id="u9T-uA-sQu"/>
                                                        <constraint firstAttribute="bottom" secondItem="Fd0-hf-aUW" secondAttribute="bottom" id="yfb-OO-BHe"/>
                                                    </constraints>
                                                </view>
                                            </subviews>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstItem="seA-54-0PO" firstAttribute="leading" secondItem="tlN-vT-2dW" secondAttribute="leading" id="1j3-Wu-men"/>
                                                <constraint firstItem="seA-54-0PO" firstAttribute="top" secondItem="tlN-vT-2dW" secondAttribute="top" id="EsZ-LC-7Ot"/>
                                                <constraint firstAttribute="bottom" secondItem="seA-54-0PO" secondAttribute="bottom" id="Ldy-yO-HCR"/>
                                                <constraint firstAttribute="trailing" secondItem="seA-54-0PO" secondAttribute="trailing" id="zsh-eE-EOV"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <connections>
                                            <outlet property="lbCardName" destination="Zc6-q5-mTH" id="kyS-qr-YIM"/>
                                            <outlet property="lbCardNumber" destination="N4M-6T-FOv" id="oCM-Ex-r4k"/>
                                            <outlet property="lbRegisterDate" destination="4xo-gY-F2p" id="B97-90-AUP"/>
                                            <outlet property="lbStatus" destination="kiA-FK-y69" id="lAZ-TO-c44"/>
                                        </connections>
                                    </tableViewCell>
                                </prototypes>
                                <connections>
                                    <outlet property="delegate" destination="cwP-XI-srs" id="5YU-a3-0hX"/>
                                </connections>
                            </tableView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="7mD-1K-BMM"/>
                        <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstItem="U5q-9H-ZMs" firstAttribute="leading" secondItem="7mD-1K-BMM" secondAttribute="leading" id="FYn-WO-BEX"/>
                            <constraint firstItem="7mD-1K-BMM" firstAttribute="bottom" secondItem="U5q-9H-ZMs" secondAttribute="bottom" id="H2O-8Q-kV0"/>
                            <constraint firstItem="7mD-1K-BMM" firstAttribute="trailing" secondItem="U5q-9H-ZMs" secondAttribute="trailing" id="XEe-uf-7wJ"/>
                            <constraint firstItem="U5q-9H-ZMs" firstAttribute="top" secondItem="7mD-1K-BMM" secondAttribute="top" id="aKA-AV-Lmd"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="headerView" destination="TCD-g0-sKD" id="CDY-SX-k7f"/>
                        <outlet property="tableView" destination="U5q-9H-ZMs" id="4fP-SX-h71"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="iZY-y9-gbg" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-530.39999999999998" y="-68.815592203898049"/>
        </scene>
        <!--Confirm Pass View Controller-->
        <scene sceneID="gOj-Wg-ccW">
            <objects>
                <viewController storyboardIdentifier="ConfirmPassViewController" id="OPs-mp-IXh" customClass="ConfirmPassViewController" customModule="Beta_Cinemas" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="bub-TD-pzg">
                        <rect key="frame" x="0.0" y="0.0" width="320" height="568"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Để bảo mật, vui lòng nhập mật khẩu của bạn" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="pFx-OT-cYx" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                <rect key="frame" x="35" y="40" width="250" height="37.5"/>
                                <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                <color key="textColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <nil key="highlightedColor"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Confirm.GuideText"/>
                                </userDefinedRuntimeAttributes>
                            </label>
                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" borderStyle="roundedRect" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="Csx-1a-FoR" customClass="InputTextField" customModule="Beta_Cinemas" customModuleProvider="target">
                                <rect key="frame" x="20" y="91.5" width="280" height="50"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="50" id="itc-aO-CI3"/>
                                </constraints>
                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                <textInputTraits key="textInputTraits" returnKeyType="go" secureTextEntry="YES"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="image" keyPath="leftIcon" value="ic_password"/>
                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Confirm.Password"/>
                                </userDefinedRuntimeAttributes>
                                <connections>
                                    <outlet property="delegate" destination="OPs-mp-IXh" id="ZhW-gX-kGL"/>
                                </connections>
                            </textField>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="suu-Iw-wN9" customClass="GradientButton" customModule="Beta_Cinemas" customModuleProvider="target">
                                <rect key="frame" x="20" y="165.5" width="280" height="56"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="56" id="EjM-Cc-i5I"/>
                                </constraints>
                                <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="20"/>
                                <state key="normal" title="CẬP NHẬT"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="4"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="color" keyPath="secondColor">
                                        <color key="value" red="0.99215686270000003" green="0.48627450979999998" blue="0.0078431372550000003" alpha="0.98999999999999999" colorSpace="custom" customColorSpace="sRGB"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="color" keyPath="firstColor">
                                        <color key="value" red="0.99215686270000003" green="0.15686274510000001" blue="0.0078431372550000003" alpha="0.97999999999999998" colorSpace="custom" customColorSpace="sRGB"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="shadowOpacity">
                                        <real key="value" value="0.20000000000000001"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="color" keyPath="shadowColor">
                                        <color key="value" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="point" keyPath="shadowOffset">
                                        <point key="value" x="0.0" y="6"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="shadowRadius">
                                        <real key="value" value="4"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Bt.Confirm"/>
                                </userDefinedRuntimeAttributes>
                                <connections>
                                    <action selector="confirmBtPressed:" destination="OPs-mp-IXh" eventType="touchUpInside" id="Bhb-XN-CeY"/>
                                </connections>
                            </button>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="c98-va-uLk"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="suu-Iw-wN9" firstAttribute="top" secondItem="Csx-1a-FoR" secondAttribute="bottom" constant="24" id="6KI-eg-aI7"/>
                            <constraint firstItem="c98-va-uLk" firstAttribute="trailing" secondItem="suu-Iw-wN9" secondAttribute="trailing" constant="20" id="ADb-Z6-uea"/>
                            <constraint firstItem="pFx-OT-cYx" firstAttribute="leading" secondItem="c98-va-uLk" secondAttribute="leading" constant="35" id="C13-JJ-qfv"/>
                            <constraint firstItem="suu-Iw-wN9" firstAttribute="leading" secondItem="c98-va-uLk" secondAttribute="leading" constant="20" id="Ejr-N8-XdG"/>
                            <constraint firstItem="pFx-OT-cYx" firstAttribute="top" secondItem="c98-va-uLk" secondAttribute="top" constant="20" id="QTf-ft-p0o"/>
                            <constraint firstItem="Csx-1a-FoR" firstAttribute="leading" secondItem="c98-va-uLk" secondAttribute="leading" constant="20" id="Xs4-t0-nHu"/>
                            <constraint firstItem="Csx-1a-FoR" firstAttribute="top" secondItem="pFx-OT-cYx" secondAttribute="bottom" constant="14" id="aGy-Ad-RMk"/>
                            <constraint firstItem="c98-va-uLk" firstAttribute="trailing" secondItem="Csx-1a-FoR" secondAttribute="trailing" constant="20" id="aYD-qa-RGE"/>
                            <constraint firstItem="c98-va-uLk" firstAttribute="trailing" secondItem="pFx-OT-cYx" secondAttribute="trailing" constant="35" id="wgZ-6O-ihl"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="tfPassword" destination="Csx-1a-FoR" id="Uye-sI-2Uf"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="bEn-eQ-U1r" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-550" y="631"/>
        </scene>
        <!--Account Info View Controller-->
        <scene sceneID="gkc-by-dh9">
            <objects>
                <viewController storyboardIdentifier="AccountInfoViewController" id="L11-hR-pPO" customClass="AccountInfoViewController" customModule="Beta_Cinemas" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="P7q-b6-vTm">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="800"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="rRG-kC-7u9">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="800"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="18" translatesAutoresizingMaskIntoConstraints="NO" id="jC7-Ar-mBF">
                                        <rect key="frame" x="20" y="0.0" width="335" height="1081"/>
                                        <subviews>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Rbc-hP-cus">
                                                <rect key="frame" x="0.0" y="0.0" width="335" height="128"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="THÔNG TIN CƠ BẢN" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="GzD-E6-Lyj" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="22" width="335" height="106"/>
                                                        <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="20"/>
                                                        <color key="textColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                        <nil key="highlightedColor"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="AccountInfo.BasicInfo"/>
                                                        </userDefinedRuntimeAttributes>
                                                    </label>
                                                </subviews>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstItem="GzD-E6-Lyj" firstAttribute="top" secondItem="Rbc-hP-cus" secondAttribute="top" constant="22" id="HNP-wm-O78"/>
                                                    <constraint firstItem="GzD-E6-Lyj" firstAttribute="leading" secondItem="Rbc-hP-cus" secondAttribute="leading" id="Q9t-62-WnP"/>
                                                    <constraint firstAttribute="trailing" secondItem="GzD-E6-Lyj" secondAttribute="trailing" id="hbt-QK-WtQ"/>
                                                    <constraint firstAttribute="bottom" secondItem="GzD-E6-Lyj" secondAttribute="bottom" id="msc-wK-ziy"/>
                                                </constraints>
                                            </view>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="vMa-PP-F3R">
                                                <rect key="frame" x="0.0" y="146" width="335" height="128"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="EMAIL" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Rep-Gm-M11" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="0.0" width="47.5" height="19"/>
                                                        <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                        <color key="textColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="248" verticalHuggingPriority="248" text="<EMAIL>" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="3" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Xxi-KE-n2C" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                                        <rect key="frame" x="66.5" y="0.0" width="258.5" height="128"/>
                                                        <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                        <color key="textColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstItem="Xxi-KE-n2C" firstAttribute="top" secondItem="Rep-Gm-M11" secondAttribute="top" id="0ch-2Y-MFS"/>
                                                    <constraint firstItem="Xxi-KE-n2C" firstAttribute="top" secondItem="vMa-PP-F3R" secondAttribute="top" id="Kz3-sW-2eF"/>
                                                    <constraint firstItem="Xxi-KE-n2C" firstAttribute="leading" secondItem="Rep-Gm-M11" secondAttribute="trailing" constant="19" id="Q5Z-Uc-FrC"/>
                                                    <constraint firstItem="Rep-Gm-M11" firstAttribute="leading" secondItem="vMa-PP-F3R" secondAttribute="leading" id="UGf-CV-gue"/>
                                                    <constraint firstAttribute="trailing" secondItem="Xxi-KE-n2C" secondAttribute="trailing" constant="10" id="sgh-wD-wrT"/>
                                                    <constraint firstAttribute="bottom" secondItem="Xxi-KE-n2C" secondAttribute="bottom" id="uBg-cD-0s0"/>
                                                </constraints>
                                            </view>
                                            <textField hidden="YES" opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Họ tên" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="6he-8c-i0x" customClass="InputTextField" customModule="Beta_Cinemas" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="283" width="335" height="50"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="50" id="uXa-tj-0l2"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                <textInputTraits key="textInputTraits" autocapitalizationType="words" returnKeyType="next" textContentType="name"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="image" keyPath="leftIcon" value="ic_firstname"/>
                                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Register.FirstName"/>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <outlet property="delegate" destination="L11-hR-pPO" id="oFM-w2-wr9"/>
                                                </connections>
                                            </textField>
                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Họ tên" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="HRa-VS-nfp" customClass="InputTextField" customModule="Beta_Cinemas" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="292" width="335" height="50"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="50" id="XZc-cS-6wr"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                <textInputTraits key="textInputTraits" autocapitalizationType="words" returnKeyType="next" textContentType="name"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="image" keyPath="leftIcon" value="ic_name"/>
                                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Register.Name"/>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <outlet property="delegate" destination="L11-hR-pPO" id="tBn-iQ-Q8F"/>
                                                </connections>
                                            </textField>
                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Giới tính" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="Y0v-zI-Vqy" customClass="PickerTextField" customModule="Beta_Cinemas" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="360" width="335" height="50"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="50" id="Qrw-aQ-NwD"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                <textInputTraits key="textInputTraits" returnKeyType="next"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="image" keyPath="leftIcon" value="ic_sex"/>
                                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Register.Gender"/>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <outlet property="delegate" destination="L11-hR-pPO" id="Qsu-zO-Mpz"/>
                                                </connections>
                                            </textField>
                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Ngày sinh" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="wQ8-d6-i7q" customClass="DateTextField" customModule="Beta_Cinemas" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="428" width="335" height="50"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="50" id="uJa-HC-bI5"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                <textInputTraits key="textInputTraits" returnKeyType="next"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="image" keyPath="leftIcon" value="ic_birthday"/>
                                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Register.Birthday"/>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <outlet property="delegate" destination="L11-hR-pPO" id="JXk-Y8-ydw"/>
                                                </connections>
                                            </textField>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="zCH-th-NII">
                                                <rect key="frame" x="0.0" y="496" width="335" height="128"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="THÔNG TIN LIÊN HỆ" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="v3h-vS-3lS" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="4" width="335" height="124"/>
                                                        <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="20"/>
                                                        <color key="textColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                        <nil key="highlightedColor"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="AccountInfo.ContactInfo"/>
                                                        </userDefinedRuntimeAttributes>
                                                    </label>
                                                </subviews>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="trailing" secondItem="v3h-vS-3lS" secondAttribute="trailing" id="1cd-h7-fWV"/>
                                                    <constraint firstItem="v3h-vS-3lS" firstAttribute="top" secondItem="zCH-th-NII" secondAttribute="top" constant="4" id="aJA-uQ-UGP"/>
                                                    <constraint firstItem="v3h-vS-3lS" firstAttribute="leading" secondItem="zCH-th-NII" secondAttribute="leading" id="jrM-Mq-Iel"/>
                                                    <constraint firstAttribute="bottom" secondItem="v3h-vS-3lS" secondAttribute="bottom" id="ypt-oj-fgQ"/>
                                                </constraints>
                                            </view>
                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="CMND/ Hộ chiếu" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="JR4-HF-3bc" customClass="InputTextField" customModule="Beta_Cinemas" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="642" width="335" height="50"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="50" id="6f0-1p-6S4"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                <textInputTraits key="textInputTraits" keyboardType="numbersAndPunctuation" returnKeyType="next"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="image" keyPath="leftIcon" value="ic_cmnd"/>
                                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Register.CMND"/>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <outlet property="delegate" destination="L11-hR-pPO" id="Rxx-qs-jLH"/>
                                                </connections>
                                            </textField>
                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Số điện thoại" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="5Ll-4t-fzu" customClass="InputTextField" customModule="Beta_Cinemas" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="710" width="335" height="50"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="50" id="ElX-Sy-ShI"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                <textInputTraits key="textInputTraits" keyboardType="phonePad" returnKeyType="next" textContentType="tel"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="image" keyPath="leftIcon" value="ic_phone"/>
                                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Register.Phone"/>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <outlet property="delegate" destination="L11-hR-pPO" id="BTl-kd-pCS"/>
                                                </connections>
                                            </textField>
                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Tỉnh/ Thành phố" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="yrM-oU-2aa" customClass="PickerTextField" customModule="Beta_Cinemas" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="778" width="335" height="50"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="50" id="H6v-7A-638"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                <textInputTraits key="textInputTraits" returnKeyType="next" textContentType="address-level2"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="image" keyPath="leftIcon" value="ic_tower"/>
                                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Register.City"/>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <outlet property="delegate" destination="L11-hR-pPO" id="nni-u9-Yli"/>
                                                </connections>
                                            </textField>
                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Quận/ Huyện" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="qhb-tg-cyP" customClass="PickerTextField" customModule="Beta_Cinemas" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="846" width="335" height="50"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="50" id="u4i-Nk-dqZ"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                <textInputTraits key="textInputTraits" returnKeyType="next" textContentType="address-level1"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="image" keyPath="leftIcon" value="ic_home"/>
                                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Register.District"/>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <outlet property="delegate" destination="L11-hR-pPO" id="g1j-zk-NVX"/>
                                                </connections>
                                            </textField>
                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Địa chỉ liên hệ" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="dum-0L-heg" customClass="InputTextField" customModule="Beta_Cinemas" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="914" width="335" height="50"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="50" id="iln-T3-C58"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                <textInputTraits key="textInputTraits" returnKeyType="go" textContentType="street-address"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="image" keyPath="leftIcon" value="ic_address"/>
                                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Register.Address"/>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <outlet property="delegate" destination="L11-hR-pPO" id="D6X-VN-aT5"/>
                                                </connections>
                                            </textField>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="icE-3n-LG4" customClass="GradientButton" customModule="Beta_Cinemas" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="982" width="335" height="56"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="56" id="W45-wX-b3E"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="20"/>
                                                <state key="normal" title="ĐĂNG KÝ">
                                                    <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                </state>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="color" keyPath="shadowColor">
                                                        <color key="value" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="shadowOpacity">
                                                        <real key="value" value="0.20000000000000001"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="point" keyPath="shadowOffset">
                                                        <point key="value" x="0.0" y="6"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="shadowRadius">
                                                        <real key="value" value="14"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                        <real key="value" value="4"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Bt.Update"/>
                                                </userDefinedRuntimeAttributes>
                                                <connections>
                                                    <action selector="updateBtPressed:" destination="L11-hR-pPO" eventType="touchUpInside" id="yPN-Yh-Lwo"/>
                                                </connections>
                                            </button>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="HTr-ZC-4CE">
                                                <rect key="frame" x="0.0" y="1056" width="335" height="25"/>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="25" id="VdI-Bi-ypm"/>
                                                </constraints>
                                            </view>
                                        </subviews>
                                        <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="calibratedRGB"/>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="icE-3n-LG4" secondAttribute="trailing" id="8em-Pf-6ah"/>
                                            <constraint firstItem="icE-3n-LG4" firstAttribute="leading" secondItem="jC7-Ar-mBF" secondAttribute="leading" id="Z0d-2M-ygv"/>
                                        </constraints>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="calibratedRGB"/>
                                <constraints>
                                    <constraint firstItem="jC7-Ar-mBF" firstAttribute="top" secondItem="rRG-kC-7u9" secondAttribute="top" id="Jkx-Rx-Lzo"/>
                                    <constraint firstAttribute="trailing" secondItem="jC7-Ar-mBF" secondAttribute="trailing" constant="20" id="VnP-QG-GtV"/>
                                    <constraint firstItem="jC7-Ar-mBF" firstAttribute="leading" secondItem="rRG-kC-7u9" secondAttribute="leading" constant="20" id="YtQ-lF-tZV"/>
                                    <constraint firstAttribute="bottom" secondItem="jC7-Ar-mBF" secondAttribute="bottom" id="p7r-jo-zPT"/>
                                    <constraint firstItem="jC7-Ar-mBF" firstAttribute="width" secondItem="rRG-kC-7u9" secondAttribute="width" constant="-40" id="vdU-6V-Ufb"/>
                                </constraints>
                            </scrollView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="uGE-Ar-Tju"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="rRG-kC-7u9" firstAttribute="leading" secondItem="P7q-b6-vTm" secondAttribute="leading" id="PNu-LX-v5W"/>
                            <constraint firstItem="rRG-kC-7u9" firstAttribute="top" secondItem="P7q-b6-vTm" secondAttribute="top" id="bfB-r1-bXy"/>
                            <constraint firstItem="uGE-Ar-Tju" firstAttribute="trailing" secondItem="rRG-kC-7u9" secondAttribute="trailing" id="gKg-rm-SPg"/>
                            <constraint firstAttribute="bottom" secondItem="rRG-kC-7u9" secondAttribute="bottom" id="w83-7d-PeZ"/>
                        </constraints>
                    </view>
                    <size key="freeformSize" width="375" height="800"/>
                    <connections>
                        <outlet property="cmndTextField" destination="JR4-HF-3bc" id="Iet-KJ-ZSL"/>
                        <outlet property="lbEmail" destination="Xxi-KE-n2C" id="S3U-1G-BHG"/>
                        <outlet property="tfAddress" destination="dum-0L-heg" id="FNd-mu-v2Z"/>
                        <outlet property="tfBirthdate" destination="wQ8-d6-i7q" id="8AB-b5-UGG"/>
                        <outlet property="tfCity" destination="yrM-oU-2aa" id="2b9-LN-328"/>
                        <outlet property="tfDistrict" destination="qhb-tg-cyP" id="qWq-nX-qPr"/>
                        <outlet property="tfFirstName" destination="6he-8c-i0x" id="qY9-hJ-tQe"/>
                        <outlet property="tfGender" destination="Y0v-zI-Vqy" id="syH-ql-jgz"/>
                        <outlet property="tfName" destination="HRa-VS-nfp" id="XpU-2h-zW3"/>
                        <outlet property="tfPhoneNumber" destination="5Ll-4t-fzu" id="9Lv-ac-eWb"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="M0M-69-lPv" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="162" y="923"/>
        </scene>
        <!--Member View Controller-->
        <scene sceneID="d0p-Zh-oEN">
            <objects>
                <viewController storyboardIdentifier="MemberViewController" extendedLayoutIncludesOpaqueBars="YES" id="8t3-92-lHa" customClass="MemberViewController" customModule="Beta_Cinemas" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="64O-ck-v6N">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="800"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="Dws-Dr-Nl0">
                                <rect key="frame" x="0.0" y="20" width="375" height="780"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <view key="tableHeaderView" contentMode="scaleToFill" id="lzt-2Z-LcX">
                                    <rect key="frame" x="0.0" y="0.0" width="375" height="672"/>
                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                    <subviews>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="3Qb-3a-wSw">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="163"/>
                                            <subviews>
                                                <imageView userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="demo_avatar" translatesAutoresizingMaskIntoConstraints="NO" id="AmL-C0-mLW">
                                                    <rect key="frame" x="0.0" y="0.0" width="375" height="163"/>
                                                </imageView>
                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="gOp-t2-UIy" customClass="VisualEffectView" customModule="VisualEffectView">
                                                    <rect key="frame" x="0.0" y="0.0" width="375" height="163"/>
                                                    <color key="backgroundColor" red="0.0**********" green="0.34901960780000002" blue="0.61568627450000002" alpha="0.72999999999999998" colorSpace="custom" customColorSpace="sRGB"/>
                                                </view>
                                            </subviews>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstAttribute="trailing" secondItem="gOp-t2-UIy" secondAttribute="trailing" id="11h-wS-p7j"/>
                                                <constraint firstItem="gOp-t2-UIy" firstAttribute="leading" secondItem="3Qb-3a-wSw" secondAttribute="leading" id="5ev-xb-hIf"/>
                                                <constraint firstAttribute="trailing" secondItem="AmL-C0-mLW" secondAttribute="trailing" id="UXa-8d-sgM"/>
                                                <constraint firstItem="AmL-C0-mLW" firstAttribute="leading" secondItem="3Qb-3a-wSw" secondAttribute="leading" id="fQy-Wq-0yd"/>
                                                <constraint firstItem="gOp-t2-UIy" firstAttribute="top" secondItem="3Qb-3a-wSw" secondAttribute="top" id="fq0-qE-hrY"/>
                                                <constraint firstItem="AmL-C0-mLW" firstAttribute="top" secondItem="3Qb-3a-wSw" secondAttribute="top" id="jwQ-US-3Q4"/>
                                                <constraint firstAttribute="height" constant="163" id="oe2-Iy-bjF"/>
                                                <constraint firstAttribute="bottom" secondItem="gOp-t2-UIy" secondAttribute="bottom" id="ovo-Mj-9iL"/>
                                                <constraint firstAttribute="bottom" secondItem="AmL-C0-mLW" secondAttribute="bottom" id="uC1-BY-PIM"/>
                                            </constraints>
                                        </view>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="6Ce-6j-R0E" customClass="RoundView" customModule="Beta_Cinemas" customModuleProvider="target">
                                            <rect key="frame" x="8" y="149" width="359" height="515"/>
                                            <subviews>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="248" verticalCompressionResistancePriority="752" text="Vương Văn Trường" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="f9X-kF-BRA">
                                                    <rect key="frame" x="46" y="75" width="267" height="28"/>
                                                    <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="24"/>
                                                    <color key="textColor" red="0.**********2352941" green="0.12156862745098039" blue="0.15686274509803921" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="249" ambiguous="YES" text="Thẻ thành viên" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="EFW-8o-LFA" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                                    <rect key="frame" x="6" y="135" width="119" height="0.0"/>
                                                    <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                    <color key="textColor" red="0.28627450980392155" green="0.29803921568627451" blue="0.38431372549019605" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <nil key="highlightedColor"/>
                                                    <userDefinedRuntimeAttributes>
                                                        <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Member.MemberCard"/>
                                                    </userDefinedRuntimeAttributes>
                                                </label>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" ambiguous="YES" text="9000050001150323" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumScaleFactor="0.5" translatesAutoresizingMaskIntoConstraints="NO" id="tEB-1a-YXe">
                                                    <rect key="frame" x="135" y="135" width="214" height="0.0"/>
                                                    <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="24"/>
                                                    <color key="textColor" red="0.**********" green="0.**********" blue="0.15686274510000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="demo_barcode" translatesAutoresizingMaskIntoConstraints="NO" id="JT5-fd-F0m">
                                                    <rect key="frame" x="8" y="154.5" width="343" height="112"/>
                                                    <constraints>
                                                        <constraint firstAttribute="width" secondItem="JT5-fd-F0m" secondAttribute="height" multiplier="343:112" id="FtS-FU-OF3"/>
                                                    </constraints>
                                                </imageView>
                                                <view alpha="0.20000000000000001" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ydE-Sd-RLy">
                                                    <rect key="frame" x="0.0" y="284.5" width="359" height="1"/>
                                                    <color key="backgroundColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <constraints>
                                                        <constraint firstAttribute="height" constant="1" id="8lf-WA-5n4"/>
                                                    </constraints>
                                                </view>
                                                <view alpha="0.20000000298023224" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="nWr-xr-z0y">
                                                    <rect key="frame" x="0.0" y="350.5" width="359" height="1"/>
                                                    <color key="backgroundColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <constraints>
                                                        <constraint firstAttribute="height" constant="1" id="pXR-G0-rZ9"/>
                                                    </constraints>
                                                </view>
                                                <view alpha="0.20000000298023224" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="VKX-rD-kyP">
                                                    <rect key="frame" x="179" y="285.5" width="1" height="65"/>
                                                    <color key="backgroundColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <constraints>
                                                        <constraint firstAttribute="width" constant="1" id="u1Z-IR-lfH"/>
                                                    </constraints>
                                                </view>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="254" text="Tổng chi tiêu" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="WJx-t8-zph" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                                    <rect key="frame" x="0.0" y="297.5" width="179.5" height="0.0"/>
                                                    <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                    <color key="textColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <nil key="highlightedColor"/>
                                                    <userDefinedRuntimeAttributes>
                                                        <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Member.TotalSpent"/>
                                                    </userDefinedRuntimeAttributes>
                                                </label>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="253" text="Điểm thưởng" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="jfm-5o-nR6" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                                    <rect key="frame" x="179.5" y="297.5" width="179.5" height="19"/>
                                                    <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                    <color key="textColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <nil key="highlightedColor"/>
                                                    <userDefinedRuntimeAttributes>
                                                        <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Member.TotalPoint"/>
                                                    </userDefinedRuntimeAttributes>
                                                </label>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="254" verticalCompressionResistancePriority="751" text="150.000đ" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Hag-FQ-3xM">
                                                    <rect key="frame" x="8" y="305.5" width="163.5" height="33"/>
                                                    <fontDescription key="fontDescription" name="SourceSansPro-Bold" family="Source Sans Pro" pointSize="28"/>
                                                    <color key="textColor" red="0.0**********" green="0.34901960780000002" blue="0.61568627450000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="254" text="77" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="WoY-Ba-g9b">
                                                    <rect key="frame" x="187.5" y="324.5" width="163.5" height="33"/>
                                                    <fontDescription key="fontDescription" name="SourceSansPro-Bold" family="Source Sans Pro" pointSize="28"/>
                                                    <color key="textColor" red="0.0**********" green="0.34901960780000002" blue="0.61568627450000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_membercard" translatesAutoresizingMaskIntoConstraints="NO" id="xln-Q9-9Kb">
                                                    <rect key="frame" x="6" y="408" width="42" height="30"/>
                                                    <constraints>
                                                        <constraint firstAttribute="height" constant="30" id="Lj6-rH-3b2"/>
                                                        <constraint firstAttribute="width" constant="42" id="r5C-Hh-DfR"/>
                                                    </constraints>
                                                </imageView>
                                                <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_vip" translatesAutoresizingMaskIntoConstraints="NO" id="JOV-Vv-khe">
                                                    <rect key="frame" x="311" y="408" width="42" height="30"/>
                                                    <constraints>
                                                        <constraint firstAttribute="width" constant="42" id="DRc-St-KBk"/>
                                                        <constraint firstAttribute="height" constant="30" id="EOJ-O3-n2o"/>
                                                    </constraints>
                                                </imageView>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="255" verticalCompressionResistancePriority="752" text="Bạn cần tích luỹ thêm 2.750.000vnđ để nâng hạng VIP" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="LmS-EP-rRi">
                                                    <rect key="frame" x="67" y="408" width="225" height="33"/>
                                                    <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="14"/>
                                                    <color key="textColor" red="0.**********" green="0.**********" blue="0.15686274510000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <progressView opaque="NO" contentMode="scaleToFill" verticalHuggingPriority="750" progress="0.5" translatesAutoresizingMaskIntoConstraints="NO" id="HOZ-3K-gac">
                                                    <rect key="frame" x="16" y="466" width="327" height="6"/>
                                                    <constraints>
                                                        <constraint firstAttribute="height" constant="6" id="yNz-RA-PM6"/>
                                                    </constraints>
                                                    <color key="progressTintColor" red="0.99215686270000003" green="0.15686274510000001" blue="0.0078431372550000003" alpha="0.97999999999999998" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <color key="trackTintColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="calibratedRGB"/>
                                                </progressView>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="0" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Hgh-yq-ybS">
                                                    <rect key="frame" x="6" y="476" width="9" height="19"/>
                                                    <fontDescription key="fontDescription" name="SourceSansPro-Bold" family="Source Sans Pro" pointSize="16"/>
                                                    <color key="textColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" verticalCompressionResistancePriority="752" text="3.000.000" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="J1A-pr-4Mw">
                                                    <rect key="frame" x="281.5" y="476" width="71.5" height="19"/>
                                                    <fontDescription key="fontDescription" name="SourceSansPro-Bold" family="Source Sans Pro" pointSize="16"/>
                                                    <color key="textColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="255" verticalCompressionResistancePriority="752" text="Bạn cần tích luỹ thêm 2.750.000vnđ để nâng hạng VIP" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Kqv-wy-wTn">
                                                    <rect key="frame" x="8" y="371.5" width="343" height="16.5"/>
                                                    <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="14"/>
                                                    <color key="textColor" red="0.**********" green="0.**********" blue="0.15686274510000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                    <nil key="highlightedColor"/>
                                                </label>
                                            </subviews>
                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstItem="VKX-rD-kyP" firstAttribute="top" secondItem="ydE-Sd-RLy" secondAttribute="bottom" id="0wT-nE-JKU"/>
                                                <constraint firstItem="Kqv-wy-wTn" firstAttribute="leading" secondItem="6Ce-6j-R0E" secondAttribute="leading" constant="8" id="1vo-zB-3dl"/>
                                                <constraint firstItem="WoY-Ba-g9b" firstAttribute="centerX" secondItem="jfm-5o-nR6" secondAttribute="centerX" id="55R-jl-44j"/>
                                                <constraint firstItem="jfm-5o-nR6" firstAttribute="top" secondItem="ydE-Sd-RLy" secondAttribute="bottom" constant="12" id="6BP-S6-ONt"/>
                                                <constraint firstItem="Hgh-yq-ybS" firstAttribute="leading" secondItem="6Ce-6j-R0E" secondAttribute="leading" constant="6" id="79h-fM-D1T"/>
                                                <constraint firstItem="Hag-FQ-3xM" firstAttribute="leading" secondItem="6Ce-6j-R0E" secondAttribute="leading" constant="8" id="8gQ-Vh-9ZB"/>
                                                <constraint firstItem="JT5-fd-F0m" firstAttribute="leading" secondItem="6Ce-6j-R0E" secondAttribute="leading" constant="8" id="8jf-DU-Xj0"/>
                                                <constraint firstAttribute="trailing" secondItem="nWr-xr-z0y" secondAttribute="trailing" id="8zv-3g-YFe"/>
                                                <constraint firstItem="xln-Q9-9Kb" firstAttribute="top" secondItem="Kqv-wy-wTn" secondAttribute="bottom" constant="20" id="Czl-Oo-6nn"/>
                                                <constraint firstItem="nWr-xr-z0y" firstAttribute="leading" secondItem="6Ce-6j-R0E" secondAttribute="leading" id="Dis-9v-DLz"/>
                                                <constraint firstItem="tEB-1a-YXe" firstAttribute="leading" relation="lessThanOrEqual" secondItem="EFW-8o-LFA" secondAttribute="trailing" constant="10" id="Drw-If-C1E"/>
                                                <constraint firstItem="Hgh-yq-ybS" firstAttribute="top" secondItem="HOZ-3K-gac" secondAttribute="bottom" constant="4" id="FBd-qh-yP2"/>
                                                <constraint firstAttribute="trailing" secondItem="J1A-pr-4Mw" secondAttribute="trailing" constant="6" id="Fdw-ej-okc"/>
                                                <constraint firstItem="VKX-rD-kyP" firstAttribute="centerX" secondItem="6Ce-6j-R0E" secondAttribute="centerX" id="GWw-vb-xVU"/>
                                                <constraint firstAttribute="trailing" secondItem="JOV-Vv-khe" secondAttribute="trailing" constant="6" id="LIw-od-wqi"/>
                                                <constraint firstAttribute="trailing" secondItem="f9X-kF-BRA" secondAttribute="trailing" constant="46" id="MW1-k9-iy9"/>
                                                <constraint firstAttribute="trailing" secondItem="JT5-fd-F0m" secondAttribute="trailing" constant="8" id="PEo-WS-j2W"/>
                                                <constraint firstAttribute="trailing" secondItem="ydE-Sd-RLy" secondAttribute="trailing" id="QFc-Nn-JLS"/>
                                                <constraint firstItem="WJx-t8-zph" firstAttribute="top" secondItem="ydE-Sd-RLy" secondAttribute="bottom" constant="12" id="QJ6-b2-K7N"/>
                                                <constraint firstItem="ydE-Sd-RLy" firstAttribute="leading" secondItem="6Ce-6j-R0E" secondAttribute="leading" id="USa-jO-Asa"/>
                                                <constraint firstAttribute="trailing" secondItem="jfm-5o-nR6" secondAttribute="trailing" id="XEh-0z-t52"/>
                                                <constraint firstItem="EFW-8o-LFA" firstAttribute="leading" secondItem="6Ce-6j-R0E" secondAttribute="leading" constant="6" id="Y70-AR-grD"/>
                                                <constraint firstItem="Kqv-wy-wTn" firstAttribute="top" secondItem="nWr-xr-z0y" secondAttribute="bottom" constant="20" id="bis-G9-9gW"/>
                                                <constraint firstAttribute="trailing" secondItem="Kqv-wy-wTn" secondAttribute="trailing" constant="8" id="d31-Lq-Nzg"/>
                                                <constraint firstItem="ydE-Sd-RLy" firstAttribute="top" secondItem="JT5-fd-F0m" secondAttribute="bottom" constant="18" id="dPT-ba-K0k"/>
                                                <constraint firstItem="f9X-kF-BRA" firstAttribute="top" secondItem="6Ce-6j-R0E" secondAttribute="top" constant="75" id="eKa-VV-trB"/>
                                                <constraint firstItem="xln-Q9-9Kb" firstAttribute="leading" secondItem="6Ce-6j-R0E" secondAttribute="leading" constant="6" id="fI3-8s-YgG"/>
                                                <constraint firstItem="HOZ-3K-gac" firstAttribute="leading" secondItem="6Ce-6j-R0E" secondAttribute="leading" constant="16" id="fXR-FM-l4u"/>
                                                <constraint firstItem="Hag-FQ-3xM" firstAttribute="top" secondItem="WJx-t8-zph" secondAttribute="bottom" constant="8" id="gDm-sM-tbq"/>
                                                <constraint firstItem="tEB-1a-YXe" firstAttribute="centerY" secondItem="EFW-8o-LFA" secondAttribute="centerY" id="ikh-SL-qpI"/>
                                                <constraint firstItem="JT5-fd-F0m" firstAttribute="top" secondItem="tEB-1a-YXe" secondAttribute="bottom" constant="18" id="j6o-XP-3iZ"/>
                                                <constraint firstItem="JOV-Vv-khe" firstAttribute="leading" secondItem="LmS-EP-rRi" secondAttribute="trailing" constant="19" id="jz1-aM-x6c"/>
                                                <constraint firstItem="WoY-Ba-g9b" firstAttribute="top" secondItem="jfm-5o-nR6" secondAttribute="bottom" constant="8" id="k13-Kg-F20"/>
                                                <constraint firstItem="HOZ-3K-gac" firstAttribute="top" secondItem="LmS-EP-rRi" secondAttribute="bottom" constant="25" id="kOt-sC-h2U"/>
                                                <constraint firstItem="WJx-t8-zph" firstAttribute="leading" secondItem="6Ce-6j-R0E" secondAttribute="leading" id="kyK-ZQ-SKS"/>
                                                <constraint firstItem="J1A-pr-4Mw" firstAttribute="top" secondItem="HOZ-3K-gac" secondAttribute="bottom" constant="4" id="mcN-0Q-aAy"/>
                                                <constraint firstItem="jfm-5o-nR6" firstAttribute="leading" secondItem="WJx-t8-zph" secondAttribute="trailing" id="o1E-ol-2jd"/>
                                                <constraint firstItem="Hag-FQ-3xM" firstAttribute="centerX" secondItem="WJx-t8-zph" secondAttribute="centerX" id="odW-vj-X5J"/>
                                                <constraint firstAttribute="trailing" secondItem="WoY-Ba-g9b" secondAttribute="trailing" constant="8" id="p4n-m1-M2d"/>
                                                <constraint firstItem="JOV-Vv-khe" firstAttribute="top" secondItem="Kqv-wy-wTn" secondAttribute="bottom" constant="20" id="p5P-h1-MId"/>
                                                <constraint firstItem="jfm-5o-nR6" firstAttribute="width" secondItem="WJx-t8-zph" secondAttribute="width" id="pGz-RI-lKe"/>
                                                <constraint firstAttribute="bottom" secondItem="J1A-pr-4Mw" secondAttribute="bottom" priority="999" constant="20" id="qeN-r6-igY"/>
                                                <constraint firstAttribute="trailing" secondItem="HOZ-3K-gac" secondAttribute="trailing" constant="16" id="rwA-Rq-rPe"/>
                                                <constraint firstItem="LmS-EP-rRi" firstAttribute="top" secondItem="Kqv-wy-wTn" secondAttribute="bottom" constant="20" id="sBP-6z-9z2"/>
                                                <constraint firstAttribute="trailing" secondItem="tEB-1a-YXe" secondAttribute="trailing" constant="10" id="t1m-Or-OGs"/>
                                                <constraint firstItem="EFW-8o-LFA" firstAttribute="top" secondItem="f9X-kF-BRA" secondAttribute="bottom" constant="24" id="tnb-AX-IFj"/>
                                                <constraint firstItem="LmS-EP-rRi" firstAttribute="leading" secondItem="xln-Q9-9Kb" secondAttribute="trailing" constant="19" id="vNZ-Wp-ljY"/>
                                                <constraint firstItem="f9X-kF-BRA" firstAttribute="leading" secondItem="6Ce-6j-R0E" secondAttribute="leading" constant="46" id="vxm-dM-7Lr"/>
                                                <constraint firstItem="nWr-xr-z0y" firstAttribute="top" secondItem="VKX-rD-kyP" secondAttribute="bottom" id="web-5G-xyN"/>
                                                <constraint firstItem="nWr-xr-z0y" firstAttribute="top" secondItem="Hag-FQ-3xM" secondAttribute="bottom" constant="12" id="z13-n3-GHJ"/>
                                            </constraints>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                    <real key="value" value="6"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="point" keyPath="shadowOffset">
                                                    <point key="value" x="0.0" y="4"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="color" keyPath="shadowColor">
                                                    <color key="value" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="number" keyPath="shadowRadius">
                                                    <real key="value" value="8"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="number" keyPath="shadowOpacity">
                                                    <real key="value" value="0.10000000000000001"/>
                                                </userDefinedRuntimeAttribute>
                                            </userDefinedRuntimeAttributes>
                                        </view>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="16m-dw-YJS" customClass="RoundView" customModule="Beta_Cinemas" customModuleProvider="target">
                                            <rect key="frame" x="137.5" y="112" width="100" height="100"/>
                                            <subviews>
                                                <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ceH-GK-Joy" customClass="RoundView" customModule="Beta_Cinemas" customModuleProvider="target">
                                                    <rect key="frame" x="0.0" y="0.0" width="100" height="100"/>
                                                    <subviews>
                                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="ymI-DJ-cU6">
                                                            <rect key="frame" x="0.0" y="0.0" width="100" height="100"/>
                                                            <state key="normal" image="ic_account_white"/>
                                                            <connections>
                                                                <action selector="changeAvatarBtPressed:" destination="8t3-92-lHa" eventType="touchUpInside" id="Tnk-vr-uny"/>
                                                            </connections>
                                                        </button>
                                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="uvG-Xb-OaJ" customClass="LocalizableButton" customModule="Beta_Cinemas" customModuleProvider="target">
                                                            <rect key="frame" x="0.0" y="72" width="100" height="28"/>
                                                            <color key="backgroundColor" red="0.0**********2352941" green="0.34901960784313724" blue="0.61568627450980395" alpha="0.72999999999999998" colorSpace="custom" customColorSpace="sRGB"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="28" id="7jz-jX-Zqj"/>
                                                            </constraints>
                                                            <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="14"/>
                                                            <state key="normal" title="Chon anh"/>
                                                            <userDefinedRuntimeAttributes>
                                                                <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Member.ChooseImage"/>
                                                            </userDefinedRuntimeAttributes>
                                                            <connections>
                                                                <action selector="changeAvatarBtPressed:" destination="8t3-92-lHa" eventType="touchUpInside" id="pvf-js-Hoh"/>
                                                            </connections>
                                                        </button>
                                                    </subviews>
                                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <constraints>
                                                        <constraint firstItem="uvG-Xb-OaJ" firstAttribute="leading" secondItem="ceH-GK-Joy" secondAttribute="leading" id="2aW-ds-tf9"/>
                                                        <constraint firstAttribute="bottom" secondItem="uvG-Xb-OaJ" secondAttribute="bottom" id="DKB-tu-Hb3"/>
                                                        <constraint firstAttribute="bottom" secondItem="ymI-DJ-cU6" secondAttribute="bottom" id="Ljl-w2-Mdl"/>
                                                        <constraint firstItem="ymI-DJ-cU6" firstAttribute="leading" secondItem="ceH-GK-Joy" secondAttribute="leading" id="Mco-HK-FPK"/>
                                                        <constraint firstItem="ymI-DJ-cU6" firstAttribute="top" secondItem="ceH-GK-Joy" secondAttribute="top" id="Oqw-gs-1JT"/>
                                                        <constraint firstAttribute="trailing" secondItem="ymI-DJ-cU6" secondAttribute="trailing" id="Qvb-qN-Ewn"/>
                                                        <constraint firstAttribute="trailing" secondItem="uvG-Xb-OaJ" secondAttribute="trailing" id="f8B-Ll-vEF"/>
                                                    </constraints>
                                                    <userDefinedRuntimeAttributes>
                                                        <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                            <real key="value" value="50"/>
                                                        </userDefinedRuntimeAttribute>
                                                    </userDefinedRuntimeAttributes>
                                                </view>
                                            </subviews>
                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstAttribute="trailing" secondItem="ceH-GK-Joy" secondAttribute="trailing" id="1cL-R5-rGb"/>
                                                <constraint firstItem="ceH-GK-Joy" firstAttribute="leading" secondItem="16m-dw-YJS" secondAttribute="leading" id="8oO-zF-NxI"/>
                                                <constraint firstAttribute="bottom" secondItem="ceH-GK-Joy" secondAttribute="bottom" id="Dsy-sV-jcH"/>
                                                <constraint firstItem="ceH-GK-Joy" firstAttribute="top" secondItem="16m-dw-YJS" secondAttribute="top" id="ceF-Eg-J7q"/>
                                                <constraint firstAttribute="height" constant="100" id="eHS-Gk-v7Y"/>
                                                <constraint firstAttribute="width" constant="100" id="lV8-YC-KWk"/>
                                            </constraints>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                    <real key="value" value="50"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                                    <color key="value" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                                    <real key="value" value="1"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="point" keyPath="shadowOffset">
                                                    <point key="value" x="0.0" y="6"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="number" keyPath="shadowRadius">
                                                    <real key="value" value="12"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="color" keyPath="shadowColor">
                                                    <color key="value" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="number" keyPath="shadowOpacity">
                                                    <real key="value" value="0.25"/>
                                                </userDefinedRuntimeAttribute>
                                            </userDefinedRuntimeAttributes>
                                        </view>
                                    </subviews>
                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <constraints>
                                        <constraint firstItem="3Qb-3a-wSw" firstAttribute="top" secondItem="lzt-2Z-LcX" secondAttribute="top" id="3oL-Rw-Gc8"/>
                                        <constraint firstItem="3Qb-3a-wSw" firstAttribute="leading" secondItem="lzt-2Z-LcX" secondAttribute="leading" id="9Bc-yO-N8u"/>
                                        <constraint firstItem="6Ce-6j-R0E" firstAttribute="leading" secondItem="lzt-2Z-LcX" secondAttribute="leading" constant="8" id="EgG-R4-h3d"/>
                                        <constraint firstAttribute="trailing" secondItem="6Ce-6j-R0E" secondAttribute="trailing" constant="8" id="Svw-0F-xU0"/>
                                        <constraint firstItem="6Ce-6j-R0E" firstAttribute="top" secondItem="lzt-2Z-LcX" secondAttribute="top" constant="149" id="anF-rk-5kI"/>
                                        <constraint firstItem="16m-dw-YJS" firstAttribute="centerX" secondItem="lzt-2Z-LcX" secondAttribute="centerX" id="bng-59-bVK"/>
                                        <constraint firstItem="16m-dw-YJS" firstAttribute="top" secondItem="lzt-2Z-LcX" secondAttribute="top" constant="112" id="fsC-uz-J1E"/>
                                        <constraint firstAttribute="trailing" secondItem="3Qb-3a-wSw" secondAttribute="trailing" id="mcC-CB-ktC"/>
                                        <constraint firstAttribute="bottom" secondItem="6Ce-6j-R0E" secondAttribute="bottom" constant="8" id="wPR-oP-izw"/>
                                    </constraints>
                                </view>
                                <prototypes>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" reuseIdentifier="LogoutCell" rowHeight="57" id="wcq-Y7-zd8">
                                        <rect key="frame" x="0.0" y="722" width="375" height="57"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="wcq-Y7-zd8" id="cFo-B6-rAT">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="57"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="3gD-mI-2Fi">
                                                    <rect key="frame" x="0.0" y="0.0" width="375" height="57"/>
                                                    <subviews>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Logout" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="r9s-nD-j2B" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                                            <rect key="frame" x="20" y="18" width="53.5" height="21"/>
                                                            <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                            <color key="textColor" red="0.99215686270000003" green="0.15686274510000001" blue="0.0078431372550000003" alpha="0.97999999999999998" colorSpace="custom" customColorSpace="sRGB"/>
                                                            <nil key="highlightedColor"/>
                                                            <userDefinedRuntimeAttributes>
                                                                <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Member.Logout"/>
                                                            </userDefinedRuntimeAttributes>
                                                        </label>
                                                    </subviews>
                                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <constraints>
                                                        <constraint firstAttribute="bottom" secondItem="r9s-nD-j2B" secondAttribute="bottom" constant="18" id="64m-nP-Lhb"/>
                                                        <constraint firstItem="r9s-nD-j2B" firstAttribute="top" secondItem="3gD-mI-2Fi" secondAttribute="top" constant="18" id="J2H-gc-UDy"/>
                                                        <constraint firstItem="r9s-nD-j2B" firstAttribute="leading" secondItem="3gD-mI-2Fi" secondAttribute="leading" constant="20" id="zUo-ul-mgi"/>
                                                    </constraints>
                                                </view>
                                            </subviews>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstAttribute="trailing" secondItem="3gD-mI-2Fi" secondAttribute="trailing" id="6x1-r9-Frc"/>
                                                <constraint firstItem="3gD-mI-2Fi" firstAttribute="top" secondItem="cFo-B6-rAT" secondAttribute="top" id="EG1-yh-9om"/>
                                                <constraint firstItem="3gD-mI-2Fi" firstAttribute="leading" secondItem="cFo-B6-rAT" secondAttribute="leading" id="iks-k5-kgU"/>
                                                <constraint firstAttribute="bottom" secondItem="3gD-mI-2Fi" secondAttribute="bottom" id="sen-Ni-ief"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    </tableViewCell>
                                </prototypes>
                                <connections>
                                    <outlet property="dataSource" destination="8t3-92-lHa" id="onX-GD-WQf"/>
                                    <outlet property="delegate" destination="8t3-92-lHa" id="sus-ex-cvC"/>
                                </connections>
                            </tableView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="cce-FK-KuS"/>
                        <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstItem="cce-FK-KuS" firstAttribute="bottom" secondItem="Dws-Dr-Nl0" secondAttribute="bottom" id="9ho-t1-dq8"/>
                            <constraint firstItem="Dws-Dr-Nl0" firstAttribute="leading" secondItem="cce-FK-KuS" secondAttribute="leading" id="FwU-KV-ZJU"/>
                            <constraint firstAttribute="trailing" secondItem="Dws-Dr-Nl0" secondAttribute="trailing" id="jTX-VO-92M"/>
                            <constraint firstItem="Dws-Dr-Nl0" firstAttribute="top" secondItem="cce-FK-KuS" secondAttribute="top" id="zkF-px-cqp"/>
                        </constraints>
                    </view>
                    <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
                    <size key="freeformSize" width="375" height="800"/>
                    <connections>
                        <outlet property="bannerBlurView" destination="gOp-t2-UIy" id="SHe-YS-1HS"/>
                        <outlet property="bannerView" destination="3Qb-3a-wSw" id="YVJ-hO-cMc"/>
                        <outlet property="btAvatar" destination="ymI-DJ-cU6" id="UET-7f-Szh"/>
                        <outlet property="cTop" destination="1vo-zB-3dl" id="lxW-Gx-1Va"/>
                        <outlet property="headerView" destination="lzt-2Z-LcX" id="Eue-u4-VpH"/>
                        <outlet property="ivBanner" destination="AmL-C0-mLW" id="HdG-hi-QQG"/>
                        <outlet property="ivBarCode" destination="JT5-fd-F0m" id="ZmZ-7C-jg5"/>
                        <outlet property="ivMemberCard" destination="xln-Q9-9Kb" id="ehd-zZ-QBr"/>
                        <outlet property="ivVip" destination="JOV-Vv-khe" id="PmJ-hY-O7y"/>
                        <outlet property="lbCardNumber" destination="tEB-1a-YXe" id="EOK-BZ-Tlm"/>
                        <outlet property="lbName" destination="f9X-kF-BRA" id="Vyd-ih-DMt"/>
                        <outlet property="lbRemainPoint" destination="Kqv-wy-wTn" id="cld-70-sO5"/>
                        <outlet property="lbSpentMoney" destination="LmS-EP-rRi" id="0bx-D4-IGS"/>
                        <outlet property="lbTotalMoney" destination="Hag-FQ-3xM" id="GVs-1W-YT2"/>
                        <outlet property="lbTotalPoint" destination="WoY-Ba-g9b" id="6aU-Ax-xpq"/>
                        <outlet property="lbVipCondition" destination="J1A-pr-4Mw" id="k4m-hX-AwS"/>
                        <outlet property="pvSpentMoney" destination="HOZ-3K-gac" id="cxe-aT-xBI"/>
                        <outlet property="tableView" destination="Dws-Dr-Nl0" id="q1j-P3-QB6"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="C10-Lg-7U5" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="119.0625" y="-70.774647887323951"/>
        </scene>
        <!--Change Pass View Controller-->
        <scene sceneID="92S-Oz-yBn">
            <objects>
                <viewController storyboardIdentifier="ChangePassViewController" id="eYY-Ay-g4w" customClass="ChangePassViewController" customModule="Beta_Cinemas" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="vda-7x-ZCC">
                        <rect key="frame" x="0.0" y="0.0" width="320" height="568"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="uCn-M0-9EM">
                                <rect key="frame" x="20" y="20" width="280" height="350"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Nhập mật khẩu hiện tại" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="uJy-hX-Xlu" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="22" width="167" height="19"/>
                                        <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                        <color key="textColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="ChangePass.EnterCurrentPass"/>
                                        </userDefinedRuntimeAttributes>
                                    </label>
                                    <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Mật khẩu hiện tại" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="hOK-iG-sOK" customClass="InputTextField" customModule="Beta_Cinemas" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="47" width="280" height="50"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="50" id="64z-1a-hlg"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                        <textInputTraits key="textInputTraits" returnKeyType="next" secureTextEntry="YES"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="image" keyPath="leftIcon" value="ic_password"/>
                                            <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="ChangePass.CurrentPass"/>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <outlet property="delegate" destination="eYY-Ay-g4w" id="gAl-jy-YYV"/>
                                        </connections>
                                    </textField>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Nhập mật khẩu mới" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="UYp-t3-kFV" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="117" width="280" height="19"/>
                                        <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                        <color key="textColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="ChangePass.EnterNewPass"/>
                                        </userDefinedRuntimeAttributes>
                                    </label>
                                    <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Mật khẩu mới" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="e5u-Dx-bPd" customClass="InputTextField" customModule="Beta_Cinemas" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="142" width="280" height="50"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="50" id="ex2-P8-3n8"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                        <textInputTraits key="textInputTraits" returnKeyType="next" secureTextEntry="YES"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="image" keyPath="leftIcon" value="ic_password"/>
                                            <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="ChangePass.NewPass"/>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <outlet property="delegate" destination="eYY-Ay-g4w" id="opP-g8-S88"/>
                                        </connections>
                                    </textField>
                                    <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Nhập lại khẩu mới" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="DjY-5S-D3I" customClass="InputTextField" customModule="Beta_Cinemas" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="210" width="280" height="50"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="50" id="tCc-kn-Afy"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                        <textInputTraits key="textInputTraits" returnKeyType="go" secureTextEntry="YES"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="image" keyPath="leftIcon" value="ic_comfirm_password"/>
                                            <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="ChangePass.ReNewPass"/>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <outlet property="delegate" destination="eYY-Ay-g4w" id="rlA-My-cwA"/>
                                        </connections>
                                    </textField>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="pGF-Ey-UYd" customClass="GradientButton" customModule="Beta_Cinemas" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="294" width="280" height="56"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="56" id="fiw-AF-4h3"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="20"/>
                                        <state key="normal" title="CẬP NHẬT"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                <real key="value" value="4"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="color" keyPath="secondColor">
                                                <color key="value" red="0.99215686270000003" green="0.48627450979999998" blue="0.0078431372550000003" alpha="0.98999999999999999" colorSpace="custom" customColorSpace="sRGB"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="color" keyPath="firstColor">
                                                <color key="value" red="0.99215686270000003" green="0.15686274510000001" blue="0.0078431372550000003" alpha="0.97999999999999998" colorSpace="custom" customColorSpace="sRGB"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="number" keyPath="shadowOpacity">
                                                <real key="value" value="0.20000000000000001"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="color" keyPath="shadowColor">
                                                <color key="value" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="point" keyPath="shadowOffset">
                                                <point key="value" x="0.0" y="6"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="number" keyPath="shadowRadius">
                                                <real key="value" value="4"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Bt.Update"/>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="updateBtPressed:" destination="eYY-Ay-g4w" eventType="touchUpInside" id="hMk-Ks-zGH"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="uJy-hX-Xlu" firstAttribute="top" secondItem="uCn-M0-9EM" secondAttribute="top" constant="22" id="17O-0C-cQT"/>
                                    <constraint firstItem="e5u-Dx-bPd" firstAttribute="leading" secondItem="uCn-M0-9EM" secondAttribute="leading" id="4sJ-c0-LaY"/>
                                    <constraint firstItem="DjY-5S-D3I" firstAttribute="leading" secondItem="uCn-M0-9EM" secondAttribute="leading" id="AIR-6F-xkI"/>
                                    <constraint firstItem="hOK-iG-sOK" firstAttribute="leading" secondItem="uCn-M0-9EM" secondAttribute="leading" id="EGQ-BG-l2e"/>
                                    <constraint firstItem="UYp-t3-kFV" firstAttribute="top" secondItem="hOK-iG-sOK" secondAttribute="bottom" constant="20" id="GdZ-8C-tA2"/>
                                    <constraint firstItem="UYp-t3-kFV" firstAttribute="leading" secondItem="uCn-M0-9EM" secondAttribute="leading" id="J1M-p9-juf"/>
                                    <constraint firstAttribute="trailing" secondItem="UYp-t3-kFV" secondAttribute="trailing" id="TDl-cW-6Lx"/>
                                    <constraint firstAttribute="trailing" secondItem="pGF-Ey-UYd" secondAttribute="trailing" id="UOP-SK-73b"/>
                                    <constraint firstItem="e5u-Dx-bPd" firstAttribute="top" secondItem="UYp-t3-kFV" secondAttribute="bottom" constant="6" id="WDL-Df-FBY"/>
                                    <constraint firstAttribute="trailing" secondItem="e5u-Dx-bPd" secondAttribute="trailing" id="bEO-0a-7Xk"/>
                                    <constraint firstItem="pGF-Ey-UYd" firstAttribute="leading" secondItem="uCn-M0-9EM" secondAttribute="leading" id="bFT-ka-Oqh"/>
                                    <constraint firstItem="hOK-iG-sOK" firstAttribute="top" secondItem="uJy-hX-Xlu" secondAttribute="bottom" constant="6" id="dMp-FH-snQ"/>
                                    <constraint firstAttribute="trailing" secondItem="DjY-5S-D3I" secondAttribute="trailing" id="krk-2i-ech"/>
                                    <constraint firstAttribute="trailing" secondItem="hOK-iG-sOK" secondAttribute="trailing" id="m1G-dV-bEs"/>
                                    <constraint firstItem="uJy-hX-Xlu" firstAttribute="leading" secondItem="uCn-M0-9EM" secondAttribute="leading" id="vNq-5M-fO5"/>
                                    <constraint firstAttribute="bottom" secondItem="pGF-Ey-UYd" secondAttribute="bottom" id="yPs-kj-ACm"/>
                                    <constraint firstItem="DjY-5S-D3I" firstAttribute="top" secondItem="e5u-Dx-bPd" secondAttribute="bottom" constant="18" id="ydr-qM-DDx"/>
                                    <constraint firstItem="pGF-Ey-UYd" firstAttribute="top" secondItem="DjY-5S-D3I" secondAttribute="bottom" constant="34" id="yhi-P1-8CQ"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="d7u-b6-vbV"/>
                        <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstItem="uCn-M0-9EM" firstAttribute="centerX" secondItem="vda-7x-ZCC" secondAttribute="centerX" id="SBb-Vw-k4a"/>
                            <constraint firstItem="uCn-M0-9EM" firstAttribute="top" secondItem="d7u-b6-vbV" secondAttribute="top" id="ScV-wM-3ky"/>
                            <constraint firstItem="uCn-M0-9EM" firstAttribute="leading" secondItem="d7u-b6-vbV" secondAttribute="leading" constant="20" id="ysb-bl-p4p"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="tfCurrentPass" destination="hOK-iG-sOK" id="rhP-oF-O8E"/>
                        <outlet property="tfNewPass" destination="e5u-Dx-bPd" id="Krn-1h-Q2R"/>
                        <outlet property="tfNewPass2" destination="DjY-5S-D3I" id="P4H-XR-WDc"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="O0y-AD-Qmr" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="764" y="-68.815592203898049"/>
        </scene>
        <!--Reward Points View Controller-->
        <scene sceneID="7Vg-Ya-9wX">
            <objects>
                <viewController storyboardIdentifier="RewardPointsViewController" id="awP-Eg-set" customClass="RewardPointsViewController" customModule="Beta_Cinemas" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="RZL-9t-5Zp">
                        <rect key="frame" x="0.0" y="0.0" width="320" height="568"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="0mD-ju-3uX" customClass="RoundView" customModule="Beta_Cinemas" customModuleProvider="target">
                                <rect key="frame" x="8" y="40" width="304" height="110"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" distribution="fillEqually" translatesAutoresizingMaskIntoConstraints="NO" id="c3I-oK-JAo">
                                        <rect key="frame" x="0.0" y="0.0" width="304" height="110"/>
                                        <subviews>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ycR-wH-VUv" userLabel="View1">
                                                <rect key="frame" x="0.0" y="0.0" width="101.5" height="110"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Điểm đã tích luỹ" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="QQ0-mz-ecy" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                                        <rect key="frame" x="7" y="28" width="87" height="14"/>
                                                        <fontDescription key="fontDescription" name="SourceSansPro-SemiBold" family="Source Sans Pro" pointSize="12"/>
                                                        <color key="textColor" red="0.72549019607843135" green="0.72549019607843135" blue="0.72549019607843135" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                        <nil key="highlightedColor"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="BetaPoint.CurrentPoint"/>
                                                        </userDefinedRuntimeAttributes>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="0" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="j9b-vT-xOc">
                                                        <rect key="frame" x="44" y="42" width="13.5" height="28"/>
                                                        <fontDescription key="fontDescription" name="SourceSansPro-Bold" family="Source Sans Pro" pointSize="24"/>
                                                        <color key="textColor" red="0.0**********" green="0.34901960780000002" blue="0.61568627450000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstItem="QQ0-mz-ecy" firstAttribute="centerY" secondItem="ycR-wH-VUv" secondAttribute="centerY" constant="-20" id="74x-Gt-4Sg"/>
                                                    <constraint firstItem="QQ0-mz-ecy" firstAttribute="centerX" secondItem="ycR-wH-VUv" secondAttribute="centerX" id="7k9-SJ-3Vc"/>
                                                    <constraint firstItem="j9b-vT-xOc" firstAttribute="top" secondItem="QQ0-mz-ecy" secondAttribute="bottom" id="XKk-4I-dyC"/>
                                                    <constraint firstItem="j9b-vT-xOc" firstAttribute="centerX" secondItem="QQ0-mz-ecy" secondAttribute="centerX" id="uS0-Vh-BPt"/>
                                                </constraints>
                                            </view>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Zxi-6d-enc" userLabel="View1">
                                                <rect key="frame" x="101.5" y="0.0" width="101" height="110"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Điểm đã tích luỹ" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="nKv-YQ-v17" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                                        <rect key="frame" x="7" y="28" width="87" height="14"/>
                                                        <fontDescription key="fontDescription" name="SourceSansPro-SemiBold" family="Source Sans Pro" pointSize="12"/>
                                                        <color key="textColor" red="0.72549019609999998" green="0.72549019609999998" blue="0.72549019609999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                        <nil key="highlightedColor"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="BetaPoint.TotalPoint"/>
                                                        </userDefinedRuntimeAttributes>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="0" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="yk6-IV-LOk">
                                                        <rect key="frame" x="44" y="42" width="13.5" height="28"/>
                                                        <fontDescription key="fontDescription" name="SourceSansPro-Bold" family="Source Sans Pro" pointSize="24"/>
                                                        <color key="textColor" red="0.494**********2355" green="0.82745098039215681" blue="0.1294**********234" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstItem="yk6-IV-LOk" firstAttribute="centerX" secondItem="nKv-YQ-v17" secondAttribute="centerX" id="anU-cL-3XW"/>
                                                    <constraint firstItem="nKv-YQ-v17" firstAttribute="centerX" secondItem="Zxi-6d-enc" secondAttribute="centerX" id="kde-ow-aBg"/>
                                                    <constraint firstItem="nKv-YQ-v17" firstAttribute="centerY" secondItem="Zxi-6d-enc" secondAttribute="centerY" constant="-20" id="leS-Ox-0Wo"/>
                                                    <constraint firstItem="yk6-IV-LOk" firstAttribute="top" secondItem="nKv-YQ-v17" secondAttribute="bottom" id="ntz-74-ekf"/>
                                                </constraints>
                                            </view>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="NRd-zH-evE" userLabel="View1">
                                                <rect key="frame" x="202.5" y="0.0" width="101.5" height="110"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Điểm đã tích luỹ" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="jNf-pW-pkK" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                                        <rect key="frame" x="7.5" y="28" width="87" height="14"/>
                                                        <fontDescription key="fontDescription" name="SourceSansPro-SemiBold" family="Source Sans Pro" pointSize="12"/>
                                                        <color key="textColor" red="0.72549019609999998" green="0.72549019609999998" blue="0.72549019609999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                        <nil key="highlightedColor"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="BetaPoint.UsedPoint"/>
                                                        </userDefinedRuntimeAttributes>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="0" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="26S-5o-rT7">
                                                        <rect key="frame" x="44" y="42" width="13.5" height="28"/>
                                                        <fontDescription key="fontDescription" name="SourceSansPro-Bold" family="Source Sans Pro" pointSize="24"/>
                                                        <color key="textColor" red="0.99215686274509807" green="0.15686274509803921" blue="0.0078431372549019607" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstItem="26S-5o-rT7" firstAttribute="centerX" secondItem="jNf-pW-pkK" secondAttribute="centerX" id="Dh1-aO-oI5"/>
                                                    <constraint firstItem="jNf-pW-pkK" firstAttribute="centerY" secondItem="NRd-zH-evE" secondAttribute="centerY" constant="-20" id="fD3-8C-O8u"/>
                                                    <constraint firstItem="jNf-pW-pkK" firstAttribute="centerX" secondItem="NRd-zH-evE" secondAttribute="centerX" id="lc1-s2-H50"/>
                                                    <constraint firstItem="26S-5o-rT7" firstAttribute="top" secondItem="jNf-pW-pkK" secondAttribute="bottom" id="pXr-4e-GO4"/>
                                                </constraints>
                                            </view>
                                        </subviews>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="c3I-oK-JAo" firstAttribute="leading" secondItem="0mD-ju-3uX" secondAttribute="leading" id="2Q0-GE-Kwy"/>
                                    <constraint firstItem="c3I-oK-JAo" firstAttribute="top" secondItem="0mD-ju-3uX" secondAttribute="top" id="Jbm-O0-xBU"/>
                                    <constraint firstAttribute="bottom" secondItem="c3I-oK-JAo" secondAttribute="bottom" id="ab0-lg-pzQ"/>
                                    <constraint firstAttribute="trailing" secondItem="c3I-oK-JAo" secondAttribute="trailing" id="mvh-nG-LZ0"/>
                                    <constraint firstAttribute="height" constant="110" id="ne3-F8-gD8"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="5"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="7n2-Bh-7nl" customClass="GradientButton" customModule="Beta_Cinemas" customModuleProvider="target">
                                <rect key="frame" x="20" y="498" width="280" height="50"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="50" id="xPv-Ib-oov"/>
                                </constraints>
                                <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="16"/>
                                <state key="normal" title="SỬ DỤNG">
                                    <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </state>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="3"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                                <connections>
                                    <action selector="donateTapped:" destination="awP-Eg-set" eventType="touchUpInside" id="8tg-lW-pBg"/>
                                </connections>
                            </button>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Điểm đã tích luỹ" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="tHb-mI-aQq" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                <rect key="frame" x="8" y="165" width="304" height="16.5"/>
                                <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="14"/>
                                <color key="textColor" red="0.72549019609999998" green="0.72549019609999998" blue="0.72549019609999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <nil key="highlightedColor"/>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="BetaPoint.CurrentPoint"/>
                                </userDefinedRuntimeAttributes>
                            </label>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="sMA-f0-TRg"/>
                        <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstItem="0mD-ju-3uX" firstAttribute="leading" secondItem="RZL-9t-5Zp" secondAttribute="leading" constant="8" id="4Pk-LX-DPO"/>
                            <constraint firstItem="0mD-ju-3uX" firstAttribute="top" secondItem="sMA-f0-TRg" secondAttribute="top" constant="20" id="BiW-0f-zYX"/>
                            <constraint firstItem="7n2-Bh-7nl" firstAttribute="leading" secondItem="sMA-f0-TRg" secondAttribute="leading" constant="20" id="CGz-nX-EVE"/>
                            <constraint firstItem="sMA-f0-TRg" firstAttribute="bottom" secondItem="7n2-Bh-7nl" secondAttribute="bottom" constant="20" id="Jba-Ra-95d"/>
                            <constraint firstItem="tHb-mI-aQq" firstAttribute="leading" secondItem="sMA-f0-TRg" secondAttribute="leading" constant="8" id="Ql7-kT-CSd"/>
                            <constraint firstItem="tHb-mI-aQq" firstAttribute="top" secondItem="0mD-ju-3uX" secondAttribute="bottom" constant="15" id="WIN-4Y-RwW"/>
                            <constraint firstItem="sMA-f0-TRg" firstAttribute="trailing" secondItem="tHb-mI-aQq" secondAttribute="trailing" constant="8" id="fdw-Q3-elm"/>
                            <constraint firstItem="sMA-f0-TRg" firstAttribute="trailing" secondItem="0mD-ju-3uX" secondAttribute="trailing" constant="8" id="nvi-F8-EMg"/>
                            <constraint firstItem="sMA-f0-TRg" firstAttribute="trailing" secondItem="7n2-Bh-7nl" secondAttribute="trailing" constant="20" id="uIC-mC-bga"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="donateButton" destination="7n2-Bh-7nl" id="hUl-vy-aPL"/>
                        <outlet property="lbCurrentPoint" destination="j9b-vT-xOc" id="MaM-gZ-c90"/>
                        <outlet property="lbRemainPoint" destination="tHb-mI-aQq" id="Jjy-Xe-pSj"/>
                        <outlet property="lbTotalPoint" destination="yk6-IV-LOk" id="m0L-UT-IaP"/>
                        <outlet property="lbUsedPoint" destination="26S-5o-rT7" id="r01-K5-wNl"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="eaQ-ja-L5o" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1400.625" y="-69.718309859154928"/>
        </scene>
        <!--Vourcher Coupon View Controller-->
        <scene sceneID="ifJ-hD-ftR">
            <objects>
                <viewController storyboardIdentifier="VourcherCouponViewController" id="h47-At-9QJ" customClass="VourcherCouponViewController" customModule="Beta_Cinemas" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="m4v-CL-hj0">
                        <rect key="frame" x="0.0" y="0.0" width="320" height="568"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="kmX-Xd-fqi">
                                <rect key="frame" x="0.0" y="20" width="320" height="548"/>
                                <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="calibratedRGB"/>
                                <connections>
                                    <outlet property="delegate" destination="h47-At-9QJ" id="Ste-yA-hpO"/>
                                </connections>
                            </tableView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="K2O-l1-E5O"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="kmX-Xd-fqi" firstAttribute="leading" secondItem="K2O-l1-E5O" secondAttribute="leading" id="4NC-qL-YEN"/>
                            <constraint firstItem="K2O-l1-E5O" firstAttribute="bottom" secondItem="kmX-Xd-fqi" secondAttribute="bottom" id="B9r-GW-uEn"/>
                            <constraint firstItem="K2O-l1-E5O" firstAttribute="trailing" secondItem="kmX-Xd-fqi" secondAttribute="trailing" id="K8Q-b0-NSg"/>
                            <constraint firstItem="kmX-Xd-fqi" firstAttribute="top" secondItem="K2O-l1-E5O" secondAttribute="top" id="SYF-tI-4qc"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="tableView" destination="kmX-Xd-fqi" id="ilP-Tr-XMk"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="WxH-uS-bBd" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="2071" y="-67"/>
        </scene>
        <!--Card Gift View Controller-->
        <scene sceneID="9Nh-uT-aqC">
            <objects>
                <viewController storyboardIdentifier="CardGiftViewController" id="KQy-IN-hEJ" customClass="CardGiftViewController" customModule="Beta_Cinemas" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="uPz-GH-K8S">
                        <rect key="frame" x="0.0" y="0.0" width="320" height="568"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <viewLayoutGuide key="safeArea" id="WWx-ia-l8J"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="pdG-nS-Jwt" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="2735" y="-67"/>
        </scene>
        <!--Voucher View Controller-->
        <scene sceneID="9iL-TK-5Fp">
            <objects>
                <viewController storyboardIdentifier="VoucherViewController" id="iJz-jB-uny" customClass="VoucherViewController" customModule="Beta_Cinemas" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Lx0-LA-Wyt">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="800"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="PaK-Qf-o6k">
                                <rect key="frame" x="0.0" y="20" width="375" height="780"/>
                                <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="calibratedRGB"/>
                                <view key="tableHeaderView" contentMode="scaleToFill" id="zYx-nT-yP6">
                                    <rect key="frame" x="0.0" y="0.0" width="375" height="364"/>
                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                    <subviews>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="252" text="THÔNG TIN VOUCHER" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="DZU-zS-zga" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                            <rect key="frame" x="8" y="22" width="359" height="23.5"/>
                                            <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="20"/>
                                            <color key="textColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            <nil key="highlightedColor"/>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Voucher.Info"/>
                                            </userDefinedRuntimeAttributes>
                                        </label>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Mã Voucher" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="X47-LK-cf0" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                            <rect key="frame" x="20" y="63.5" width="85.5" height="49.5"/>
                                            <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="16"/>
                                            <color key="textColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            <nil key="highlightedColor"/>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Voucher.Code"/>
                                            </userDefinedRuntimeAttributes>
                                        </label>
                                        <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="db9-T7-HiE" customClass="RoundTextField" customModule="Beta_Cinemas" customModuleProvider="target">
                                            <rect key="frame" x="20" y="121" width="335" height="50"/>
                                            <constraints>
                                                <constraint firstAttribute="height" constant="50" id="O2H-21-RNM"/>
                                            </constraints>
                                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                            <textInputTraits key="textInputTraits"/>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                    <real key="value" value="2"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                                    <color key="value" red="0.58431372549019611" green="0.58431372549019611" blue="0.58431372549019611" alpha="0.59999999999999998" colorSpace="custom" customColorSpace="sRGB"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                                    <real key="value" value="1"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="number" keyPath="horizontalSpace">
                                                    <real key="value" value="10"/>
                                                </userDefinedRuntimeAttribute>
                                            </userDefinedRuntimeAttributes>
                                        </textField>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="252" text="Mã PIN" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Jgh-5J-yZR" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                            <rect key="frame" x="20" y="187" width="335" height="19"/>
                                            <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="16"/>
                                            <color key="textColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            <nil key="highlightedColor"/>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Voucher.PIN"/>
                                            </userDefinedRuntimeAttributes>
                                        </label>
                                        <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="cw7-RS-4Ra" customClass="RoundTextField" customModule="Beta_Cinemas" customModuleProvider="target">
                                            <rect key="frame" x="20" y="214" width="335" height="50"/>
                                            <constraints>
                                                <constraint firstAttribute="height" constant="50" id="uo7-UU-yNy"/>
                                            </constraints>
                                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                            <textInputTraits key="textInputTraits"/>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                    <real key="value" value="2"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                                    <color key="value" red="0.58431372550000005" green="0.58431372550000005" blue="0.58431372550000005" alpha="0.59999999999999998" colorSpace="custom" customColorSpace="sRGB"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                                    <real key="value" value="1"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="number" keyPath="horizontalSpace">
                                                    <real key="value" value="10"/>
                                                </userDefinedRuntimeAttribute>
                                            </userDefinedRuntimeAttributes>
                                        </textField>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="eb9-hy-Fjq" customClass="RoundView" customModule="Beta_Cinemas" customModuleProvider="target">
                                            <rect key="frame" x="20" y="288" width="335" height="56"/>
                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                    <real key="value" value="4"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="color" keyPath="shadowColor">
                                                    <color key="value" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="number" keyPath="shadowOpacity">
                                                    <real key="value" value="0.20000000000000001"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="point" keyPath="shadowOffset">
                                                    <point key="value" x="0.0" y="6"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="number" keyPath="shadowRadius">
                                                    <real key="value" value="4"/>
                                                </userDefinedRuntimeAttribute>
                                            </userDefinedRuntimeAttributes>
                                        </view>
                                        <button opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Jhe-pc-vdO" customClass="GradientButton" customModule="Beta_Cinemas" customModuleProvider="target">
                                            <rect key="frame" x="20" y="288" width="335" height="56"/>
                                            <constraints>
                                                <constraint firstAttribute="height" constant="56" id="r5Y-PU-pgP"/>
                                            </constraints>
                                            <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="20"/>
                                            <state key="normal" title="ĐĂNG KÝ">
                                                <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            </state>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                    <real key="value" value="4"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="color" keyPath="firstColor">
                                                    <color key="value" red="0.99215686270000003" green="0.15686274510000001" blue="0.0078431372550000003" alpha="0.97999999999999998" colorSpace="custom" customColorSpace="sRGB"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="color" keyPath="secondColor">
                                                    <color key="value" red="0.99215686270000003" green="0.48627450979999998" blue="0.0078431372550000003" alpha="0.98999999999999999" colorSpace="custom" customColorSpace="sRGB"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Bt.REGISTER"/>
                                            </userDefinedRuntimeAttributes>
                                            <connections>
                                                <action selector="didTouchRegisterVoucher:" destination="iJz-jB-uny" eventType="touchUpInside" id="gZ5-7U-Qns"/>
                                            </connections>
                                        </button>
                                    </subviews>
                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <constraints>
                                        <constraint firstItem="cw7-RS-4Ra" firstAttribute="top" secondItem="Jgh-5J-yZR" secondAttribute="bottom" constant="8" id="9Uh-nz-O2z"/>
                                        <constraint firstItem="Jgh-5J-yZR" firstAttribute="leading" secondItem="zYx-nT-yP6" secondAttribute="leading" constant="20" id="Axd-oB-akS"/>
                                        <constraint firstItem="Jhe-pc-vdO" firstAttribute="top" secondItem="eb9-hy-Fjq" secondAttribute="top" id="D7j-x9-yN6"/>
                                        <constraint firstAttribute="trailing" secondItem="Jgh-5J-yZR" secondAttribute="trailing" constant="20" id="Gys-gF-AwM"/>
                                        <constraint firstItem="db9-T7-HiE" firstAttribute="top" secondItem="X47-LK-cf0" secondAttribute="bottom" constant="8" id="LR3-Yi-JG7"/>
                                        <constraint firstItem="DZU-zS-zga" firstAttribute="leading" secondItem="zYx-nT-yP6" secondAttribute="leading" constant="8" id="NJC-wg-Lgi"/>
                                        <constraint firstItem="Jhe-pc-vdO" firstAttribute="trailing" secondItem="eb9-hy-Fjq" secondAttribute="trailing" id="NNS-jo-142"/>
                                        <constraint firstAttribute="trailing" secondItem="DZU-zS-zga" secondAttribute="trailing" constant="8" id="Srs-j0-eHp"/>
                                        <constraint firstAttribute="trailing" secondItem="Jhe-pc-vdO" secondAttribute="trailing" constant="20" id="TAH-pl-Nko"/>
                                        <constraint firstItem="Jgh-5J-yZR" firstAttribute="top" secondItem="db9-T7-HiE" secondAttribute="bottom" constant="16" id="UWK-TY-P8S"/>
                                        <constraint firstItem="Jhe-pc-vdO" firstAttribute="leading" secondItem="zYx-nT-yP6" secondAttribute="leading" constant="20" id="aYK-cQ-zcT"/>
                                        <constraint firstItem="DZU-zS-zga" firstAttribute="top" secondItem="zYx-nT-yP6" secondAttribute="top" constant="22" id="bnp-HH-Ihj"/>
                                        <constraint firstItem="X47-LK-cf0" firstAttribute="top" secondItem="DZU-zS-zga" secondAttribute="bottom" constant="18" id="c78-Gi-xbe"/>
                                        <constraint firstItem="Jhe-pc-vdO" firstAttribute="top" secondItem="cw7-RS-4Ra" secondAttribute="bottom" constant="24" id="exY-4B-xYK"/>
                                        <constraint firstItem="cw7-RS-4Ra" firstAttribute="leading" secondItem="zYx-nT-yP6" secondAttribute="leading" constant="20" id="f3u-Iv-8iD"/>
                                        <constraint firstItem="Jhe-pc-vdO" firstAttribute="leading" secondItem="eb9-hy-Fjq" secondAttribute="leading" id="ksM-OO-LFL"/>
                                        <constraint firstItem="db9-T7-HiE" firstAttribute="leading" secondItem="zYx-nT-yP6" secondAttribute="leading" constant="20" id="m3o-AP-Xcd"/>
                                        <constraint firstItem="Jhe-pc-vdO" firstAttribute="bottom" secondItem="eb9-hy-Fjq" secondAttribute="bottom" id="mDB-jw-UbZ"/>
                                        <constraint firstItem="X47-LK-cf0" firstAttribute="leading" secondItem="zYx-nT-yP6" secondAttribute="leading" constant="20" id="mMx-cH-xe3"/>
                                        <constraint firstAttribute="trailing" secondItem="db9-T7-HiE" secondAttribute="trailing" constant="20" id="t2i-Vq-soe"/>
                                        <constraint firstAttribute="bottom" secondItem="Jhe-pc-vdO" secondAttribute="bottom" constant="20" id="wBg-tR-lNE"/>
                                        <constraint firstAttribute="trailing" secondItem="cw7-RS-4Ra" secondAttribute="trailing" constant="20" id="wW5-LL-Mrd"/>
                                        <constraint firstItem="cw7-RS-4Ra" firstAttribute="top" secondItem="Jgh-5J-yZR" secondAttribute="bottom" constant="8" id="yWR-9W-SJs"/>
                                    </constraints>
                                </view>
                                <connections>
                                    <outlet property="dataSource" destination="iJz-jB-uny" id="LZg-aT-uJn"/>
                                    <outlet property="delegate" destination="iJz-jB-uny" id="Usc-61-sxH"/>
                                </connections>
                            </tableView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="Qby-ZH-M5k"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="PaK-Qf-o6k" firstAttribute="leading" secondItem="Qby-ZH-M5k" secondAttribute="leading" id="Hti-X8-Wxq"/>
                            <constraint firstItem="Qby-ZH-M5k" firstAttribute="bottom" secondItem="PaK-Qf-o6k" secondAttribute="bottom" id="cUv-ge-0iK"/>
                            <constraint firstAttribute="trailing" secondItem="PaK-Qf-o6k" secondAttribute="trailing" id="gR3-np-bhb"/>
                            <constraint firstItem="PaK-Qf-o6k" firstAttribute="top" secondItem="Qby-ZH-M5k" secondAttribute="top" id="z3e-KZ-0J6"/>
                        </constraints>
                    </view>
                    <size key="freeformSize" width="375" height="800"/>
                    <connections>
                        <outlet property="headerView" destination="zYx-nT-yP6" id="y6C-p8-X5U"/>
                        <outlet property="tableView" destination="PaK-Qf-o6k" id="wpp-64-3hh"/>
                        <outlet property="tfPinCode" destination="cw7-RS-4Ra" id="Xb4-8m-w2R"/>
                        <outlet property="tfVoucherCode" destination="db9-T7-HiE" id="Ans-mo-E7n"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="tGm-Fg-WnN" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="3405.5999999999999" y="-65.667166416791616"/>
        </scene>
        <!--List Film Watched View Controller-->
        <scene sceneID="JD3-0t-D2s">
            <objects>
                <viewController storyboardIdentifier="ListFilmWatchedViewController" id="DzS-b4-NKf" customClass="ListFilmWatchedViewController" customModule="Beta_Cinemas" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="raI-w7-SkZ">
                        <rect key="frame" x="0.0" y="0.0" width="320" height="568"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <viewLayoutGuide key="safeArea" id="wp6-Gy-xHc"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Jf1-5q-dkh" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="3498" y="-788"/>
        </scene>
        <!--Tranfer History View Controller-->
        <scene sceneID="bTH-Km-CgV">
            <objects>
                <viewController storyboardIdentifier="TranferHistoryViewController" id="99m-f3-625" customClass="TranferHistoryViewController" customModule="Beta_Cinemas" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="n3i-ot-fFo">
                        <rect key="frame" x="0.0" y="0.0" width="320" height="568"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="qcG-MF-elv">
                                <rect key="frame" x="0.0" y="20" width="320" height="548"/>
                                <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="calibratedRGB"/>
                                <view key="tableHeaderView" contentMode="scaleToFill" id="DQM-wd-Igw">
                                    <rect key="frame" x="0.0" y="0.0" width="320" height="110"/>
                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                    <subviews>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Hiển thị giao dịch trong 3 tháng gần nhất. Vui lòng truy cập website để xem toàn bộ lịch sử giao dịch" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="3" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="XbY-05-TIO" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                            <rect key="frame" x="20" y="20" width="280" height="70"/>
                                            <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                            <color key="textColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            <nil key="highlightedColor"/>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="TransactionHistory.GuideText"/>
                                            </userDefinedRuntimeAttributes>
                                        </label>
                                    </subviews>
                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <constraints>
                                        <constraint firstItem="XbY-05-TIO" firstAttribute="top" secondItem="DQM-wd-Igw" secondAttribute="top" constant="20" id="19j-gm-udb"/>
                                        <constraint firstItem="XbY-05-TIO" firstAttribute="leading" secondItem="DQM-wd-Igw" secondAttribute="leading" constant="20" id="CL1-ZN-G9v"/>
                                        <constraint firstAttribute="bottom" secondItem="XbY-05-TIO" secondAttribute="bottom" constant="20" id="ezP-Wi-Kv3"/>
                                        <constraint firstAttribute="trailing" secondItem="XbY-05-TIO" secondAttribute="trailing" constant="20" id="uVO-6U-lF6"/>
                                    </constraints>
                                </view>
                                <prototypes>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" reuseIdentifier="TransactionHistoryCell" rowHeight="210" id="rjh-2T-eSh" customClass="TransactionHistoryCell" customModule="Beta_Cinemas" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="160" width="320" height="210"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="rjh-2T-eSh" id="Zwg-Qb-h8g">
                                            <rect key="frame" x="0.0" y="0.0" width="320" height="210"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="nyn-vL-bZf">
                                                    <rect key="frame" x="8" y="3" width="304" height="204"/>
                                                    <subviews>
                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Eqw-LG-sfG" customClass="RoundView" customModule="Beta_Cinemas" customModuleProvider="target">
                                                            <rect key="frame" x="8" y="5" width="288" height="194"/>
                                                            <subviews>
                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Wdi-pn-qP1">
                                                                    <rect key="frame" x="0.0" y="0.0" width="179.5" height="194"/>
                                                                    <subviews>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" text="Pacific Rim: Trỗi Dậy" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="IBI-Ba-Mxj">
                                                                            <rect key="frame" x="12" y="12" width="155.5" height="120"/>
                                                                            <fontDescription key="fontDescription" name="Oswald-Bold" family="Oswald" pointSize="18"/>
                                                                            <color key="textColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="27/03/ 2018  |  7:00 PM" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="BbP-lu-DFJ">
                                                                            <rect key="frame" x="12" y="144" width="155.5" height="19"/>
                                                                            <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                                            <color key="textColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Beta Cinemas Mỹ Đình" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="yDd-xp-Q36">
                                                                            <rect key="frame" x="12" y="163" width="155.5" height="19"/>
                                                                            <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                                            <color key="textColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                    </subviews>
                                                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                    <constraints>
                                                                        <constraint firstItem="yDd-xp-Q36" firstAttribute="leading" secondItem="Wdi-pn-qP1" secondAttribute="leading" constant="12" id="05o-RI-KbR"/>
                                                                        <constraint firstItem="BbP-lu-DFJ" firstAttribute="top" secondItem="IBI-Ba-Mxj" secondAttribute="bottom" constant="12" id="1ct-NE-zbe"/>
                                                                        <constraint firstAttribute="trailing" secondItem="IBI-Ba-Mxj" secondAttribute="trailing" constant="12" id="8Mq-cG-6Jp"/>
                                                                        <constraint firstItem="BbP-lu-DFJ" firstAttribute="leading" secondItem="Wdi-pn-qP1" secondAttribute="leading" constant="12" id="LTP-sc-7f4"/>
                                                                        <constraint firstAttribute="trailing" secondItem="BbP-lu-DFJ" secondAttribute="trailing" constant="12" id="N8W-gO-Jjy"/>
                                                                        <constraint firstAttribute="trailing" secondItem="yDd-xp-Q36" secondAttribute="trailing" constant="12" id="W6u-pB-ULW"/>
                                                                        <constraint firstItem="IBI-Ba-Mxj" firstAttribute="leading" secondItem="Wdi-pn-qP1" secondAttribute="leading" constant="12" id="fQe-wL-mpC"/>
                                                                        <constraint firstItem="IBI-Ba-Mxj" firstAttribute="top" secondItem="Wdi-pn-qP1" secondAttribute="top" constant="12" id="jPx-2n-H4M"/>
                                                                        <constraint firstAttribute="bottom" secondItem="yDd-xp-Q36" secondAttribute="bottom" constant="12" id="v67-kv-m7e"/>
                                                                        <constraint firstItem="yDd-xp-Q36" firstAttribute="top" secondItem="BbP-lu-DFJ" secondAttribute="bottom" id="xQg-2y-qRA"/>
                                                                    </constraints>
                                                                </view>
                                                                <view alpha="0.20000000000000001" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="bb7-8s-IWD">
                                                                    <rect key="frame" x="179.5" y="0.0" width="1" height="194"/>
                                                                    <color key="backgroundColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="width" constant="1" id="pFO-yA-DqJ"/>
                                                                    </constraints>
                                                                </view>
                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="h7c-Bx-fIM">
                                                                    <rect key="frame" x="180.5" y="0.0" width="107.5" height="194"/>
                                                                    <subviews>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="253" text="392.000đ" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" minimumScaleFactor="0.5" translatesAutoresizingMaskIntoConstraints="NO" id="v1l-db-pA8">
                                                                            <rect key="frame" x="10" y="12" width="87.5" height="26"/>
                                                                            <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="22"/>
                                                                            <color key="textColor" red="0.0**********" green="0.34901960780000002" blue="0.61568627450000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Điểm tích luỹ" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="4hx-gG-gA8" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                                                            <rect key="frame" x="10" y="48" width="87.5" height="16.5"/>
                                                                            <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="14"/>
                                                                            <color key="textColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                            <nil key="highlightedColor"/>
                                                                            <userDefinedRuntimeAttributes>
                                                                                <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="TransactionHistory.Point"/>
                                                                            </userDefinedRuntimeAttributes>
                                                                        </label>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="109" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="8wD-Df-cH0">
                                                                            <rect key="frame" x="10" y="64.5" width="87.5" height="19"/>
                                                                            <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                                            <color key="textColor" red="0.99215686270000003" green="0.15686274510000001" blue="0.0078431372550000003" alpha="0.97999999999999998" colorSpace="custom" customColorSpace="sRGB"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Thời hạn của điểm" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="UED-Zi-1c5" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                                                            <rect key="frame" x="10" y="93.5" width="87.5" height="16.5"/>
                                                                            <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="14"/>
                                                                            <color key="textColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                            <nil key="highlightedColor"/>
                                                                            <userDefinedRuntimeAttributes>
                                                                                <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="TransactionHistory.DatePoint"/>
                                                                            </userDefinedRuntimeAttributes>
                                                                        </label>
                                                                        <view alpha="0.10000000000000001" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="a0B-fg-D58" customClass="RoundView" customModule="Beta_Cinemas" customModuleProvider="target">
                                                                            <rect key="frame" x="8" y="46" width="91.5" height="24"/>
                                                                            <color key="backgroundColor" red="0.99215686274509807" green="0.48627450980392156" blue="0.0078431372549019607" alpha="1" colorSpace="calibratedRGB"/>
                                                                            <constraints>
                                                                                <constraint firstAttribute="height" constant="24" id="g82-1k-llr"/>
                                                                            </constraints>
                                                                            <userDefinedRuntimeAttributes>
                                                                                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                                                    <real key="value" value="3"/>
                                                                                </userDefinedRuntimeAttribute>
                                                                            </userDefinedRuntimeAttributes>
                                                                        </view>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="1LP-4c-Tge">
                                                                            <rect key="frame" x="8" y="51" width="91.5" height="14"/>
                                                                            <fontDescription key="fontDescription" name="SourceSansPro-SemiBold" family="Source Sans Pro" pointSize="12"/>
                                                                            <color key="textColor" red="0.99215686274509807" green="0.48627450980392156" blue="0.0078431372549019607" alpha="1" colorSpace="calibratedRGB"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="20/10/2023" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="SWl-wx-iBa">
                                                                            <rect key="frame" x="10" y="110" width="87.5" height="19"/>
                                                                            <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                                            <color key="textColor" red="1" green="0.20000000000000001" blue="0.46666666666666667" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                    </subviews>
                                                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                    <constraints>
                                                                        <constraint firstItem="SWl-wx-iBa" firstAttribute="top" secondItem="UED-Zi-1c5" secondAttribute="bottom" id="0lK-9Y-NR4"/>
                                                                        <constraint firstAttribute="trailing" secondItem="v1l-db-pA8" secondAttribute="trailing" constant="10" id="134-Aq-rns"/>
                                                                        <constraint firstItem="a0B-fg-D58" firstAttribute="leading" secondItem="h7c-Bx-fIM" secondAttribute="leading" constant="8" id="8Wa-74-laN"/>
                                                                        <constraint firstItem="UED-Zi-1c5" firstAttribute="top" secondItem="8wD-Df-cH0" secondAttribute="bottom" constant="10" id="95M-cQ-zFj"/>
                                                                        <constraint firstAttribute="trailing" secondItem="SWl-wx-iBa" secondAttribute="trailing" constant="10" id="9En-IZ-2wb"/>
                                                                        <constraint firstItem="1LP-4c-Tge" firstAttribute="leading" secondItem="a0B-fg-D58" secondAttribute="leading" id="QJG-BI-B1Y"/>
                                                                        <constraint firstAttribute="trailing" secondItem="UED-Zi-1c5" secondAttribute="trailing" constant="10" id="R6q-ET-1U2"/>
                                                                        <constraint firstItem="4hx-gG-gA8" firstAttribute="leading" secondItem="h7c-Bx-fIM" secondAttribute="leading" constant="10" id="Sgz-TX-Kcu"/>
                                                                        <constraint firstItem="SWl-wx-iBa" firstAttribute="leading" secondItem="h7c-Bx-fIM" secondAttribute="leading" constant="10" id="U29-VA-Rf6"/>
                                                                        <constraint firstItem="8wD-Df-cH0" firstAttribute="top" secondItem="4hx-gG-gA8" secondAttribute="bottom" id="V9k-QG-UPm"/>
                                                                        <constraint firstItem="4hx-gG-gA8" firstAttribute="top" secondItem="v1l-db-pA8" secondAttribute="bottom" constant="10" id="VZ7-OO-mbD"/>
                                                                        <constraint firstItem="a0B-fg-D58" firstAttribute="top" secondItem="v1l-db-pA8" secondAttribute="bottom" constant="8" id="XyU-Mp-kVl"/>
                                                                        <constraint firstItem="1LP-4c-Tge" firstAttribute="centerY" secondItem="a0B-fg-D58" secondAttribute="centerY" id="bbs-cj-1Eb"/>
                                                                        <constraint firstAttribute="trailing" secondItem="4hx-gG-gA8" secondAttribute="trailing" constant="10" id="cER-gR-AtV"/>
                                                                        <constraint firstAttribute="bottom" relation="greaterThanOrEqual" secondItem="SWl-wx-iBa" secondAttribute="bottom" constant="10" id="gte-pt-Gba"/>
                                                                        <constraint firstItem="1LP-4c-Tge" firstAttribute="centerX" secondItem="a0B-fg-D58" secondAttribute="centerX" id="jEI-Ta-HgL"/>
                                                                        <constraint firstItem="8wD-Df-cH0" firstAttribute="leading" secondItem="h7c-Bx-fIM" secondAttribute="leading" constant="10" id="jTU-q7-2wi"/>
                                                                        <constraint firstAttribute="trailing" secondItem="a0B-fg-D58" secondAttribute="trailing" constant="8" id="ksB-yJ-KUb"/>
                                                                        <constraint firstItem="v1l-db-pA8" firstAttribute="top" secondItem="h7c-Bx-fIM" secondAttribute="top" constant="12" id="nIb-WI-lYZ"/>
                                                                        <constraint firstItem="UED-Zi-1c5" firstAttribute="leading" secondItem="h7c-Bx-fIM" secondAttribute="leading" constant="10" id="nJb-zK-bw5"/>
                                                                        <constraint firstItem="v1l-db-pA8" firstAttribute="leading" secondItem="h7c-Bx-fIM" secondAttribute="leading" constant="10" id="sIO-dY-WwD"/>
                                                                        <constraint firstAttribute="trailing" secondItem="8wD-Df-cH0" secondAttribute="trailing" constant="10" id="sjm-aT-3Ni"/>
                                                                    </constraints>
                                                                </view>
                                                            </subviews>
                                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <constraints>
                                                                <constraint firstItem="Wdi-pn-qP1" firstAttribute="top" secondItem="Eqw-LG-sfG" secondAttribute="top" id="0lM-oT-YGw"/>
                                                                <constraint firstItem="bb7-8s-IWD" firstAttribute="top" secondItem="Eqw-LG-sfG" secondAttribute="top" id="9YO-xy-5Sh"/>
                                                                <constraint firstItem="h7c-Bx-fIM" firstAttribute="leading" secondItem="bb7-8s-IWD" secondAttribute="trailing" id="KJd-U8-2LW"/>
                                                                <constraint firstItem="h7c-Bx-fIM" firstAttribute="width" secondItem="Wdi-pn-qP1" secondAttribute="width" multiplier="0.6" id="X6i-Tg-S7E"/>
                                                                <constraint firstAttribute="trailing" secondItem="h7c-Bx-fIM" secondAttribute="trailing" id="bIu-hM-vUO"/>
                                                                <constraint firstAttribute="bottom" secondItem="h7c-Bx-fIM" secondAttribute="bottom" id="erJ-Rt-le8"/>
                                                                <constraint firstAttribute="bottom" secondItem="Wdi-pn-qP1" secondAttribute="bottom" id="hfZ-EG-wNQ"/>
                                                                <constraint firstItem="h7c-Bx-fIM" firstAttribute="top" secondItem="Eqw-LG-sfG" secondAttribute="top" id="ivg-5a-m6q"/>
                                                                <constraint firstAttribute="bottom" secondItem="bb7-8s-IWD" secondAttribute="bottom" id="izZ-YU-Bcp"/>
                                                                <constraint firstItem="bb7-8s-IWD" firstAttribute="leading" secondItem="Wdi-pn-qP1" secondAttribute="trailing" id="lJY-mt-BtW"/>
                                                                <constraint firstItem="Wdi-pn-qP1" firstAttribute="leading" secondItem="Eqw-LG-sfG" secondAttribute="leading" id="o4b-Kq-Wj8"/>
                                                            </constraints>
                                                            <userDefinedRuntimeAttributes>
                                                                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                                    <real key="value" value="4"/>
                                                                </userDefinedRuntimeAttribute>
                                                                <userDefinedRuntimeAttribute type="point" keyPath="shadowOffset">
                                                                    <point key="value" x="0.0" y="2"/>
                                                                </userDefinedRuntimeAttribute>
                                                                <userDefinedRuntimeAttribute type="number" keyPath="shadowRadius">
                                                                    <real key="value" value="6"/>
                                                                </userDefinedRuntimeAttribute>
                                                                <userDefinedRuntimeAttribute type="number" keyPath="shadowOpacity">
                                                                    <real key="value" value="0.10000000000000001"/>
                                                                </userDefinedRuntimeAttribute>
                                                                <userDefinedRuntimeAttribute type="color" keyPath="shadowColor">
                                                                    <color key="value" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                </userDefinedRuntimeAttribute>
                                                            </userDefinedRuntimeAttributes>
                                                        </view>
                                                    </subviews>
                                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <constraints>
                                                        <constraint firstAttribute="trailing" secondItem="Eqw-LG-sfG" secondAttribute="trailing" constant="8" id="8wq-pl-P5l"/>
                                                        <constraint firstItem="Eqw-LG-sfG" firstAttribute="top" secondItem="nyn-vL-bZf" secondAttribute="top" constant="5" id="HKn-Wq-g8e"/>
                                                        <constraint firstAttribute="bottom" secondItem="Eqw-LG-sfG" secondAttribute="bottom" constant="5" id="eJo-ww-xAv"/>
                                                        <constraint firstItem="Eqw-LG-sfG" firstAttribute="leading" secondItem="nyn-vL-bZf" secondAttribute="leading" constant="8" id="q6v-1w-Msu"/>
                                                    </constraints>
                                                </view>
                                            </subviews>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstItem="nyn-vL-bZf" firstAttribute="leading" secondItem="Zwg-Qb-h8g" secondAttribute="leadingMargin" constant="-8" id="7ZQ-GI-Ozp"/>
                                                <constraint firstAttribute="bottomMargin" secondItem="nyn-vL-bZf" secondAttribute="bottom" constant="-8" id="9Ja-Mo-2HJ"/>
                                                <constraint firstAttribute="trailingMargin" secondItem="nyn-vL-bZf" secondAttribute="trailing" constant="-8" id="VKV-sU-OeF"/>
                                                <constraint firstItem="nyn-vL-bZf" firstAttribute="top" secondItem="Zwg-Qb-h8g" secondAttribute="topMargin" constant="-8" id="nPA-D5-Muf"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <edgeInsets key="layoutMargins" top="0.0" left="0.0" bottom="0.0" right="0.0"/>
                                        <connections>
                                            <outlet property="lbDatePoint" destination="SWl-wx-iBa" id="RVA-s3-UQY"/>
                                            <outlet property="lbDatePointTitle" destination="UED-Zi-1c5" id="XQz-ip-AE2"/>
                                            <outlet property="lbFilmCinema" destination="yDd-xp-Q36" id="dc5-lO-s2W"/>
                                            <outlet property="lbFilmDate" destination="BbP-lu-DFJ" id="bWO-R6-phS"/>
                                            <outlet property="lbFilmName" destination="IBI-Ba-Mxj" id="e6W-JU-KVt"/>
                                            <outlet property="lbMoney" destination="v1l-db-pA8" id="EQg-Qk-Ur8"/>
                                            <outlet property="lbPoint" destination="8wD-Df-cH0" id="5s9-kT-rCb"/>
                                            <outlet property="lbPointTitle" destination="4hx-gG-gA8" id="RgH-2O-Wfa"/>
                                            <outlet property="waitingLabel" destination="1LP-4c-Tge" id="k1y-Ng-tvD"/>
                                            <outlet property="waitingView" destination="a0B-fg-D58" id="DKQ-Xh-O3y"/>
                                        </connections>
                                    </tableViewCell>
                                </prototypes>
                                <connections>
                                    <outlet property="dataSource" destination="99m-f3-625" id="IG9-Vv-hDZ"/>
                                    <outlet property="delegate" destination="99m-f3-625" id="1a4-Ne-M7R"/>
                                </connections>
                            </tableView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="cDn-ey-re6"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="qcG-MF-elv" firstAttribute="leading" secondItem="cDn-ey-re6" secondAttribute="leading" id="9UK-EV-ELq"/>
                            <constraint firstItem="cDn-ey-re6" firstAttribute="bottom" secondItem="qcG-MF-elv" secondAttribute="bottom" id="NPn-J3-eF6"/>
                            <constraint firstItem="qcG-MF-elv" firstAttribute="top" secondItem="cDn-ey-re6" secondAttribute="top" id="bKt-j6-e1E"/>
                            <constraint firstItem="cDn-ey-re6" firstAttribute="trailing" secondItem="qcG-MF-elv" secondAttribute="trailing" id="rrY-qC-L5k"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="tableView" destination="qcG-MF-elv" id="cn4-Jf-jcv"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Vhv-nE-t3h" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="4203.75" y="-789.08450704225356"/>
        </scene>
        <!--Transaction Detail View Controller-->
        <scene sceneID="3Ef-wN-ZXH">
            <objects>
                <viewController storyboardIdentifier="TransactionDetailViewController" id="diV-4z-Dyz" customClass="TransactionDetailViewController" customModule="Beta_Cinemas" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="lqY-B9-pxI">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="1000"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Yx1-XY-HcM">
                                <rect key="frame" x="0.0" y="20" width="375" height="980"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="szr-vO-abU">
                                        <rect key="frame" x="8" y="8" width="359" height="1008.5"/>
                                        <subviews>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="9e2-j6-3bm" customClass="RoundView" customModule="Beta_Cinemas" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="0.0" width="359" height="675.5"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Pacific Rim: Trỗi Dậy" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Yod-MT-UwG">
                                                        <rect key="frame" x="12" y="12" width="335" height="48"/>
                                                        <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="24"/>
                                                        <color key="textColor" red="0.**********" green="0.**********" blue="0.15686274510000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="C16  |  2D - LT  |  Võ thuật, Viễn Tưởng  |  135 phút " textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="l7O-iu-tY1">
                                                        <rect key="frame" x="10" y="60" width="339" height="19"/>
                                                        <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                        <color key="textColor" red="0.**********" green="0.**********" blue="0.15686274510000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <view alpha="0.20000000298023224" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="sxb-Uo-Vhd" userLabel="lineView">
                                                        <rect key="frame" x="0.0" y="91" width="359" height="1"/>
                                                        <color key="backgroundColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="1" id="FY6-3s-iMp"/>
                                                        </constraints>
                                                    </view>
                                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="3ES-g7-Z96">
                                                        <rect key="frame" x="0.0" y="100" width="359" height="372"/>
                                                        <subviews>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="xUe-h7-bpr">
                                                                <rect key="frame" x="0.0" y="0.0" width="359" height="50"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Rạp chiếu" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="kj2-CZ-bph" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                                                        <rect key="frame" x="12" y="2" width="110.5" height="19"/>
                                                                        <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                                        <color key="textColor" red="0.**********" green="0.**********" blue="0.15686274510000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                        <nil key="highlightedColor"/>
                                                                        <userDefinedRuntimeAttributes>
                                                                            <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="TransactionDetail.Cinema"/>
                                                                        </userDefinedRuntimeAttributes>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Beta Cinemas Mỹ ĐÌnh" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="pLw-LW-tlj">
                                                                        <rect key="frame" x="126.5" y="2" width="220.5" height="46"/>
                                                                        <fontDescription key="fontDescription" name="HelveticaNeue-Bold" family="Helvetica Neue" pointSize="16"/>
                                                                        <color key="textColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstItem="kj2-CZ-bph" firstAttribute="top" secondItem="xUe-h7-bpr" secondAttribute="top" constant="2" id="Fyv-ly-OfQ"/>
                                                                    <constraint firstAttribute="trailing" secondItem="pLw-LW-tlj" secondAttribute="trailing" constant="12" id="Hnz-kO-Ed1"/>
                                                                    <constraint firstItem="pLw-LW-tlj" firstAttribute="width" secondItem="kj2-CZ-bph" secondAttribute="width" multiplier="2:1" id="Ptp-D4-UfI"/>
                                                                    <constraint firstItem="pLw-LW-tlj" firstAttribute="top" secondItem="xUe-h7-bpr" secondAttribute="top" constant="2" id="WpI-sc-Ezb"/>
                                                                    <constraint firstAttribute="bottom" secondItem="pLw-LW-tlj" secondAttribute="bottom" constant="2" id="abG-JS-Hlc"/>
                                                                    <constraint firstItem="pLw-LW-tlj" firstAttribute="leading" secondItem="kj2-CZ-bph" secondAttribute="trailing" constant="4" id="iC8-La-cHD"/>
                                                                    <constraint firstItem="kj2-CZ-bph" firstAttribute="leading" secondItem="xUe-h7-bpr" secondAttribute="leading" constant="12" id="oxw-7v-vjE"/>
                                                                </constraints>
                                                            </view>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="UXY-Qc-0e0">
                                                                <rect key="frame" x="0.0" y="50" width="359" height="65"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Ngày chiếu" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="9hB-tF-6qD" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                                                        <rect key="frame" x="12" y="2" width="110.5" height="19"/>
                                                                        <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                                        <color key="textColor" red="0.**********" green="0.**********" blue="0.15686274510000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                        <nil key="highlightedColor"/>
                                                                        <userDefinedRuntimeAttributes>
                                                                            <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="TransactionDetail.ShowDate"/>
                                                                        </userDefinedRuntimeAttributes>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="16/03/2018" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="dii-U2-D99">
                                                                        <rect key="frame" x="126.5" y="2" width="220.5" height="61"/>
                                                                        <fontDescription key="fontDescription" name="HelveticaNeue-Bold" family="Helvetica Neue" pointSize="16"/>
                                                                        <color key="textColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstItem="dii-U2-D99" firstAttribute="width" secondItem="9hB-tF-6qD" secondAttribute="width" multiplier="2:1" id="Atc-gT-EKx"/>
                                                                    <constraint firstItem="9hB-tF-6qD" firstAttribute="top" secondItem="UXY-Qc-0e0" secondAttribute="top" constant="2" id="E0X-FL-sHx"/>
                                                                    <constraint firstAttribute="trailing" secondItem="dii-U2-D99" secondAttribute="trailing" constant="12" id="edD-Ut-OpO"/>
                                                                    <constraint firstItem="9hB-tF-6qD" firstAttribute="leading" secondItem="UXY-Qc-0e0" secondAttribute="leading" constant="12" id="mpj-t1-4Xb"/>
                                                                    <constraint firstItem="dii-U2-D99" firstAttribute="top" secondItem="UXY-Qc-0e0" secondAttribute="top" constant="2" id="quN-QO-5ka"/>
                                                                    <constraint firstAttribute="bottom" secondItem="dii-U2-D99" secondAttribute="bottom" constant="2" id="rUL-ih-ag5"/>
                                                                    <constraint firstItem="dii-U2-D99" firstAttribute="leading" secondItem="9hB-tF-6qD" secondAttribute="trailing" constant="4" id="zVx-dT-D8S"/>
                                                                </constraints>
                                                            </view>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="k2l-s6-w9g">
                                                                <rect key="frame" x="0.0" y="115" width="359" height="65"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Giờ chiếu" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="zWB-um-YRt" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                                                        <rect key="frame" x="12" y="2" width="110.5" height="19"/>
                                                                        <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                                        <color key="textColor" red="0.**********" green="0.**********" blue="0.15686274510000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                        <nil key="highlightedColor"/>
                                                                        <userDefinedRuntimeAttributes>
                                                                            <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="TransactionDetail.ShowTime"/>
                                                                        </userDefinedRuntimeAttributes>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="16:45" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="MpA-iI-VMb">
                                                                        <rect key="frame" x="126.5" y="2" width="220.5" height="61"/>
                                                                        <fontDescription key="fontDescription" name="HelveticaNeue-Bold" family="Helvetica Neue" pointSize="16"/>
                                                                        <color key="textColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstItem="MpA-iI-VMb" firstAttribute="top" secondItem="k2l-s6-w9g" secondAttribute="top" constant="2" id="1Pw-bT-H9p"/>
                                                                    <constraint firstItem="zWB-um-YRt" firstAttribute="top" secondItem="k2l-s6-w9g" secondAttribute="top" constant="2" id="8n7-qo-Rne"/>
                                                                    <constraint firstAttribute="bottom" secondItem="MpA-iI-VMb" secondAttribute="bottom" constant="2" id="Gfg-J6-OHy"/>
                                                                    <constraint firstItem="MpA-iI-VMb" firstAttribute="leading" secondItem="zWB-um-YRt" secondAttribute="trailing" constant="4" id="JY4-Kx-tCf"/>
                                                                    <constraint firstItem="zWB-um-YRt" firstAttribute="leading" secondItem="k2l-s6-w9g" secondAttribute="leading" constant="12" id="ZhM-qw-gZt"/>
                                                                    <constraint firstItem="MpA-iI-VMb" firstAttribute="width" secondItem="zWB-um-YRt" secondAttribute="width" multiplier="2:1" id="kF4-mS-1Of"/>
                                                                    <constraint firstAttribute="trailing" secondItem="MpA-iI-VMb" secondAttribute="trailing" constant="12" id="yX7-6h-vWV"/>
                                                                </constraints>
                                                            </view>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="MYI-80-5Eq">
                                                                <rect key="frame" x="0.0" y="180" width="359" height="58"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Phòng chiếu" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="fhn-fH-D4b" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                                                        <rect key="frame" x="12" y="2" width="110.5" height="19"/>
                                                                        <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                                        <color key="textColor" red="0.**********" green="0.**********" blue="0.15686274510000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                        <nil key="highlightedColor"/>
                                                                        <userDefinedRuntimeAttributes>
                                                                            <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="TransactionDetail.ShowRoom"/>
                                                                        </userDefinedRuntimeAttributes>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="MD-SCREEN - 1" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="qUm-CS-a6B">
                                                                        <rect key="frame" x="126.5" y="2" width="220.5" height="54"/>
                                                                        <fontDescription key="fontDescription" name="HelveticaNeue-Bold" family="Helvetica Neue" pointSize="16"/>
                                                                        <color key="textColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="trailing" secondItem="qUm-CS-a6B" secondAttribute="trailing" constant="12" id="Tho-tQ-XEc"/>
                                                                    <constraint firstItem="fhn-fH-D4b" firstAttribute="top" secondItem="MYI-80-5Eq" secondAttribute="top" constant="2" id="cdy-fT-wXO"/>
                                                                    <constraint firstItem="qUm-CS-a6B" firstAttribute="width" secondItem="fhn-fH-D4b" secondAttribute="width" multiplier="2:1" id="eMn-V1-nGM"/>
                                                                    <constraint firstItem="qUm-CS-a6B" firstAttribute="top" secondItem="MYI-80-5Eq" secondAttribute="top" constant="2" id="k79-Mm-eIg"/>
                                                                    <constraint firstAttribute="bottom" secondItem="qUm-CS-a6B" secondAttribute="bottom" constant="2" id="nzx-GD-aO4"/>
                                                                    <constraint firstItem="qUm-CS-a6B" firstAttribute="leading" secondItem="fhn-fH-D4b" secondAttribute="trailing" constant="4" id="uYC-B4-p05"/>
                                                                    <constraint firstItem="fhn-fH-D4b" firstAttribute="leading" secondItem="MYI-80-5Eq" secondAttribute="leading" constant="12" id="vMY-03-CMn"/>
                                                                </constraints>
                                                            </view>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Rth-va-E62">
                                                                <rect key="frame" x="0.0" y="238" width="359" height="61"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Ghế ngồi(2)" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="SzU-4a-UiH" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                                                        <rect key="frame" x="12" y="2" width="110.5" height="19"/>
                                                                        <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                                        <color key="textColor" red="0.**********" green="0.**********" blue="0.15686274510000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="C4, C5" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Ah7-1J-3Wq">
                                                                        <rect key="frame" x="126.5" y="2" width="220.5" height="57"/>
                                                                        <fontDescription key="fontDescription" name="HelveticaNeue-Bold" family="Helvetica Neue" pointSize="16"/>
                                                                        <color key="textColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstItem="Ah7-1J-3Wq" firstAttribute="leading" secondItem="SzU-4a-UiH" secondAttribute="trailing" constant="4" id="72M-bO-k9T"/>
                                                                    <constraint firstAttribute="bottom" secondItem="Ah7-1J-3Wq" secondAttribute="bottom" constant="2" id="7yF-aR-DIz"/>
                                                                    <constraint firstItem="Ah7-1J-3Wq" firstAttribute="top" secondItem="Rth-va-E62" secondAttribute="top" constant="2" id="C2Z-mD-qxh"/>
                                                                    <constraint firstItem="Ah7-1J-3Wq" firstAttribute="width" secondItem="SzU-4a-UiH" secondAttribute="width" multiplier="2:1" id="FSE-iZ-dpn"/>
                                                                    <constraint firstAttribute="trailing" secondItem="Ah7-1J-3Wq" secondAttribute="trailing" constant="12" id="WHL-rC-8Zp"/>
                                                                    <constraint firstItem="SzU-4a-UiH" firstAttribute="top" secondItem="Rth-va-E62" secondAttribute="top" constant="2" id="jaw-gU-DSv"/>
                                                                    <constraint firstItem="SzU-4a-UiH" firstAttribute="leading" secondItem="Rth-va-E62" secondAttribute="leading" constant="12" id="pNW-Yv-Th2"/>
                                                                </constraints>
                                                            </view>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ncD-vV-2B2">
                                                                <rect key="frame" x="0.0" y="299" width="359" height="73"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Combo (3)" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="DG5-rH-8cv" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                                                        <rect key="frame" x="12" y="2" width="110.5" height="19"/>
                                                                        <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                                        <color key="textColor" red="0.**********" green="0.**********" blue="0.15686274510000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="VIP Box-Beta 440z combo: x2" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="VzX-p0-EVs">
                                                                        <rect key="frame" x="126.5" y="2" width="220.5" height="69"/>
                                                                        <fontDescription key="fontDescription" name="HelveticaNeue-Bold" family="Helvetica Neue" pointSize="16"/>
                                                                        <color key="textColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstItem="DG5-rH-8cv" firstAttribute="top" secondItem="ncD-vV-2B2" secondAttribute="top" constant="2" id="BrU-pn-dbc"/>
                                                                    <constraint firstAttribute="bottom" secondItem="VzX-p0-EVs" secondAttribute="bottom" constant="2" id="k1b-Hc-Rwh"/>
                                                                    <constraint firstAttribute="trailing" secondItem="VzX-p0-EVs" secondAttribute="trailing" constant="12" id="m4q-Dh-uw4"/>
                                                                    <constraint firstItem="VzX-p0-EVs" firstAttribute="top" secondItem="ncD-vV-2B2" secondAttribute="top" constant="2" id="nTX-jb-mAA"/>
                                                                    <constraint firstItem="DG5-rH-8cv" firstAttribute="leading" secondItem="ncD-vV-2B2" secondAttribute="leading" constant="12" id="sgE-z1-PLB"/>
                                                                    <constraint firstItem="VzX-p0-EVs" firstAttribute="leading" secondItem="DG5-rH-8cv" secondAttribute="trailing" constant="4" id="tiN-xy-P91"/>
                                                                    <constraint firstItem="VzX-p0-EVs" firstAttribute="width" secondItem="DG5-rH-8cv" secondAttribute="width" multiplier="2:1" id="v31-YR-FlD"/>
                                                                </constraints>
                                                            </view>
                                                        </subviews>
                                                    </stackView>
                                                    <view alpha="0.20000000298023224" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="eAz-4D-hLG" userLabel="lineView1">
                                                        <rect key="frame" x="0.0" y="497" width="359" height="1"/>
                                                        <color key="backgroundColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                        <constraints>
                                                            <constraint firstAttribute="height" constant="1" id="7dm-Kc-eyl"/>
                                                        </constraints>
                                                    </view>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="4Gi-JQ-fnL">
                                                        <rect key="frame" x="0.0" y="498" width="359" height="177.5"/>
                                                        <subviews>
                                                            <view alpha="0.20000000298023224" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="uPc-1F-dD0" userLabel="line">
                                                                <rect key="frame" x="213.5" y="0.0" width="1" height="177.5"/>
                                                                <color key="backgroundColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" constant="1" id="M2J-ds-5CB"/>
                                                                </constraints>
                                                            </view>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="mnz-u6-3zD">
                                                                <rect key="frame" x="214.5" y="10" width="144.5" height="157.5"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Tổng tiền" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="C1i-WT-uNf" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                                                        <rect key="frame" x="10" y="5" width="124.5" height="19"/>
                                                                        <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                                        <color key="textColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                        <nil key="highlightedColor"/>
                                                                        <userDefinedRuntimeAttributes>
                                                                            <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="TransactionDetail.TotalMoney"/>
                                                                        </userDefinedRuntimeAttributes>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="180.000đ" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="YKM-aY-LZ5">
                                                                        <rect key="frame" x="10" y="24" width="124.5" height="23.5"/>
                                                                        <fontDescription key="fontDescription" name="SourceSansPro-Bold" family="Source Sans Pro" pointSize="20"/>
                                                                        <color key="textColor" red="0.99215686270000003" green="0.15686274510000001" blue="0.0078431372550000003" alpha="0.97999999999999998" colorSpace="custom" customColorSpace="sRGB"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Điểm tích luỹ" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="eZF-L4-TzI" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                                                        <rect key="frame" x="10" y="57.5" width="124.5" height="19"/>
                                                                        <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                                        <color key="textColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                        <nil key="highlightedColor"/>
                                                                        <userDefinedRuntimeAttributes>
                                                                            <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="TransactionHistory.Point"/>
                                                                        </userDefinedRuntimeAttributes>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="12345" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="kui-Ka-jFy">
                                                                        <rect key="frame" x="10" y="76.5" width="124.5" height="23.5"/>
                                                                        <fontDescription key="fontDescription" name="SourceSansPro-Bold" family="Source Sans Pro" pointSize="20"/>
                                                                        <color key="textColor" red="0.0**********2352941" green="0.34901960784313724" blue="0.61568627450980395" alpha="0.97999999999999998" colorSpace="custom" customColorSpace="sRGB"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Thời hạn của điểm" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="2GM-rC-kMz" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                                                        <rect key="frame" x="5" y="110" width="134.5" height="19"/>
                                                                        <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                                        <color key="textColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                        <nil key="highlightedColor"/>
                                                                        <userDefinedRuntimeAttributes>
                                                                            <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="TransactionHistory.DatePoint"/>
                                                                        </userDefinedRuntimeAttributes>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="20/10/2023" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="iTK-ph-3cG">
                                                                        <rect key="frame" x="10" y="129" width="124.5" height="23.5"/>
                                                                        <fontDescription key="fontDescription" name="SourceSansPro-Bold" family="Source Sans Pro" pointSize="20"/>
                                                                        <color key="textColor" red="1" green="0.20000000000000001" blue="0.46666666666666667" alpha="0.97999999999999998" colorSpace="custom" customColorSpace="sRGB"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstItem="C1i-WT-uNf" firstAttribute="leading" secondItem="mnz-u6-3zD" secondAttribute="leading" constant="10" id="AVk-aI-hu5"/>
                                                                    <constraint firstAttribute="trailing" secondItem="iTK-ph-3cG" secondAttribute="trailing" constant="10" id="BDk-9g-MVG"/>
                                                                    <constraint firstItem="YKM-aY-LZ5" firstAttribute="leading" secondItem="mnz-u6-3zD" secondAttribute="leading" constant="10" id="I6t-PW-MBT"/>
                                                                    <constraint firstAttribute="trailing" secondItem="2GM-rC-kMz" secondAttribute="trailing" constant="5" id="NnF-wN-Yok"/>
                                                                    <constraint firstAttribute="trailing" secondItem="C1i-WT-uNf" secondAttribute="trailing" constant="10" id="Obo-ag-oL5"/>
                                                                    <constraint firstAttribute="bottom" secondItem="iTK-ph-3cG" secondAttribute="bottom" constant="5" id="U20-f0-Dfh"/>
                                                                    <constraint firstAttribute="trailing" secondItem="YKM-aY-LZ5" secondAttribute="trailing" constant="10" id="ZOM-m2-ean"/>
                                                                    <constraint firstItem="2GM-rC-kMz" firstAttribute="leading" secondItem="mnz-u6-3zD" secondAttribute="leading" constant="5" id="bKK-Bj-KKq"/>
                                                                    <constraint firstItem="eZF-L4-TzI" firstAttribute="top" secondItem="YKM-aY-LZ5" secondAttribute="bottom" constant="10" id="d3G-BQ-MAV"/>
                                                                    <constraint firstItem="kui-Ka-jFy" firstAttribute="top" secondItem="eZF-L4-TzI" secondAttribute="bottom" id="eYc-f6-86E"/>
                                                                    <constraint firstItem="eZF-L4-TzI" firstAttribute="leading" secondItem="mnz-u6-3zD" secondAttribute="leading" constant="10" id="f60-yt-Zhn"/>
                                                                    <constraint firstAttribute="trailing" secondItem="eZF-L4-TzI" secondAttribute="trailing" constant="10" id="gzS-Ty-gm8"/>
                                                                    <constraint firstItem="C1i-WT-uNf" firstAttribute="top" secondItem="mnz-u6-3zD" secondAttribute="top" constant="5" id="hLn-k6-94V"/>
                                                                    <constraint firstAttribute="trailing" secondItem="kui-Ka-jFy" secondAttribute="trailing" constant="10" id="j48-7s-rcS"/>
                                                                    <constraint firstItem="kui-Ka-jFy" firstAttribute="leading" secondItem="mnz-u6-3zD" secondAttribute="leading" constant="10" id="k3x-y4-Idv"/>
                                                                    <constraint firstItem="YKM-aY-LZ5" firstAttribute="top" secondItem="C1i-WT-uNf" secondAttribute="bottom" id="nuX-Ge-hsZ"/>
                                                                    <constraint firstItem="iTK-ph-3cG" firstAttribute="leading" secondItem="mnz-u6-3zD" secondAttribute="leading" constant="10" id="uXz-zx-7sl"/>
                                                                    <constraint firstItem="2GM-rC-kMz" firstAttribute="top" secondItem="kui-Ka-jFy" secondAttribute="bottom" constant="10" id="w1y-M4-2QI"/>
                                                                    <constraint firstItem="iTK-ph-3cG" firstAttribute="top" secondItem="2GM-rC-kMz" secondAttribute="bottom" id="yEX-Eh-PYu"/>
                                                                </constraints>
                                                            </view>
                                                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="default" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="-1" estimatedSectionHeaderHeight="-1" sectionFooterHeight="-1" estimatedSectionFooterHeight="-1" translatesAutoresizingMaskIntoConstraints="NO" id="twg-h8-JMf">
                                                                <rect key="frame" x="0.0" y="0.0" width="213.5" height="177.5"/>
                                                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                                                <prototypes>
                                                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" reuseIdentifier="TransactionDetailCell" id="DAh-N1-cLN" customClass="TransactionDetailCell" customModule="Beta_Cinemas" customModuleProvider="target">
                                                                        <rect key="frame" x="0.0" y="50" width="213.5" height="23.5"/>
                                                                        <autoresizingMask key="autoresizingMask"/>
                                                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="DAh-N1-cLN" id="myV-Tl-Vo9">
                                                                            <rect key="frame" x="0.0" y="0.0" width="213.5" height="23.5"/>
                                                                            <autoresizingMask key="autoresizingMask"/>
                                                                            <subviews>
                                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Shopee Pay" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Zdv-yZ-JQO" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                                                                    <rect key="frame" x="12" y="2" width="90" height="19.5"/>
                                                                                    <constraints>
                                                                                        <constraint firstAttribute="width" constant="90" id="fXg-Gs-502"/>
                                                                                    </constraints>
                                                                                    <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                                                    <color key="textColor" red="0.**********" green="0.**********" blue="0.15686274510000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                                    <nil key="highlightedColor"/>
                                                                                </label>
                                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="10.000.000đ" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Q1r-dT-n9q">
                                                                                    <rect key="frame" x="112" y="0.0" width="90" height="23.5"/>
                                                                                    <fontDescription key="fontDescription" name="HelveticaNeue-Bold" family="Helvetica Neue" pointSize="16"/>
                                                                                    <color key="textColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                                    <nil key="highlightedColor"/>
                                                                                </label>
                                                                            </subviews>
                                                                            <constraints>
                                                                                <constraint firstItem="Q1r-dT-n9q" firstAttribute="top" secondItem="myV-Tl-Vo9" secondAttribute="top" id="1CA-yc-0aV"/>
                                                                                <constraint firstAttribute="bottom" secondItem="Zdv-yZ-JQO" secondAttribute="bottom" constant="2" id="6In-TN-n3f"/>
                                                                                <constraint firstAttribute="trailing" relation="greaterThanOrEqual" secondItem="Q1r-dT-n9q" secondAttribute="trailing" constant="5" id="7CI-jx-RbI"/>
                                                                                <constraint firstItem="Zdv-yZ-JQO" firstAttribute="top" secondItem="myV-Tl-Vo9" secondAttribute="top" constant="2" id="GJg-XP-dMc"/>
                                                                                <constraint firstAttribute="bottom" secondItem="Q1r-dT-n9q" secondAttribute="bottom" id="Nu2-0l-Olb"/>
                                                                                <constraint firstItem="Q1r-dT-n9q" firstAttribute="leading" secondItem="Zdv-yZ-JQO" secondAttribute="trailing" constant="10" id="xz3-Zi-kbp"/>
                                                                                <constraint firstItem="Zdv-yZ-JQO" firstAttribute="leading" secondItem="myV-Tl-Vo9" secondAttribute="leading" constant="12" id="ysR-T3-e4r"/>
                                                                            </constraints>
                                                                        </tableViewCellContentView>
                                                                        <connections>
                                                                            <outlet property="lbTitle" destination="Zdv-yZ-JQO" id="96m-oW-TUK"/>
                                                                            <outlet property="lbValue" destination="Q1r-dT-n9q" id="51l-5Q-dXo"/>
                                                                        </connections>
                                                                    </tableViewCell>
                                                                </prototypes>
                                                            </tableView>
                                                        </subviews>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstAttribute="trailing" secondItem="mnz-u6-3zD" secondAttribute="trailing" id="8qM-Yn-MrS"/>
                                                            <constraint firstItem="uPc-1F-dD0" firstAttribute="top" secondItem="4Gi-JQ-fnL" secondAttribute="top" id="FhF-Ge-nVp"/>
                                                            <constraint firstItem="twg-h8-JMf" firstAttribute="leading" secondItem="4Gi-JQ-fnL" secondAttribute="leading" id="Oib-Kl-8Ej"/>
                                                            <constraint firstItem="mnz-u6-3zD" firstAttribute="centerY" secondItem="4Gi-JQ-fnL" secondAttribute="centerY" id="W1P-vd-XOW"/>
                                                            <constraint firstItem="twg-h8-JMf" firstAttribute="top" secondItem="4Gi-JQ-fnL" secondAttribute="top" id="YsT-G8-KzI"/>
                                                            <constraint firstAttribute="bottom" secondItem="twg-h8-JMf" secondAttribute="bottom" id="aFz-PL-Xcl"/>
                                                            <constraint firstItem="uPc-1F-dD0" firstAttribute="trailing" secondItem="twg-h8-JMf" secondAttribute="trailing" constant="1" id="cAn-E9-udx"/>
                                                            <constraint firstAttribute="bottom" secondItem="mnz-u6-3zD" secondAttribute="bottom" constant="10" id="gge-Xy-jBq"/>
                                                            <constraint firstItem="mnz-u6-3zD" firstAttribute="leading" secondItem="uPc-1F-dD0" secondAttribute="trailing" id="rAb-ux-UUi"/>
                                                            <constraint firstAttribute="bottom" secondItem="uPc-1F-dD0" secondAttribute="bottom" id="sxE-1L-Lph"/>
                                                            <constraint firstItem="mnz-u6-3zD" firstAttribute="top" secondItem="4Gi-JQ-fnL" secondAttribute="top" constant="10" id="wEV-Rj-MFO"/>
                                                        </constraints>
                                                    </view>
                                                </subviews>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstItem="Yod-MT-UwG" firstAttribute="leading" secondItem="9e2-j6-3bm" secondAttribute="leading" constant="12" id="57M-1o-86n"/>
                                                    <constraint firstItem="sxb-Uo-Vhd" firstAttribute="leading" secondItem="9e2-j6-3bm" secondAttribute="leading" id="A5b-Ib-poo"/>
                                                    <constraint firstItem="Yod-MT-UwG" firstAttribute="top" secondItem="9e2-j6-3bm" secondAttribute="top" constant="12" id="BbG-u3-sPr"/>
                                                    <constraint firstAttribute="bottom" secondItem="4Gi-JQ-fnL" secondAttribute="bottom" id="IKb-RY-1UW"/>
                                                    <constraint firstAttribute="trailing" secondItem="3ES-g7-Z96" secondAttribute="trailing" id="Ogm-2N-0MW"/>
                                                    <constraint firstItem="4Gi-JQ-fnL" firstAttribute="top" secondItem="eAz-4D-hLG" secondAttribute="bottom" id="QmJ-pf-rRG"/>
                                                    <constraint firstItem="4Gi-JQ-fnL" firstAttribute="leading" secondItem="9e2-j6-3bm" secondAttribute="leading" id="TMa-8P-9Ul"/>
                                                    <constraint firstItem="eAz-4D-hLG" firstAttribute="top" secondItem="3ES-g7-Z96" secondAttribute="bottom" constant="25" id="VXQ-cd-rpx"/>
                                                    <constraint firstAttribute="trailing" secondItem="4Gi-JQ-fnL" secondAttribute="trailing" id="a3I-z1-oNB"/>
                                                    <constraint firstAttribute="trailing" secondItem="eAz-4D-hLG" secondAttribute="trailing" id="ci1-ne-KDW"/>
                                                    <constraint firstAttribute="trailing" secondItem="Yod-MT-UwG" secondAttribute="trailing" constant="12" id="igC-vy-Fcz"/>
                                                    <constraint firstAttribute="trailing" secondItem="sxb-Uo-Vhd" secondAttribute="trailing" id="ivh-Na-ieW"/>
                                                    <constraint firstItem="eAz-4D-hLG" firstAttribute="leading" secondItem="9e2-j6-3bm" secondAttribute="leading" id="nmp-lG-VAS"/>
                                                    <constraint firstItem="sxb-Uo-Vhd" firstAttribute="top" secondItem="l7O-iu-tY1" secondAttribute="bottom" constant="12" id="qiW-lO-pro"/>
                                                    <constraint firstItem="3ES-g7-Z96" firstAttribute="top" secondItem="sxb-Uo-Vhd" secondAttribute="bottom" constant="8" id="rEc-gL-wgK"/>
                                                    <constraint firstAttribute="trailing" secondItem="l7O-iu-tY1" secondAttribute="trailing" constant="10" id="tCU-eQ-6OX"/>
                                                    <constraint firstItem="l7O-iu-tY1" firstAttribute="top" secondItem="Yod-MT-UwG" secondAttribute="bottom" id="tpz-rw-uaY"/>
                                                    <constraint firstItem="3ES-g7-Z96" firstAttribute="leading" secondItem="9e2-j6-3bm" secondAttribute="leading" id="uIv-uY-1ls"/>
                                                    <constraint firstItem="l7O-iu-tY1" firstAttribute="leading" secondItem="9e2-j6-3bm" secondAttribute="leading" constant="10" id="wnQ-6g-WH1"/>
                                                </constraints>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="shadowOpacity">
                                                        <real key="value" value="0.25"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="point" keyPath="shadowOffset">
                                                        <point key="value" x="0.0" y="2"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="shadowRadius">
                                                        <real key="value" value="8"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                        <real key="value" value="4"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="color" keyPath="shadowColor">
                                                        <color key="value" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                            </view>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="nad-uK-7ir" userLabel="dashView">
                                                <rect key="frame" x="0.0" y="675.5" width="359" height="6"/>
                                                <subviews>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Vap-j7-ynv" customClass="DashView" customModule="Beta_Cinemas" customModuleProvider="target">
                                                        <rect key="frame" x="10" y="0.0" width="339" height="6"/>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="dashNumber">
                                                                <integer key="value" value="25"/>
                                                            </userDefinedRuntimeAttribute>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="dashWidth">
                                                                <real key="value" value="4"/>
                                                            </userDefinedRuntimeAttribute>
                                                            <userDefinedRuntimeAttribute type="color" keyPath="dashColor">
                                                                <color key="value" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            </userDefinedRuntimeAttribute>
                                                        </userDefinedRuntimeAttributes>
                                                    </view>
                                                </subviews>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstItem="Vap-j7-ynv" firstAttribute="leading" secondItem="nad-uK-7ir" secondAttribute="leading" constant="10" id="LIP-i7-bbv"/>
                                                    <constraint firstAttribute="height" constant="6" id="QlY-Qb-rcK"/>
                                                    <constraint firstAttribute="bottom" secondItem="Vap-j7-ynv" secondAttribute="bottom" id="h1z-Mq-IhZ"/>
                                                    <constraint firstAttribute="trailing" secondItem="Vap-j7-ynv" secondAttribute="trailing" constant="10" id="yf2-lM-7Gj"/>
                                                    <constraint firstItem="Vap-j7-ynv" firstAttribute="top" secondItem="nad-uK-7ir" secondAttribute="top" id="ykG-GB-Yo4"/>
                                                </constraints>
                                            </view>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="X3I-i5-3wT" customClass="RoundView" customModule="Beta_Cinemas" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="681.5" width="359" height="190"/>
                                                <subviews>
                                                    <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="demo_barcode" translatesAutoresizingMaskIntoConstraints="NO" id="UoI-gp-rvh">
                                                        <rect key="frame" x="8" y="8" width="343" height="113"/>
                                                        <constraints>
                                                            <constraint firstAttribute="width" secondItem="UoI-gp-rvh" secondAttribute="height" multiplier="343:113" id="JfP-fY-cMd"/>
                                                        </constraints>
                                                    </imageView>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="649826820" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="mzN-JI-dke">
                                                        <rect key="frame" x="20" y="126" width="319" height="56"/>
                                                        <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="22"/>
                                                        <color key="textColor" red="0.**********" green="0.**********" blue="0.15686274510000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                </subviews>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="trailing" secondItem="mzN-JI-dke" secondAttribute="trailing" constant="20" id="8F7-JJ-aTt"/>
                                                    <constraint firstItem="mzN-JI-dke" firstAttribute="leading" secondItem="X3I-i5-3wT" secondAttribute="leading" constant="20" id="MZh-iZ-j1k"/>
                                                    <constraint firstItem="mzN-JI-dke" firstAttribute="top" secondItem="UoI-gp-rvh" secondAttribute="bottom" constant="5" id="SoJ-eA-Lml"/>
                                                    <constraint firstItem="UoI-gp-rvh" firstAttribute="top" secondItem="X3I-i5-3wT" secondAttribute="top" constant="8" id="YOh-Db-fkX"/>
                                                    <constraint firstAttribute="bottom" secondItem="mzN-JI-dke" secondAttribute="bottom" constant="8" id="btm-rO-K9E"/>
                                                    <constraint firstAttribute="trailing" secondItem="UoI-gp-rvh" secondAttribute="trailing" constant="8" id="hBM-sv-esJ"/>
                                                    <constraint firstItem="UoI-gp-rvh" firstAttribute="leading" secondItem="X3I-i5-3wT" secondAttribute="leading" constant="8" id="siv-de-Hrn"/>
                                                </constraints>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="shadowOpacity">
                                                        <real key="value" value="0.25"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="point" keyPath="shadowOffset">
                                                        <point key="value" x="0.0" y="2"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="shadowRadius">
                                                        <real key="value" value="8"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                        <real key="value" value="4"/>
                                                    </userDefinedRuntimeAttribute>
                                                    <userDefinedRuntimeAttribute type="color" keyPath="shadowColor">
                                                        <color key="value" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    </userDefinedRuntimeAttribute>
                                                </userDefinedRuntimeAttributes>
                                            </view>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="yOJ-pM-j76">
                                                <rect key="frame" x="0.0" y="871.5" width="359" height="137"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Vui lòng đưa mã số này đến quầy vé Beta để nhận vé" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Uu9-bP-HYT" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="8" width="359" height="37.5"/>
                                                        <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                        <color key="textColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                        <nil key="highlightedColor"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="TransactionDetail.GuideText"/>
                                                        </userDefinedRuntimeAttributes>
                                                    </label>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Lưu ý: Beta không chấp nhận hoàn tiền hoặc đổi vé đã thanh toán thành công trên Website và Ứng dụng Beta " textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Zzc-HZ-nYU" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="60.5" width="359" height="56.5"/>
                                                        <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                        <color key="textColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                        <nil key="highlightedColor"/>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="TransactionDetail.NoticeText"/>
                                                        </userDefinedRuntimeAttributes>
                                                    </label>
                                                </subviews>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="trailing" secondItem="Zzc-HZ-nYU" secondAttribute="trailing" id="7vX-m1-il0"/>
                                                    <constraint firstItem="Uu9-bP-HYT" firstAttribute="leading" secondItem="yOJ-pM-j76" secondAttribute="leading" id="NUT-ha-fMF"/>
                                                    <constraint firstAttribute="bottom" secondItem="Zzc-HZ-nYU" secondAttribute="bottom" constant="20" id="ZFN-kC-wMR"/>
                                                    <constraint firstItem="Zzc-HZ-nYU" firstAttribute="leading" secondItem="yOJ-pM-j76" secondAttribute="leading" id="hiP-0r-MhG"/>
                                                    <constraint firstItem="Zzc-HZ-nYU" firstAttribute="top" secondItem="Uu9-bP-HYT" secondAttribute="bottom" constant="15" id="iQc-x9-Mcg"/>
                                                    <constraint firstItem="Uu9-bP-HYT" firstAttribute="top" secondItem="yOJ-pM-j76" secondAttribute="top" constant="8" id="xPK-FK-oiV"/>
                                                    <constraint firstAttribute="trailing" secondItem="Uu9-bP-HYT" secondAttribute="trailing" id="xhJ-io-aSx"/>
                                                </constraints>
                                            </view>
                                        </subviews>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="nad-uK-7ir" secondAttribute="trailing" id="2Uk-s6-0tj"/>
                                            <constraint firstItem="nad-uK-7ir" firstAttribute="leading" secondItem="szr-vO-abU" secondAttribute="leading" id="uIz-be-DOi"/>
                                        </constraints>
                                    </stackView>
                                </subviews>
                                <constraints>
                                    <constraint firstItem="szr-vO-abU" firstAttribute="leading" secondItem="Yx1-XY-HcM" secondAttribute="leading" constant="8" id="1BS-Vq-JT4"/>
                                    <constraint firstItem="szr-vO-abU" firstAttribute="top" secondItem="Yx1-XY-HcM" secondAttribute="top" constant="8" id="3Uw-ZD-KiW"/>
                                    <constraint firstItem="szr-vO-abU" firstAttribute="width" secondItem="Yx1-XY-HcM" secondAttribute="width" constant="-16" id="4dL-vm-RWS"/>
                                    <constraint firstAttribute="trailing" secondItem="szr-vO-abU" secondAttribute="trailing" constant="8" id="nK6-hb-9sh"/>
                                    <constraint firstAttribute="bottom" secondItem="szr-vO-abU" secondAttribute="bottom" id="whK-83-fYV"/>
                                </constraints>
                            </scrollView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="CU0-iu-hOZ"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="Yx1-XY-HcM" firstAttribute="top" secondItem="CU0-iu-hOZ" secondAttribute="top" id="Azb-8g-aQB"/>
                            <constraint firstItem="Yx1-XY-HcM" firstAttribute="leading" secondItem="CU0-iu-hOZ" secondAttribute="leading" id="XNA-CB-IG6"/>
                            <constraint firstItem="CU0-iu-hOZ" firstAttribute="bottom" secondItem="Yx1-XY-HcM" secondAttribute="bottom" id="ZnT-nj-UB3"/>
                            <constraint firstAttribute="trailing" secondItem="Yx1-XY-HcM" secondAttribute="trailing" id="oxB-dq-VCe"/>
                        </constraints>
                    </view>
                    <size key="freeformSize" width="375" height="1000"/>
                    <connections>
                        <outlet property="ivBarcode" destination="UoI-gp-rvh" id="lfC-GW-6KB"/>
                        <outlet property="lbCinema" destination="pLw-LW-tlj" id="WxO-Ja-fZ5"/>
                        <outlet property="lbCodeNumber" destination="mzN-JI-dke" id="ZXT-k1-vOT"/>
                        <outlet property="lbCombo" destination="VzX-p0-EVs" id="PHR-01-Ywd"/>
                        <outlet property="lbComboTitle" destination="DG5-rH-8cv" id="mZg-Jl-vFA"/>
                        <outlet property="lbDate" destination="dii-U2-D99" id="AQN-NI-wfD"/>
                        <outlet property="lbFilmDetail" destination="l7O-iu-tY1" id="dQe-D5-2O2"/>
                        <outlet property="lbFilmName" destination="Yod-MT-UwG" id="PCc-fU-V2j"/>
                        <outlet property="lbNotice" destination="Zzc-HZ-nYU" id="xN5-Mo-ltK"/>
                        <outlet property="lbRoom" destination="qUm-CS-a6B" id="khW-Ie-rmT"/>
                        <outlet property="lbSeat" destination="Ah7-1J-3Wq" id="KXy-wy-VUS"/>
                        <outlet property="lbSeatTitle" destination="SzU-4a-UiH" id="Hxa-iN-PPd"/>
                        <outlet property="lbTime" destination="MpA-iI-VMb" id="gxH-Sz-Glr"/>
                        <outlet property="lbTotalMoney" destination="YKM-aY-LZ5" id="j8v-05-1sd"/>
                        <outlet property="stackView" destination="szr-vO-abU" id="gEQ-78-KCh"/>
                        <outlet property="tableView" destination="twg-h8-JMf" id="Y31-oR-iXF"/>
                        <outlet property="titleDatePoint" destination="iTK-ph-3cG" id="uhl-p4-eJp"/>
                        <outlet property="titlePoint" destination="kui-Ka-jFy" id="HI6-Ad-Y4x"/>
                        <outlet property="vDashView" destination="nad-uK-7ir" id="XBu-zz-zv3"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="iRY-NB-AJs" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="4871.1999999999998" y="-799.70014992503752"/>
        </scene>
        <!--Coupon View Controller-->
        <scene sceneID="NdY-2C-iwx">
            <objects>
                <viewController storyboardIdentifier="CouponViewController" id="Zvy-n1-FXO" customClass="CouponViewController" customModule="Beta_Cinemas" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="HxJ-P1-15Y">
                        <rect key="frame" x="0.0" y="0.0" width="320" height="568"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="7fx-si-bGD">
                                <rect key="frame" x="0.0" y="0.0" width="320" height="568"/>
                                <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="calibratedRGB"/>
                                <view key="tableHeaderView" contentMode="scaleToFill" id="Nqq-le-zOp">
                                    <rect key="frame" x="0.0" y="0.0" width="320" height="364"/>
                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                    <subviews>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="252" text="THÔNG TIN COUPON" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="dBR-Vv-I5l" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                            <rect key="frame" x="8" y="22" width="304" height="23.5"/>
                                            <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="20"/>
                                            <color key="textColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            <nil key="highlightedColor"/>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Coupon.Info"/>
                                            </userDefinedRuntimeAttributes>
                                        </label>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Mã Coupon" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="F6e-XJ-2Vm" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                            <rect key="frame" x="20" y="63.5" width="84.5" height="49.5"/>
                                            <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="16"/>
                                            <color key="textColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            <nil key="highlightedColor"/>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="CouponCode"/>
                                            </userDefinedRuntimeAttributes>
                                        </label>
                                        <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="5P9-oh-CIS" customClass="RoundTextField" customModule="Beta_Cinemas" customModuleProvider="target">
                                            <rect key="frame" x="20" y="121" width="280" height="50"/>
                                            <constraints>
                                                <constraint firstAttribute="height" constant="50" id="8By-8W-u0M"/>
                                            </constraints>
                                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                            <textInputTraits key="textInputTraits"/>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                    <real key="value" value="2"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                                    <color key="value" red="0.58431372550000005" green="0.58431372550000005" blue="0.58431372550000005" alpha="0.59999999999999998" colorSpace="custom" customColorSpace="sRGB"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                                    <real key="value" value="1"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="number" keyPath="horizontalSpace">
                                                    <real key="value" value="10"/>
                                                </userDefinedRuntimeAttribute>
                                            </userDefinedRuntimeAttributes>
                                        </textField>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="252" text="Mã PIN" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="XVg-FY-Idj" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                            <rect key="frame" x="20" y="187" width="280" height="19"/>
                                            <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="16"/>
                                            <color key="textColor" red="0.*****************" green="0.*****************" blue="0.*****************" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            <nil key="highlightedColor"/>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Coupon.PIN"/>
                                            </userDefinedRuntimeAttributes>
                                        </label>
                                        <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="n08-zE-9gP" customClass="RoundTextField" customModule="Beta_Cinemas" customModuleProvider="target">
                                            <rect key="frame" x="20" y="214" width="280" height="50"/>
                                            <constraints>
                                                <constraint firstAttribute="height" constant="50" id="LfN-Mr-AJj"/>
                                            </constraints>
                                            <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                            <textInputTraits key="textInputTraits"/>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                    <real key="value" value="2"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                                    <color key="value" red="0.58431372550000005" green="0.58431372550000005" blue="0.58431372550000005" alpha="0.59999999999999998" colorSpace="custom" customColorSpace="sRGB"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                                    <real key="value" value="1"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="number" keyPath="horizontalSpace">
                                                    <real key="value" value="10"/>
                                                </userDefinedRuntimeAttribute>
                                            </userDefinedRuntimeAttributes>
                                        </textField>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="haE-4H-BFc" customClass="RoundView" customModule="Beta_Cinemas" customModuleProvider="target">
                                            <rect key="frame" x="20" y="288" width="280" height="56"/>
                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                    <real key="value" value="4"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="color" keyPath="shadowColor">
                                                    <color key="value" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="number" keyPath="shadowOpacity">
                                                    <real key="value" value="0.20000000000000001"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="point" keyPath="shadowOffset">
                                                    <point key="value" x="0.0" y="6"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="number" keyPath="shadowRadius">
                                                    <real key="value" value="4"/>
                                                </userDefinedRuntimeAttribute>
                                            </userDefinedRuntimeAttributes>
                                        </view>
                                        <button opaque="NO" clipsSubviews="YES" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="EbQ-Bs-Kfd" customClass="GradientButton" customModule="Beta_Cinemas" customModuleProvider="target">
                                            <rect key="frame" x="20" y="288" width="280" height="56"/>
                                            <constraints>
                                                <constraint firstAttribute="height" constant="56" id="CHD-bj-6pf"/>
                                            </constraints>
                                            <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="20"/>
                                            <state key="normal" title="ĐĂNG KÝ">
                                                <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            </state>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                    <real key="value" value="4"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="color" keyPath="firstColor">
                                                    <color key="value" red="0.99215686270000003" green="0.15686274510000001" blue="0.0078431372550000003" alpha="0.97999999999999998" colorSpace="custom" customColorSpace="sRGB"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="color" keyPath="secondColor">
                                                    <color key="value" red="0.99215686270000003" green="0.48627450979999998" blue="0.0078431372550000003" alpha="0.98999999999999999" colorSpace="custom" customColorSpace="sRGB"/>
                                                </userDefinedRuntimeAttribute>
                                                <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Bt.REGISTER"/>
                                            </userDefinedRuntimeAttributes>
                                            <connections>
                                                <action selector="didSelectRegisterCoupon:" destination="Zvy-n1-FXO" eventType="touchUpInside" id="e5n-fu-gZl"/>
                                            </connections>
                                        </button>
                                    </subviews>
                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <constraints>
                                        <constraint firstItem="5P9-oh-CIS" firstAttribute="top" secondItem="F6e-XJ-2Vm" secondAttribute="bottom" constant="8" id="0I6-tT-vgE"/>
                                        <constraint firstAttribute="trailing" secondItem="dBR-Vv-I5l" secondAttribute="trailing" constant="8" id="24t-Cl-VGK"/>
                                        <constraint firstItem="F6e-XJ-2Vm" firstAttribute="leading" secondItem="Nqq-le-zOp" secondAttribute="leading" constant="20" id="41e-5H-ZD6"/>
                                        <constraint firstItem="n08-zE-9gP" firstAttribute="top" secondItem="XVg-FY-Idj" secondAttribute="bottom" constant="8" id="5zK-kW-9k8"/>
                                        <constraint firstItem="EbQ-Bs-Kfd" firstAttribute="leading" secondItem="haE-4H-BFc" secondAttribute="leading" id="8DU-rT-Moe"/>
                                        <constraint firstItem="5P9-oh-CIS" firstAttribute="leading" secondItem="Nqq-le-zOp" secondAttribute="leading" constant="20" id="BJT-6j-8ai"/>
                                        <constraint firstItem="EbQ-Bs-Kfd" firstAttribute="top" secondItem="n08-zE-9gP" secondAttribute="bottom" constant="24" id="C6j-ZG-A0N"/>
                                        <constraint firstItem="dBR-Vv-I5l" firstAttribute="leading" secondItem="Nqq-le-zOp" secondAttribute="leading" constant="8" id="CW3-74-yfE"/>
                                        <constraint firstItem="EbQ-Bs-Kfd" firstAttribute="top" secondItem="haE-4H-BFc" secondAttribute="top" id="JwG-I1-HUv"/>
                                        <constraint firstItem="dBR-Vv-I5l" firstAttribute="top" secondItem="Nqq-le-zOp" secondAttribute="top" constant="22" id="Ny0-2b-Mv5"/>
                                        <constraint firstItem="XVg-FY-Idj" firstAttribute="leading" secondItem="Nqq-le-zOp" secondAttribute="leading" constant="20" id="Pv5-sS-Y3H"/>
                                        <constraint firstItem="EbQ-Bs-Kfd" firstAttribute="bottom" secondItem="haE-4H-BFc" secondAttribute="bottom" id="Q5Y-bp-5DQ"/>
                                        <constraint firstItem="n08-zE-9gP" firstAttribute="top" secondItem="XVg-FY-Idj" secondAttribute="bottom" constant="8" id="Sra-qN-lWN"/>
                                        <constraint firstAttribute="trailing" secondItem="n08-zE-9gP" secondAttribute="trailing" constant="20" id="T20-GI-KGb"/>
                                        <constraint firstAttribute="trailing" secondItem="EbQ-Bs-Kfd" secondAttribute="trailing" constant="20" id="TLF-Yn-wPl"/>
                                        <constraint firstAttribute="trailing" secondItem="5P9-oh-CIS" secondAttribute="trailing" constant="20" id="Wnc-3d-fnU"/>
                                        <constraint firstItem="EbQ-Bs-Kfd" firstAttribute="trailing" secondItem="haE-4H-BFc" secondAttribute="trailing" id="ahq-2a-zdZ"/>
                                        <constraint firstItem="XVg-FY-Idj" firstAttribute="top" secondItem="5P9-oh-CIS" secondAttribute="bottom" constant="16" id="gq8-Ie-iU4"/>
                                        <constraint firstItem="n08-zE-9gP" firstAttribute="leading" secondItem="Nqq-le-zOp" secondAttribute="leading" constant="20" id="lBN-LH-6eD"/>
                                        <constraint firstAttribute="bottom" secondItem="EbQ-Bs-Kfd" secondAttribute="bottom" constant="20" id="nmg-p3-7bt"/>
                                        <constraint firstItem="EbQ-Bs-Kfd" firstAttribute="leading" secondItem="Nqq-le-zOp" secondAttribute="leading" constant="20" id="qKC-Fv-Kve"/>
                                        <constraint firstItem="F6e-XJ-2Vm" firstAttribute="top" secondItem="dBR-Vv-I5l" secondAttribute="bottom" constant="18" id="tb1-dl-Gab"/>
                                        <constraint firstAttribute="trailing" secondItem="XVg-FY-Idj" secondAttribute="trailing" constant="20" id="vvp-cX-jH3"/>
                                    </constraints>
                                </view>
                                <connections>
                                    <outlet property="dataSource" destination="Zvy-n1-FXO" id="e0R-bU-Dko"/>
                                    <outlet property="delegate" destination="Zvy-n1-FXO" id="ffB-97-5aM"/>
                                </connections>
                            </tableView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="uu4-EO-xSW"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="bottom" secondItem="7fx-si-bGD" secondAttribute="bottom" id="0LE-eJ-ejZ"/>
                            <constraint firstItem="uu4-EO-xSW" firstAttribute="trailing" secondItem="7fx-si-bGD" secondAttribute="trailing" id="Cbx-8e-wnc"/>
                            <constraint firstItem="7fx-si-bGD" firstAttribute="leading" secondItem="HxJ-P1-15Y" secondAttribute="leading" id="L2c-fw-kpj"/>
                            <constraint firstItem="7fx-si-bGD" firstAttribute="top" secondItem="HxJ-P1-15Y" secondAttribute="top" id="oxa-gX-Om8"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="headerView" destination="Nqq-le-zOp" id="8Y8-0t-hyQ"/>
                        <outlet property="tableView" destination="7fx-si-bGD" id="HVq-xh-43s"/>
                        <outlet property="tfCouponCode" destination="5P9-oh-CIS" id="pbv-9w-TrF"/>
                        <outlet property="tfPinCode" destination="n08-zE-9gP" id="S46-cJ-Sf1"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="UGX-EJ-aqa" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="4130" y="-65"/>
        </scene>
    </scenes>
    <designables>
        <designable name="2GM-rC-kMz">
            <size key="intrinsicContentSize" width="134.5" height="19"/>
        </designable>
        <designable name="3dx-Jb-616">
            <size key="intrinsicContentSize" width="47" height="19"/>
        </designable>
        <designable name="4hx-gG-gA8">
            <size key="intrinsicContentSize" width="81.5" height="16.5"/>
        </designable>
        <designable name="5Ll-4t-fzu">
            <size key="intrinsicContentSize" width="85" height="18.5"/>
        </designable>
        <designable name="6he-8c-i0x">
            <size key="intrinsicContentSize" width="43" height="18.5"/>
        </designable>
        <designable name="7n2-Bh-7nl">
            <size key="intrinsicContentSize" width="76" height="31"/>
        </designable>
        <designable name="9hB-tF-6qD">
            <size key="intrinsicContentSize" width="81" height="19"/>
        </designable>
        <designable name="C1i-WT-uNf">
            <size key="intrinsicContentSize" width="67" height="19"/>
        </designable>
        <designable name="Csx-1a-FoR">
            <size key="intrinsicContentSize" width="32" height="34"/>
        </designable>
        <designable name="DG5-rH-8cv">
            <size key="intrinsicContentSize" width="75" height="19"/>
        </designable>
        <designable name="DZU-zS-zga">
            <size key="intrinsicContentSize" width="210" height="23.5"/>
        </designable>
        <designable name="DjY-5S-D3I">
            <size key="intrinsicContentSize" width="115" height="17"/>
        </designable>
        <designable name="EFW-8o-LFA">
            <size key="intrinsicContentSize" width="105.5" height="19"/>
        </designable>
        <designable name="EbQ-Bs-Kfd">
            <size key="intrinsicContentSize" width="89" height="36"/>
        </designable>
        <designable name="F6e-XJ-2Vm">
            <size key="intrinsicContentSize" width="84.5" height="19"/>
        </designable>
        <designable name="GzD-E6-Lyj">
            <size key="intrinsicContentSize" width="192.5" height="23.5"/>
        </designable>
        <designable name="HRa-VS-nfp">
            <size key="intrinsicContentSize" width="43" height="18.5"/>
        </designable>
        <designable name="JR4-HF-3bc">
            <size key="intrinsicContentSize" width="107" height="18.5"/>
        </designable>
        <designable name="Jgh-5J-yZR">
            <size key="intrinsicContentSize" width="53.5" height="19"/>
        </designable>
        <designable name="Jhe-pc-vdO">
            <size key="intrinsicContentSize" width="89" height="36"/>
        </designable>
        <designable name="QQ0-mz-ecy">
            <size key="intrinsicContentSize" width="87" height="14"/>
        </designable>
        <designable name="Rep-Gm-M11">
            <size key="intrinsicContentSize" width="47.5" height="19"/>
        </designable>
        <designable name="SzU-4a-UiH">
            <size key="intrinsicContentSize" width="82.5" height="19"/>
        </designable>
        <designable name="UED-Zi-1c5">
            <size key="intrinsicContentSize" width="117.5" height="16.5"/>
        </designable>
        <designable name="UYp-t3-kFV">
            <size key="intrinsicContentSize" width="143" height="19"/>
        </designable>
        <designable name="Uu9-bP-HYT">
            <size key="intrinsicContentSize" width="383" height="19"/>
        </designable>
        <designable name="WJx-t8-zph">
            <size key="intrinsicContentSize" width="92.5" height="19"/>
        </designable>
        <designable name="X47-LK-cf0">
            <size key="intrinsicContentSize" width="85.5" height="19"/>
        </designable>
        <designable name="XVg-FY-Idj">
            <size key="intrinsicContentSize" width="53.5" height="19"/>
        </designable>
        <designable name="XbY-05-TIO">
            <size key="intrinsicContentSize" width="721" height="19"/>
        </designable>
        <designable name="Xxi-KE-n2C">
            <size key="intrinsicContentSize" width="203.5" height="19"/>
        </designable>
        <designable name="Y0v-zI-Vqy">
            <size key="intrinsicContentSize" width="53.5" height="18.5"/>
        </designable>
        <designable name="Zdv-yZ-JQO">
            <size key="intrinsicContentSize" width="87" height="19"/>
        </designable>
        <designable name="Zzc-HZ-nYU">
            <size key="intrinsicContentSize" width="794" height="19"/>
        </designable>
        <designable name="dBR-Vv-I5l">
            <size key="intrinsicContentSize" width="200" height="23.5"/>
        </designable>
        <designable name="dum-0L-heg">
            <size key="intrinsicContentSize" width="90.5" height="18.5"/>
        </designable>
        <designable name="e5u-Dx-bPd">
            <size key="intrinsicContentSize" width="87" height="17"/>
        </designable>
        <designable name="eZF-L4-TzI">
            <size key="intrinsicContentSize" width="93" height="19"/>
        </designable>
        <designable name="fhn-fH-D4b">
            <size key="intrinsicContentSize" width="90" height="19"/>
        </designable>
        <designable name="hOK-iG-sOK">
            <size key="intrinsicContentSize" width="110.5" height="17"/>
        </designable>
        <designable name="icE-3n-LG4">
            <size key="intrinsicContentSize" width="89" height="36"/>
        </designable>
        <designable name="idk-wW-sLG">
            <size key="intrinsicContentSize" width="54" height="19"/>
        </designable>
        <designable name="jNf-pW-pkK">
            <size key="intrinsicContentSize" width="87" height="14"/>
        </designable>
        <designable name="jfm-5o-nR6">
            <size key="intrinsicContentSize" width="96" height="19"/>
        </designable>
        <designable name="kiA-FK-y69">
            <size key="intrinsicContentSize" width="65.5" height="14"/>
        </designable>
        <designable name="kj2-CZ-bph">
            <size key="intrinsicContentSize" width="73" height="19"/>
        </designable>
        <designable name="nKv-YQ-v17">
            <size key="intrinsicContentSize" width="87" height="14"/>
        </designable>
        <designable name="pFx-OT-cYx">
            <size key="intrinsicContentSize" width="323.5" height="19"/>
        </designable>
        <designable name="pGF-Ey-UYd">
            <size key="intrinsicContentSize" width="101" height="36"/>
        </designable>
        <designable name="qhb-tg-cyP">
            <size key="intrinsicContentSize" width="83.5" height="18.5"/>
        </designable>
        <designable name="r9s-nD-j2B">
            <size key="intrinsicContentSize" width="53.5" height="20.5"/>
        </designable>
        <designable name="suu-Iw-wN9">
            <size key="intrinsicContentSize" width="101" height="36"/>
        </designable>
        <designable name="tHb-mI-aQq">
            <size key="intrinsicContentSize" width="101" height="16.5"/>
        </designable>
        <designable name="uJy-hX-Xlu">
            <size key="intrinsicContentSize" width="167" height="19"/>
        </designable>
        <designable name="uvG-Xb-OaJ">
            <size key="intrinsicContentSize" width="61" height="29"/>
        </designable>
        <designable name="v3h-vS-3lS">
            <size key="intrinsicContentSize" width="189.5" height="23.5"/>
        </designable>
        <designable name="vbt-uk-BAZ">
            <size key="intrinsicContentSize" width="59" height="19"/>
        </designable>
        <designable name="wQ8-d6-i7q">
            <size key="intrinsicContentSize" width="64" height="18.5"/>
        </designable>
        <designable name="yrM-oU-2aa">
            <size key="intrinsicContentSize" width="105" height="18.5"/>
        </designable>
        <designable name="zWB-um-YRt">
            <size key="intrinsicContentSize" width="70" height="19"/>
        </designable>
    </designables>
    <resources>
        <image name="demo_avatar" width="424" height="444"/>
        <image name="demo_barcode" width="824" height="274"/>
        <image name="ic_account_white" width="24" height="24"/>
        <image name="ic_address" width="26" height="26"/>
        <image name="ic_birthday" width="26" height="26"/>
        <image name="ic_cmnd" width="26" height="26"/>
        <image name="ic_comfirm_password" width="26" height="26"/>
        <image name="ic_firstname" width="26" height="26"/>
        <image name="ic_home" width="26" height="26"/>
        <image name="ic_membercard" width="42" height="30"/>
        <image name="ic_name" width="26" height="26"/>
        <image name="ic_password" width="26" height="26"/>
        <image name="ic_phone" width="26" height="26"/>
        <image name="ic_sex" width="26" height="26"/>
        <image name="ic_tower" width="26" height="26"/>
        <image name="ic_vip" width="25" height="30"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>

//
//  TranferHistoryViewController.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 3/14/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

class TranferHistoryViewController: BaseViewController {
    @IBOutlet weak var tableView: UITableView!

    let cellId = "TransactionHistoryCell"

    var items: [TransactionHistoryModel] = []
    override func viewDidLoad() {
        super.viewDidLoad()

        localizableTitle = "TransactionHistory.Title"
        getData()
        NotificationCenter.default.addObserver(self,
                                               selector: #selector(applicationDidBecomeActive),
                                               name: .UIApplicationDidBecomeActive,
                                               object: nil)
    }

    deinit {
        NotificationCenter.default.removeObserver(self,
                                                  name: .UIApplicationDidBecomeActive,
                                                  object: nil)
    }

    @objc func applicationDidBecomeActive() {
        print("Application become active")
        getData()
    }

    func getData() {
        guard let user = Global.shared.user, let userId = user.UserId else { return }
        self.showLoading()
        AccountProvider.rx.request(.getTransactionHistory(userId)).mapObject(DDKCResponse<TransactionHistoryModel>.self)
            
            .subscribe(onNext: { [weak self] result in
                self?.dismissLoading()
                guard let `self` = self else {return}
                self.handlerResponse(result, success: {
                    guard let data = result.ListObject else{
                        return
                    }
                    self.items = data
                    self.tableView.reloadData()
                })
        }, onError: { [weak self] _ in
            self?.dismissLoading()
        }).disposed(by: disposeBag)
    }
}

extension TranferHistoryViewController: UITableViewDelegate, UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return items.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: cellId) as! TransactionHistoryCell
        cell.fillData(items[indexPath.row])
        return cell
    }
    
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: false)
        let item = items[indexPath.row]
        if item.Invoice_Id == "00000000-0000-0000-0000-000000000000", let url = item.AirpayLandingUrl {
            UIApplication.shared.open(url)
            return
        }
        let vc = UIStoryboard.member[.transactionDetail] as! TransactionDetailViewController
        vc.item = item
        show(vc, sender: nil)
    }
}

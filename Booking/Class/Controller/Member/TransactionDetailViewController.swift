//
//  TransactionDetailViewController.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/18/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit
import RSBarcodes_Swift
import AVFoundation

class TransactionDetailViewController: BaseViewController {
    @IBOutlet weak var stackView: UIStackView!
    @IBOutlet weak var lbFilmName: UILabel!
    @IBOutlet weak var lbFilmDetail: UILabel!
    @IBOutlet weak var lbCinema: UILabel!
    @IBOutlet weak var lbDate: UILabel!
    @IBOutlet weak var lbTime: UILabel!
    @IBOutlet weak var lbRoom: UILabel!
    @IBOutlet weak var lbSeat: UILabel!
    @IBOutlet weak var lbCombo: UILabel!
    @IBOutlet weak var lbTotalMoney: UILabel!
    @IBOutlet weak var ivBarcode: UIImageView!
    @IBOutlet weak var lbCodeNumber: UILabel!
    @IBOutlet weak var lbNotice: LocalizableLabel!
    @IBOutlet weak var vDashView: UIView!
    @IBOutlet weak var lbSeatTitle: LocalizableLabel!
    @IBOutlet weak var lbComboTitle: LocalizableLabel!
    
    @IBOutlet weak var titlePoint: UILabel!
    @IBOutlet weak var titleDatePoint: UILabel!
    @IBOutlet weak var tableView: UITableView!

    var backToHome: Bool = false
    let cellId = "TransactionDetailCell"
    var items: [KeyValueEntry] = []
    
    var item: TransactionHistoryModel?
    var invoiceId: String?

    override func viewDidLoad() {
        super.viewDidLoad()
        navigationController?.setTransparent(false)
        // Do any additional setup after loading the view.
        localizableTitle = "TransactionDetail.Title"
        stackView.isHidden = true;
        ivBarcode.image = RSUnifiedCodeGenerator.shared.generateCode("123456789", machineReadableCodeObjectType: AVMetadataObject.ObjectType.code128.rawValue)
        updateNoticeText()
        self.tableView.dataSource = self
        self.tableView.delegate = self
        self.tableView.separatorStyle = .none
        
        getData()

        if backToHome {
            let viewControllers = [self.navigationController?.viewControllers.first, self].compactMap {$0}
            self.navigationController?.viewControllers = viewControllers
        }
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        vDashView.superview?.bringSubview(toFront: vDashView)
    }

    override func localizationDidChange() {
        super.localizationDidChange()

        updateNoticeText()
    }

    func updateNoticeText() {
        let attributedString = NSMutableAttributedString(string: "TransactionDetail.NoticeText".localized, attributes: [
            .font: UIFont(fontName: .SourceSansPro, style: .Regular, size: 16),
            .foregroundColor: UIColor(red: 73.0 / 255.0, green: 76.0 / 255.0, blue: 98.0 / 255.0, alpha: 1.0)
            ])
        attributedString.addAttributes([
            .font: UIFont(fontName: .SourceSansPro, style: .Bold, size: 16),
            .foregroundColor: UIColor(red: 253.0 / 255.0, green: 40.0 / 255.0, blue: 2.0 / 255.0, alpha: 1.0)
            ], range: NSRange(location: 0, length: "TransactionDetail.Notice".localized.count))
        lbNotice.attributedText = attributedString
    }
    
    private func fillData(_ model: TransactionHistoryDetailModel){
        stackView.isHidden = false
        lbFilmName.text = model.FilmModel?.getName()
        lbFilmDetail.text = "\(model.FilmModel?.RestrictAgeString ?? "") | \(model.FilmModel?.FilmFormatName ?? "")\(model.FilmModel?.getFormatNameInDetail() ?? "") | \(model.FilmModel?.Duration ?? 0) phút"
        lbCinema.text = model.CinemaName
        lbDate.text = model.showTime.0
        lbTime.text = model.showTime.1
        lbRoom.text = model.ScreenName
        
        if model.seatName.0 > 0{
            lbSeat.attributedText = NSAttributedString(string: model.seatName.1)
            lbSeatTitle.text = "TransactionDetail.Seat".lang(["SEAT_COUNT": "\(model.seatName.0)"])
        }
        
        if model.combos.0 > 0{
            lbCombo.text = model.combos.1
            lbComboTitle.text = "TransactionDetail.Combo".lang(["COMBO_COUNT": "\(model.combos.0)"])
        }else{
            lbComboTitle.text = "TransactionDetail.Combo".lang(["COMBO_COUNT": "\(0)"])
            lbCombo.text = ""
        }
        
        if let total = model.TotalPayment {
            lbTotalMoney.text = total.toCurrency()
        }
        
        if let point = model.QuantityPoint {
            titlePoint.text = "\(point)"
        }
        
        if let datePoint = model.DateExpiredPoint {
            titleDatePoint.text = Date.dateFromServerSavis(datePoint).toStringStandard()
        }
        
        if let no = model.No {
            let code = no//String(format: "%.0f", no)
            ivBarcode.image = RSUnifiedCodeGenerator.shared.generateCode(code, machineReadableCodeObjectType: AVMetadataObject.ObjectType.code128.rawValue)
            lbCodeNumber.text = code
        }

        if (model.paymentValue(type: .CASH) > 0) {
            items.append(KeyValueEntry(key: "TransactionDetail.Cash".localized, value: model.paymentValue(type: .CASH).toCurrency()))
        }
        
        if (model.paymentValue(type: .VOUCHER) > 0) {
            items.append(KeyValueEntry(key: "TransactionDetail.Voucher".localized, value: model.paymentValue(type: .VOUCHER).toCurrency()))
        }
        
        if (model.paymentValue(types: [.CARD, .ONEPAY]) > 0) {
            items.append(KeyValueEntry(key: "TransactionDetail.Card".localized, value: model.paymentValue(types: [.CARD, .ONEPAY]).toCurrency()))
        }
        
        if (model.paymentValue(type: .BETAID) > 0) {
            items.append(KeyValueEntry(key: "TransactionDetail.Point".localized, value: "\(model.SpendingPoint ?? 0) - \(model.paymentValue(type: .BETAID).toCurrency())"))
        }
        
        if (model.paymentValue(type: .SHOPEEPAY_ONLINE) > 0) {
            items.append(KeyValueEntry(key: "TransactionDetail.Airpay".localized, value: model.paymentValue(type: .SHOPEEPAY_ONLINE).toCurrency()))
        }
        
        if (model.paymentValue(type: .MOMO) > 0) {
            items.append(KeyValueEntry(key: "TransactionDetail.Momo".localized, value: model.paymentValue(type: .MOMO).toCurrency()))
        }
        
        if (model.paymentValue(type: .ZALOPAY) > 0) {
            items.append(KeyValueEntry(key: "TransactionDetail.ZaloPay".localized, value: model.paymentValue(type: .ZALOPAY).toCurrency()))
        }
        
        updateTableViewContentInset()
    }
    
    private func getData(){
        guard let user = Global.shared.user, let userId = user.UserId, let tranId = (item?.Invoice_Id ?? invoiceId) else {
            return
        }
        self.showLoading()
        AccountProvider.rx.request(.getTransactionDetails(userId, tranId)).mapObject(DDKCResponse<TransactionHistoryDetailModel>.self)
            .subscribe(onNext: { [weak self] result in
                self?.dismissLoading()
                guard let `self` = self else {return}
                self.handlerResponse(result, success: {
                    guard let data = result.Object else{
                        return
                    }
                    self.fillData(data)
                })
                }, onError: { [weak self] _ in
                    self?.dismissLoading()
            }).disposed(by: disposeBag)
    }
    
    func updateTableViewContentInset() {
        self.tableView.reloadData()
        DispatchQueue.main.async {
            let tableViewHeight = self.tableView.frame.height
            let contentHeight = self.tableView.contentSize.height
            let centeringInset = (tableViewHeight - contentHeight) / 2.0
            let topInset = max(centeringInset, 0.0)
            self.tableView.contentInset = UIEdgeInsets(top: topInset, left: 0.0, bottom: 0.0, right: 0.0)
        }
    }
}

extension TransactionDetailViewController: UITableViewDelegate, UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return items.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: cellId) as! TransactionDetailCell
        cell.fillData(items[indexPath.row])
        return cell
    }
}

//
//  MemberCardCell.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 5/22/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit
import SwiftDate

class MemberCardCell: UITableViewCell {
    @IBOutlet weak var lbCardName: UILabel!
    @IBOutlet weak var lbStatus: LocalizableLabel!
    @IBOutlet weak var lbCardNumber: UILabel!
    @IBOutlet weak var lbRegisterDate: UILabel!

    override func awakeFromNib() {
        super.awakeFromNib()
        // Initialization code
    }

    override func updateViewWithItem(_ item: TableItem, indexPath: IndexPath) {
        guard let card = item.data as? CardModel else {
            return
        }
        lbCardName.text = Utils.shared.isEng() ? card.ClassNameF : card.ClassName
        lbCardNumber.text = card.CardNumber
        lbStatus.isHidden = card.Status != 1
        lbRegisterDate.text = card.DateEntered?.toDate("yyyy-MM-dd'T'HH:mm:ss")?.toString(dateFormat: "dd/MM/yyyy")
        if card.Status == 1 {
            lbCardName.textColor = .selected
            lbCardNumber.textColor = .selected
            lbRegisterDate.textColor = .selected
        } else {
            lbCardName.textColor = .inputText
            lbCardNumber.textColor = .inputText
            lbRegisterDate.textColor = .inputText
        }
    }
}

//
//  VoucherTableCell.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/20/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit
import RSBarcodes_Swift
import AVFoundation

class VoucherTableCell: UITableViewCell {
    @IBOutlet weak var lbCardType: UILabel!
    @IBOutlet weak var lbCardCode: UILabel!
    @IBOutlet weak var lbExpireDate: UILabel!
    @IBOutlet weak var ivBarcode: UIImageView!

    override func awakeFromNib() {
        super.awakeFromNib()
    }
    
    func fillData(_ item: VoucherModel){
        ivBarcode.image = RSUnifiedCodeGenerator.shared.generateCode(item.VoucherCode ?? "", machineReadableCodeObjectType: AVMetadataObject.ObjectType.code128.rawValue)
        lbCardCode.text = item.VoucherCode
        lbCardType.text = item.VoucherName
        if let endDate = Date.dateFromServer(item.EndDate){
            lbExpireDate.text = endDate.toStringStandard()
        }
    }

    override func updateViewWithItem(_ item: TableItem, indexPath: IndexPath) {
        
    }
}

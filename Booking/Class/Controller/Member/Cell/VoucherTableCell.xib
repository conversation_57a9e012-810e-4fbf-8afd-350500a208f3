<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14490.70" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14490.49"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="SourceSansPro-Bold.ttf">
            <string>SourceSansPro-Bold</string>
        </array>
        <array key="SourceSansPro-Regular.ttf">
            <string>SourceSansPro-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" rowHeight="281" id="aXS-vY-5hi" customClass="VoucherTableCell" customModule="Booking_dev" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="375" height="281"/>
            <autoresizingMask key="autoresizingMask"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="aXS-vY-5hi" id="7x7-T1-aKz">
                <rect key="frame" x="0.0" y="0.0" width="375" height="280.5"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="wnF-8n-gJF">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="280.5"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ZZh-DZ-u4u" customClass="RoundView" customModule="Booking_dev" customModuleProvider="target">
                                <rect key="frame" x="8" y="8" width="359" height="130"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Loại thẻ" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="39c-Vt-x3D" customClass="LocalizableLabel" customModule="Booking_dev" customModuleProvider="target">
                                        <rect key="frame" x="8" y="8" width="145.5" height="19"/>
                                        <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                        <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Voucher.CardType"/>
                                        </userDefinedRuntimeAttributes>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="2D AllDays" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="isQ-Xb-G5h">
                                        <rect key="frame" x="161.5" y="8" width="189.5" height="76"/>
                                        <fontDescription key="fontDescription" name="SourceSansPro-Bold" family="Source Sans Pro" pointSize="16"/>
                                        <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Mã thẻ" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="a6Q-1F-p9p" customClass="LocalizableLabel" customModule="Booking_dev" customModuleProvider="target">
                                        <rect key="frame" x="8" y="84" width="145.5" height="19"/>
                                        <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                        <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Voucher.CardCode"/>
                                        </userDefinedRuntimeAttributes>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="252" text="890 920 245" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="PJM-Dv-Q4F">
                                        <rect key="frame" x="161.5" y="84" width="189.5" height="19"/>
                                        <fontDescription key="fontDescription" name="SourceSansPro-Bold" family="Source Sans Pro" pointSize="16"/>
                                        <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Thời hạn sử dụng" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Exr-VE-h51" customClass="LocalizableLabel" customModule="Booking_dev" customModuleProvider="target">
                                        <rect key="frame" x="8" y="103" width="145.5" height="19"/>
                                        <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                        <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Voucher.ExpireDate"/>
                                        </userDefinedRuntimeAttributes>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="252" text="15/04/2018" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Pjf-9z-SO6">
                                        <rect key="frame" x="161.5" y="103" width="189.5" height="19"/>
                                        <fontDescription key="fontDescription" name="SourceSansPro-Bold" family="Source Sans Pro" pointSize="16"/>
                                        <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="Pjf-9z-SO6" firstAttribute="top" secondItem="Exr-VE-h51" secondAttribute="top" id="1Jg-M5-FRn"/>
                                    <constraint firstItem="PJM-Dv-Q4F" firstAttribute="width" secondItem="a6Q-1F-p9p" secondAttribute="width" multiplier="1.3" id="3cV-UI-y8A"/>
                                    <constraint firstItem="a6Q-1F-p9p" firstAttribute="leading" secondItem="ZZh-DZ-u4u" secondAttribute="leading" constant="8" id="60t-l6-QKE"/>
                                    <constraint firstAttribute="trailing" secondItem="Pjf-9z-SO6" secondAttribute="trailing" constant="8" id="8ca-Ky-ho8"/>
                                    <constraint firstAttribute="bottom" secondItem="Pjf-9z-SO6" secondAttribute="bottom" constant="8" id="E0i-Hd-osT"/>
                                    <constraint firstAttribute="trailing" secondItem="isQ-Xb-G5h" secondAttribute="trailing" constant="8" id="L4O-Iv-ygz"/>
                                    <constraint firstItem="PJM-Dv-Q4F" firstAttribute="top" secondItem="isQ-Xb-G5h" secondAttribute="bottom" id="OCU-dK-4Y4"/>
                                    <constraint firstItem="PJM-Dv-Q4F" firstAttribute="top" secondItem="a6Q-1F-p9p" secondAttribute="top" id="RvZ-1e-mYr"/>
                                    <constraint firstItem="Exr-VE-h51" firstAttribute="leading" secondItem="ZZh-DZ-u4u" secondAttribute="leading" constant="8" id="UTq-kw-keW"/>
                                    <constraint firstItem="isQ-Xb-G5h" firstAttribute="leading" secondItem="39c-Vt-x3D" secondAttribute="trailing" constant="8" id="Y5w-Jf-2cY"/>
                                    <constraint firstItem="Pjf-9z-SO6" firstAttribute="top" secondItem="PJM-Dv-Q4F" secondAttribute="bottom" id="fAN-eM-M5C"/>
                                    <constraint firstItem="39c-Vt-x3D" firstAttribute="top" secondItem="ZZh-DZ-u4u" secondAttribute="top" constant="8" id="ff4-rl-a5I"/>
                                    <constraint firstItem="Pjf-9z-SO6" firstAttribute="width" secondItem="Exr-VE-h51" secondAttribute="width" multiplier="1.3" id="jbZ-ks-Ec0"/>
                                    <constraint firstItem="Pjf-9z-SO6" firstAttribute="leading" secondItem="Exr-VE-h51" secondAttribute="trailing" constant="8" id="kns-Gv-7xm"/>
                                    <constraint firstItem="PJM-Dv-Q4F" firstAttribute="leading" secondItem="a6Q-1F-p9p" secondAttribute="trailing" constant="8" id="l7I-Jd-yVy"/>
                                    <constraint firstItem="isQ-Xb-G5h" firstAttribute="width" secondItem="39c-Vt-x3D" secondAttribute="width" multiplier="1.3" id="o5H-xC-h1M"/>
                                    <constraint firstItem="39c-Vt-x3D" firstAttribute="leading" secondItem="ZZh-DZ-u4u" secondAttribute="leading" constant="8" id="pCa-XQ-5Pg"/>
                                    <constraint firstItem="isQ-Xb-G5h" firstAttribute="top" secondItem="39c-Vt-x3D" secondAttribute="top" id="sFt-GR-8bK"/>
                                    <constraint firstAttribute="trailing" secondItem="PJM-Dv-Q4F" secondAttribute="trailing" constant="8" id="saY-6F-nvD"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="4"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="6WI-JH-6b4" customClass="DashView" customModule="Booking_dev" customModuleProvider="target">
                                <rect key="frame" x="18" y="138" width="339" height="6"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="6" id="bKT-1H-zmc"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="dashNumber">
                                        <integer key="value" value="25"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="dashWidth">
                                        <real key="value" value="4"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="color" keyPath="dashColor">
                                        <color key="value" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="U8O-MS-4BO" customClass="RoundView" customModule="Booking_dev" customModuleProvider="target">
                                <rect key="frame" x="8" y="144" width="359" height="128.5"/>
                                <subviews>
                                    <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="demo_barcode" translatesAutoresizingMaskIntoConstraints="NO" id="rrM-JE-W4O">
                                        <rect key="frame" x="8" y="8" width="343" height="112.5"/>
                                        <constraints>
                                            <constraint firstAttribute="width" secondItem="rrM-JE-W4O" secondAttribute="height" multiplier="343:113" constant="1" id="9u1-43-f28"/>
                                        </constraints>
                                    </imageView>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="rrM-JE-W4O" firstAttribute="top" secondItem="U8O-MS-4BO" secondAttribute="top" constant="8" id="IXy-PM-qMz"/>
                                    <constraint firstItem="rrM-JE-W4O" firstAttribute="leading" secondItem="U8O-MS-4BO" secondAttribute="leading" constant="8" id="Mgu-eb-Gce"/>
                                    <constraint firstAttribute="bottom" secondItem="rrM-JE-W4O" secondAttribute="bottom" constant="8" id="Tvv-Ip-8NC"/>
                                    <constraint firstAttribute="trailing" secondItem="rrM-JE-W4O" secondAttribute="trailing" constant="8" id="ql6-QF-pFr"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="4"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                        </subviews>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="6WI-JH-6b4" firstAttribute="leading" secondItem="wnF-8n-gJF" secondAttribute="leading" constant="18" id="17E-f6-QjH"/>
                            <constraint firstItem="ZZh-DZ-u4u" firstAttribute="top" secondItem="wnF-8n-gJF" secondAttribute="top" constant="8" id="2N0-Sm-9IO"/>
                            <constraint firstItem="ZZh-DZ-u4u" firstAttribute="leading" secondItem="wnF-8n-gJF" secondAttribute="leading" constant="8" id="2QZ-OS-wxJ"/>
                            <constraint firstItem="U8O-MS-4BO" firstAttribute="top" secondItem="6WI-JH-6b4" secondAttribute="bottom" id="6qG-Jz-mIa"/>
                            <constraint firstItem="U8O-MS-4BO" firstAttribute="leading" secondItem="wnF-8n-gJF" secondAttribute="leading" constant="8" id="7cg-ny-Iym"/>
                            <constraint firstAttribute="trailing" secondItem="U8O-MS-4BO" secondAttribute="trailing" constant="8" id="8es-qF-OGT"/>
                            <constraint firstAttribute="bottom" secondItem="U8O-MS-4BO" secondAttribute="bottom" constant="8" id="FR7-14-WPV"/>
                            <constraint firstAttribute="trailing" secondItem="6WI-JH-6b4" secondAttribute="trailing" constant="18" id="Hu9-BG-vmn"/>
                            <constraint firstAttribute="trailing" secondItem="ZZh-DZ-u4u" secondAttribute="trailing" constant="8" id="N2m-Zv-5MS"/>
                            <constraint firstItem="6WI-JH-6b4" firstAttribute="top" secondItem="ZZh-DZ-u4u" secondAttribute="bottom" id="ht7-3b-W4S"/>
                        </constraints>
                    </view>
                </subviews>
                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                <constraints>
                    <constraint firstItem="wnF-8n-gJF" firstAttribute="leading" secondItem="7x7-T1-aKz" secondAttribute="leading" id="Y7o-j2-Xs6"/>
                    <constraint firstAttribute="bottom" secondItem="wnF-8n-gJF" secondAttribute="bottom" id="crG-Xj-lkI"/>
                    <constraint firstAttribute="trailing" secondItem="wnF-8n-gJF" secondAttribute="trailing" id="fZw-z8-2D2"/>
                    <constraint firstItem="wnF-8n-gJF" firstAttribute="top" secondItem="7x7-T1-aKz" secondAttribute="top" id="tVr-mp-AN0"/>
                </constraints>
            </tableViewCellContentView>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <connections>
                <outlet property="ivBarcode" destination="rrM-JE-W4O" id="KSX-72-I3Z"/>
                <outlet property="lbCardCode" destination="PJM-Dv-Q4F" id="n2u-Q0-3df"/>
                <outlet property="lbCardType" destination="isQ-Xb-G5h" id="dQU-sZ-fad"/>
                <outlet property="lbExpireDate" destination="Pjf-9z-SO6" id="Z6b-1H-hl7"/>
            </connections>
            <point key="canvasLocation" x="115.5" y="120.5"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="demo_barcode" width="824" height="274"/>
    </resources>
</document>

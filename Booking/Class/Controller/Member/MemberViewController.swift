//
//  MemberViewController.swift
//  Booking
//
//  Created by <PERSON><PERSON> Vu on 3/14/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit
import VisualEffectView
import AlamofireImage
import RSBarcodes_Swift
import AVFoundation

class MemberViewController: BaseViewController {
    @IBOutlet weak var tableView: UITableView!
    @IBOutlet weak var headerView: UIView!
    @IBOutlet weak var bannerView: UIView!
    @IBOutlet weak var ivBanner: UIImageView!
    @IBOutlet weak var lbName: UILabel!
    @IBOutlet weak var lbCardNumber: UILabel!
    @IBOutlet weak var ivBarCode: UIImageView!
    @IBOutlet weak var ivMemberCard: UIImageView!
    @IBOutlet weak var ivVip: UIImageView!
    @IBOutlet weak var lbTotalMoney: UILabel!
    @IBOutlet weak var lbTotalPoint: UILabel!
    @IBOutlet weak var pvSpentMoney: UIProgressView!
    @IBOutlet weak var lbSpentMoney: UILabel!
    @IBOutlet weak var bannerBlurView: VisualEffectView!
    @IBOutlet weak var btAvatar: UIButton!
    @IBOutlet weak var lbVipCondition: UILabel!
    @IBOutlet weak var lbRemainPoint: UILabel!
    @IBOutlet weak var cTop: NSLayoutConstraint!
    
    let dataSource = SimpleTableViewDataSource()
    var vipCardClass: CardClassModel?
    fileprivate var lisVipCardClasses: [CardClassModel] = []

    let cellId = "SettingTableCell"

    var firstTime = true

    override func viewDidLoad() {
        super.viewDidLoad()
        localizableTitle = "Member.Title"
        self.navigationController?.isNavigationBarHidden = false
        self.navigationController?.setTransparent(false)
        initTableView()

        loadData()
        getCardClass()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        if let userId = Global.shared.user?.UserId {
            getProfile(id: userId)
        }
        firstTime = false
    }

    override func barTransparent() -> Bool {
        return false
    }

    override func localizationDidChange() {
        super.localizationDidChange()
        initMenu()
    }

    func initTableView() {
        dataSource.register(tableView: tableView, cellId: cellId)
        tableView.dataSource = dataSource

        headerView.autoFitSize()
        tableView.tableFooterView = nil
        tableView.tableHeaderView = headerView

        bannerView.addBottomRoundedEdge()

        if #available(iOS 11.0, *) {
            self.tableView.contentInsetAdjustmentBehavior = .never
        } else {
            self.automaticallyAdjustsScrollViewInsets = false
        }
    }

    func initMenu() {
        dataSource.removeAll()
        let accountItem = TableItem(title: "Member.AccountInfo".localized, cellId: cellId, tag: .accountInfo)
        let changePassItem = TableItem(title: "Member.ChangePass".localized, cellId: cellId, tag: .changePass)
        let cardItem = TableItem(title: "Member.MemberCard".localized, cellId: cellId, tag: .memberCard)
        let pointItem = TableItem(title: "Member.BetaPoint".localized, cellId: cellId, tag: .betaPoint)
        let historyItem = TableItem(title: "Member.TransactionHistory".localized, cellId: cellId, tag: .historyTransaction)
        let introItem = TableItem(title: "Member.Intro".localized, cellId: cellId, tag: .intro)
        let deleteItem = TableItem(title: "Member.DeleteAccount".localized, cellId: cellId, tag: .deleteAccount)


        let logoutItem = TableItem(cellId: "LogoutCell", tag: .logout)

        let rows = [pointItem, introItem, historyItem, cardItem, accountItem, changePassItem, deleteItem, logoutItem]

        dataSource.addRows(rows)
        tableView.reloadData()
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()

        tableView.frame = self.view.bounds

        headerView.autoFitSize()
        tableView.tableFooterView = nil
        tableView.tableHeaderView = headerView

        bannerView.addBottomRoundedEdge(1.5)

        self.pvSpentMoney.subviews.forEach { subview in
            subview.layer.masksToBounds = true
            subview.layer.cornerRadius = self.pvSpentMoney.frame.height / 2.0
        }
    }

    func openUpdateView() {
        let vc = UIStoryboard.authen[.updatePass] as! UpdatePasswordViewController
        vc.openAccountInfo = true
        self.navigationController?.pushViewController(vc, animated: true)
    }

    func loadData() {
        guard let user = Global.shared.user else {
            return
        }
        self.loadCurrentAvatar()
        let totalBill = user.TotalBillPayment ?? 0
        lbName.text = user.FullName
        lbCardNumber.text = user.CardNumber
        lbTotalMoney.text = totalBill.toCurrency()
        lbTotalPoint.text = user.TotalPoint?.toString ?? "0"
        ivBarCode.image =  RSUnifiedCodeGenerator.shared.generateCode(user.CardNumber ?? "", machineReadableCodeObjectType: AVMetadataObject.ObjectType.code128.rawValue)

        let topVipCard = self.lisVipCardClasses.last
        let currentCard = self.lisVipCardClasses.first(where: { $0.ClassId == user.ClassId })
        var nextCard: CardClassModel?
        var previousCard: CardClassModel?
        if let index = lisVipCardClasses.index(where: {$0.ClassId == currentCard?.ClassId}) {
            if index + 1 < lisVipCardClasses.count {
                nextCard = lisVipCardClasses[index + 1]
            } else {
                nextCard = lisVipCardClasses.last
            }
            if index - 1 >= 0 {
                previousCard = lisVipCardClasses[index - 1]
            } else {
                previousCard = lisVipCardClasses.first
            }
        }
        let vipMoney = topVipCard?.TotalPaymentCondition ?? 3000000
        lbVipCondition.text = (Int64(vipMoney)).toCurrency("")
        if let code = currentCard?.Code, code.contains("VIP") {
            pvSpentMoney.progress = 1.0
        } else {
            pvSpentMoney.progress = Float(totalBill) / Float(vipMoney)
        }
        let remainMoney = user.TotalRemainingBillsToUpgradeClass ?? 0

        ivVip.image = nil
        ivMemberCard.image = nil
        if currentCard?.ClassId != nil, currentCard?.ClassId == topVipCard?.ClassId {
            let cardName = currentCard?.Code ?? ""
            let text = "Member.ReachMaxVip".localized
            lbSpentMoney.text = text

            if let url = currentCard?.UrlIcon?.url(baseUrl: Config.BaseURLResource) {
                self.ivVip.af_setImage(withURL: url)
            }
            if let url = previousCard?.UrlIcon?.url(baseUrl: Config.BaseURLResource) {
                self.ivMemberCard.af_setImage(withURL: url)
            }
        } else {
            let leftMoney = Int(remainMoney).toCurrency("vnđ")
            let text = "Member.SpentToVip".localized.replacingOccurrences(of: "XXX", with: leftMoney)
            let attrbutedString = NSMutableAttributedString(string: text, attributes: [.font: UIFont(fontName: .SourceSansPro, style: .Bold,size: 14), .foregroundColor: UIColor.inputText])
            attrbutedString.addAttributes([.foregroundColor: UIColor(red: 253, green: 40, blue: 2)], range: NSRange(text.range(of: leftMoney)!, in: text))
            lbSpentMoney.attributedText = attrbutedString
            if let url = currentCard?.UrlIcon?.url(baseUrl: Config.BaseURLResource) {
                self.ivMemberCard.af_setImage(withURL: url)
            }
            if let url = nextCard?.UrlIcon?.url(baseUrl: Config.BaseURLResource) {
                self.ivVip.af_setImage(withURL: url)
            }
            if (totalBill >= vipMoney) {
                let hour = "23:59"
                let content = String(format: "Member.NextVip".localized, hour)
                let attrbutedString = NSMutableAttributedString(string: content, attributes: [.font: UIFont(fontName: .SourceSansPro, style: .Bold,size: 14), .foregroundColor: UIColor.inputText])
                attrbutedString.addAttributes([.foregroundColor: UIColor(red: 253, green: 40, blue: 2)], range: NSRange(content.range(of: hour)!, in: content))
                lbSpentMoney.attributedText = attrbutedString
            }
        }
        self.lbRemainPoint.remainPoint(point: user.AlmostExpiredPoint ?? 0, expiredOn: user.AlmostExpiredPointDate ?? "", cTop: cTop)
        initMenu()
    }

    private func getProfile(id: String){
        if firstTime {
            showLoading()
        }
        AccountProvider.rx.request(.getProfileById(id)).mapObject(DDKCResponse<UserModel>.self)
            .subscribe(onNext:{ [weak self] response in
                self?.dismissLoading()
                guard let object = response.Object else{
                    print("Data wrong")
                    self?.showAlert(title: "Alert.Error".localized, message: "Alert.ErrorServer".localized)
                    return
                }
                if response.isSuccess(){
                    if let user = Global.shared.user{
                        let fullUser = object
                        fullUser.Token = user.Token
                        fullUser.UserId = user.UserId
                        Global.shared.saveUser(fullUser)
                    }
                    self?.loadData()
                } else {
                    self?.showAlert(title: "Alert.Error".localized, message: response.Message ?? "Alert.ErrorServer".localized)
                }

                }, onError: { [weak self] _ in
                    self?.showAlert(title: "Alert.Error".localized, message: "Alert.ErrorServer".localized)
                    self?.dismissLoading()
            }).disposed(by: disposeBag)
    }

    func getCardClass() {
        AccountProvider.rx.request(.getCardClass).mapObject(DDKCResponse<CardClassModel>.self).subscribe(onNext: { response in
            self.lisVipCardClasses = response.ListObject ?? []
            self.loadData()
        }).disposed(by: disposeBag)
    }

    func loadCurrentAvatar() {
        guard let user = Global.shared.user else {
            return
        }

        let csCopy = CharacterSet(bitmapRepresentation: CharacterSet.urlPathAllowed.bitmapRepresentation)
        if let avatarUrl = user.Picture?.addingPercentEncoding(withAllowedCharacters: csCopy), let url = URL(string: Config.BaseURLImage + avatarUrl) {
            print("avatar url: \(avatarUrl) \(url)")
            btAvatar.setImage(#imageLiteral(resourceName: "ic_account_white"), for: .normal)
            btAvatar.af_setImage(for: .normal, url: url)
            ivBanner.af_setImage(withURL: url)
        } else {
            btAvatar.setImage(#imageLiteral(resourceName: "ic_account_white"), for: .normal)
            ivBanner.image = nil
        }
    }

    func uploadAvatar(_ image: UIImage) {
        guard let user = Global.shared.user else { return }
        guard let data = UIImageJPEGRepresentation(image, 0.8) else {
            // error
            return
        }
        let imageBase64 = data.base64EncodedString()
        self.showLoading()
        AccountProvider.rx.request(.uploadAvatar(user.AccountId ?? "", imageBase64)).mapObject(DDKCResponse<AvatarImageModel>.self).subscribe(onNext: { [weak self] result in
            self?.dismissLoading()
            if let avatar = result.Object {
                user.Picture = avatar.AvatarUrl
                Global.shared.saveUser(user)
            }

            self?.loadCurrentAvatar()
        }, onError: { [weak self] _ in
            self?.dismissLoading()
            self?.loadCurrentAvatar()
        }).disposed(by: disposeBag)
    }
    
    private func unRegisterDeviceToken() {
        guard let token: String = UserDefaults.standard.object(forKey: DefaultKey.deviceToken.rawValue) as? String,
            let accountId = Global.shared.user?.UserId else {
                return
        }
        let deviceId = UIDevice.current.identifierForVendor?.uuidString
        AccountProvider.rx.request(.unRegisterDeviceToken(deviceId ?? "", accountId, token)).subscribe().disposed(by: disposeBag)
    }

    @IBAction func changeAvatarBtPressed(_ sender: Any) {
        ImagePickerController.showImagePicker(allowEdit: true, from: self) { [weak self] image in
            self?.btAvatar.setImage(image, for: .normal)
            self?.ivBanner.image = image
            if let image = image {
                self?.uploadAvatar(image)
            }
        }
    }
}

extension MemberViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: false)

        let item = dataSource[indexPath]
        if item.tag == .accountInfo {
            if Global.shared.user?.IsUpdatedFacebookPassword == false || Global.shared.user?.IsUpdatedApplePassword == false {
                self.openUpdateView()
            } else {
                let vc = UIStoryboard.member[.confirmPass]
                show(vc, sender: nil)
            }
        } else if item.tag == .changePass {
            let vc = UIStoryboard.member[.changePass]
            show(vc, sender: nil)
        } else if item.tag == .memberCard {
            let vc = UIStoryboard.member[.memberCard]
            show(vc, sender: nil)
        } else if item.tag == .betaPoint {
            let vc = UIStoryboard.member[.rewardPoints]
            show(vc, sender: nil)
        } else if item.tag == .intro {
            guard let appIntro = Global.shared.appIntro?.value else {
                return
            }
            if appIntro == "0" {
                return
            }
            let vc = IntroToFriendViewController()
            show(vc, sender: nil)
        } else if item.tag == .historyTransaction {
            let vc = UIStoryboard.member[.transactionHistory]
            show(vc, sender: nil)
        } else if item.tag == .deleteAccount {
            UIAlertController.showConfirmRed(self, title:"Alert.DeleteAccountTitle".localized,
                                        message:"Alert.DeleteAccountMessage".localized,cancelButton:"Bt.Cancel".localized,okHandler: { _ in
                //delete
                let preferences = UserDefaults.standard
                let currentKey = "delete_account"
                let currentValue = Global.shared.user?.UserId
                preferences.set(currentValue, forKey: currentKey)
                let didSave = preferences.synchronize()
                //logout
                self.logOut()
            })
       }else if item.tag == .logout {
           self.logOut()
        }
    }
    
    func logOut(){
        Tracking.shared.logout()
        self.unRegisterDeviceToken()
        Global.shared.logout()
        self.updateNotifications(true)
        let loginVC = UIStoryboard.authen[.login]
        self.navigationController?.pushViewController(loginVC, animated: true)
        self.navigationController?.viewControllers.remove(at: 1)
    }
}

extension TableItemTag {
    static let accountInfo = TableItemTag(value: 1)
    static let changePass = TableItemTag(value: 2)
    static let memberCard = TableItemTag(value: 3)
    static let betaPoint = TableItemTag(value: 4)
    static let intro = TableItemTag(value: 5)
    static let dealsCard = TableItemTag(value: 6)
    static let historyTransaction = TableItemTag(value: 7)
    static let deleteAccount = TableItemTag(value: 8)
    static let logout = TableItemTag(value: 9)
}

//
//  FilmDetailViewController.swift
//  Booking
//
//  Created by <PERSON><PERSON> Vu on 3/14/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit
import AlamofireImage
import UITableView_FDTemplateLayoutCell
import AVKit
import SwiftDate
import youtube_ios_player_helper

class FilmDetailCell: UITableViewCell {
    @IBOutlet weak var lbTitle: UILabel!
    @IBOutlet weak var lbContent: UILabel!

    override func awakeFromNib() {
        super.awakeFromNib()
        selectionStyle = .none
    }

    override func updateViewWithItem(_ item: TableItem, indexPath: IndexPath) {
        lbTitle.text = item.title
        lbContent.text = item.content
    }
}

extension TableSectionTag {
    static let filmDetail = TableSectionTag(value: 1)
    static let newsAndDeals = TableSectionTag(value: 2)
}

class FilmDetailViewController: StickyHeaderViewController {
    @IBOutlet weak var tableView: UITableView!
    @IBOutlet weak var ivFilmBanner:UIImageView!
    @IBOutlet weak var ivFilmLogo: UIImageView!
    @IBOutlet weak var headerView: UIView!
    @IBOutlet weak var lbName: UILabel!
    @IBOutlet weak var lbWarning: UILabel!
    @IBOutlet weak var vBottom: UIView!
    @IBOutlet weak var vAgeRestrict: RoundView!
    @IBOutlet weak var shareButton: LocalizableButton!
    

    let cellId = "FilmDetailCell"
    let descCellId = "FilmDescriptionTableCell"
    let newsCellId = "NewsAndDealsCell"
    var film: FilmModel?
    var newsData: [NewsModel]?
    var isNowShowing: Bool = true
    var filmId: String?
    var fromBooking = false

    override func barTransparent() -> Bool {
        return false
    }
    
    var titles = ["FilmDetail.Director", "FilmDetail.Actor", "FilmDetail.Type", "FilmDetail.Duration", "FilmDetail.Language", "FilmDetail.DateShow"]
    fileprivate let dataSource = SimpleTableViewDataSource()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        navigationController?.isNavigationBarHidden = false
        navigationController?.setTransparent(false)
        if #available(iOS 11.0, *) {
            self.tableView.contentInsetAdjustmentBehavior = .never
        } else {
            self.automaticallyAdjustsScrollViewInsets = false
        }
        isNowShowing = film?.HasShow ?? false

        localizableTitle = "FilmDetail.Title"
        self.initTableView()
        self.fillData()

        viewDidLayoutSubviews()

        getFilmDetail(film?.FilmId ?? filmId)
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        self.fillData()
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        setFilmBannerMask()
        headerView.autoFitSize()
        tableView.tableHeaderView = headerView
        tableView.frame = CGRect(x: 0, y: 0, width: self.view.bounds.width, height: self.view.bounds.height - vBottom.bounds.height)

        var safe:CGFloat = 0
        if #available(iOS 11.0, *) {
            safe = self.additionalSafeAreaInsets.bottom
        }
        let bottom = UIEdgeInsets(top: 0, left: 0, bottom: vBottom.frame.height + safe, right: 0)
        tableView.contentInset = bottom
        tableView.scrollIndicatorInsets = bottom
    }

    func setFilmBannerMask() {
        ivFilmBanner.addBottomRoundedEdge()
    }

    func playVideo(_ url: URL) {
        let player = AVPlayer(url: url)
        let playerViewController = AVPlayerViewController()
        playerViewController.player = player
        self.present(playerViewController, animated: true) {
            playerViewController.player!.play()
        }
    }

    @IBAction func shareButtonPressed(_ sender: Any) {
        if film?.HasShow == true, !fromBooking {
            let vc = UIStoryboard.film[.filmChooseTime] as! FilmChooseTimeViewController
            vc.film = self.film
            Tracking.shared.selectMovie(movieId:self.film?.FilmId,movieName: self.film?.getName())
            show(vc)
        } else {
            share(data: [film?.getFilmURL()].compactMap{$0})
        }
    }

    @IBAction func playTrailerButtonPressed(_ sender: Any) {
        guard let urlString = film?.TrailerURL, let url = URL(string: urlString) else {
            self.showAlert(message: "Alert.NoFilmTrailerURL".localized)
            return
        }

        if url.isYoutubeURL {
            let vc = UIStoryboard.home[.youtube] as! YoutubeViewController
            vc.videoURL = urlString
            present(vc, animated: true, completion: nil)
        } else {
            self.playVideo(url)
        }
    }
    @IBAction func buyTicketButtonPressed(_ sender: Any) {

    }
}

extension FilmDetailViewController{
    private func initTableView(){
        self.tableView.register(UINib(nibName: newsCellId, bundle: nil), forCellReuseIdentifier: newsCellId)
        self.tableView.dataSource = dataSource
    }
    private func fillData(){
        guard let film = self.film else {return}

        var posterUrl: String?
        if let url = film.MainPosterUrl{
            let urlString = Config.BaseURLResource + url
            self.ivFilmLogo.af_setImage(withURL: URL(string: urlString)!)
            posterUrl = urlString
        }

        if let posterModel = film.ListPosterUrl?.filter({!($0.MainPoster ?? true)}).first{
            let url = Config.BaseURLResource + (posterModel.AbsolutePath ?? "")
            self.ivFilmBanner.af_setImage(withURL: URL(string: url)!)
        } else if let posterUrl = posterUrl {
            self.ivFilmBanner.af_setImage(withURL: URL(string: posterUrl)!)
        }
        
        lbName.text = film.getName()

        vAgeRestrict.isHidden = false
        if film.FilmRestrictAgeName == FilmModel.RestrictAge.c13 {
            lbWarning.text = "AgeRestrict.C13".localized;
        } else if film.FilmRestrictAgeName == FilmModel.RestrictAge.c16 {
            lbWarning.text = "AgeRestrict.C16".localized;
        } else if film.FilmRestrictAgeName == FilmModel.RestrictAge.c18 {
            lbWarning.text = "AgeRestrict.C18".localized;
        } else {
            vAgeRestrict.isHidden = true
        }

        var items = [TableItem]()
        items.append(TableItem(title: titles[0].localized.uppercased(), content: film.Director, cellId: cellId))

        items.append(TableItem(title: titles[1].localized.uppercased(), content: film.Actors, cellId: cellId))

        items.append(TableItem(title: titles[2].localized.uppercased(), content: film.getFilmGenre(), cellId: cellId))

        items.append(TableItem(title: titles[3].localized.uppercased(), content: "\(film.Duration ?? 0) " + "Home.Minute".localized, cellId: cellId))

        items.append(TableItem(title: titles[4].localized.uppercased(), content: film.getLanguage(), cellId: cellId))

        items.append(TableItem(title: titles[5].localized.uppercased(), content: Date.dateFromServerSavis(film.OpeningDate ?? "").toString(dateFormat: "dd/MM/yyyy"), cellId: cellId))
        let filmSection = TableSection(items: items, tag: .filmDetail)

        let descSection = TableSection(items: [TableItem(title: film.getShortDescription(), cellId: descCellId)], tag: .none)

        let newsCell = NewsAndDealsCell.nib()
        newsCell.data = newsData
        newsCell.newsAndDealsView.delegate = self
        let newsSection = TableSection(items: [TableItem(cell: newsCell)], tag: .newsAndDeals)
        dataSource.removeAll()
        dataSource.addSection([filmSection, descSection, newsSection])

        if newsData?.isEmpty != false {
            self.getPromotionCategory()
        }
        
        self.tableView.reloadData()

        if film.HasShow == true, !fromBooking {
            shareButton.localizableString = "FilmDetail.BuyTicket"
        } else {
            shareButton.localizableString = "FilmDetail.Share"
        }
    }
}

extension FilmDetailViewController{
    private func getFilmDetail(_ id: String?){
        guard let id = id else {
            return
        }
        if filmId != nil {
            self.showLoading()
        }
        FilmProvider.rx.request(.filmDetail(id)).mapObject(DDKCResponse<FilmModel>.self)
            .subscribe(onNext:{ response in
                self.dismissLoading()
                if let film = response.Object {
                    self.film = film
                    self.fillData()
                }
            }).disposed(by: disposeBag)
    }

    private func getPromotionCategory(){
        EcmProvider.rx.request(.getNewPromotion).mapObject(DDKCResponse<NewModel>.self)
            .subscribe(onNext:{[weak self] response in
                self?.handlerResponse(response, success: {
                    guard let objects = response.ListObject else{
                        print("Data wrong")
                        return
                    }
                    if objects.count > 0{
                        self?.getListPromotion(objects.first?.CategoryId)
                    }
                })
            }).disposed(by: disposeBag)
    }

    private func getListPromotion(_ categoryId: String?){
        guard let id = categoryId else {
            return
        }
        EcmProvider.rx.request(.getNewForCategory(id, 3, nil)).mapObject(DDKCResponse<NewsModel>.self)
            
            .subscribe(onNext:{[weak self] response in
                self?.handlerResponse(response, success: {
                    guard let items = response.ListObject, let `self` = self else {return}
                    DispatchQueue.main.async {
                        let cellItem = self.dataSource.itemAt(IndexPath(row: 0, section: 2))
                        if let cell = cellItem?.cell as? NewsAndDealsCell {
                            cell.data = items
                            self.tableView.reloadRows(at: [IndexPath(row: 0, section: 2)], with: .automatic)
                        }
                    }
                })
            }).disposed(by: disposeBag)
    }
}

extension FilmDetailViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: false)
    }
}

extension FilmDetailViewController: UIScrollViewDelegate {
    
}

extension FilmDetailViewController: NewsAndDealsViewDelegate {
    func newsView(_ view: NewAndDealsView, didSelected item: NewsModel) {
        let vc = UIStoryboard.home[.newsDetail] as! NewsDetailViewController
        vc.type = NewType.news(item)
        show(vc, sender: nil)
    }

    func newsViewDidShowAll() {
        let vc = UIStoryboard.home[.newsAndDeals]
        show(vc, sender: nil)
    }

    func didChangeHeight(_ height: CGFloat) {
        if let cell = dataSource.sections.first(where: {$0.tag == .newsAndDeals})?.items.first?.cell as? NewsAndDealsCell {
            tableView.beginUpdates()
            cell.heightConstraint.constant = height + 50
            tableView.endUpdates()
        }
    }
}

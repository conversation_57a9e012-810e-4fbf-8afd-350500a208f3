//
//  FilmTimeTableCell.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/27/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit
import SwiftDate

protocol CinemaFilmTimeDelegate: class {
    func didSelect(_ cinema: CinemaModel?, show: ShowModel, index: IndexPath?)
}

class CinemaFilmTimeTableCell: UITableViewCell {
    @IBOutlet weak var morningTimeListView: TimeListView!
    @IBOutlet weak var morningView: UIView!
    @IBOutlet weak var filmTypeLabel: UILabel!
    @IBOutlet weak var separatorView: UIView!

    weak var delegate: CinemaFilmTimeDelegate?
    var cinema: CinemaModel?
    var indexPath: IndexPath?

    override func awakeFromNib() {
        super.awakeFromNib()
        morningTimeListView.delegate = self
        selectionStyle = .none
    }

    override func updateViewWithItem(_ item: TableItem, indexPath: IndexPath) {
        guard let show = item.data as? ListFilm else {
            return
        }

        filmType<PERSON><PERSON>.text = show.filmFormat?.uppercased()

        morningTimeListView.data = show.dayList
        morningView.isHidden = show.dayList.isEmpty
        separatorView.isHidden = indexPath.row == 0
    }

    func configure(_ item: ListFilm?, indexPath: IndexPath) {
        guard let show = item else {
            return
        }

        self.indexPath = indexPath

        filmTypeLabel.text = show.filmFormat?.uppercased()

        morningTimeListView.data = show.dayList
        morningView.isHidden = show.dayList.isEmpty
        separatorView.isHidden = indexPath.row == 0
    }
}

extension CinemaFilmTimeTableCell: TimeListViewDelegate {
    func timeListDidSelected(_ item: ShowModel) {
        delegate?.didSelect(cinema, show: item, index: indexPath)
    }
}

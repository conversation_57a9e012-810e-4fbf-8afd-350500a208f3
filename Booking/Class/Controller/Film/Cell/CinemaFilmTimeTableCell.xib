<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14490.70" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14490.49"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Oswald-Bold.ttf">
            <string>Oswald-Bold</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" rowHeight="248" id="p2L-Tv-Te1" customClass="CinemaFilmTimeTableCell" customModule="Booking_dev" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="414" height="248"/>
            <autoresizingMask key="autoresizingMask"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="p2L-Tv-Te1" id="WBZ-90-ceG">
                <rect key="frame" x="0.0" y="0.0" width="414" height="247.5"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="2rs-29-Wdm">
                        <rect key="frame" x="16" y="45" width="382" height="72"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="TXt-qr-feP" userLabel="View1">
                                <rect key="frame" x="0.0" y="0.0" width="382" height="72"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="6ci-l5-vqa" customClass="TimeListView" customModule="Booking_dev" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="6" width="382" height="60"/>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="bottom" secondItem="6ci-l5-vqa" secondAttribute="bottom" priority="900" constant="6" id="0Gk-Xh-hfo"/>
                                    <constraint firstItem="6ci-l5-vqa" firstAttribute="top" secondItem="TXt-qr-feP" secondAttribute="top" priority="900" constant="6" id="KrV-OO-4Rp"/>
                                    <constraint firstAttribute="height" constant="72" id="fBC-4k-atq"/>
                                    <constraint firstItem="6ci-l5-vqa" firstAttribute="leading" secondItem="TXt-qr-feP" secondAttribute="leading" id="gVk-L4-1UC"/>
                                    <constraint firstAttribute="trailing" secondItem="6ci-l5-vqa" secondAttribute="trailing" id="yDb-8Q-ReR"/>
                                </constraints>
                            </view>
                        </subviews>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="TXt-qr-feP" firstAttribute="leading" secondItem="2rs-29-Wdm" secondAttribute="leading" id="8DD-bt-LUK"/>
                            <constraint firstAttribute="trailing" secondItem="TXt-qr-feP" secondAttribute="trailing" id="HtF-6c-5i5"/>
                            <constraint firstItem="TXt-qr-feP" firstAttribute="top" secondItem="2rs-29-Wdm" secondAttribute="top" id="rJJ-Nr-y6c"/>
                        </constraints>
                    </stackView>
                    <view alpha="0.10000000149011612" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="eVS-ry-PVM">
                        <rect key="frame" x="8" y="0.0" width="398" height="1"/>
                        <color key="backgroundColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="1" id="CWn-dy-MAx"/>
                        </constraints>
                    </view>
                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="3D PHỤ ĐỀ" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="JEX-Le-AfS">
                        <rect key="frame" x="16" y="17" width="71" height="24"/>
                        <fontDescription key="fontDescription" name="Oswald-Bold" family="Oswald" pointSize="16"/>
                        <color key="textColor" white="0.33333333329999998" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <nil key="highlightedColor"/>
                    </label>
                </subviews>
                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                <constraints>
                    <constraint firstAttribute="trailing" secondItem="eVS-ry-PVM" secondAttribute="trailing" constant="8" id="2YY-IU-fN6"/>
                    <constraint firstAttribute="trailing" secondItem="2rs-29-Wdm" secondAttribute="trailing" constant="16" id="6fw-8c-syE"/>
                    <constraint firstItem="JEX-Le-AfS" firstAttribute="top" secondItem="eVS-ry-PVM" secondAttribute="bottom" constant="16" id="GWr-Sr-vH3"/>
                    <constraint firstItem="2rs-29-Wdm" firstAttribute="leading" secondItem="WBZ-90-ceG" secondAttribute="leading" constant="16" id="Hhp-5S-QEk"/>
                    <constraint firstItem="JEX-Le-AfS" firstAttribute="leading" secondItem="WBZ-90-ceG" secondAttribute="leading" constant="16" id="KKQ-zS-pyT"/>
                    <constraint firstItem="2rs-29-Wdm" firstAttribute="top" secondItem="JEX-Le-AfS" secondAttribute="bottom" constant="4" id="Mm7-dW-HaJ"/>
                    <constraint firstItem="2rs-29-Wdm" firstAttribute="top" secondItem="JEX-Le-AfS" secondAttribute="bottom" constant="4" id="fwn-Nr-FKU"/>
                    <constraint firstItem="eVS-ry-PVM" firstAttribute="top" secondItem="WBZ-90-ceG" secondAttribute="top" id="gde-2g-Kpn"/>
                    <constraint firstItem="eVS-ry-PVM" firstAttribute="leading" secondItem="WBZ-90-ceG" secondAttribute="leading" constant="8" id="h4S-Py-lef"/>
                    <constraint firstAttribute="bottom" relation="greaterThanOrEqual" secondItem="2rs-29-Wdm" secondAttribute="bottom" id="irT-nR-nqd"/>
                </constraints>
            </tableViewCellContentView>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <connections>
                <outlet property="filmTypeLabel" destination="JEX-Le-AfS" id="FNR-jW-32K"/>
                <outlet property="morningTimeListView" destination="6ci-l5-vqa" id="fcs-w7-ryz"/>
                <outlet property="morningView" destination="TXt-qr-feP" id="RGu-9Q-ACN"/>
                <outlet property="separatorView" destination="eVS-ry-PVM" id="CDC-Xg-ZVM"/>
            </connections>
            <point key="canvasLocation" x="-101" y="148"/>
        </tableViewCell>
    </objects>
</document>

<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14109" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14088"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="none" indentationWidth="10" reuseIdentifier="NewsAndDealsCell" rowHeight="104" id="Flf-YR-beb" customClass="NewsAndDealsCell" customModule="Booking_dev" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="375" height="104"/>
            <autoresizingMask key="autoresizingMask"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="Flf-YR-beb" id="8ES-xd-lhV">
                <rect key="frame" x="0.0" y="0.0" width="375" height="103.5"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="5jr-oF-uPS" customClass="NewAndDealsView" customModule="Booking_dev" customModuleProvider="target">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="103.5"/>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="103.5" id="fl2-bX-F0f"/>
                        </constraints>
                    </view>
                </subviews>
                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                <constraints>
                    <constraint firstAttribute="bottom" secondItem="5jr-oF-uPS" secondAttribute="bottom" id="CVL-Ic-doi"/>
                    <constraint firstItem="5jr-oF-uPS" firstAttribute="leading" secondItem="8ES-xd-lhV" secondAttribute="leading" id="FZn-kF-td5"/>
                    <constraint firstItem="5jr-oF-uPS" firstAttribute="top" secondItem="8ES-xd-lhV" secondAttribute="top" id="as4-OB-Yta"/>
                    <constraint firstAttribute="trailing" secondItem="5jr-oF-uPS" secondAttribute="trailing" id="jg3-qV-tao"/>
                </constraints>
            </tableViewCellContentView>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <connections>
                <outlet property="heightConstraint" destination="fl2-bX-F0f" id="Uox-Ol-diL"/>
                <outlet property="newsAndDealsView" destination="5jr-oF-uPS" id="YcW-nh-Ug6"/>
            </connections>
            <point key="canvasLocation" x="-31.5" y="139"/>
        </tableViewCell>
    </objects>
</document>

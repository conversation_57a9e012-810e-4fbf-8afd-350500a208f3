//
//  NewsAndDealsCell.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/10/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

class NewsAndDealsCell: UITableViewCell {
    @IBOutlet weak var newsAndDealsView: NewAndDealsView!
    @IBOutlet weak var heightConstraint: NSLayoutConstraint!

    var data: [NewsModel]? {
        didSet {
            if let data = data {
                newsAndDealsView.dataList = data
                heightConstraint.constant = CGFloat(data.count) * 120 + 50                
            }
        }
    }

    static func nib() -> NewsAndDealsCell {
        return Bundle(for: NewsAndDealsCell.self).loadNibNamed(self.className, owner: self, options: nil)?.first as! NewsAndDealsCell
    }

    override func awakeFromNib() {
        super.awakeFromNib()
    }
}

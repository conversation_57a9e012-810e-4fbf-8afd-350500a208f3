//
//  FilmChooseTimeViewController.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/26/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit
import PopupDialog

class FilmChooseTimeViewController: BaseViewController {

    @IBOutlet weak var tableView: UITableView!
    @IBOutlet weak var headerView: UIView!
    @IBOutlet weak var ivFilmBanner: UIImageView!
    @IBOutlet weak var vGradientBanner: GradientView!
    @IBOutlet weak var lbFimName: UILabel!
    @IBOutlet weak var lbFilmType: UILabel!
    @IBOutlet weak var calendarView: CalendarHeaderView!
    @IBOutlet weak var lbRegion: UILabel!
    @IBOutlet weak var detailButton: RoundButton!

    fileprivate var selectedRegion: CinemaProvinceModel? {
        didSet {
            lbRegion.text = selectedRegion?.CityName ?? "Home.All".localized
        }
    }
    fileprivate var selectedShow: ShowModel?

    let dataSource = SimpleTableViewDataSource()
    let cellId = "CinemaFilmTimeTableCell"
    let headerViewId = "AreaCinemaHeaderView"

    var film: FilmModel?
    var showCinemaList: [ShowCinemaModel] = []
    var fromHome: Bool = false
    var currentPage: Int = 1
    var isLoading: Bool = false
    var hasMoreData: Bool = true
    var dateFirst: Date?
    private let footerView = UIView()
    private let loadMoreButton = UIButton(type: .system)
    private let activityIndicator = UIActivityIndicatorView(activityIndicatorStyle: .medium)

    override func viewDidLoad() {
        super.viewDidLoad()
        navigationController?.setTransparent(false)
        localizableTitle = "BookingByFilm.Title"
        lbRegion.text = "Home.All".localized

        calendarView.delegate = self

        // Do any additional setup after loading the view.
        updateData()
         
        initTableView()
        initGradientView()

        calendarView.selectIndex(0)

        if !LocationManager.shared.systemEnable() {
            LocationManager.shared.openLocationAlert()
        }

        LocationManager.shared.requestCurrentLocation { location in
            self.updateListShow()
        }

        getFilmData()

    }

    override func didReceiveMemoryWarning() {
        super.didReceiveMemoryWarning()
        // Dispose of any resources that can be recreated.
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        updateTableView()
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)

        guard let show = self.selectedShow, Global.shared.isLogined else {
            selectedShow = nil
            return
        }
        selecteShow(show)
    }

    override func localizationDidChange() {
        super.localizationDidChange()
        lbRegion.text = selectedRegion?.CityName ?? "Home.All".localized
    }

    func initTableView() {
        tableView.register(UINib(nibName: headerViewId, bundle: nil), forHeaderFooterViewReuseIdentifier: headerViewId)
        tableView.registerCell(id: cellId)
        updateTableView()
        configureTableFooter()
        tableView.dataSource = dataSource

        detailButton.isHidden = !fromHome
        detailButton.setTitle("film_detail".localized, for: .normal)
    }

    @objc private func loadMoreData() {
        guard hasMoreData else { return }
        currentPage += 1
        if let date = dateFirst {
            loadData(date: date, page: currentPage)
        }
    }

    private func configureTableFooter() {
        footerView.frame = CGRect(x: 0, y: 0, width: view.frame.width, height: 50)
        let image = UIImage(named: "ic_arrow_down_render")
        loadMoreButton.setImage(image, for: .normal)
        let titleButton = " " + "see_more".localized
        loadMoreButton.setTitle(titleButton, for: .normal)
        loadMoreButton.addTarget(self, action: #selector(loadMoreData), for: .touchUpInside)
        loadMoreButton.frame = footerView.bounds
        footerView.addSubview(loadMoreButton)

        // Activity Indicator
        activityIndicator.hidesWhenStopped = true
        activityIndicator.center = footerView.center
        footerView.addSubview(activityIndicator)

        tableView.tableFooterView = nil
    }

    func updateTableView() {
        headerView.autoFitSize()
        tableView.tableHeaderView = headerView
    }

    private func updateFooterView(isLoading: Bool) {
        loadMoreButton.isHidden = isLoading
        if isLoading {
            activityIndicator.startAnimating()
        } else {
            activityIndicator.stopAnimating()
        }
    }

    func loadData(date: Date, page: Int) {
        guard !isLoading else { return }
        isLoading = true
        updateFooterView(isLoading: true)
        print("Đang tải dữ liệu cho trang \(page)...")

        let currentLat = UserDefaults.standard.double(forKey: Constant.Lat)
        let currentLng = UserDefaults.standard.double(forKey: Constant.Lng)
        let dateString = date.toString(dateFormat: "yyyy-MM-dd")
        FilmProvider.rx.request(.filmShowLocation(film?.FilmId ?? "", dateString, "\(currentLat)", "\(currentLng)", "\(currentPage)")).mapObject(DDKCResponse<PaginatedResponse>.self).subscribe(onNext: { [weak self] result in
            guard let `self` = self else { return }
            guard let items = result.Object else {
                return
            }
            self.showCinemaList.append(contentsOf: items.content ?? [])
            self.updateListShow()
            print("number : \(items.totalPages ?? 1) ")
            if currentPage >= (items.totalPages ?? 1) {
                self.hasMoreData = false
                self.tableView.tableFooterView = nil // Ẩn footer nếu không còn dữ liệu
            }
            self.isLoading = false
            self.updateFooterView(isLoading: false)
        }, onError: { [weak self] _ in
            self?.isLoading = false
            self?.updateFooterView(isLoading: false)
        }).disposed(by: disposeBag)
    }

    func initGradientView() {
        let color = UIColor(red: 243, green: 243, blue: 243)
        vGradientBanner.colors = [color, color.withAlphaComponent(0.58), color.withAlphaComponent(0.0)]
    }

    func updateData() {
        guard let film = film else { return }
        var posterUrl: String?
        if let url = film.MainPosterUrl{
            let urlString = Config.BaseURLResource + url
            posterUrl = urlString
        }

        if let posterModel = film.ListPosterUrl?.filter({!($0.MainPoster ?? true)}).first{
            let url = Config.BaseURLResource + (posterModel.AbsolutePath ?? "")
            self.ivFilmBanner.af_setImage(withURL: URL(string: url)!)
        } else if let posterUrl = posterUrl {
            self.ivFilmBanner.af_setImage(withURL: URL(string: posterUrl)!)
        }
        lbFimName.text = film.getName()
        lbFilmType.text = film.getHalfOptions()
    }

    func getFilmData() {
        getFilmShowTime()
    }

    func getFilmShowTime() {
        guard let film = self.film else { return }
        showLoading()
        FilmProvider.rx.request(.filmShowDate(film.FilmId ?? "")).mapObject(MyResponse<String>.self).subscribe(onNext: { response in
            guard response.isSuccess() else {
                self.calendarView.dates = []
                self.showAlert(message: response.Message ?? "")
                self.dismissLoading()
                return
            }
            guard let list = response.ListObject else {
                self.calendarView.dates = []
                self.dismissLoading()
                return
            }
          let listDate = list.compactMap { $0.dateFromServer() }.sorted{ $0 < $1}
            self.calendarView.dates = listDate
            if let date = listDate.first {
                self.getFilmShowAtDate(date)
                self.calendarView.selectDate(date)
            } else {
                self.dismissLoading()
            }
        }).disposed(by: disposeBag)
    }

    func getFilmShowAtDate(_ date: Date) {
        dateFirst = date
        currentPage = 1
        hasMoreData = true
        let currentLat = UserDefaults.standard.double(forKey: Constant.Lat)
        let currentLng = UserDefaults.standard.double(forKey: Constant.Lng)
        let dateString = date.toString(dateFormat: "yyyy-MM-dd")
        FilmProvider.rx.request(.filmShowLocation(film?.FilmId ?? "", dateString, "\(currentLat)", "\(currentLng)", "1")).mapObject(DDKCResponse<PaginatedResponse>.self).subscribe(onNext: { [weak self] result in
            guard let `self` = self else { return }
            self.dismissLoading()
            guard let items = result.Object else {
                return
            }
            self.showCinemaList = items.content ?? []

            tableView.tableFooterView = currentPage >= items.totalPages ?? 1 ? nil : footerView
            self.updateListShow()
        }, onError: { [weak self] _ in
            self?.dismissLoading()
        }).disposed(by: disposeBag)
    }

    func updateListShow() {
        if LocationManager.shared.systemEnable() {
            let items = self.showCinemaList.filter { show in self.selectedRegion == nil ? true : self.selectedRegion?.ListCinema?.first { $0.CinemaId == show.cinemaId } != nil }
                .sorted(by: {$0.getDistanceDouble() < $1.getDistanceDouble()})
            self.dataSource.removeAll()
            self.dataSource.addSection(items.map{ TableSection(title: $0.getName(), subTitle: $0.getDistance() + "km", items: ($0.listFilm ?? []).map({ (film) -> TableItem in
                return TableItem(title: "\(film)", data: film, cellId: self.cellId)
            }), isOpen: false) } )
        } else {
            let items = self.showCinemaList.filter { show in self.selectedRegion == nil ? true : self.selectedRegion?.ListCinema?.first { $0.CinemaId == show.cinemaId } != nil }
                .sorted(by: {$0.getName()?.compare($1.getName() ?? "") == .orderedAscending})
            self.dataSource.removeAll()
            self.dataSource.addSection(items.map{ TableSection(title: $0.getName(), subTitle: "", items: ($0.listFilm ?? []).map({ (film) -> TableItem in
                return TableItem(title: "\(film)", data: film, cellId: self.cellId)
            }), isOpen: false) } )
        }
        self.dataSource.sections.first?.isOpen = true
        self.tableView.reloadData()
    }

    func selecteShow(_ show: ShowModel) {
        var cinemaId: String?
        var cinemaName: String?
        if let first = (self.showCinemaList.first{
            return $0.listFilm?.contains{
                return $0.listShow?.contains{$0.showId == show.showId
                } == true
            } == true}) {
            cinemaId = first.cinemaId
            cinemaName = first.getName()
            }
        let vc = UIStoryboard.cinema[.chooseSeat] as! ChooseSeatViewController
        vc.showTime = show
        vc.film = film
        vc.cinemaId =  cinemaId
        vc.cinemaName =  cinemaName
        Tracking.shared.selectShowtimeComplete(cinemaId:  vc.cinemaId, cinemaName: vc.cinemaName, movieId: film?.FilmId, movieName: film?.getName(), date: vc.showTime?.getStartDate(), time:  vc.showTime?.getStartDate())
        self.show(vc)

        self.selectedShow = nil
    }

    @IBAction func selectRegionButtonPressed(_ sender: Any) {
        let vc = UIStoryboard.cinema[.selectRegion] as! SelectRegionViewController
        vc.delegate = self
        show(vc)
    }

    @IBAction func detailTapped(_ sender: Any) {
        let vc = UIStoryboard.film[.filmDetail] as! FilmDetailViewController
        vc.film = film
        vc.fromBooking = true
        show(vc)
    }

}

extension FilmChooseTimeViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        let view = tableView.dequeueReusableHeaderFooterView(withIdentifier: headerViewId) as? AreaCinemaHeaderView
        let tbSection = dataSource[section]
        view?.updateViewWithSection(tbSection)
        view?.onTapAction = { [weak self] view in
            guard let `self` = self else {
                return
            }
            tbSection.isOpen = !tbSection.isOpen
            if tbSection.isOpen && tbSection.count > 0 {
                self.tableView.insertRows(at: tbSection.items.enumerated().map { IndexPath(row: $0.offset, section: section) }, with: .automatic)
            } else if !tbSection.isOpen && tbSection.count > 0 {
                self.tableView.deleteRows(at: tbSection.items.enumerated().map { IndexPath(row: $0.offset, section: section) }, with: .fade)
            } else {
                self.tableView.reloadSections([section], with: .fade)
            }
            UIView.animate(withDuration: 0.3, animations: {
                view.updateViewWithSection(tbSection)
            })
        }
        return view
    }

    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return 50
    }

    func tableView(_ tableView: UITableView, willDisplay cell: UITableViewCell, forRowAt indexPath: IndexPath) {
        let filmCell = cell as? CinemaFilmTimeTableCell
        filmCell?.delegate = self
    }
}

extension FilmChooseTimeViewController: CalendarHeaderViewDelegate {
    func calendarView(_ calendarView: CalendarHeaderView, didSelected date: Date) {

        showLoading()
        getFilmShowAtDate(date)
    }
}

extension FilmChooseTimeViewController: CinemaFilmTimeDelegate {
    func didSelect(_ cinema: CinemaModel?, show: ShowModel, index: IndexPath?) {
        guard let date = show.getTimeLockDate(), date >= Date() else {
            self.showAlert(message: "ShowFilm.TimeOut".localized)
            return
        }
        if show.isShowScreenIntro ?? false {
            updateMessage(show: show)
        } else {
            setupLogin(show: show)
        }
    }

    func setupLogin(show: ShowModel) {
        guard Global.shared.isLogined else {
            let vc = UIStoryboard.authen[.login]
            self.show(vc)
            self.selectedShow = show
            return
        }
        selecteShow(show)
    }
}

extension FilmChooseTimeViewController: SelectRegionDelegate {
    func didSelectedRegion(_ province: CinemaProvinceModel?) {
        selectedRegion = province
        updateListShow()
    }
}

extension FilmChooseTimeViewController {
    func updateMessage(show: ShowModel) {
        let vc = UIStoryboard.cinema[.confirmVipZoom] as! ConfirmVipZoomViewController
        vc.message = show.screenDesc ?? ""
        vc.titleZoom = show.screenTitle ?? ""
        vc.pathImage = show.screenImageUrl ?? ""
        let alert = PopupDialog(viewController: vc, buttonAlignment: .horizontal, preferredWidth: self.view.frame.width - 100)
        let buttonOne = CancelButton(title: "Bt.Cancel".localized, dismissOnTap: true) {

        }
        // This button will not the dismiss the dialog
        let buttonTwo = DefaultButton(title: "Bt.Yes".localized, dismissOnTap: true) { [weak self] in
            guard let self = self else { return }
            setupLogin(show: show)
        }
        alert.addButtons([buttonOne, buttonTwo])
        self.present(alert, animated: true, completion: nil)
    }
}

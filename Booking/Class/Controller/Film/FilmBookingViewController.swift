//
//  FilmBookingViewController.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/22/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

class FilmBookingViewController: BaseViewController {
    @IBOutlet weak var tabPager: ScrollPager!
    fileprivate weak var pageViewController: UIPageViewController?

    var nowShowingVC: ListFilmViewController!
    var specialVC: ListFilmViewController!

    override func viewDidLoad() {
        super.viewDidLoad()

        localizableTitle = "BookingByFilm.Title"

        tabPager.addSegmentsWithTitles(segmentTitles: ["Film.NowShowing", "Film.Special"])
        tabPager.font = UIFont(fontName: .Oswald, size: 20)
        tabPager.selectedFont = UIFont(fontName: .<PERSON>, size: 20)

        pageViewController = childViewControllers.first(where: { $0 is UIPageViewController }) as? UIPageViewController
        pageViewController?.delegate = self
        pageViewController?.dataSource = self

        nowShowingVC = UIStoryboard.film[.listFilm] as! ListFilmViewController
        specialVC = UIStoryboard.film[.listFilm] as! ListFilmViewController
        specialVC.type = .special
        pageViewController?.setViewControllers([nowShowingVC], direction: .forward, animated: false, completion: nil)
    }
}

extension FilmBookingViewController: UIPageViewControllerDataSource, UIPageViewControllerDelegate {
    func pageViewController(_ pageViewController: UIPageViewController, viewControllerAfter viewController: UIViewController) -> UIViewController? {
        if viewController == nowShowingVC {
            return specialVC
        }
        return nil
    }

    func pageViewController(_ pageViewController: UIPageViewController, viewControllerBefore viewController: UIViewController) -> UIViewController? {
        if viewController == specialVC {
            return nowShowingVC
        }
        return nil
    }

    func pageViewController(_ pageViewController: UIPageViewController, didFinishAnimating finished: Bool, previousViewControllers: [UIViewController], transitionCompleted completed: Bool) {
        guard completed else {
            return
        }
        guard let vc = pageViewController.viewControllers?.first else {
            return
        }
        if vc == nowShowingVC {
            tabPager.setSelectedIndex(index: 0, animated: true)
        } else {
            tabPager.setSelectedIndex(index: 1, animated: true)
        }
    }
}

extension FilmBookingViewController: ScrollPagerDelegate {
    func scrollPager(scrollPager: ScrollPager, changedIndex: Int) {
        if changedIndex == 0 {
            pageViewController?.setViewControllers([nowShowingVC], direction: .reverse, animated: true, completion: nil)
        } else {
            pageViewController?.setViewControllers([specialVC], direction: .forward, animated: true, completion: nil)
        }
    }
}

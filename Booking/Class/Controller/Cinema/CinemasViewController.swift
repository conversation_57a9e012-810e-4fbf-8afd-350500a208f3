//
//  CinemasViewController.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 8/19/19.
//  Copyright © 2019 ddkc. All rights reserved.
//

import UIKit

class CinemasViewController: BaseViewController {

    @IBOutlet weak var collectionView: UICollectionView!
    @IBOutlet weak var tableView: UITableView!
    @IBOutlet weak var nearbyLabel: LocalizableLabel!
    @IBOutlet weak var byAreaLabel: LocalizableLabel!
    private var cinemas: [CinemaModel] = []{
        didSet{
            collectionView.reloadData()
        }
    }
    
    private var allCinemas: [CinemaModel] = []
    private var cinemasByCity: [CinemaProvinceModel] = []{
        didSet{
            tableView.reloadData()
        }
    }

    var isBooking: Bool = false

    override func viewDidLoad() {
        super.viewDidLoad()

        configureViews()

        if !LocationManager.shared.systemEnable() {
            LocationManager.shared.openLocationAlert()
        }

        LocationManager.shared.requestCurrentLocation { location in
            guard !self.cinemas.isEmpty else { return }
            self.cinemas = self.allCinemas.sorted(by: { $0.getDistanceDouble() < $1.getDistanceDouble() })
        }
        self.getData()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        self.navigationController?.setTransparent(false)
        if !isBooking {
            guard let tabbarVC = AppDelegate.shared.tabbarVC else {
                return
            }
            tabbarVC.My_tabBar.isHidden = true
        }
    }

    override func localizationDidChange() {
        localizableTitle = "ListCinema.Title"
        nearbyLabel.text = "CinemaList.NearCinema".localized.uppercased()
        byAreaLabel.text = "CinemaList.ChooseByArea".localized.uppercased()
    }

    private func configureViews() {
        localizationDidChange()
        collectionView.register(UINib(nibName: CinemaCollectionViewCell.id, bundle: nil), forCellWithReuseIdentifier: CinemaCollectionViewCell.id)

        tableView.registerCell(id: CinemaByProvinceTableViewCell.id)
        tableView.allowsSelection = false
        tableView.separatorStyle = .none

        if isBooking {
            tableView.contentInset = UIEdgeInsets(top: 0, left: 0, bottom: 70, right: 0)
        }
    }

    private func itemTapped(_ cinema: CinemaModel) {
        if isBooking {
            let vc = UIStoryboard.cinema[.chooseCinema] as! ChooseCinemasViewController
            vc.cinema = cinema
            vc.hidesBottomBarWhenPushed = true
            Tracking.shared.selectTheater(cinemaId: cinema.CinemaId, cinemaName:cinema.Name)
            show(vc)
            return
        }

        let vc = UIStoryboard.cinema[.cinemaDetail] as! CinemaDetailViewController
        vc.cinema = cinema
        show(vc)
    }

}

extension CinemasViewController {
    private func getListNearbyCinema(){
        self.showLoading()
        CinemaProvider.rx.request(.listCinema).mapObject(DDKCResponse<CinemaModel>.self)
            .subscribe(onNext:{[weak self] response in
                guard let `self` = self else {return}
                self.dismissLoading()
                self.handlerResponse(response, success: {
                    guard let items = response.ListObject else {
                        self.dismissLoading()
                        return
                    }
                    self.allCinemas = items
                    self.cinemas = items.sorted(by: { $0.getDistanceDouble() < $1.getDistanceDouble() }).filter { $0.getDistanceDouble() < 100 }
                }, error: {
                })
            }).disposed(by: disposeBag)
    }

    private func getCinemaByCity(){
        self.showLoading()
        CinemaProvider.rx.request(.listCinemaByProvince).mapObject(DDKCResponse<CinemaProvinceModel>.self)
            .subscribe(onNext:{[weak self] response in
                guard let `self` = self else {return}
                self.dismissLoading()
                self.handlerResponse(response, success: {
                    guard let items = response.ListObject else {return}
                    self.cinemasByCity = items
                }, error: {
                    self.dismissLoading()
                })
            }).disposed(by: disposeBag)
    }

    private func getData(){
        self.getListNearbyCinema()
        self.getCinemaByCity()
    }
}

extension CinemasViewController: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return cinemas.count
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        guard let cell = collectionView.dequeueReusableCell(withReuseIdentifier: CinemaCollectionViewCell.id, for: indexPath) as? CinemaCollectionViewCell else {
            return CinemaCollectionViewCell()
        }

        cell.configure(cinemas[indexPath.row])

        return cell
    }

    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let itemWidth: CGFloat = 160.0
        return CGSize(width: itemWidth, height: collectionView.frame.size.height)
    }

    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        collectionView.deselectItem(at: indexPath, animated: true)
        itemTapped(cinemas[indexPath.row])
    }
}

extension CinemasViewController: UITableViewDelegate, UITableViewDataSource {

    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return cinemasByCity.count
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let cell = tableView.dequeueReusableCell(withIdentifier: CinemaByProvinceTableViewCell.id) as? CinemaByProvinceTableViewCell else {
            return CinemaByProvinceTableViewCell()
        }
        cell.configure(cinemasByCity[indexPath.row], index: indexPath)

        cell.expandHandler = { [weak self] index in
            self?.tableView.reloadRows(at: [index], with: .automatic)
        }

        cell.selectHandler = { [weak self] cinema in
            self?.itemTapped(cinema)
        }

        return cell
    }

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return cinemasByCity[indexPath.row].height
    }

}

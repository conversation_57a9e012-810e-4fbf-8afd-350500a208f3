//
//  ConfirmVipZoomViewController.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 18/11/24.
//  Copyright © 2024 ddkc. All rights reserved.
//

import Foundation

import UIKit
import SDWebImage

class ConfirmVipZoomViewController: BaseViewController {

    @IBOutlet weak var zoomImage: UIImageView!
    @IBOutlet weak var messageZoomLabel: UILabel!
    @IBOutlet weak var titleZoomLabel: UILabel!

    var titleZoom: String = ""
    var message: String = ""
    var pathImage: String = ""

    override func viewDidLoad() {
        super.viewDidLoad()
        zoomImage.sd_setImage(with: URL(string: pathImage))
        updateMessage()
    }

    override func localizationDidChange() {
        super.localizationDidChange()
        updateMessage()
    }

    func updateMessage() {
        messageZoomLabel.font = UIFont.systemFont(ofSize: 14)
        messageZoomLabel.text = message
        messageZoomLabel.textColor = UIColor(red: 3, green: 3, blue: 3)
        titleZoomLabel.text = titleZoom
        titleZoomLabel.font = UIFont.boldSystemFont(ofSize: 16)
        let tap = UITapGestureRecognizer(target: self, action: #selector(tapOnPolicyLabel(gesture:)))
        messageZoomLabel.addGestureRecognizer(tap)
        messageZoomLabel.isUserInteractionEnabled = true
    }

    func openRule() {
        let vc = UIStoryboard.setting[.other] as! OtherViewController
        vc.type = .PolicyPayment
        show(BaseNavigationViewController(rootViewController: vc))
    }
}

extension ConfirmVipZoomViewController {
    @objc func tapOnPolicyLabel(gesture: UITapGestureRecognizer) {
        if checkTouchOnPolicy(gesture) {
            openRule()
        }
    }

    func checkTouchOnPolicy(_ gesture: UIGestureRecognizer) -> Bool {
        guard let text = messageZoomLabel.text else {
            return false
        }
        let pos = gesture.location(in: messageZoomLabel)
        let textRange = (text as NSString).range(of: "ConfirmAge.Rule".localized)
        return messageZoomLabel.isPoint(pos: pos, at: textRange)
    }
}

//
//  ChooseSeatViewController.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 5/7/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit
import SignalRSwift
import PopupDialog
import SwiftSignalRClient

class ChooseSeatViewController: BaseViewController {
    @IBOutlet weak var vHeaderGradient: GradientView!
    @IBOutlet weak var lbFilmName: UILabel!
    @IBOutlet weak var lbFilmOption: UILabel!
    @IBOutlet var collectionView: UICollectionView!
    @IBOutlet weak var columnLayout: ColumnLayout!
    @IBOutlet weak var ivFilmBanner: UIImageView!
    @IBOutlet weak var scrollView: UIScrollView!
    @IBOutlet weak var collectionViewHeight: NSLayoutConstraint!

    @IBOutlet weak var mainView: UIStackView!
    @IBOutlet weak var lbNormalPrice: UILabel!
    @IBOutlet weak var lbVipPrice: UILabel!
    @IBOutlet weak var lbCouplePrice: UILabel!
    @IBOutlet weak var vPriceBottom: NSLayoutConstraint!
    @IBOutlet weak var vPrice: UIView!
    @IBOutlet weak var lbSelectedSeats: UILabel!
    @IBOutlet weak var lbTotalPrice: UILabel!
    @IBOutlet weak var vSeatsList: UIView!
    @IBOutlet weak var seatScrollView: UIScrollView!
    @IBOutlet var screenView: UIView!
    @IBOutlet var seatContainer: UIView!
    @IBOutlet weak var collectionViewWidth: NSLayoutConstraint!
    @IBOutlet weak var seatHeight: NSLayoutConstraint!
    @IBOutlet weak var timeLeftBottomConstraint: NSLayoutConstraint!
    @IBOutlet weak var timeLeftLabel: UILabel!    
    
    var cinemaId: String?
    var cinemaName: String?
    var showTime: ShowModel?
    var film: FilmInformation?
    var totalPrice = 0

    fileprivate var listSeat: ListSeatModel?
    fileprivate var selectedSeats: [SeatModel] = []

    fileprivate var cellId = "SeatCollectionViewCell"
    fileprivate var headerViewId = "SeatCollectionHeaderView"

    fileprivate lazy var hubConnection = HubConnection(withUrl: Config.SignalRURL)
    fileprivate lazy var hub = hubConnection.createHubProxy(hubName: "chooseSeatHub")
    
    fileprivate var isObserving: Bool = false
    fileprivate var timer: Timer?
    fileprivate var timeStartBooking: TimeInterval = 0

    var formatName: String?
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        hub?.on(eventName: "broadcastMessage") { (args) in
            let connectionId = args[safe: 0] as? String
            let showId = args[safe: 1] as? String
            let seatIndex = args[safe: 2] as? Int
            var soldStatus = args[safe: 3] as? Int

            guard connectionId != self.hubConnection.connectionId, showId == self.showTime?.showId, seatIndex != nil, soldStatus != nil else {
                return
            }
            if soldStatus == SeatSoldStatus.SELECTING {
                soldStatus = SeatSoldStatus.SELECTED.rawValue
            }
            self.setSeatIndex(seatIndex!, status: soldStatus!)
        }
        
        hubConnection.started = {
            print("Connected")
        }

        hubConnection.reconnecting = {
            print("Reconnecting...")
        }

        hubConnection.reconnected = {
            print("Reconnected.")
        }

        hubConnection.closed = {
            print("Disconnected")
        }

        hubConnection.connectionSlow = { print("Connection slow...") }

        hubConnection.error = { error in
          print("Error \(error)")
        }

        hubConnection.start()
    }
    


    override func viewDidLoad() {
        super.viewDidLoad()

        localizableTitle = "BookingByFilm.Title"

        // Do any additional setup after loading the view.
        vHeaderGradient.colors = [UIColor.viewBg.withAlphaComponent(0), UIColor.viewBg.withAlphaComponent(0.58), UIColor.viewBg]

        columnLayout.delegate = self
        collectionView.allowsMultipleSelection = true
        collectionView.register(UINib(nibName: headerViewId, bundle: nil), forSupplementaryViewOfKind: UICollectionElementKindSectionHeader, withReuseIdentifier: headerViewId)

        vPriceBottom.constant = -vPrice.frame.height * 1.5
        updateData()

        self.getSeatData()
        self.timeStartBooking = NSDate().timeIntervalSince1970
    }

    deinit {
        removeAllSeatsAndStop()
        hubConnection.stop()
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        if !isObserving {
            collectionView.addObserver(self, forKeyPath: "contentSize", options: .new, context: nil)
            isObserving = true
        }
        if hubConnection.state == .disconnected {
            hubConnection.start()
        }
        
        if selectedSeats.isEmpty {
            hidePriceView()
        } else {
            showPriceView()
        }
        startTimer()
    }

    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        if isObserving {
            collectionView.removeObserver(self, forKeyPath: "contentSize")
            isObserving = false
        }

        if self.navigationController?.viewControllers.contains(self) != true {
            removeAllSeatsAndStop()
        }
        
        stopTimer()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        if (timeStartBooking > 0) {
            updateTimer()
        }
    }

    func startTimer() {
        stopTimer()
        timer = Timer.scheduledTimer(timeInterval: 1, target: self, selector: #selector(updateTimer), userInfo: nil, repeats: true)
        timer?.fire()
    }

    func stopTimer() {
        if (timer != nil) {
            timer?.invalidate()
            timer = nil;
        }
    }

    @objc func updateTimer() {
        let currentTime = NSDate().timeIntervalSince1970
        let timeLeft = Int(Config.TimeExpired - (currentTime - timeStartBooking));
        if (timeLeft <= 0) {
            stopTimer()
            self.showAlert(message: "SeatsTimeOut".localized) { _ in
                self.navigationController?.popToRootViewController(animated: true)
            }
            timeLeftLabel.text = Utils.shared.timeFormatted(0)
        } else {
            timeLeftLabel.text = Utils.shared.timeFormatted(timeLeft)
        }
    }
    
    func showPriceView() {
        vPriceBottom.constant = 0
        timeLeftBottomConstraint.constant = 0;
    }

    func hidePriceView() {
        vPriceBottom.constant = -vPrice.frame.height - self.bottomLayoutGuide.length
        timeLeftBottomConstraint.constant = -30.0;
    }

    func updatePriceView() {
        if selectedSeats.isEmpty {
            hidePriceView()
        } else {
            showPriceView()
        }
        lbSelectedSeats.text = selectedSeats.compactMap { $0.SeatName }.joined(separator: ", ")

        let normalTicket = self.listSeat?.TicketTypes?.first(where: { $0.isNormal })
        let vipTicket = self.listSeat?.TicketTypes?.first(where: { $0.isVip })
        let coupleTicket = self.listSeat?.TicketTypes?.first(where: { $0.isCouple })

        let realSeats: [SeatModel] = selectedSeats.compactMap{
            return $0
        }
        self.totalPrice = realSeats.compactMap {
            if $0.seatType?.isVip == true {
                return vipTicket?.Price
            } else if $0.seatType?.isCouple == true {
                return coupleTicket?.Price
            } else if $0.seatType?.isNormal == true {
                return normalTicket?.Price
            } else {
                return nil
            }
        }.reduce(0, +)
        lbTotalPrice.text = self.totalPrice.toCurrency()
    }

    func removeAllSeatsAndStop() {
        removeAllSeats() {
            self.hubConnection.stop()
        }
    }

    func removeAllSeats(_ completion: (()->Void)? = nil) {
        selectedSeats.removeAll()
        if let indexPaths = collectionView.indexPathsForSelectedItems{
            for (_, indexPath) in indexPaths.enumerated(){
                self.collectionView.deselectItem(at: indexPath, animated: false)
                if let seat = self.listSeat?.Screen?.SeatPosition?[indexPath.section][indexPath.row] {
                    self.sendSeat(seat, status: SeatSoldStatus.EMPTY.rawValue, indexPath: indexPath)
                    if let couple = seat.coupleSeat {
                        self.sendSeat(couple, status: SeatSoldStatus.EMPTY.rawValue, indexPath: indexPath)
                    }
                }
            }
        }
//        if let indexPaths = collectionView.indexPathsForSelectedItems {
//            let requestGroup = DispatchGroup()
//            let _ = DispatchQueue.global(qos: .userInitiated)
//            DispatchQueue.concurrentPerform(iterations: indexPaths.count) { (i) in
//                let indexPath = indexPaths[i]
//                requestGroup.enter()
//                DispatchQueue.main.async {
//                    self.collectionView.deselectItem(at: indexPath, animated: false)
//                    if let seat = self.listSeat?.Screen?.SeatPosition?[indexPath.section][indexPath.row] {
//                        self.sendSeat(seat, status: SeatSoldStatus.EMPTY.rawValue, indexPath: indexPath) {
//                            requestGroup.leave()
//                        }
//                        if let couple = seat.coupleSeat {
//                            self.sendSeat(couple, status: SeatSoldStatus.EMPTY.rawValue, indexPath: indexPath)
//                        }
//                    }
//                }
//            }
//
//            requestGroup.notify(queue: DispatchQueue.main){
//                self.updatePriceView()
//                completion?()
//            }
//        }
        updatePriceView()
       
    }

    func updateData() {
        lbFilmName.text = film?.getName()
        lbFilmOption.text = ""
        let posterUrl = film?.getFilmPoster()

        if let posterUrl = posterUrl {
            self.ivFilmBanner.af_setImage(withURL: URL(string: posterUrl)!)
        }
    }
    
    func connected()->Bool{
//        return hubConnection.state == ConnectionState.connected;
        return true;
    }

    func checkValidSeats() -> Bool {
        guard let listSeat = self.listSeat?.Screen?.SeatPosition?.filter({ $0.first(where: {$0.SoldStatus == SeatSoldStatus.SELECTING && $0.coupleSeat == nil && $0.Status?.isWay == false && $0.Status?.isBroken == false }) != nil }) else { return true }
        for seats in listSeat {
            let selectingSeatGroups = seats.enumerated().split { s -> Bool in
                s.element.SoldStatus != SeatSoldStatus.SELECTING.rawValue
                }.map{ Array($0) }

            // check empty seat between
            for i in 0..<(selectingSeatGroups.count - 1) {
                let group1 = selectingSeatGroups[i]
                let group2 = selectingSeatGroups[i + 1]
                if group1.last!.offset == group2.first!.offset - 2 && seats[group1.last!.offset + 1].SoldStatus == SeatSoldStatus.EMPTY {
                    let seat = seats[group1.last!.offset + 1]
                    self.showAlert(message: "DontEmptySeat".lang(["seat_name": seat.SeatName ?? ""]))
//                    self.showAlert(message: "DontEmptyBetweenSeat".localized)
                    return false
                }
            }

            // check empty seat left and right
            for group in selectingSeatGroups {
                print("group size: \(group.count)")
                var leftEmpty = 0
                var rightEmpty = 0
                var index = group.first!.offset
                while (index > 0) {
                    index -= 1
                    let seat = seats[index]
                    if seat.SoldStatus == SeatSoldStatus.EMPTY {
                        leftEmpty += 1
                    } else {
                        break
                    }
                }
                index = group.last!.offset
                while (index < seats.count - 1) {
                    index += 1
                    let seat = seats[index]
                    if seat.SoldStatus == SeatSoldStatus.EMPTY {
                        rightEmpty += 1
                    } else {
                        break
                    }
                }
                if leftEmpty == 1 && rightEmpty > 0 {
                    let seat = seats[group.first!.offset - 1]
                    self.showAlert(message: "DontEmptySeat".lang(["seat_name": seat.SeatName ?? ""]))
//                    self.showAlert(message: "DontEmptyLeftRightSeat".localized)
                    return false
                }
                if leftEmpty > 0 && rightEmpty == 1 {
                    let seat = seats[group.last!.offset + 1]
                    self.showAlert(message: "DontEmptySeat".lang(["seat_name": seat.SeatName ?? ""]))
//                    self.showAlert(message: "DontEmptyLeftRightSeat".localized)
                    return false
                }
            }
        }
        return true
    }

    func getSeatData() {
        showLoading()
        FilmProvider.rx.request(.showSeat(showTime?.showId ?? "")).mapObject(DDKCResponse<ListSeatModel>.self).subscribe(onNext: { [weak self] result in
            guard let `self` = self else { return }
            self.dismissLoading()
            self.listSeat = result.Object
            self.lbFilmOption.text = result.Object?.getFullOptions()
            self.formatName = result.Object?.formatName
            guard let colNumber = self.listSeat?.Screen?.NumberCol, colNumber > 0 else { return }

            // update prices
            if let ticket = self.listSeat?.TicketTypes?.first(where: { $0.isNormal }) {
                self.lbNormalPrice.text = ticket.Price?.toCurrency()
            }
            if let ticket = self.listSeat?.TicketTypes?.first(where: { $0.isVip }) {
                self.lbVipPrice.text = ticket.Price?.toCurrency()
            }
            if let ticket = self.listSeat?.TicketTypes?.first(where: { $0.isCouple }) {
                self.lbCouplePrice.text = ((ticket.Price ?? 0) * 2).toCurrency()
            }

            result.Object?.Screen?.SeatPosition?.enumerated().forEach {
                var seats = [SeatModel]()
                var coupleSeat: SeatModel?
                $0.element.forEach {
                    if $0.Status?.isNotUsed == true {
                        return
                    }
                    let index = $0.SeatIndex ?? 0
                    $0.SeatRow = index / colNumber + 1
                    $0.SeatCol = index % colNumber
                    if $0.SeatCol == 0 {
                        $0.SeatRow -= 1
                        $0.SeatCol = colNumber
                    }
                    if $0.seatType?.isCouple == true && $0.Status?.isUsed == true {

                        if coupleSeat?.Status?.isUsed == true {
                            $0.coupleSeat = coupleSeat

                            seats.append($0)
                            coupleSeat = nil
                        } else {
                            coupleSeat = $0
                        }
                    } else {
                        coupleSeat = nil
                        seats.append($0)
                    }
                }
                self.listSeat?.Screen?.SeatPosition?[$0.offset] = seats
            }

            self.collectionView.reloadData()
            self.collectionViewHeight.constant = self.collectionView(self.collectionView, heightAt: 0) * CGFloat(self.listSeat?.Screen?.NumberRow ?? 5) + 80
            self.collectionView.performBatchUpdates({

            }, completion: { _ in
                self.collectionViewWidth.constant = self.collectionView.contentSize.width
                self.collectionViewHeight.constant = self.collectionView.contentSize.height
                self.seatScrollView.minimumZoomScale = self.seatScrollView.frame.width / self.collectionView.contentSize.width
                self.seatScrollView.zoomScale = self.seatScrollView.minimumZoomScale
            })
        }, onError: { [weak self] _ in
            self?.dismissLoading()
        }).disposed(by: disposeBag)
    }

    func setSeatIndex(_ seatIndex: Int, status: Int) {
        var row: Int?
        let col = listSeat?.Screen?.SeatPosition?.index {
            row = $0.index { $0.SeatIndex == seatIndex || $0.coupleSeat?.SeatIndex == seatIndex }
            return row != nil
        }
        if let row = row, let col = col {
            let indexPath = IndexPath(row: row, section: col)
            let seat = listSeat?.Screen?.SeatPosition?[col][row]
            seat?.SoldStatus = status
            seat?.coupleSeat?.SoldStatus = status
            collectionView.reloadItems(at: [indexPath])
        }
    }

    override func observeValue(forKeyPath keyPath: String?, of object: Any?, change: [NSKeyValueChangeKey : Any]?, context: UnsafeMutableRawPointer?) {
        if keyPath == "contentSize" {
            let scale = seatScrollView.zoomScale
            collectionViewHeight.constant = collectionView.contentSize.height
            collectionViewWidth.constant = collectionView.contentSize.width

            seatScrollView.minimumZoomScale = seatScrollView.frame.width / collectionView.contentSize.width
            seatScrollView.zoomScale = seatScrollView.minimumZoomScale
             seatHeight.constant = (collectionViewHeight.constant + screenView.frame.height) * scale
            let centerOffsetX = (seatScrollView.contentSize.width - seatScrollView.frame.size.width) / 2
            let centerPoint = CGPoint(x: centerOffsetX, y: 0)
            seatScrollView.setContentOffset(centerPoint, animated: true)
        }
    }

    func sendSeat(_ seat: SeatModel, status: Int, indexPath: IndexPath, completion: (() -> Void)? = nil) {
        if(connected()){
            let lastStatus = seat.SoldStatus
            seat.SoldStatus = status
            try? hub?.invoke(method: "sendMessage", withArgs: [hubConnection.connectionId ?? "", showTime?.showId ?? "", "\(seat.SeatIndex ?? 0)", "\(status)"]) { response, error in
                print("response: \(response) error: \(error)")
                DispatchQueue.main.async {
                    completion?()
//                    if error != nil {
//                        self.showAlert(title: "Alert.Error".localized, message: "Alert.ErrorServer".localized)
//                        if seat.SoldStatus == SeatSoldStatus.EMPTY && status == SeatSoldStatus.SELECTING {
//                            self.collectionView.deselectItem(at: indexPath, animated: true)
//                            self.selectedSeats.remove(element: seat)
//                            self.updatePriceView()
//                        }
//                        seat.SoldStatus = lastStatus
//                    } else {
//                        seat.SoldStatus = status
//                    }
                    seat.SoldStatus = status
                }
            }
        }
    }

    func openPaymentView() {
        let vc = UIStoryboard.payment[.payment] as! PaymentViewController
                
        vc.timeStartBooking = timeStartBooking 
        vc.show = self.showTime
        vc.seat = selectedSeats
        vc.listSeat = self.listSeat
        vc.film = self.film
        vc.filmFormat = self.formatName
        vc.seatsPrice = self.totalPrice
        vc.cinemaId = cinemaId
        vc.cinemaName = cinemaName
        let normalSeats = selectedSeats.filter{$0.seatType?.isNormal == true}
        let vipSeats =  selectedSeats.filter{ $0.seatType?.isVip == true}
        let doubleSeats = selectedSeats.filter{ $0.seatType?.isCouple == true}
        let totalSeats = normalSeats.count + vipSeats.count + doubleSeats.count
        let movieId =  listSeat?.FilmId
        let movieName = listSeat?.FilmName
        let date = showTime?.getStartDate()
        let time = showTime?.getStartDate()
        let screen = self.listSeat?.PhongChieu
        Tracking.shared.selectSeatComplete(
            cinemaId: cinemaId,
            cinemaName: cinemaName,
            movieId: movieId,
            movieName: movieName,
            date: date,
            time: time,
            screen: screen,
            normalSeats: normalSeats.count,
            vipSeats: vipSeats.count,
            doubleSeats: doubleSeats.count,
            totalSeats: totalSeats,
            totalAmount: self.totalPrice
        )
        
        show(vc)
    }
}

extension ChooseSeatViewController {
    @IBAction func continueButtonPressed(_ sender: Any) {
        guard checkValidSeats() else {
//            self.showAlert(message: "InvalidSeats".localized)
            return
        }
        var ageConfirmText: String?
        if film?.getFilmRestrictAgeName() == FilmModel.RestrictAge.c13 {
            ageConfirmText = "AgeAbove13"
        } else if film?.getFilmRestrictAgeName() == FilmModel.RestrictAge.c16 {
            ageConfirmText = "AgeAbove16"
        } else if film?.getFilmRestrictAgeName() == FilmModel.RestrictAge.c18 {
            ageConfirmText = "AgeAbove18"
        }

        if let ageConfirmText = ageConfirmText {
            let vc = UIStoryboard.cinema[.confirmBookAge] as! ConfirmBookAgeViewController
            vc.ageTextLocalized = ageConfirmText
            let alert = PopupDialog(viewController: vc, buttonAlignment: .horizontal, preferredWidth: self.view.frame.width - 100)

            let buttonOne = CancelButton(title: "Bt.Cancel".localized, dismissOnTap: true) {

            }

            // This button will not the dismiss the dialog
            let buttonTwo = DefaultButton(title: "Bt.Yes".localized, dismissOnTap: true) {
                self.openPaymentView()
            }
            alert.addButtons([buttonOne, buttonTwo])
            self.present(alert, animated: true, completion: nil)
        } else {
            self.openPaymentView()
        }
    }

    @IBAction func showAllSeatsPressed(_ sender: Any) {
        let message = selectedSeats.compactMap { $0.SeatName }.joined(separator: "    ")
        let alert = PopupDialog(title: "Seat.SelectedSeats".localized, message: message, buttonAlignment: .horizontal, preferredWidth: self.view.frame.width - 100)
        let buttonOne = CancelButton(title: "Bt.Close".localized) {

        }
        alert.addButton(buttonOne)
        self.present(alert, animated: true, completion: nil)
    }
}

extension ChooseSeatViewController: UICollectionViewDataSource {
    func numberOfSections(in collectionView: UICollectionView) -> Int {
        return listSeat?.Screen?.SeatPosition?.count ?? 0
    }

    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        if let listSeat = self.listSeat?.Screen?.SeatPosition?[section] {
            return listSeat.count
        }

        return 0
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: cellId, for: indexPath) as! SeatCollectionViewCell
        cell.seat = listSeat?.Screen?.SeatPosition?[indexPath.section][indexPath.row]
        return cell
    }

//    func collectionView(_ collectionView: UICollectionView, viewForSupplementaryElementOfKind kind: String, at indexPath: IndexPath) -> UICollectionReusableView {
//        if kind == UICollectionElementKindSectionHeader {
//            let view = collectionView.dequeueReusableSupplementaryView(ofKind: kind, withReuseIdentifier: headerViewId, for: indexPath) as! SeatCollectionHeaderView
//            view.lbText.text = listSeat?.Screen?.SeatPosition?[indexPath.section].first?.SeatRowName
//            return view
//        }
//        return UICollectionReusableView()
//    }
}

extension ChooseSeatViewController: UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, shouldSelectItemAt indexPath: IndexPath) -> Bool {
        if(connected()){
            let seat = listSeat?.Screen?.SeatPosition?[indexPath.section][indexPath.row]
            if seat?.SoldStatus == SeatSoldStatus.EMPTY &&
                seat?.Status?.isBroken != true && seat?.Status?.isUsed == true {
                if ((selectedSeats.count < 8 && seat?.seatType?.isCouple != true) || (selectedSeats.count < 7 && seat?.seatType?.isCouple == true)) {
                    return true
                } else {
                    showAlert(message: "Seat.MaxSelectedSeats".localized)
                }
            }
        }
        return false
    }

    func collectionView(_ collectionView: UICollectionView, shouldDeselectItemAt indexPath: IndexPath) -> Bool {
        let seat = listSeat?.Screen?.SeatPosition?[indexPath.section][indexPath.row]
        if seat?.SoldStatus == SeatSoldStatus.SELECTING {
            return true
        }
        return false
    }

    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let seat = listSeat?.Screen?.SeatPosition?[indexPath.section][indexPath.row]
//        print("connection Id: \(connection.connectionID)")
        if let seat = seat {
            if !selectedSeats.contains(seat) {
                selectedSeats.append(seat)
            }
            sendSeat(seat, status: SeatSoldStatus.SELECTING.rawValue, indexPath: indexPath)
        }

        if let coupleSeat = seat?.coupleSeat {
            if !selectedSeats.contains(coupleSeat) {
                selectedSeats.append(coupleSeat)
            }
            sendSeat(coupleSeat, status: SeatSoldStatus.SELECTING.rawValue, indexPath: indexPath)
        }
        self.updatePriceView()
    }

    func collectionView(_ collectionView: UICollectionView, didDeselectItemAt indexPath: IndexPath) {
        let seat = listSeat?.Screen?.SeatPosition?[indexPath.section][indexPath.row]
        if let seat = seat {
            selectedSeats.remove(element: seat)
            sendSeat(seat, status: SeatSoldStatus.EMPTY.rawValue, indexPath: indexPath)
        }

        if let coupleSeat = seat?.coupleSeat {
            selectedSeats.remove(element: coupleSeat)
            sendSeat(coupleSeat, status: SeatSoldStatus.EMPTY.rawValue, indexPath: indexPath)
        }
        self.updatePriceView()
    }
}

extension ChooseSeatViewController: UIScrollViewDelegate {
    func scrollViewDidScroll(_ scrollView: UIScrollView) {
//        let contentOffset = scrollView.contentOffset
//        if scrollView == collectionView {
//            screenLeft.constant = (collectionView.contentSize.width - screenView.frame.width) / 2 - contentOffset.x
//        }
    }

    func viewForZooming(in scrollView: UIScrollView) -> UIView? {
        if scrollView == seatScrollView {
            return seatContainer
        }
        return nil
    }

    func scrollViewDidZoom(_ scrollView: UIScrollView) {
        let scale = scrollView.zoomScale
//        collectionViewHeight.constant = (collectionView.contentSize.height)
        seatHeight.constant = (collectionView.contentSize.height + screenView.frame.height) * scale
    }
}

extension ChooseSeatViewController: ColumnLayoutDelegate {
    func collectionView(_ collectionView: UICollectionView, heightAt column: Int) -> CGFloat {
        return 40
    }

    func collectionView(_ collectionView: UICollectionView, widthAt indexPath: IndexPath) -> CGFloat {
        let seat = listSeat?.Screen?.SeatPosition?[indexPath.section][indexPath.row]
        if seat?.seatType?.isCouple == true && seat?.coupleSeat != nil {
            return 86
        }
        return 40
    }
}

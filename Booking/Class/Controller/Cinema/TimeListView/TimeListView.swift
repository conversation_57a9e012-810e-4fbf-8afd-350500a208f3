//
//  TimeListView.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/25/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit
import SwiftDate
import AlamofireImage

protocol TimeListViewDelegate: class {
    func timeListDidSelected(_ item: ShowModel)
}

class TimeListView: UIView {
    var collectionView: UICollectionView!

    private var leftView: UIView!
    private var rightView: UIView!

    var data: [ShowModel] = [] {
        didSet {
            self.data.sort {
                guard let time1 = $0.getStartDate(), let time2 = $1.getStartDate() else { return false }
                return time1 < time2
                }
            self.collectionView.reloadData()
        }
    }
    fileprivate let cellId = "TimeListCollectionCell"
    weak var delegate: TimeListViewDelegate?

    override func awakeFromNib() {
        super.awakeFromNib()

        setup()
    }

    func setup() {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        collectionView = UICollectionView(frame: self.bounds, collectionViewLayout: layout)
        collectionView.autoresizingMask = [.flexibleWidth, .flexibleHeight]
        collectionView.dataSource = self
        collectionView.delegate = self
        collectionView.backgroundColor = .clear
        collectionView.register(UINib(nibName: cellId, bundle: nil), forCellWithReuseIdentifier: cellId)
        addSubview(collectionView)
        collectionView.showsHorizontalScrollIndicator = false
        collectionView.addObserver(self, forKeyPath: "contentSize", options: .new, context: nil)

        leftView = UIView(frame: CGRect(x: 0, y: 0, width: 20, height: bounds.height))
        leftView.backgroundColor = .clear
        leftView.isHidden = true
        leftView.autoresizingMask = [.flexibleRightMargin, .flexibleBottomMargin]

        let leftTranparentView = UIView(frame: leftView.bounds)
        leftTranparentView.backgroundColor = .white
        leftTranparentView.alpha = 0.8
        leftTranparentView.layer.cornerRadius = 10.0
        leftTranparentView.clipsToBounds = true

        leftView.addSubview(leftTranparentView)

        let leftArrowView = UIImageView(image: UIImage(named: "ic_back"))
        leftArrowView.contentMode = .scaleAspectFit
        leftArrowView.frame = CGRect(x: 0, y: leftView.bounds.height/2 - 10, width: 20, height: 20)
        leftView.addSubview(leftArrowView)

        addSubview(leftView)


        rightView = UIView(frame: CGRect(x: bounds.width - 20, y: 0, width: 20, height: bounds.height))
        rightView.backgroundColor = .clear
        rightView.isHidden = true
        rightView.autoresizingMask = [.flexibleRightMargin, .flexibleBottomMargin]

        let rightTranparentView = UIView(frame: rightView.bounds)
        rightTranparentView.backgroundColor = .white
        rightTranparentView.alpha = 0.8
        rightTranparentView.layer.cornerRadius = 10.0
        rightTranparentView.clipsToBounds = true

        rightView.addSubview(rightTranparentView)

        let rightArrowView = UIImageView(image: UIImage(named: "ic_back"))
        rightArrowView.transform = CGAffineTransform(rotationAngle: .pi)
        rightArrowView.contentMode = .scaleAspectFit
        rightArrowView.frame = CGRect(x: 0, y: rightView.bounds.height/2 - 10, width: 20, height: 20)
        rightView.addSubview(rightArrowView)

        addSubview(rightView)
    }

    override func willMove(toSuperview newSuperview: UIView?) {
        super.willMove(toSuperview: newSuperview)
        if newSuperview == nil {
            collectionView.removeObserver(self, forKeyPath: "contentSize")
        }
    }

    override func observeValue(forKeyPath keyPath: String?, of object: Any?, change: [NSKeyValueChangeKey : Any]?, context: UnsafeMutableRawPointer?) {
        if keyPath == "contentSize" {
            rightView.isHidden = collectionView.contentSize.width <= collectionView.frame.width || collectionView.contentOffset.x + collectionView.frame.width >= collectionView.contentSize.width
            leftView.isHidden = collectionView.contentOffset.x <= 0
        }
    }
}

extension TimeListView: UICollectionViewDataSource, UICollectionViewDelegate {
    func numberOfSections(in collectionView: UICollectionView) -> Int {
        return 1
    }

    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return data.count
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: cellId, for: indexPath) as! TimeListCollectionCell
        cell.show = data[indexPath.row]
        return cell
    }

    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        delegate?.timeListDidSelected(data[indexPath.row])
    }
}

extension TimeListView: UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let size = CGSize(width: 90, height: collectionView.frame.height)
        return size
    }
}

extension TimeListView: UIScrollViewDelegate {
    func scrollViewDidScroll(_ scrollView: UIScrollView) {
        if scrollView.contentSize.width > scrollView.frame.width {
            rightView.isHidden = scrollView.contentOffset.x + scrollView.frame.width >= scrollView.contentSize.width
        } else {
            rightView.isHidden = true
        }
        leftView.isHidden = collectionView.contentOffset.x <= 0
    }
}

//
//  TimeListCollectionCell.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/25/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

class TimeListCollectionCell: UICollectionViewCell {
    @IBOutlet weak var lbTime: UILabel!
    @IBOutlet weak var lbEmptyNumber: UILabel!
    @IBOutlet weak var vRound: RoundView!

    var show: ShowModel? {
        didSet {
            self.updateData()
        }
    }

    override var isHighlighted: Bool {
        didSet {
            setSelected(isHighlighted)
        }
    }

    override func awakeFromNib() {
        super.awakeFromNib()
    }

    func setSelected(_ selected: Bool) {
        let color = vRound.backgroundColor
        vRound.backgroundColor = selected ? color?.withAlphaComponent(0.5) : color?.withAlphaComponent(1)
    }

    func updateData() {
        guard let show = self.show else { return }
      lbTime.text = show.getStartDate()?.toString(dateFormat: "H:mm")
        lbEmptyNumber.text = String(show.emptySeat) + " " + "empty".localized
    }
}

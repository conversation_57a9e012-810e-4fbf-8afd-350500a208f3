//
//  ChooseCinemasViewController.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 3/14/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit
import SwiftDate
import PopupDialog

class ChooseCinemasViewController: BaseViewController {
    @IBOutlet weak var headerView: UIView!
    @IBOutlet weak var tableView: UITableView!
    @IBOutlet weak var lbCinemaName: UILabel!
    @IBOutlet weak var calendarView: CalendarHeaderView!

    var cinema: CinemaModel!
    let cellId = "FilmTimeTableViewCell"
    let dataSource = SimpleTableViewDataSource()
    fileprivate var selectedShow: ShowModel?
    fileprivate var selectedFilm: ShowFilmModel?
    


    override func viewDidLoad() {
        super.viewDidLoad()
        navigationController?.setTransparent(false)
        localizableTitle = "BookingByTheater.Title"

        tableView.register(UINib(nibName: cellId, bundle: nil), forCellReuseIdentifier: cellId)
        tableView.dataSource = dataSource
        // test
//        dataSource.addRows((0...10).map { TableItem(title: "\($0)", cellId: cellId) })

        fillData()
        updateTableView()

        calendarView.delegate = self
        calendarView.selectIndex(0)

//        getShowAtDate(Date())
        
        getListDate()
       }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        updateTableView()
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)

        guard let show = selectedShow, Global.shared.isLogined else {
            selectedFilm = nil
            selectedShow = nil
            return
        }
        select(show, film: selectedFilm)
    }

    func updateTableView() {
        headerView.autoFitSize()
        tableView.tableHeaderView = headerView
    }

    func fillData() {
        lbCinemaName.text = cinema.getName()
    }

    func getListDate() {
        self.showLoading()
        CinemaProvider.rx.request(.cinemaShowDate(cinema.CinemaId ?? "")).mapObject(MyResponse<String>.self).subscribe(onNext: { response in
            guard response.isSuccess() else {
                self.calendarView.dates = []
                self.showAlert(message: response.Message ?? "")
                self.dismissLoading()
                return
            }
            guard let list = response.ListObject else {
                self.calendarView.dates = []
                self.dismissLoading()
                return
            }
            let listDate = list.compactMap { $0.dateFromServer() }.sorted{ $0 < $1}
            self.calendarView.dates = listDate
            if let date = listDate.first {
                self.getShowAtDate(date)
                self.calendarView.selectDate(date)
            } else {
                self.dismissLoading()
            }
        }).disposed(by: disposeBag)
    }

    func getShowAtDate(_ date: Date) {
//        self.showLoading()
        let dateString = date.toString(dateFormat: "yyyy-MM-dd")
        CinemaProvider.rx.request(.cinemaFilmShow(cinema.CinemaId ?? "", dateString)).mapObject(DDKCResponse<ShowFilmModel>.self).subscribe(onNext: { [weak self] result in
            self?.dismissLoading()
            
            if let listShow = result.ListObject {
//                listShow = listShow.filter {
//                    $0.listFilm?.filter {
//                        $0.listShow?.filter {
//                            guard let time = $0.getTimeLockDate() else { return false}
//                            return time > Date() }.isEmpty == false
//                        }
//                    }.filter { $0.listFilm?.isEmpty == false }
                self?.dataSource.removeAll()
                self?.dataSource.addRows(listShow.map { TableItem(data: $0, cellId: self?.cellId) })
                self?.tableView.reloadData()
            } else {
                self?.dataSource.removeAll()
                self?.tableView.reloadData()
            }
            }, onError: { [weak self] _ in
            self?.dismissLoading()
        }).disposed(by: disposeBag)
    }

    func select(_ show: ShowModel, film: ShowFilmModel?) {
        let vc = UIStoryboard.cinema[.chooseSeat] as! ChooseSeatViewController
        vc.cinemaId = self.cinema.CinemaId
        vc.cinemaName = self.cinema.getName()
        vc.showTime = show
        vc.film = film
        var filmId:String?
        if let film = film?.listFilm?.first {
            filmId = film.filmId
        }
        Tracking.shared.selectShowtimeComplete(cinemaId:  vc.cinemaId, cinemaName: vc.cinemaName, movieId: filmId, movieName: vc.film?.getName(), date: vc.showTime?.getStartDate(), time:  vc.showTime?.getStartDate())
        self.show(vc)

        selectedFilm = nil
        selectedShow = nil
    }
}

extension ChooseCinemasViewController: CalendarHeaderViewDelegate {
    func calendarView(_ calendarView: CalendarHeaderView, didSelected date: Date) {
        showLoading()
        self.getShowAtDate(date)
    }
}

extension ChooseCinemasViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, willDisplay cell: UITableViewCell, forRowAt indexPath: IndexPath) {
        let filmCell = cell as? FilmTimeTableViewCell
        filmCell?.cinema = self.cinema
        filmCell?.delegate = self
        let film = dataSource[indexPath].data as? ShowFilmModel
        filmCell?.onPlayFilmTrailer = { [weak self] in
            let vc = UIStoryboard.home[.youtube] as! YoutubeViewController
            vc.videoURL = film?.trailerURL
            self?.present(vc, animated: true, completion: nil)
        }
    }

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        let film = dataSource[indexPath].data as? ShowFilmModel
        return 220 + (film?.height ?? 0)
    }
}

extension ChooseCinemasViewController: FilmeTimeDelegate {
    func didSelect(_ film: ShowFilmModel?, show: ShowModel) {
        guard let date = show.getTimeLockDate(), date >= Date() else {
            self.showAlert(message: "ShowFilm.TimeOut".localized)
            return
        }
        if show.isShowScreenIntro ?? false {
            updateMessage(show: show, film: film)
        } else {
            setupLogin(show: show, film: film)
        }

    }

    func setupLogin(show: ShowModel, film: ShowFilmModel?) {
        guard Global.shared.isLogined else {
            selectedFilm = film
            selectedShow = show

            let vc = UIStoryboard.authen[.login]
            self.show(vc)
            return
        }
        select(show, film: film)
    }
}

extension ChooseCinemasViewController {
    func updateMessage(show: ShowModel, film: ShowFilmModel?) {
        let vc = UIStoryboard.cinema[.confirmVipZoom] as! ConfirmVipZoomViewController
        vc.message = show.screenDesc ?? ""
        vc.titleZoom = show.screenTitle ?? ""
        vc.pathImage = show.screenImageUrl ?? ""
        let alert = PopupDialog(viewController: vc, buttonAlignment: .horizontal, preferredWidth: self.view.frame.width - 100)
        let buttonOne = CancelButton(title: "Bt.Cancel".localized, dismissOnTap: true) {

        }
        // This button will not the dismiss the dialog
        let buttonTwo = DefaultButton(title: "Bt.Yes".localized, dismissOnTap: true) { [weak self] in
            guard let self = self else { return }
            setupLogin(show: show, film: film)
        }
        alert.addButtons([buttonOne, buttonTwo])
        self.present(alert, animated: true, completion: nil)
    }
}

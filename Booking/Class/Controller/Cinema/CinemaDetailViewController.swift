//
//  CinemaDetailViewController.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 3/14/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit
import GoogleMaps
import PKHUD
import MapKit

class CinemaDetailViewController: BaseViewController {
    @IBOutlet weak var scrollView: UIScrollView!
    @IBOutlet weak var btTicketPrice: UIButton!
    @IBOutlet weak var btSessionTime: UIButton!
    @IBOutlet weak var btCallPhone: UIButton!

    @IBOutlet weak var lbCinemaName: UILabel!
    @IBOutlet weak var ivCinemaLogo: UIImageView!
    @IBOutlet weak var mapView: GMSMapView!
    @IBOutlet weak var lbAddress: UILabel!

    @IBOutlet weak var vPromotion: NewAndDealsView!
    @IBOutlet weak var vPromotionHeight: NSLayoutConstraint!

    var isGuiding: Bool = false

    override func barTransparent() -> Bool {
        return false
    }

    var cinema: CinemaModel?

    override func viewDidLoad() {
        super.viewDidLoad()

        localizableTitle = "CinemaDetail.Title"

        // Do any additional setup after loading the view.
        self.navigationController?.setTransparent(false)

        if #available(iOS 11.0, *) {
            scrollView.contentInsetAdjustmentBehavior = .never
        } else {
            self.automaticallyAdjustsScrollViewInsets = false
        }
        mapView.delegate = self

        vPromotion.delegate = self
        updateData()
        if self.cinema?.NewsId == nil {
            getCinemaDetail()
        }
        getPromotionCategory()
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()

        scrollView.frame = self.view.bounds
        btTicketPrice.centerVertically(padding: 0)
        btSessionTime.centerVertically(padding: 0)
        btCallPhone.centerVertically(padding: 0)

        ivCinemaLogo.addBottomRoundedEdge()
    }

    func updateData() {

        btTicketPrice.setTitle("ticket_price".localized, for: .normal)
        btSessionTime.setTitle("show".localized, for: .normal)
        btCallPhone.setTitle("call_now".localized, for: .normal)

        guard let cinema = self.cinema else { return }

        lbCinemaName.text = cinema.getName()
        if let url = cinema.pictureURL {
            ivCinemaLogo.af_setImage(withURL: url)
        }
        let addressAttributed = NSMutableAttributedString(string: "Address".localized + ": ", attributes: [NSAttributedStringKey.font: UIFont(fontName: .SourceSansPro, style: .Bold, size: 16), NSAttributedStringKey.foregroundColor: UIColor.inputText])
        addressAttributed.append(NSAttributedString(string: (cinema.getAddress() ?? ""), attributes: [NSAttributedStringKey.font: UIFont(fontName: .SourceSansPro, size: 16), NSAttributedStringKey.foregroundColor: UIColor.inputText]))
        lbAddress.attributedText = addressAttributed

        if let latStr = cinema.Latitude, let longStr = cinema.Longtitude, let lat = CLLocationDegrees(latStr), let long = CLLocationDegrees(longStr) {
            let coordinate = CLLocationCoordinate2D(latitude: lat, longitude: long)
            let camera = GMSCameraPosition.camera(withLatitude: lat, longitude: long, zoom: 17.0)
            mapView.animate(to: camera)

            let marker = GMSMarker()
            marker.position = coordinate
            marker.title = cinema.getName() ?? ""
            marker.snippet = cinema.getAddress() ?? ""
            marker.map = mapView
        }
    }

    func getCinemaDetail() {
        guard let cinema = self.cinema else { return }

        HUD.show(.progress)
        CinemaProvider.rx.request(.cinemaDetail(cinema.CinemaId ?? "")).mapObject(DDKCResponse<CinemaModel>.self).subscribe(onNext: { response in
            HUD.hide()
            if let cinema = response.Object {
                self.cinema = cinema
            }
            self.updateData()
        }).disposed(by: disposeBag)
    }

    private func getPromotionCategory(){
        EcmProvider.rx.request(.getNewPromotion).mapObject(DDKCResponse<NewModel>.self)
            
            .subscribe(onNext:{[weak self] response in
                self?.handlerResponse(response, success: {
                    guard let objects = response.ListObject else{
                        print("Data wrong")
                        return
                    }
                    if objects.count > 0{
                        self?.getListPromotion(objects.first?.CategoryId)
                    }
                })
            }).disposed(by: disposeBag)
    }

    private func getListPromotion(_ categoryId: String?){
        guard let id = categoryId else {
            return
        }
        EcmProvider.rx.request(.getNewForCategory(id, 3, nil)).mapObject(DDKCResponse<NewsModel>.self)
            
            .subscribe(onNext:{[weak self] response in
                self?.handlerResponse(response, success: {
                    guard let items = response.ListObject, let `self` = self else {return}
                    DispatchQueue.main.async {
                        self.vPromotion.dataList = items
                        self.vPromotionHeight.constant = CGFloat(items.count) * 120 + 50
                    }
                })
            }).disposed(by: disposeBag)
    }

    func routeInAppleMap(_ destination: CLLocation) {
        let placeMark = MKPlacemark(coordinate: destination.coordinate, addressDictionary: nil)
        
        let mapItem = MKMapItem(placemark: placeMark)
        mapItem.name = cinema?.getName()
        mapItem.openInMaps(launchOptions: [MKLaunchOptionsDirectionsModeKey: MKLaunchOptionsDirectionsModeDriving])
    }

    func routeInGoogleMap(_ destination: CLLocation) {
        let url = "comgooglemaps://?saddr=&daddr=\(destination.coordinate.latitude),\(destination.coordinate.longitude)&directionsmode=driving"
        UIApplication.shared.open(URL(string: url)!, options: [:], completionHandler: nil)
    }
}

extension CinemaDetailViewController {
    @IBAction func btTicketPressed(_ sender: Any) {
        let vc = UIStoryboard.cinema[.price] as! CinemaPriceViewController
        vc.cinemaId = self.cinema?.NewsId
        showPopup(vc, height: self.view.frame.height - 100)
    }

    @IBAction func btSessionPressed(_ sender: Any) {
        let vc = UIStoryboard.cinema[.chooseCinema] as! ChooseCinemasViewController
        vc.cinema = self.cinema
        Tracking.shared.selectTheater(cinemaId: vc.cinema?.CinemaId, cinemaName: vc.cinema?.Name)
        show(vc)
    }

    @IBAction func btCallPressed(_ sender: Any) {
        guard let phone = self.cinema?.PhoneNumber?.removeAllSpaces() else { return }

        if let url = URL(string: "tel://" + phone), UIApplication.shared.canOpenURL(url) {
            if #available(iOS 10, *) {
                UIApplication.shared.open(url)
            } else {
                UIApplication.shared.openURL(url)
            }
        }
    }

    @IBAction func btGuidePressed(_ sender: Any) {
        guard !isGuiding else {
            return
        }

        guard let latStr = self.cinema?.Latitude, let longStr = self.cinema?.Longtitude, let lat = CLLocationDegrees(latStr), let long = CLLocationDegrees(longStr) else {
            return
        }

        let cinemaLoc = CLLocation(latitude: lat, longitude: long)

        let googleMapURL = URL(string: "comgooglemaps://")!
        if UIApplication.shared.canOpenURL(googleMapURL) {
            let actionSheet = UIAlertController(title: "RouteInMap.Title".localized, message: nil, preferredStyle: .actionSheet)
            let googleMapAction = UIAlertAction(title: "OpenInGoogleMap".localized, style: .default) { _ in
                self.routeInGoogleMap(cinemaLoc)
            }
            let appleMapAction = UIAlertAction(title: "OpenInAppleMap".localized, style: .default) { _ in
                self.routeInAppleMap(cinemaLoc)
            }
            let cancelAction = UIAlertAction(title: "Bt.Cancel".localized, style: .cancel) { _ in

            }
            actionSheet.addAction(googleMapAction)
            actionSheet.addAction(appleMapAction)
            actionSheet.addAction(cancelAction)
            self.present(actionSheet, animated: true, completion: nil)
        } else {
            self.routeInAppleMap(cinemaLoc)
        }
    }
}

extension CinemaDetailViewController: GMSMapViewDelegate {

}

extension CinemaDetailViewController: NewsAndDealsViewDelegate {
    func newsView(_ view: NewAndDealsView, didSelected item: NewsModel) {
        let vc = UIStoryboard.home[.newsDetail] as! NewsDetailViewController
        vc.type = NewType.news(item)
        show(vc, sender: nil)
    }

    func newsViewDidShowAll() {
        let vc = UIStoryboard.home[.newsAndDeals]
        show(vc, sender: nil)
    }

    func didChangeHeight(_ height: CGFloat) {
        vPromotionHeight.constant = height + 50
    }
}

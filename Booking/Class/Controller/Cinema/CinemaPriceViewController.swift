//
//  CinemaPriceViewController.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 5/22/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit
import WebKit

class CinemaPriceViewController: BaseViewController {
    @IBOutlet weak var tableView: UITableView!
    @IBOutlet weak var btPrice2D: UIButton!
    @IBOutlet weak var btPrice3D: UIButton!
    @IBOutlet weak var mainView: UIView!

    fileprivate var dataSource = SimpleTableViewDataSource()

    fileprivate let priceCellId = "CinemaPriceHeaderView"
    fileprivate let priceCell2Id = ""
    fileprivate let priceHeaderId = "CinemaPriceHeaderView"

    var cinemaId: String?

    fileprivate lazy var webView: WKWebView = {
        let wkWebConfig = WKWebViewConfiguration()

        let webView = WKWebView(frame: mainView.bounds, configuration: wkWebConfig)
        webView.navigationDelegate = self
        webView.autoresizingMask = [.flexibleWidth, .flexibleHeight]
        return webView
    }()

    override func viewDidLoad() {
        super.viewDidLoad()

        // Do any additional setup after loading the view.
        tableView.dataSource = dataSource
        tableView.register(UINib(nibName: priceHeaderId, bundle: nil), forHeaderFooterViewReuseIdentifier: priceHeaderId)

        mainView.addSubview(webView)

//        getPriceId()
        if let id = cinemaId {
            getPriceDetail(id)
        } else {
            getPriceId()
        }
    }

    override func didReceiveMemoryWarning() {
        super.didReceiveMemoryWarning()
        // Dispose of any resources that can be recreated.
    }

    func getPriceId() {
        showLoading()
        EcmProvider.rx.request(.getPrice).mapObject(DDKCResponse<PolicyModel>.self).subscribe(onNext: { [weak self] result in
            if let id = result.Object?.ParameterValue {
                self?.getPriceDetail(id)
            } else {
                self?.dismissLoading()
            }
        }).disposed(by: disposeBag)
    }

    func getPriceDetail(_ id: String) {
        EcmProvider.rx.request(.getNewWithId(id, nil, nil)).mapObject(DDKCResponse<PolicyContentModel>.self).subscribe(onNext: { [weak self] result in
                self?.dismissLoading()
            if let data = result.Object?.Noi_dung_chi_tiet?.first?.ParagraphData?.ParagraphContent {
                self?.webView.loadHTMLString(data, baseURL: URL(string: Config.BaseURL))
            }
            }, onError: { [weak self] _ in
                self?.dismissLoading()
        }).disposed(by: disposeBag)
    }

    @IBAction func closeButtonPressed(_ sender: Any) {
        self.dismiss(animated: true, completion: nil)
    }

    @IBAction func price2DPressed(_ sender: Any) {
        btPrice2D.isSelected = true
        btPrice3D.isSelected = false
        btPrice2D.backgroundColor = .selected
        btPrice3D.backgroundColor = .clear
    }
    
    @IBAction func price3DPressed(_ sender: Any) {
        btPrice2D.isSelected = false
        btPrice3D.isSelected = true
        btPrice2D.backgroundColor = .clear
        btPrice3D.backgroundColor = .selected
    }
}

extension CinemaPriceViewController: WKNavigationDelegate {
    func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        self.dismissLoading()
    }

    func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
        self.dismissLoading()
    }
}

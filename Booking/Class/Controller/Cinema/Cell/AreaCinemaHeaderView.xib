<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14109" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14088"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="SourceSansPro-Regular.ttf">
            <string>SourceSansPro-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="iN0-l3-epB" customClass="AreaCinemaHeaderView" customModule="Booking_dev" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="375" height="96"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="U4t-1s-6Sg">
                    <rect key="frame" x="0.0" y="0.0" width="375" height="96"/>
                    <subviews>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Wuj-XR-qr6" customClass="RoundView" customModule="Booking_dev" customModuleProvider="target">
                            <rect key="frame" x="8" y="3" width="359" height="90"/>
                            <subviews>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" verticalHuggingPriority="251" text="Beta Mỹ Đình" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="XqR-SO-dhd">
                                    <rect key="frame" x="12" y="15" width="250" height="60"/>
                                    <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                    <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    <nil key="highlightedColor"/>
                                </label>
                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="1,5 km" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="yZp-ig-6CU">
                                    <rect key="frame" x="268" y="35" width="45" height="21"/>
                                    <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                    <color key="textColor" red="0.01176470588" green="0.34901960780000002" blue="0.61568627450000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                    <nil key="highlightedColor"/>
                                </label>
                                <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_drop_down" translatesAutoresizingMaskIntoConstraints="NO" id="4xf-g9-wiY">
                                    <rect key="frame" x="321" y="32" width="26" height="26"/>
                                    <constraints>
                                        <constraint firstAttribute="width" constant="26" id="Lej-wV-7Fv"/>
                                        <constraint firstAttribute="height" constant="26" id="YH1-ph-y30"/>
                                    </constraints>
                                </imageView>
                                <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="kpj-pl-vOb">
                                    <rect key="frame" x="0.0" y="0.0" width="359" height="90"/>
                                    <connections>
                                        <action selector="didTapOnHeader:" destination="iN0-l3-epB" eventType="touchUpInside" id="Ruc-XP-Fat"/>
                                    </connections>
                                </button>
                            </subviews>
                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <constraints>
                                <constraint firstItem="4xf-g9-wiY" firstAttribute="leading" secondItem="yZp-ig-6CU" secondAttribute="trailing" priority="750" constant="8" id="7cq-6J-Yg8"/>
                                <constraint firstItem="yZp-ig-6CU" firstAttribute="centerY" secondItem="Wuj-XR-qr6" secondAttribute="centerY" id="8c9-C6-dlE"/>
                                <constraint firstAttribute="trailing" secondItem="4xf-g9-wiY" secondAttribute="trailing" priority="750" constant="12" id="9ly-ia-Xe8"/>
                                <constraint firstItem="yZp-ig-6CU" firstAttribute="leading" secondItem="XqR-SO-dhd" secondAttribute="trailing" priority="750" constant="6" id="CFo-3p-fOh"/>
                                <constraint firstAttribute="bottom" secondItem="kpj-pl-vOb" secondAttribute="bottom" id="NWS-ab-trp"/>
                                <constraint firstItem="kpj-pl-vOb" firstAttribute="leading" secondItem="Wuj-XR-qr6" secondAttribute="leading" id="Oc3-nt-xMv"/>
                                <constraint firstItem="XqR-SO-dhd" firstAttribute="top" secondItem="Wuj-XR-qr6" secondAttribute="top" constant="15" id="PjG-1H-bK6"/>
                                <constraint firstItem="XqR-SO-dhd" firstAttribute="leading" secondItem="Wuj-XR-qr6" secondAttribute="leading" priority="750" constant="12" id="Qij-1u-eLU"/>
                                <constraint firstItem="kpj-pl-vOb" firstAttribute="top" secondItem="Wuj-XR-qr6" secondAttribute="top" id="UoM-OJ-XAp"/>
                                <constraint firstAttribute="bottom" secondItem="XqR-SO-dhd" secondAttribute="bottom" constant="15" id="fAm-8n-rC4"/>
                                <constraint firstItem="4xf-g9-wiY" firstAttribute="centerY" secondItem="Wuj-XR-qr6" secondAttribute="centerY" id="kCF-Hk-OtV"/>
                                <constraint firstAttribute="trailing" secondItem="kpj-pl-vOb" secondAttribute="trailing" id="yR5-Uh-1p1"/>
                            </constraints>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                    <real key="value" value="2"/>
                                </userDefinedRuntimeAttribute>
                            </userDefinedRuntimeAttributes>
                        </view>
                    </subviews>
                    <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="calibratedRGB"/>
                    <constraints>
                        <constraint firstItem="Wuj-XR-qr6" firstAttribute="leading" secondItem="U4t-1s-6Sg" secondAttribute="leading" priority="750" constant="8" id="FUq-xn-x6a"/>
                        <constraint firstAttribute="bottom" secondItem="Wuj-XR-qr6" secondAttribute="bottom" priority="750" constant="3" id="VY9-Da-TCK"/>
                        <constraint firstAttribute="trailing" secondItem="Wuj-XR-qr6" secondAttribute="trailing" priority="750" constant="8" id="eGl-U2-DEA"/>
                        <constraint firstItem="Wuj-XR-qr6" firstAttribute="top" secondItem="U4t-1s-6Sg" secondAttribute="top" priority="750" constant="3" id="vqm-5q-kAJ"/>
                    </constraints>
                </view>
            </subviews>
            <constraints>
                <constraint firstItem="U4t-1s-6Sg" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" id="HvR-GP-BD9"/>
                <constraint firstAttribute="bottom" secondItem="U4t-1s-6Sg" secondAttribute="bottom" id="Qyj-la-ZbW"/>
                <constraint firstAttribute="trailing" secondItem="U4t-1s-6Sg" secondAttribute="trailing" id="htM-uz-PZR"/>
                <constraint firstItem="U4t-1s-6Sg" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" id="sYh-Qb-YpF"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="ivArrow" destination="4xf-g9-wiY" id="IC4-QY-eGy"/>
                <outlet property="lbSubTitle" destination="yZp-ig-6CU" id="fdt-jc-fyc"/>
                <outlet property="lbTitle" destination="XqR-SO-dhd" id="g37-dz-RnX"/>
            </connections>
            <point key="canvasLocation" x="1" y="13"/>
        </view>
    </objects>
    <resources>
        <image name="ic_drop_down" width="26" height="26"/>
    </resources>
</document>

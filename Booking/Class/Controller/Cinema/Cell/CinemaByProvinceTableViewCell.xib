<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14490.70" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14490.49"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="SourceSansPro-Regular.ttf">
            <string>SourceSansPro-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" rowHeight="268" id="KGk-i7-Jjw" customClass="CinemaByProvinceTableViewCell" customModule="Booking_dev" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="320" height="268"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="267.5"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" dataMode="none" translatesAutoresizingMaskIntoConstraints="NO" id="aZs-RH-gpZ">
                        <rect key="frame" x="8" y="63" width="304" height="196.5"/>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <collectionViewFlowLayout key="collectionViewLayout" minimumLineSpacing="10" minimumInteritemSpacing="8" id="cK1-Mb-oSs">
                            <size key="itemSize" width="50" height="50"/>
                            <size key="headerReferenceSize" width="0.0" height="0.0"/>
                            <size key="footerReferenceSize" width="0.0" height="0.0"/>
                            <inset key="sectionInset" minX="0.0" minY="0.0" maxX="0.0" maxY="0.0"/>
                        </collectionViewFlowLayout>
                        <connections>
                            <outlet property="dataSource" destination="KGk-i7-Jjw" id="jvt-TX-5Cy"/>
                            <outlet property="delegate" destination="KGk-i7-Jjw" id="GP3-LV-ajC"/>
                        </connections>
                    </collectionView>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="OO7-Tc-qAl" customClass="RoundView" customModule="Booking_dev" customModuleProvider="target">
                        <rect key="frame" x="8" y="4" width="304" height="55"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" verticalHuggingPriority="251" text="Beta Mỹ Đình" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Ui6-g0-QGh">
                                <rect key="frame" x="12" y="15" width="232" height="25"/>
                                <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="3" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="UeM-it-YTh">
                                <rect key="frame" x="250" y="17" width="8" height="21"/>
                                <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                <color key="textColor" red="0.01176470588" green="0.34901960780000002" blue="0.61568627450000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_drop_down" translatesAutoresizingMaskIntoConstraints="NO" id="4Mq-gc-D7U">
                                <rect key="frame" x="266" y="14.5" width="26" height="26"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="26" id="Fef-Kw-zrj"/>
                                    <constraint firstAttribute="height" constant="26" id="WU3-hC-zUr"/>
                                </constraints>
                            </imageView>
                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="roundedRect" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Jgw-ne-YiU">
                                <rect key="frame" x="0.0" y="0.0" width="304" height="55"/>
                                <connections>
                                    <action selector="expandTapped:" destination="KGk-i7-Jjw" eventType="touchUpInside" id="Drb-Uk-Jw2"/>
                                </connections>
                            </button>
                        </subviews>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="Ui6-g0-QGh" firstAttribute="leading" secondItem="OO7-Tc-qAl" secondAttribute="leading" priority="750" constant="12" id="0o8-ab-N6h"/>
                            <constraint firstItem="4Mq-gc-D7U" firstAttribute="centerY" secondItem="OO7-Tc-qAl" secondAttribute="centerY" id="5pi-S1-9p8"/>
                            <constraint firstAttribute="bottom" secondItem="Ui6-g0-QGh" secondAttribute="bottom" constant="15" id="T8v-zx-4nI"/>
                            <constraint firstItem="Jgw-ne-YiU" firstAttribute="leading" secondItem="OO7-Tc-qAl" secondAttribute="leading" id="Wfy-3y-SFy"/>
                            <constraint firstItem="Ui6-g0-QGh" firstAttribute="top" secondItem="OO7-Tc-qAl" secondAttribute="top" constant="15" id="XY7-Vw-7kT"/>
                            <constraint firstItem="UeM-it-YTh" firstAttribute="centerY" secondItem="OO7-Tc-qAl" secondAttribute="centerY" id="Y1R-Y5-Za0"/>
                            <constraint firstAttribute="trailing" secondItem="4Mq-gc-D7U" secondAttribute="trailing" priority="750" constant="12" id="bVX-Gq-jBL"/>
                            <constraint firstItem="UeM-it-YTh" firstAttribute="leading" secondItem="Ui6-g0-QGh" secondAttribute="trailing" priority="750" constant="6" id="fKX-x4-aq4"/>
                            <constraint firstItem="Jgw-ne-YiU" firstAttribute="top" secondItem="OO7-Tc-qAl" secondAttribute="top" id="p98-rq-wE1"/>
                            <constraint firstAttribute="bottom" secondItem="Jgw-ne-YiU" secondAttribute="bottom" id="qvm-k4-pyp"/>
                            <constraint firstAttribute="trailing" secondItem="Jgw-ne-YiU" secondAttribute="trailing" id="rJc-FD-KqS"/>
                            <constraint firstAttribute="height" constant="55" id="u1p-Jb-vFG"/>
                            <constraint firstItem="4Mq-gc-D7U" firstAttribute="leading" secondItem="UeM-it-YTh" secondAttribute="trailing" priority="750" constant="8" id="zRK-It-i37"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="2"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                </subviews>
                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                <constraints>
                    <constraint firstAttribute="bottom" secondItem="aZs-RH-gpZ" secondAttribute="bottom" constant="8" id="1jE-EZ-itr"/>
                    <constraint firstAttribute="trailing" secondItem="aZs-RH-gpZ" secondAttribute="trailing" constant="8" id="BGk-qf-9Rf"/>
                    <constraint firstAttribute="trailing" secondItem="OO7-Tc-qAl" secondAttribute="trailing" constant="8" id="D0P-hl-Qu0"/>
                    <constraint firstItem="OO7-Tc-qAl" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="8" id="VgB-vP-Wjv"/>
                    <constraint firstItem="aZs-RH-gpZ" firstAttribute="top" secondItem="OO7-Tc-qAl" secondAttribute="bottom" constant="4" id="gaT-SS-cKx"/>
                    <constraint firstItem="OO7-Tc-qAl" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="4" id="liD-A2-DGW"/>
                    <constraint firstItem="aZs-RH-gpZ" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="8" id="z0O-lz-Lhx"/>
                </constraints>
            </tableViewCellContentView>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <viewLayoutGuide key="safeArea" id="njF-e1-oar"/>
            <connections>
                <outlet property="arrowImageView" destination="4Mq-gc-D7U" id="Brj-Wq-BVH"/>
                <outlet property="cinemaCountLabel" destination="UeM-it-YTh" id="tSd-W9-jsu"/>
                <outlet property="cinemaProvince" destination="Ui6-g0-QGh" id="2oX-ZQ-2Hy"/>
                <outlet property="collectionView" destination="aZs-RH-gpZ" id="eMy-t8-ieI"/>
            </connections>
            <point key="canvasLocation" x="131.8840579710145" y="227.67857142857142"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="ic_drop_down" width="26" height="26"/>
    </resources>
</document>

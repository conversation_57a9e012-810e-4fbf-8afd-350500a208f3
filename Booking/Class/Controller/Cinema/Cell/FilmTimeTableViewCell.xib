<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14490.70" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14490.49"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Oswald-Bold.ttf">
            <string>Oswald-Bold</string>
        </array>
        <array key="SourceSansPro-Regular.ttf">
            <string>SourceSansPro-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" rowHeight="287" id="KGk-i7-Jjw" customClass="FilmTimeTableViewCell" customModule="Booking_dev" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="275" height="287"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="275" height="286.5"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="G4K-74-uGU">
                        <rect key="frame" x="0.0" y="0.0" width="275" height="286.5"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="dDY-KT-Zcc" customClass="RoundView" customModule="Booking_dev" customModuleProvider="target">
                                <rect key="frame" x="8" y="12" width="259" height="254.5"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="nLT-0g-fMc" userLabel="View1">
                                        <rect key="frame" x="0.0" y="0.0" width="82" height="108"/>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    </view>
                                    <view hidden="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="9hC-Ny-vbz" customClass="FilmSeperateView" customModule="Booking_dev" customModuleProvider="target">
                                        <rect key="frame" x="82" y="0.0" width="20" height="254.5"/>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="20" id="3wk-bE-6h3"/>
                                        </constraints>
                                    </view>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="WH8-lz-7by">
                                        <rect key="frame" x="106" y="8" width="113" height="53.5"/>
                                        <string key="text">Ông Ngoại Tuổi 30Ông Ngoại Tuổi 30Ông Ngoại Tuổi 30Ông Ngoại Tuổi 30Ông Ngoại Tuổi 30Ông Ngoại Tuổi 30Ông Ngoại Tuổi 30Ông Ngoại Tuổi 30Ông Ngoại Tuổi 30Ông Ngoại Tuổi 30Ông Ngoại Tuổi 30 Ông Ngoại Tuổi 30</string>
                                        <fontDescription key="fontDescription" name="Oswald-Bold" family="Oswald" pointSize="18"/>
                                        <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Võ thuật, Viễn Tưởng" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="nTB-pl-TsO">
                                        <rect key="frame" x="106" y="67.5" width="145" height="20.5"/>
                                        <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                        <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="135 phút" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="gn9-QN-JGL">
                                        <rect key="frame" x="106" y="94" width="145" height="20.5"/>
                                        <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                        <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_top_1" translatesAutoresizingMaskIntoConstraints="NO" id="8GM-vc-HrZ">
                                        <rect key="frame" x="227" y="8" width="24" height="37"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="24" id="0Z3-cq-65J"/>
                                            <constraint firstAttribute="height" constant="37" id="roy-Fr-hwd"/>
                                        </constraints>
                                    </imageView>
                                    <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" style="plain" separatorStyle="default" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="pUp-ca-gb7">
                                        <rect key="frame" x="0.0" y="116" width="259" height="138.5"/>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <connections>
                                            <outlet property="dataSource" destination="KGk-i7-Jjw" id="UBF-rd-914"/>
                                            <outlet property="delegate" destination="KGk-i7-Jjw" id="phu-bn-eiP"/>
                                        </connections>
                                    </tableView>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="bottom" secondItem="9hC-Ny-vbz" secondAttribute="bottom" id="3Gh-Ic-1A7"/>
                                    <constraint firstItem="nTB-pl-TsO" firstAttribute="leading" secondItem="9hC-Ny-vbz" secondAttribute="trailing" constant="4" id="6cj-iV-7WL"/>
                                    <constraint firstAttribute="trailing" secondItem="pUp-ca-gb7" secondAttribute="trailing" id="9nN-QI-mAO"/>
                                    <constraint firstItem="pUp-ca-gb7" firstAttribute="leading" secondItem="dDY-KT-Zcc" secondAttribute="leading" id="EDq-yv-q4U"/>
                                    <constraint firstItem="8GM-vc-HrZ" firstAttribute="leading" secondItem="WH8-lz-7by" secondAttribute="trailing" constant="8" id="JYY-a9-Pxs"/>
                                    <constraint firstItem="9hC-Ny-vbz" firstAttribute="leading" secondItem="nLT-0g-fMc" secondAttribute="trailing" id="KL3-PA-PpI"/>
                                    <constraint firstAttribute="trailing" secondItem="8GM-vc-HrZ" secondAttribute="trailing" constant="8" id="R7E-fY-r7l"/>
                                    <constraint firstItem="nLT-0g-fMc" firstAttribute="top" secondItem="dDY-KT-Zcc" secondAttribute="top" id="RDR-Z3-j1h"/>
                                    <constraint firstItem="9hC-Ny-vbz" firstAttribute="top" secondItem="dDY-KT-Zcc" secondAttribute="top" id="Yi7-Zd-ibI"/>
                                    <constraint firstItem="nTB-pl-TsO" firstAttribute="top" secondItem="WH8-lz-7by" secondAttribute="bottom" constant="6" id="aiW-zr-alR"/>
                                    <constraint firstAttribute="trailing" secondItem="gn9-QN-JGL" secondAttribute="trailing" constant="8" id="biI-tf-TKV"/>
                                    <constraint firstItem="WH8-lz-7by" firstAttribute="top" secondItem="dDY-KT-Zcc" secondAttribute="top" constant="8" id="c9m-9c-31E"/>
                                    <constraint firstAttribute="bottom" relation="greaterThanOrEqual" secondItem="gn9-QN-JGL" secondAttribute="bottom" constant="12" id="guo-U3-lby"/>
                                    <constraint firstItem="WH8-lz-7by" firstAttribute="leading" secondItem="9hC-Ny-vbz" secondAttribute="trailing" constant="4" id="lUI-Oy-bLD"/>
                                    <constraint firstItem="8GM-vc-HrZ" firstAttribute="top" secondItem="dDY-KT-Zcc" secondAttribute="top" constant="8" id="ljc-gk-GIV"/>
                                    <constraint firstItem="gn9-QN-JGL" firstAttribute="top" secondItem="nTB-pl-TsO" secondAttribute="bottom" constant="6" id="lmd-TZ-Izf"/>
                                    <constraint firstItem="nLT-0g-fMc" firstAttribute="leading" secondItem="dDY-KT-Zcc" secondAttribute="leading" id="nU1-fB-VNY"/>
                                    <constraint firstAttribute="bottom" secondItem="pUp-ca-gb7" secondAttribute="bottom" id="sh4-Gm-t5B"/>
                                    <constraint firstAttribute="trailing" secondItem="nTB-pl-TsO" secondAttribute="trailing" constant="8" id="vKo-jv-0ch"/>
                                    <constraint firstItem="gn9-QN-JGL" firstAttribute="leading" secondItem="9hC-Ny-vbz" secondAttribute="trailing" constant="4" id="yey-nU-Gow"/>
                                    <constraint firstItem="pUp-ca-gb7" firstAttribute="top" secondItem="nLT-0g-fMc" secondAttribute="bottom" constant="8" id="zf0-Sp-714"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="4"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="D0D-CE-CvH" customClass="RoundView" customModule="Booking_dev" customModuleProvider="target">
                                <rect key="frame" x="16" y="4" width="82" height="108"/>
                                <subviews>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="bg1.png" translatesAutoresizingMaskIntoConstraints="NO" id="w5b-PE-TeB" customClass="RoundImageView" customModule="Booking_dev" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="0.0" width="82" height="108"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                <real key="value" value="4"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                    </imageView>
                                    <imageView hidden="YES" clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_hot" translatesAutoresizingMaskIntoConstraints="NO" id="cvG-tQ-3Dn">
                                        <rect key="frame" x="42" y="0.0" width="40" height="40"/>
                                        <constraints>
                                            <constraint firstAttribute="width" secondItem="cvG-tQ-3Dn" secondAttribute="height" id="GRg-MM-aV3"/>
                                            <constraint firstAttribute="width" constant="40" id="lIT-PU-k9K"/>
                                        </constraints>
                                    </imageView>
                                    <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_c13" translatesAutoresizingMaskIntoConstraints="NO" id="tSZ-B7-fic">
                                        <rect key="frame" x="4" y="4" width="36" height="18"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="36" id="Uu6-e0-zZz"/>
                                            <constraint firstAttribute="height" constant="18" id="va2-nh-0Zh"/>
                                        </constraints>
                                    </imageView>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="GdI-9R-7sP">
                                        <rect key="frame" x="11" y="24" width="60" height="60"/>
                                        <state key="normal" image="ic_play"/>
                                        <connections>
                                            <action selector="playButtonPressed:" destination="KGk-i7-Jjw" eventType="touchUpInside" id="UJF-01-9EI"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="width" secondItem="D0D-CE-CvH" secondAttribute="height" multiplier="114:150" id="KxG-wI-ngT"/>
                                    <constraint firstItem="tSZ-B7-fic" firstAttribute="leading" secondItem="w5b-PE-TeB" secondAttribute="leading" constant="4" id="WPk-uG-G7h"/>
                                    <constraint firstItem="tSZ-B7-fic" firstAttribute="top" secondItem="w5b-PE-TeB" secondAttribute="top" constant="4" id="YIN-V1-3Hs"/>
                                    <constraint firstAttribute="bottom" secondItem="w5b-PE-TeB" secondAttribute="bottom" id="bIX-9d-mZr"/>
                                    <constraint firstItem="GdI-9R-7sP" firstAttribute="centerY" secondItem="D0D-CE-CvH" secondAttribute="centerY" id="cTs-by-M57"/>
                                    <constraint firstItem="w5b-PE-TeB" firstAttribute="top" secondItem="D0D-CE-CvH" secondAttribute="top" id="ea8-Np-GZy"/>
                                    <constraint firstItem="GdI-9R-7sP" firstAttribute="centerX" secondItem="D0D-CE-CvH" secondAttribute="centerX" id="g18-u6-aso"/>
                                    <constraint firstItem="w5b-PE-TeB" firstAttribute="leading" secondItem="D0D-CE-CvH" secondAttribute="leading" id="iGx-om-YBZ"/>
                                    <constraint firstAttribute="trailing" secondItem="w5b-PE-TeB" secondAttribute="trailing" id="pwO-R9-lRg"/>
                                    <constraint firstItem="cvG-tQ-3Dn" firstAttribute="top" secondItem="w5b-PE-TeB" secondAttribute="top" id="tLY-LI-ZWK"/>
                                    <constraint firstItem="cvG-tQ-3Dn" firstAttribute="trailing" secondItem="w5b-PE-TeB" secondAttribute="trailing" id="voL-n1-wFD"/>
                                </constraints>
                                <userDefinedRuntimeAttributes>
                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                        <real key="value" value="4"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="point" keyPath="shadowOffset">
                                        <point key="value" x="0.0" y="4"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="shadowRadius">
                                        <real key="value" value="4"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="number" keyPath="shadowOpacity">
                                        <real key="value" value="0.25"/>
                                    </userDefinedRuntimeAttribute>
                                    <userDefinedRuntimeAttribute type="color" keyPath="shadowColor">
                                        <color key="value" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    </userDefinedRuntimeAttribute>
                                </userDefinedRuntimeAttributes>
                            </view>
                        </subviews>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstAttribute="trailing" secondItem="dDY-KT-Zcc" secondAttribute="trailing" constant="8" id="3cC-lt-LSs"/>
                            <constraint firstItem="D0D-CE-CvH" firstAttribute="height" secondItem="nLT-0g-fMc" secondAttribute="height" id="3fA-UA-J3j"/>
                            <constraint firstItem="D0D-CE-CvH" firstAttribute="width" secondItem="dDY-KT-Zcc" secondAttribute="width" multiplier="114:359" id="LBr-QL-Fkg"/>
                            <constraint firstItem="D0D-CE-CvH" firstAttribute="leading" secondItem="G4K-74-uGU" secondAttribute="leading" constant="16" id="PKi-VA-mTY"/>
                            <constraint firstItem="dDY-KT-Zcc" firstAttribute="leading" secondItem="G4K-74-uGU" secondAttribute="leading" constant="8" id="e49-Wq-VXd"/>
                            <constraint firstAttribute="bottom" secondItem="dDY-KT-Zcc" secondAttribute="bottom" constant="20" id="e5A-fx-PwO"/>
                            <constraint firstItem="D0D-CE-CvH" firstAttribute="top" secondItem="G4K-74-uGU" secondAttribute="top" constant="4" id="ili-9g-6bk"/>
                            <constraint firstItem="dDY-KT-Zcc" firstAttribute="top" secondItem="G4K-74-uGU" secondAttribute="top" constant="12" id="r7L-Py-slB"/>
                            <constraint firstItem="nLT-0g-fMc" firstAttribute="width" secondItem="D0D-CE-CvH" secondAttribute="width" id="tMN-YB-1Sm"/>
                        </constraints>
                    </view>
                </subviews>
                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                <constraints>
                    <constraint firstAttribute="bottom" secondItem="G4K-74-uGU" secondAttribute="bottom" id="8f2-mX-oEL"/>
                    <constraint firstItem="G4K-74-uGU" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" id="Nk6-GQ-x9M"/>
                    <constraint firstItem="G4K-74-uGU" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" id="S2W-SP-PIN"/>
                    <constraint firstAttribute="trailing" secondItem="G4K-74-uGU" secondAttribute="trailing" id="hNJ-Ln-cjc"/>
                </constraints>
            </tableViewCellContentView>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <viewLayoutGuide key="safeArea" id="njF-e1-oar"/>
            <connections>
                <outlet property="imvHot" destination="cvG-tQ-3Dn" id="dQ8-W6-UTl"/>
                <outlet property="ivAgeRate" destination="tSZ-B7-fic" id="bCN-TZ-70X"/>
                <outlet property="ivFilmLogo" destination="w5b-PE-TeB" id="7oU-Ha-Ax0"/>
                <outlet property="ivTop1" destination="8GM-vc-HrZ" id="PF1-4X-pbX"/>
                <outlet property="lbFilmDuration" destination="gn9-QN-JGL" id="SEP-EH-Y83"/>
                <outlet property="lbFilmName" destination="WH8-lz-7by" id="yCe-Ck-4DV"/>
                <outlet property="lbFilmType" destination="nTB-pl-TsO" id="Am8-g2-QSR"/>
                <outlet property="tableView" destination="pUp-ca-gb7" id="qKV-ab-1UA"/>
            </connections>
            <point key="canvasLocation" x="866.5" y="806"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="bg1.png" width="320" height="568"/>
        <image name="ic_c13" width="56" height="27"/>
        <image name="ic_hot" width="40" height="40"/>
        <image name="ic_play" width="60" height="60"/>
        <image name="ic_top_1" width="48" height="73"/>
    </resources>
</document>

//
//  SelectRegionViewController.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 5/26/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

protocol SelectRegionDelegate: class {
    func didSelectedRegion(_ province: CinemaProvinceModel?)
}

class SelectRegionViewController: BaseViewController {
    @IBOutlet weak var tableView: UITableView!

    fileprivate var dataSource = SimpleTableViewDataSource()
    fileprivate let cellId = "SettingTableCell"

    weak var delegate: SelectRegionDelegate?

    override func viewDidLoad() {
        super.viewDidLoad()

        localizableTitle = "Film.SelectRegion"

        tableView.register(UINib(nibName: cellId, bundle: nil), forCellReuseIdentifier: cellId)
        tableView.dataSource = dataSource

        getCinemaByCity()
    }

    private func getCinemaByCity() {
       self.showLoading()
        CinemaProvider.rx.request(.listCinemaByProvince).mapObject(DDKCResponse<CinemaProvinceModel>.self)
            
            .subscribe(onNext:{[weak self] response in
                guard let `self` = self else {return}
                self.dismissLoading()
                self.handlerResponse(response, success: {
                    guard let items = response.ListObject else {return}

                    var tbItems = [TableItem(title: "Home.All".localized, content: "\(items.compactMap { $0.ListCinema }.joined().count)", cellId: self.cellId)]
                    let list = items.map { TableItem(title: $0.CityName, content: "\($0.ListCinema?.count ?? 0)", data: $0,cellId: self.cellId) }
                    tbItems.append(contentsOf: list)
                    self.dataSource.removeAll()
                    self.dataSource.addRows(tbItems)
                    self.tableView.reloadData()
                }, error: {
                    self.dismissLoading()
                })
            }).disposed(by: disposeBag)
    }
}

extension SelectRegionViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        delegate?.didSelectedRegion(dataSource[indexPath].data as? CinemaProvinceModel)
        navigationController?.popViewController(animated: true)
    }
}

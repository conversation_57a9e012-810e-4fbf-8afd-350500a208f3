//
//  ListAllCinemasViewController.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 3/14/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit
import UITableView_FDTemplateLayoutCell
import CoreLocation

class ListAllCinemasViewController: BaseViewController {
    @IBOutlet weak var tableView: UITableView!

    let cinemaCellId = "CinemaTableViewCell"
    let nearCinemaCellId = "NearCinemaTableViewCell"
    let titleHeaderViewId = "TitleHeaderView"
    let areaCinemaHeaderViewId = "AreaCinemaHeaderView"

    let dataSource = SimpleTableViewDataSource()
    
    var sections = [TableSection]()

    var isBooking: Bool = false

//    lazy var locationManager = CLLocationManager()

    private var cinemas: [CinemaModel] = []{
        didSet{
            sections.append(TableSection(title: "CinemaList.NearCinema".localized.uppercased(), items: self.cinemas.sorted(by: { return $0.getDistanceDouble() < $1.getDistanceDouble() }).map{ TableItem(title:$0.getName(), content: "\($0.getDistance()) km", data: $0, cellId: nearCinemaCellId) }))
        }
    }
    private var allCinemas: [CinemaModel] = []
    private var cinemasByCity: [CinemaProvinceModel] = []{
        didSet{
            sections.append(TableSection(title: "CinemaList.ChooseByArea".localized.uppercased(), items: []))
            let _ = self.cinemasByCity.map {
                sections.append(TableSection(title: $0.CityName, subTitle: "\($0.ListCinema?.count ?? 0)", items: ($0.ListCinema ?? []).map { TableItem(title: $0.getName(), data: $0, cellId: cinemaCellId) }, isOpen: false))
            }
        }
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()

        localizableTitle = "ListCinema.Title"

        tableView.register(UINib(nibName: titleHeaderViewId, bundle: nil), forHeaderFooterViewReuseIdentifier: titleHeaderViewId)
        tableView.register(UINib(nibName: areaCinemaHeaderViewId, bundle: nil), forHeaderFooterViewReuseIdentifier: areaCinemaHeaderViewId)
        tableView.dataSource = dataSource
        
        if !LocationManager.shared.systemEnable() {
            LocationManager.shared.openLocationAlert()
        }

        LocationManager.shared.requestCurrentLocation { location in
            guard !self.cinemas.isEmpty else { return }
            
            self.cinemas = self.allCinemas.sorted(by: { $0.getDistanceDouble() < $1.getDistanceDouble() })
            self.tableView.reloadData()
        }
        self.getData()
    }
}

extension ListAllCinemasViewController{
    private func getListNearbyCinema(completion:@escaping() -> Void){
        CinemaProvider.rx.request(.listCinema).mapObject(DDKCResponse<CinemaModel>.self)
            
            .subscribe(onNext:{[weak self] response in
                guard let `self` = self else {return}
                self.handlerResponse(response, success: {
                    guard let items = response.ListObject else {
                        self.dismissLoading()
                        return
                    }
                    self.allCinemas = items
                    self.cinemas = items.sorted(by: { $0.getDistanceDouble() < $1.getDistanceDouble() }).filter { $0.getDistanceDouble() < 100 }
                    completion()
                }, error: {
                    self.dismissLoading()
                })
            }).disposed(by: disposeBag)
    }
    
    private func getCinemaByCity(completion:@escaping() -> Void){
        CinemaProvider.rx.request(.listCinemaByProvince).mapObject(DDKCResponse<CinemaProvinceModel>.self)
            
            .subscribe(onNext:{[weak self] response in
                guard let `self` = self else {return}
                self.handlerResponse(response, success: {
                    guard let items = response.ListObject else {return}
                    self.cinemasByCity = items
                    completion()
                }, error: {
                    self.dismissLoading()
                })
            }).disposed(by: disposeBag)
    }
    
    private func getData(){
        self.showLoading()
        self.getListNearbyCinema {
            self.getCinemaByCity {
                DispatchQueue.main.async {
                    self.dismissLoading()
                    self.dataSource.addSection(self.sections)
                    self.tableView.reloadData()
                }
            }
        }
    }
}

extension ListAllCinemasViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        let tbSection = self.dataSource.sections[section]
        if section == 0 {
            let view = tableView.dequeueReusableHeaderFooterView(withIdentifier: titleHeaderViewId)
            view?.updateViewWithSection(tbSection)
            return view
        } else if section == 1 {
            let view = tableView.dequeueReusableHeaderFooterView(withIdentifier: titleHeaderViewId)
            view?.updateViewWithSection(tbSection)
            return view
        } else {
            let view = tableView.dequeueReusableHeaderFooterView(withIdentifier: areaCinemaHeaderViewId) as! AreaCinemaHeaderView
            view.updateViewWithSection(tbSection)
            view.onTapAction = { [weak self] view in
                guard let `self` = self else {
                    return
                }
                tbSection.isOpen = !tbSection.isOpen
                if tbSection.isOpen && tbSection.count > 0 {
                    self.tableView.insertRows(at: tbSection.items.enumerated().map { IndexPath(row: $0.offset, section: section) }, with: .automatic)
                } else if !tbSection.isOpen && tbSection.count > 0 {
                    self.tableView.deleteRows(at: tbSection.items.enumerated().map { IndexPath(row: $0.offset, section: section) }, with: .fade)
                } else {
                    self.tableView.reloadSections([section], with: .fade)
                }
                UIView.animate(withDuration: 0.3, animations: {
                    view.updateViewWithSection(tbSection)
                })
            }
            return view
        }
    }

    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        if section == 0 || section == 1 {
            return 60
        }

        return 55
    }

    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        let item = dataSource[indexPath]
        if indexPath.section == 0 {
            return tableView.fd_heightForCell(withIdentifier: nearCinemaCellId, configuration: { (obj) in
                guard let cell = obj as? NearCinemaTableViewCell else { return }
                cell.updateViewWithItem(item, indexPath: indexPath)
            })
        }
        return tableView.fd_heightForCell(withIdentifier: cinemaCellId, configuration: { (obj) in
            guard let cell = obj as? CinemaTableViewCell else {
                return
            }
            cell.updateViewWithItem(item, indexPath: indexPath)
        })
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: false)

        guard let cinema = dataSource[indexPath].data as? CinemaModel else {
            return
        }
        if isBooking {
            let vc = UIStoryboard.cinema[.chooseCinema] as! ChooseCinemasViewController
            vc.cinema = cinema
            vc.hidesBottomBarWhenPushed = true
            Tracking.shared.selectTheater(cinemaId: cinema.CinemaId, cinemaName:cinema.Name)
            show(vc)
            return
        }

        let vc = UIStoryboard.cinema[.cinemaDetail] as! CinemaDetailViewController
        vc.cinema = cinema
        show(vc)
    }
}

<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="32700.99.1234" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait" appearance="light"/>
    <dependencies>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22684"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="System colors in document resources" minToolsVersion="11.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Oswald-Bold.ttf">
            <string><PERSON>-<PERSON></string>
        </array>
        <array key="Oswald-Light.ttf">
            <string><PERSON>-<PERSON></string>
        </array>
        <array key="Oswald-Regular.ttf">
            <string>Oswald-Regular</string>
        </array>
        <array key="SourceSansPro-Bold.ttf">
            <string>SourceSansPro-Bold</string>
        </array>
        <array key="SourceSansPro-Regular.ttf">
            <string>SourceSansPro-Regular</string>
        </array>
    </customFonts>
    <scenes>
        <!--List All Cinemas View Controller-->
        <scene sceneID="IdC-s1-mkU">
            <objects>
                <viewController storyboardIdentifier="ListAllCinemasViewController" automaticallyAdjustsScrollViewInsets="NO" id="mfH-3J-Ut3" customClass="ListAllCinemasViewController" customModule="Beta_Cinemas" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Zh7-Tz-9wc">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="7fO-xA-69i">
                                <rect key="frame" x="0.0" y="20" width="375" height="647"/>
                                <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="calibratedRGB"/>
                                <prototypes>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" reuseIdentifier="NearCinemaTableViewCell" rowHeight="59" id="HYX-1e-6AD" customClass="NearCinemaTableViewCell" customModule="Beta_Cinemas" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="50" width="375" height="59"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="HYX-1e-6AD" id="iaZ-Gq-xLb">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="59"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="hAO-EM-L4D" customClass="RoundView" customModule="Beta_Cinemas" customModuleProvider="target">
                                                    <rect key="frame" x="8" y="3" width="359" height="53"/>
                                                    <subviews>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" verticalHuggingPriority="251" text="Beta Mỹ Đình" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="vPb-4Y-OL9">
                                                            <rect key="frame" x="12" y="15" width="284.5" height="23"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="20" id="NPh-e8-g0L"/>
                                                            </constraints>
                                                            <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                            <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="1,5 km" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Kub-vd-ypa">
                                                            <rect key="frame" x="302.5" y="16.5" width="44.5" height="20.5"/>
                                                            <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                            <color key="textColor" red="0.0**********" green="0.34901960780000002" blue="0.61568627450000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                    </subviews>
                                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <constraints>
                                                        <constraint firstAttribute="trailing" secondItem="Kub-vd-ypa" secondAttribute="trailing" constant="12" id="4RH-YL-RYu"/>
                                                        <constraint firstItem="vPb-4Y-OL9" firstAttribute="leading" secondItem="hAO-EM-L4D" secondAttribute="leading" constant="12" id="G5c-MH-Scq"/>
                                                        <constraint firstAttribute="bottom" secondItem="vPb-4Y-OL9" secondAttribute="bottom" constant="15" id="HpX-d1-uRZ"/>
                                                        <constraint firstItem="Kub-vd-ypa" firstAttribute="centerY" secondItem="hAO-EM-L4D" secondAttribute="centerY" id="IhV-db-2iB"/>
                                                        <constraint firstItem="vPb-4Y-OL9" firstAttribute="top" secondItem="hAO-EM-L4D" secondAttribute="top" constant="15" id="bEG-AP-nWa"/>
                                                        <constraint firstItem="Kub-vd-ypa" firstAttribute="leading" secondItem="vPb-4Y-OL9" secondAttribute="trailing" constant="6" id="j2a-qg-6zS"/>
                                                    </constraints>
                                                    <userDefinedRuntimeAttributes>
                                                        <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                            <real key="value" value="2"/>
                                                        </userDefinedRuntimeAttribute>
                                                    </userDefinedRuntimeAttributes>
                                                </view>
                                            </subviews>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstAttribute="trailing" secondItem="hAO-EM-L4D" secondAttribute="trailing" constant="8" id="EPR-tc-SYI"/>
                                                <constraint firstAttribute="bottom" secondItem="hAO-EM-L4D" secondAttribute="bottom" constant="3" id="KS2-No-hFN"/>
                                                <constraint firstItem="hAO-EM-L4D" firstAttribute="top" secondItem="iaZ-Gq-xLb" secondAttribute="top" constant="3" id="Z0p-NE-yn8"/>
                                                <constraint firstItem="hAO-EM-L4D" firstAttribute="leading" secondItem="iaZ-Gq-xLb" secondAttribute="leading" constant="8" id="laz-5P-av2"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <connections>
                                            <outlet property="lbSubTitle" destination="Kub-vd-ypa" id="WYS-MK-ClP"/>
                                            <outlet property="lbTitle" destination="vPb-4Y-OL9" id="4kH-5s-7Bm"/>
                                        </connections>
                                    </tableViewCell>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" reuseIdentifier="CinemaTableViewCell" rowHeight="93" id="bpf-dt-Daw" customClass="CinemaTableViewCell" customModule="Beta_Cinemas" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="109" width="375" height="93"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="bpf-dt-Daw" id="zkl-14-XQf">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="93"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="07m-82-6AQ">
                                                    <rect key="frame" x="0.0" y="0.0" width="375" height="93"/>
                                                    <subviews>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" verticalHuggingPriority="251" text="Beta Mỹ Đình" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="lYi-sz-75Q">
                                                            <rect key="frame" x="20" y="15" width="335" height="63"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" relation="greaterThanOrEqual" constant="20" id="Kjx-r7-4sD"/>
                                                            </constraints>
                                                            <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                            <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Vac-RM-MAD">
                                                            <rect key="frame" x="8" y="92" width="359" height="1"/>
                                                            <color key="backgroundColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="0.10000000000000001" colorSpace="custom" customColorSpace="sRGB"/>
                                                            <constraints>
                                                                <constraint firstAttribute="height" constant="1" id="j7w-8S-d5X"/>
                                                            </constraints>
                                                        </view>
                                                    </subviews>
                                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <constraints>
                                                        <constraint firstAttribute="bottom" secondItem="Vac-RM-MAD" secondAttribute="bottom" id="F4E-uc-9NN"/>
                                                        <constraint firstItem="lYi-sz-75Q" firstAttribute="leading" secondItem="07m-82-6AQ" secondAttribute="leading" constant="20" id="PSj-QP-ftj"/>
                                                        <constraint firstItem="Vac-RM-MAD" firstAttribute="leading" secondItem="07m-82-6AQ" secondAttribute="leading" constant="8" id="XXj-yB-ptU"/>
                                                        <constraint firstItem="lYi-sz-75Q" firstAttribute="top" secondItem="07m-82-6AQ" secondAttribute="top" constant="15" id="Yo2-6d-2eo"/>
                                                        <constraint firstAttribute="bottom" secondItem="lYi-sz-75Q" secondAttribute="bottom" constant="15" id="rsn-3O-wT5"/>
                                                        <constraint firstAttribute="trailing" secondItem="Vac-RM-MAD" secondAttribute="trailing" constant="8" id="vLa-Vf-k7q"/>
                                                        <constraint firstAttribute="trailing" secondItem="lYi-sz-75Q" secondAttribute="trailing" constant="20" id="zWs-i9-D12"/>
                                                    </constraints>
                                                </view>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="07m-82-6AQ" firstAttribute="top" secondItem="zkl-14-XQf" secondAttribute="top" id="SMX-SY-mPF"/>
                                                <constraint firstItem="07m-82-6AQ" firstAttribute="leading" secondItem="zkl-14-XQf" secondAttribute="leading" id="cAs-I1-36Z"/>
                                                <constraint firstAttribute="bottom" secondItem="07m-82-6AQ" secondAttribute="bottom" id="jon-Yg-OI5"/>
                                                <constraint firstAttribute="trailing" secondItem="07m-82-6AQ" secondAttribute="trailing" id="yUB-3c-LWk"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <connections>
                                            <outlet property="bottomLine" destination="Vac-RM-MAD" id="C16-7H-scr"/>
                                            <outlet property="lbTitle" destination="lYi-sz-75Q" id="ffl-HY-8jJ"/>
                                        </connections>
                                    </tableViewCell>
                                </prototypes>
                                <connections>
                                    <outlet property="dataSource" destination="mfH-3J-Ut3" id="vMw-Hm-gq0"/>
                                    <outlet property="delegate" destination="mfH-3J-Ut3" id="6F5-g5-TNO"/>
                                </connections>
                            </tableView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="tND-xt-LbM"/>
                        <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstItem="tND-xt-LbM" firstAttribute="bottom" secondItem="7fO-xA-69i" secondAttribute="bottom" id="AEb-aJ-2ao"/>
                            <constraint firstItem="7fO-xA-69i" firstAttribute="leading" secondItem="tND-xt-LbM" secondAttribute="leading" id="bVz-8j-rFK"/>
                            <constraint firstItem="7fO-xA-69i" firstAttribute="top" secondItem="tND-xt-LbM" secondAttribute="top" id="djy-DP-v95"/>
                            <constraint firstItem="tND-xt-LbM" firstAttribute="trailing" secondItem="7fO-xA-69i" secondAttribute="trailing" id="mcl-we-cjd"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="tableView" destination="7fO-xA-69i" id="izq-Qk-aVW"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="vxY-ou-gsS" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-178.40000000000001" y="156.97151424287858"/>
        </scene>
        <!--Choose Cinemas View Controller-->
        <scene sceneID="3pJ-4H-kK3">
            <objects>
                <viewController storyboardIdentifier="ChooseCinemasViewController" id="5C2-eh-sWn" customClass="ChooseCinemasViewController" customModule="Beta_Cinemas" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="JaJ-Ic-HBI">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="Nrc-yv-M78">
                                <rect key="frame" x="0.0" y="20" width="375" height="647"/>
                                <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="calibratedRGB"/>
                                <view key="tableHeaderView" contentMode="scaleToFill" id="2mv-WU-gwm">
                                    <rect key="frame" x="0.0" y="0.0" width="375" height="252"/>
                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                    <subviews>
                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Beta Cinemas Mỹ Đình" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="iKe-zF-CBp">
                                            <rect key="frame" x="20" y="22" width="335" height="113"/>
                                            <fontDescription key="fontDescription" name="Oswald-Bold" family="Oswald" pointSize="20"/>
                                            <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            <nil key="highlightedColor"/>
                                        </label>
                                        <view alpha="0.10000000149011612" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="BaI-JT-dua">
                                            <rect key="frame" x="21" y="153" width="333" height="1"/>
                                            <color key="backgroundColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            <constraints>
                                                <constraint firstAttribute="height" constant="1" id="aOI-nX-2pA"/>
                                            </constraints>
                                        </view>
                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="dot-F2-lKI" customClass="CalendarHeaderView" customModule="Beta_Cinemas" customModuleProvider="target">
                                            <rect key="frame" x="0.0" y="154" width="375" height="80"/>
                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstAttribute="height" constant="80" id="Qrb-U5-0hb"/>
                                            </constraints>
                                        </view>
                                    </subviews>
                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <constraints>
                                        <constraint firstItem="iKe-zF-CBp" firstAttribute="leading" secondItem="2mv-WU-gwm" secondAttribute="leading" constant="20" id="3LP-jv-955"/>
                                        <constraint firstItem="iKe-zF-CBp" firstAttribute="top" secondItem="2mv-WU-gwm" secondAttribute="top" constant="22" id="3mt-xZ-KRZ"/>
                                        <constraint firstAttribute="trailing" secondItem="dot-F2-lKI" secondAttribute="trailing" id="IYJ-1t-1k1"/>
                                        <constraint firstItem="dot-F2-lKI" firstAttribute="top" secondItem="BaI-JT-dua" secondAttribute="bottom" id="NXG-9l-fij"/>
                                        <constraint firstAttribute="bottom" secondItem="dot-F2-lKI" secondAttribute="bottom" constant="18" id="diT-lb-QG3"/>
                                        <constraint firstItem="BaI-JT-dua" firstAttribute="top" secondItem="iKe-zF-CBp" secondAttribute="bottom" constant="18" id="hOO-Fh-eaJ"/>
                                        <constraint firstItem="BaI-JT-dua" firstAttribute="leading" secondItem="2mv-WU-gwm" secondAttribute="leading" constant="21" id="hZ8-lx-UOR"/>
                                        <constraint firstAttribute="trailing" secondItem="iKe-zF-CBp" secondAttribute="trailing" constant="20" id="jYw-2j-fvG"/>
                                        <constraint firstAttribute="trailing" secondItem="BaI-JT-dua" secondAttribute="trailing" constant="21" id="rkJ-TM-m5g"/>
                                        <constraint firstItem="dot-F2-lKI" firstAttribute="leading" secondItem="2mv-WU-gwm" secondAttribute="leading" id="sKk-yh-oa6"/>
                                    </constraints>
                                </view>
                                <prototypes>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" reuseIdentifier="TableViewCellc" id="9gw-k0-0Tk">
                                        <rect key="frame" x="0.0" y="302" width="375" height="48"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="9gw-k0-0Tk" id="kFe-oD-NvU">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="48"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                        </tableViewCellContentView>
                                    </tableViewCell>
                                </prototypes>
                                <connections>
                                    <outlet property="delegate" destination="5C2-eh-sWn" id="3tb-gD-oeZ"/>
                                </connections>
                            </tableView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="qQ0-Lk-hSx"/>
                        <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstItem="qQ0-Lk-hSx" firstAttribute="trailing" secondItem="Nrc-yv-M78" secondAttribute="trailing" id="IXb-US-l4C"/>
                            <constraint firstItem="qQ0-Lk-hSx" firstAttribute="bottom" secondItem="Nrc-yv-M78" secondAttribute="bottom" id="SaV-oO-12F"/>
                            <constraint firstItem="Nrc-yv-M78" firstAttribute="top" secondItem="qQ0-Lk-hSx" secondAttribute="top" id="TGI-4e-Of7"/>
                            <constraint firstItem="Nrc-yv-M78" firstAttribute="leading" secondItem="qQ0-Lk-hSx" secondAttribute="leading" id="t82-st-OQL"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="calendarView" destination="dot-F2-lKI" id="Ffe-ab-3EZ"/>
                        <outlet property="headerView" destination="2mv-WU-gwm" id="PfG-TB-h5w"/>
                        <outlet property="lbCinemaName" destination="iKe-zF-CBp" id="9Hl-tY-azj"/>
                        <outlet property="tableView" destination="Nrc-yv-M78" id="64b-Xx-K6g"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="O1p-oV-TsU" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="480.80000000000001" y="156.97151424287858"/>
        </scene>
        <!--Cinema Detail View Controller-->
        <scene sceneID="Aaj-tm-4Zn">
            <objects>
                <viewController storyboardIdentifier="CinemaDetailViewController" extendedLayoutIncludesOpaqueBars="YES" id="K0z-1E-gwO" customClass="CinemaDetailViewController" customModule="Beta_Cinemas" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Hub-Ag-Evq">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="1000"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="K8Y-ff-61L">
                                <rect key="frame" x="0.0" y="20" width="375" height="980"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="l0G-9B-AQV">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="928"/>
                                        <subviews>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="hpE-XL-Xqk">
                                                <rect key="frame" x="0.0" y="0.0" width="375" height="800"/>
                                                <subviews>
                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="bg3.png" translatesAutoresizingMaskIntoConstraints="NO" id="u6X-nj-caq">
                                                        <rect key="frame" x="0.0" y="0.0" width="375" height="292"/>
                                                        <constraints>
                                                            <constraint firstAttribute="width" secondItem="u6X-nj-caq" secondAttribute="height" multiplier="375:292" id="JV7-Gt-Fjb"/>
                                                        </constraints>
                                                    </imageView>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Beta Cinemas Mỹ Đình" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="RCc-z5-a12">
                                                        <rect key="frame" x="20" y="304" width="335" height="30"/>
                                                        <fontDescription key="fontDescription" name="Oswald-Bold" family="Oswald" pointSize="20"/>
                                                        <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="ny0-4e-tKb">
                                                        <rect key="frame" x="0.0" y="384" width="375" height="416"/>
                                                        <subviews>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="sCs-4x-2bD" customClass="GMSMapView">
                                                                <rect key="frame" x="0.0" y="34" width="375" height="234"/>
                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" secondItem="sCs-4x-2bD" secondAttribute="height" multiplier="375:234" id="v8z-jY-B8z"/>
                                                                </constraints>
                                                            </view>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="m34-6X-jXy">
                                                                <rect key="frame" x="0.0" y="268" width="375" height="148"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" verticalHuggingPriority="249" text="Địa chỉ: Tầng hầm B1, tòa nhà Golden Palace, đường Mễ Trì, quận Nam Từ Liêm, Hà Nội" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="BfJ-lB-Gxl">
                                                                        <rect key="frame" x="8" y="10" width="257" height="118"/>
                                                                        <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                                        <color key="textColor" red="0.**********2352941" green="0.12156862745098039" blue="0.15686274509803921" alpha="0.85999999999999999" colorSpace="custom" customColorSpace="sRGB"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="gj7-NC-xJg" customClass="RoundButton" customModule="Beta_Cinemas" customModuleProvider="target">
                                                                        <rect key="frame" x="275" y="20" width="92" height="24"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="width" constant="92" id="Eza-y7-Yph"/>
                                                                            <constraint firstAttribute="height" constant="24" id="ZFf-9E-ol3"/>
                                                                        </constraints>
                                                                        <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                                        <state key="normal" title="Chỉ đường">
                                                                            <color key="titleColor" red="0.0**********" green="0.34901960780000002" blue="0.61568627450000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                        </state>
                                                                        <userDefinedRuntimeAttributes>
                                                                            <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                                                                <real key="value" value="1"/>
                                                                            </userDefinedRuntimeAttribute>
                                                                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                                                <real key="value" value="13"/>
                                                                            </userDefinedRuntimeAttribute>
                                                                            <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                                                                <color key="value" red="0.0**********" green="0.34901960780000002" blue="0.61568627450000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                            </userDefinedRuntimeAttribute>
                                                                            <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Bt.Guide"/>
                                                                        </userDefinedRuntimeAttributes>
                                                                        <connections>
                                                                            <action selector="btGuidePressed:" destination="K0z-1E-gwO" eventType="touchUpInside" id="C9w-DN-kax"/>
                                                                        </connections>
                                                                    </button>
                                                                </subviews>
                                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstItem="BfJ-lB-Gxl" firstAttribute="top" secondItem="m34-6X-jXy" secondAttribute="top" constant="10" id="5Ck-mK-BHP"/>
                                                                    <constraint firstAttribute="bottom" secondItem="BfJ-lB-Gxl" secondAttribute="bottom" constant="20" id="DGV-Zq-btY"/>
                                                                    <constraint firstAttribute="trailing" secondItem="gj7-NC-xJg" secondAttribute="trailing" constant="8" id="WWC-y8-9fL"/>
                                                                    <constraint firstItem="gj7-NC-xJg" firstAttribute="top" secondItem="m34-6X-jXy" secondAttribute="top" constant="20" id="WpQ-qG-wgB"/>
                                                                    <constraint firstItem="BfJ-lB-Gxl" firstAttribute="leading" secondItem="m34-6X-jXy" secondAttribute="leading" constant="8" id="mqA-Z8-mbv"/>
                                                                    <constraint firstItem="gj7-NC-xJg" firstAttribute="leading" secondItem="BfJ-lB-Gxl" secondAttribute="trailing" constant="10" id="yK1-kT-IfZ"/>
                                                                </constraints>
                                                            </view>
                                                        </subviews>
                                                        <color key="backgroundColor" red="0.95294**********18" green="0.95294**********18" blue="0.95294**********18" alpha="1" colorSpace="calibratedRGB"/>
                                                        <constraints>
                                                            <constraint firstItem="m34-6X-jXy" firstAttribute="top" secondItem="sCs-4x-2bD" secondAttribute="bottom" id="DJC-eo-HTa"/>
                                                            <constraint firstAttribute="trailing" secondItem="sCs-4x-2bD" secondAttribute="trailing" id="EIs-2R-fTC"/>
                                                            <constraint firstItem="m34-6X-jXy" firstAttribute="leading" secondItem="ny0-4e-tKb" secondAttribute="leading" id="EoI-39-T1U"/>
                                                            <constraint firstItem="sCs-4x-2bD" firstAttribute="leading" secondItem="ny0-4e-tKb" secondAttribute="leading" id="G0m-5r-uLP"/>
                                                            <constraint firstAttribute="trailing" secondItem="m34-6X-jXy" secondAttribute="trailing" id="Gcm-tr-Off"/>
                                                            <constraint firstItem="sCs-4x-2bD" firstAttribute="top" secondItem="ny0-4e-tKb" secondAttribute="top" constant="34" id="Vl9-k4-mZm"/>
                                                            <constraint firstAttribute="bottom" secondItem="m34-6X-jXy" secondAttribute="bottom" id="ndS-Cn-nCv"/>
                                                        </constraints>
                                                    </view>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="n90-LX-6M2">
                                                        <rect key="frame" x="0.0" y="357" width="375" height="64"/>
                                                        <subviews>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="E7l-np-Lcd" customClass="RoundView" customModule="Beta_Cinemas" customModuleProvider="target">
                                                                <rect key="frame" x="8" y="0.0" width="110.5" height="64"/>
                                                                <subviews>
                                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="NVo-W7-IQ4" customClass="RoundButton" customModule="Beta_Cinemas" customModuleProvider="target">
                                                                        <rect key="frame" x="0.0" y="0.0" width="110.5" height="64"/>
                                                                        <fontDescription key="fontDescription" name="Oswald-Light" family="Oswald" pointSize="16"/>
                                                                        <inset key="contentEdgeInsets" minX="0.0" minY="0.0" maxX="0.0" maxY="20"/>
                                                                        <inset key="titleEdgeInsets" minX="-40" minY="45" maxX="0.0" maxY="0.0"/>
                                                                        <inset key="imageEdgeInsets" minX="33" minY="0.0" maxX="0.0" maxY="0.0"/>
                                                                        <state key="normal" title="Giá vé" image="icTicket">
                                                                            <color key="titleColor" red="0.0**********2352941" green="0.34901960784313724" blue="0.61568627450980395" alpha="0.85999999999999999" colorSpace="custom" customColorSpace="sRGB"/>
                                                                        </state>
                                                                        <userDefinedRuntimeAttributes>
                                                                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                                                <real key="value" value="8"/>
                                                                            </userDefinedRuntimeAttribute>
                                                                        </userDefinedRuntimeAttributes>
                                                                        <connections>
                                                                            <action selector="btTicketPressed:" destination="K0z-1E-gwO" eventType="touchUpInside" id="mMZ-3R-t2o"/>
                                                                        </connections>
                                                                    </button>
                                                                </subviews>
                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstItem="NVo-W7-IQ4" firstAttribute="leading" secondItem="E7l-np-Lcd" secondAttribute="leading" id="3RK-z9-QxK"/>
                                                                    <constraint firstAttribute="bottom" secondItem="NVo-W7-IQ4" secondAttribute="bottom" id="B5U-0i-bKl"/>
                                                                    <constraint firstItem="NVo-W7-IQ4" firstAttribute="top" secondItem="E7l-np-Lcd" secondAttribute="top" id="QgH-JC-k5Z"/>
                                                                    <constraint firstAttribute="trailing" secondItem="NVo-W7-IQ4" secondAttribute="trailing" id="dc5-e6-Wnw"/>
                                                                </constraints>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                                        <real key="value" value="8"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="point" keyPath="shadowOffset">
                                                                        <point key="value" x="0.0" y="40"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="color" keyPath="shadowColor">
                                                                        <color key="value" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="shadowRadius">
                                                                        <real key="value" value="8"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="shadowOpacity">
                                                                        <real key="value" value="0.10000000000000001"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                </userDefinedRuntimeAttributes>
                                                            </view>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="2L5-Xq-LB0" customClass="RoundView" customModule="Beta_Cinemas" customModuleProvider="target">
                                                                <rect key="frame" x="132.5" y="0.0" width="110" height="64"/>
                                                                <subviews>
                                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="M1w-8X-qnp" customClass="RoundButton" customModule="Beta_Cinemas" customModuleProvider="target">
                                                                        <rect key="frame" x="0.0" y="0.0" width="110" height="64"/>
                                                                        <fontDescription key="fontDescription" name="Oswald-Light" family="Oswald" pointSize="16"/>
                                                                        <inset key="contentEdgeInsets" minX="0.0" minY="0.0" maxX="0.0" maxY="20"/>
                                                                        <inset key="titleEdgeInsets" minX="-27" minY="45" maxX="0.0" maxY="0.0"/>
                                                                        <inset key="imageEdgeInsets" minX="40" minY="0.0" maxX="0.0" maxY="0.0"/>
                                                                        <state key="normal" title="Suất chiếu" image="icSesiontime">
                                                                            <color key="titleColor" red="0.0**********" green="0.34901960780000002" blue="0.61568627450000002" alpha="0.85999999999999999" colorSpace="custom" customColorSpace="sRGB"/>
                                                                        </state>
                                                                        <userDefinedRuntimeAttributes>
                                                                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                                                <real key="value" value="8"/>
                                                                            </userDefinedRuntimeAttribute>
                                                                        </userDefinedRuntimeAttributes>
                                                                        <connections>
                                                                            <action selector="btSessionPressed:" destination="K0z-1E-gwO" eventType="touchUpInside" id="uux-iO-gpM"/>
                                                                        </connections>
                                                                    </button>
                                                                </subviews>
                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstItem="M1w-8X-qnp" firstAttribute="leading" secondItem="2L5-Xq-LB0" secondAttribute="leading" id="86h-PW-N2m"/>
                                                                    <constraint firstItem="M1w-8X-qnp" firstAttribute="top" secondItem="2L5-Xq-LB0" secondAttribute="top" id="c8l-OR-pXT"/>
                                                                    <constraint firstAttribute="bottom" secondItem="M1w-8X-qnp" secondAttribute="bottom" id="vf6-UR-1Bz"/>
                                                                    <constraint firstAttribute="trailing" secondItem="M1w-8X-qnp" secondAttribute="trailing" id="xrJ-r5-fLN"/>
                                                                </constraints>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                                        <real key="value" value="8"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="point" keyPath="shadowOffset">
                                                                        <point key="value" x="0.0" y="40"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="color" keyPath="shadowColor">
                                                                        <color key="value" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="shadowRadius">
                                                                        <real key="value" value="8"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="shadowOpacity">
                                                                        <real key="value" value="0.10000000000000001"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                </userDefinedRuntimeAttributes>
                                                            </view>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="UTO-uS-6Xx" customClass="RoundView" customModule="Beta_Cinemas" customModuleProvider="target">
                                                                <rect key="frame" x="256.5" y="0.0" width="110.5" height="64"/>
                                                                <subviews>
                                                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="aOZ-xQ-DS3" customClass="RoundButton" customModule="Beta_Cinemas" customModuleProvider="target">
                                                                        <rect key="frame" x="0.0" y="0.0" width="110.5" height="64"/>
                                                                        <fontDescription key="fontDescription" name="Oswald-Light" family="Oswald" pointSize="16"/>
                                                                        <inset key="contentEdgeInsets" minX="0.0" minY="0.0" maxX="0.0" maxY="20"/>
                                                                        <inset key="titleEdgeInsets" minX="-24" minY="45" maxX="0.0" maxY="0.0"/>
                                                                        <inset key="imageEdgeInsets" minX="41" minY="0.0" maxX="0.0" maxY="0.0"/>
                                                                        <state key="normal" title="Gọi ngay" image="icCall">
                                                                            <color key="titleColor" red="0.0**********" green="0.34901960780000002" blue="0.61568627450000002" alpha="0.85999999999999999" colorSpace="custom" customColorSpace="sRGB"/>
                                                                        </state>
                                                                        <userDefinedRuntimeAttributes>
                                                                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                                                <real key="value" value="8"/>
                                                                            </userDefinedRuntimeAttribute>
                                                                        </userDefinedRuntimeAttributes>
                                                                        <connections>
                                                                            <action selector="btCallPressed:" destination="K0z-1E-gwO" eventType="touchUpInside" id="aYg-6s-25G"/>
                                                                        </connections>
                                                                    </button>
                                                                </subviews>
                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="bottom" secondItem="aOZ-xQ-DS3" secondAttribute="bottom" id="D8Q-cB-v3d"/>
                                                                    <constraint firstAttribute="trailing" secondItem="aOZ-xQ-DS3" secondAttribute="trailing" id="G5d-3g-eZy"/>
                                                                    <constraint firstItem="aOZ-xQ-DS3" firstAttribute="top" secondItem="UTO-uS-6Xx" secondAttribute="top" id="K5u-kK-rLA"/>
                                                                    <constraint firstItem="aOZ-xQ-DS3" firstAttribute="leading" secondItem="UTO-uS-6Xx" secondAttribute="leading" id="l1B-EZ-eWc"/>
                                                                </constraints>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                                        <real key="value" value="8"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="point" keyPath="shadowOffset">
                                                                        <point key="value" x="0.0" y="40"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="color" keyPath="shadowColor">
                                                                        <color key="value" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="shadowRadius">
                                                                        <real key="value" value="8"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="shadowOpacity">
                                                                        <real key="value" value="0.10000000000000001"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                </userDefinedRuntimeAttributes>
                                                            </view>
                                                        </subviews>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstItem="2L5-Xq-LB0" firstAttribute="top" secondItem="n90-LX-6M2" secondAttribute="top" id="5hv-od-7VD"/>
                                                            <constraint firstItem="E7l-np-Lcd" firstAttribute="top" secondItem="n90-LX-6M2" secondAttribute="top" id="7Ho-JN-cI2"/>
                                                            <constraint firstAttribute="bottom" secondItem="2L5-Xq-LB0" secondAttribute="bottom" id="AKx-BO-ZIs"/>
                                                            <constraint firstItem="2L5-Xq-LB0" firstAttribute="leading" secondItem="E7l-np-Lcd" secondAttribute="trailing" constant="14" id="BPt-oY-eoe"/>
                                                            <constraint firstItem="UTO-uS-6Xx" firstAttribute="top" secondItem="n90-LX-6M2" secondAttribute="top" id="Ecm-q2-Pef"/>
                                                            <constraint firstAttribute="trailing" secondItem="UTO-uS-6Xx" secondAttribute="trailing" constant="8" id="Fy4-nE-Llq"/>
                                                            <constraint firstItem="UTO-uS-6Xx" firstAttribute="width" secondItem="E7l-np-Lcd" secondAttribute="width" id="JwR-Tx-Gq3"/>
                                                            <constraint firstItem="UTO-uS-6Xx" firstAttribute="height" secondItem="E7l-np-Lcd" secondAttribute="height" id="PUL-uc-u8k"/>
                                                            <constraint firstItem="2L5-Xq-LB0" firstAttribute="width" secondItem="E7l-np-Lcd" secondAttribute="width" id="VlR-kh-IQo"/>
                                                            <constraint firstItem="E7l-np-Lcd" firstAttribute="leading" secondItem="n90-LX-6M2" secondAttribute="leading" constant="8" id="WZ0-kP-Avh"/>
                                                            <constraint firstAttribute="height" constant="64" id="dmO-6l-mFB"/>
                                                            <constraint firstItem="UTO-uS-6Xx" firstAttribute="leading" secondItem="2L5-Xq-LB0" secondAttribute="trailing" constant="14" id="tCA-eE-myc"/>
                                                            <constraint firstAttribute="bottom" secondItem="E7l-np-Lcd" secondAttribute="bottom" id="v05-aU-3h4"/>
                                                            <constraint firstItem="2L5-Xq-LB0" firstAttribute="height" secondItem="E7l-np-Lcd" secondAttribute="height" id="xuP-ZQ-lag"/>
                                                        </constraints>
                                                    </view>
                                                </subviews>
                                                <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="trailing" secondItem="u6X-nj-caq" secondAttribute="trailing" id="BYe-XR-K3s"/>
                                                    <constraint firstItem="ny0-4e-tKb" firstAttribute="top" secondItem="RCc-z5-a12" secondAttribute="bottom" constant="50" id="Ch3-R7-wSK"/>
                                                    <constraint firstAttribute="bottom" secondItem="ny0-4e-tKb" secondAttribute="bottom" id="EiG-d8-jIf"/>
                                                    <constraint firstItem="RCc-z5-a12" firstAttribute="leading" secondItem="hpE-XL-Xqk" secondAttribute="leading" constant="20" id="Hao-bj-SAe"/>
                                                    <constraint firstItem="n90-LX-6M2" firstAttribute="leading" secondItem="hpE-XL-Xqk" secondAttribute="leading" id="HnZ-rV-0m0"/>
                                                    <constraint firstItem="RCc-z5-a12" firstAttribute="top" secondItem="u6X-nj-caq" secondAttribute="bottom" constant="12" id="Jnf-Ax-PlD"/>
                                                    <constraint firstItem="u6X-nj-caq" firstAttribute="top" secondItem="hpE-XL-Xqk" secondAttribute="top" id="XEW-Fb-I3j"/>
                                                    <constraint firstAttribute="trailing" secondItem="ny0-4e-tKb" secondAttribute="trailing" id="YW5-kL-Lgl"/>
                                                    <constraint firstAttribute="trailing" secondItem="n90-LX-6M2" secondAttribute="trailing" id="dbU-GM-w3N"/>
                                                    <constraint firstItem="RCc-z5-a12" firstAttribute="centerX" secondItem="hpE-XL-Xqk" secondAttribute="centerX" id="khq-Hl-JwA"/>
                                                    <constraint firstItem="ny0-4e-tKb" firstAttribute="leading" secondItem="hpE-XL-Xqk" secondAttribute="leading" id="pOg-bK-2GD"/>
                                                    <constraint firstItem="n90-LX-6M2" firstAttribute="top" secondItem="u6X-nj-caq" secondAttribute="bottom" constant="65" id="qCI-SG-xbx"/>
                                                    <constraint firstItem="u6X-nj-caq" firstAttribute="leading" secondItem="hpE-XL-Xqk" secondAttribute="leading" id="u26-jm-ryd"/>
                                                </constraints>
                                            </view>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="6La-ys-irl" customClass="NewAndDealsView" customModule="Beta_Cinemas" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="800" width="375" height="128"/>
                                                <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="calibratedRGB"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="128" id="jfi-Q8-hXE"/>
                                                </constraints>
                                            </view>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="l0G-9B-AQV" secondAttribute="trailing" id="2Vu-On-h0D"/>
                                    <constraint firstItem="l0G-9B-AQV" firstAttribute="leading" secondItem="K8Y-ff-61L" secondAttribute="leading" id="4mL-0T-hMZ"/>
                                    <constraint firstItem="l0G-9B-AQV" firstAttribute="width" secondItem="K8Y-ff-61L" secondAttribute="width" id="S8l-bd-8jY"/>
                                    <constraint firstAttribute="bottom" secondItem="l0G-9B-AQV" secondAttribute="bottom" id="mKi-03-UbS"/>
                                    <constraint firstItem="l0G-9B-AQV" firstAttribute="top" secondItem="K8Y-ff-61L" secondAttribute="top" id="skQ-ju-3sh"/>
                                </constraints>
                            </scrollView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="CgD-JY-lhc"/>
                        <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstItem="K8Y-ff-61L" firstAttribute="leading" secondItem="CgD-JY-lhc" secondAttribute="leading" id="CYz-fT-DFY"/>
                            <constraint firstItem="K8Y-ff-61L" firstAttribute="trailing" secondItem="CgD-JY-lhc" secondAttribute="trailing" id="D8V-xk-BO2"/>
                            <constraint firstItem="CgD-JY-lhc" firstAttribute="bottom" secondItem="K8Y-ff-61L" secondAttribute="bottom" id="cKB-fn-YQ9"/>
                            <constraint firstItem="K8Y-ff-61L" firstAttribute="top" secondItem="CgD-JY-lhc" secondAttribute="top" id="wEX-tI-G2z"/>
                        </constraints>
                    </view>
                    <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
                    <size key="freeformSize" width="375" height="1000"/>
                    <connections>
                        <outlet property="btCallPhone" destination="aOZ-xQ-DS3" id="45O-tJ-2Wo"/>
                        <outlet property="btSessionTime" destination="M1w-8X-qnp" id="U6v-H6-Gxf"/>
                        <outlet property="btTicketPrice" destination="NVo-W7-IQ4" id="5dO-YQ-eP0"/>
                        <outlet property="ivCinemaLogo" destination="u6X-nj-caq" id="QG4-NS-hVG"/>
                        <outlet property="lbAddress" destination="BfJ-lB-Gxl" id="Yc6-rs-zEO"/>
                        <outlet property="lbCinemaName" destination="RCc-z5-a12" id="aYW-4v-Ulv"/>
                        <outlet property="mapView" destination="sCs-4x-2bD" id="9NR-vh-1dJ"/>
                        <outlet property="scrollView" destination="K8Y-ff-61L" id="zOz-eR-4ba"/>
                        <outlet property="vPromotion" destination="6La-ys-irl" id="1y8-Q7-pe1"/>
                        <outlet property="vPromotionHeight" destination="jfi-Q8-hXE" id="nVN-Gy-F0X"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="p8F-ky-DSN" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1152.8" y="156.52173913043478"/>
        </scene>
        <!--Cinema Price View Controller-->
        <scene sceneID="zlp-95-SNT">
            <objects>
                <viewController storyboardIdentifier="CinemaPriceViewController" modalPresentationStyle="overFullScreen" id="tzr-tg-BOM" customClass="CinemaPriceViewController" customModule="Beta_Cinemas" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="uSd-b6-eKA">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="o75-Tb-rMw" customClass="RoundView" customModule="Beta_Cinemas" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="20" width="375" height="647"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="BGd-dP-Fm1">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="52"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Giá vé" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="edm-U7-PgW">
                                                <rect key="frame" x="16" y="11" width="45" height="30"/>
                                                <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="20"/>
                                                <nil key="textColor"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="gpQ-tN-00l">
                                                <rect key="frame" x="0.0" y="51" width="375" height="1"/>
                                                <color key="backgroundColor" red="0.66666666666666663" green="0.66666666666666663" blue="0.66666666666666663" alpha="1" colorSpace="calibratedRGB"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="1" id="64C-Uo-Upf"/>
                                                </constraints>
                                            </view>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="e5r-4M-rq9">
                                                <rect key="frame" x="335" y="14" width="24" height="24"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="24" id="WEU-K7-O97"/>
                                                    <constraint firstAttribute="height" constant="24" id="oXc-fW-Qye"/>
                                                </constraints>
                                                <state key="normal" image="icClose"/>
                                                <connections>
                                                    <action selector="closeButtonPressed:" destination="tzr-tg-BOM" eventType="touchUpInside" id="egs-He-Yl0"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstItem="edm-U7-PgW" firstAttribute="centerY" secondItem="BGd-dP-Fm1" secondAttribute="centerY" id="42g-So-F9F"/>
                                            <constraint firstAttribute="bottom" secondItem="gpQ-tN-00l" secondAttribute="bottom" id="HFe-S7-FmV"/>
                                            <constraint firstItem="edm-U7-PgW" firstAttribute="leading" secondItem="BGd-dP-Fm1" secondAttribute="leading" constant="16" id="Ile-I5-2m7"/>
                                            <constraint firstItem="e5r-4M-rq9" firstAttribute="centerY" secondItem="BGd-dP-Fm1" secondAttribute="centerY" id="LQE-QZ-HWB"/>
                                            <constraint firstAttribute="trailing" secondItem="gpQ-tN-00l" secondAttribute="trailing" id="OBY-Yk-Ycs"/>
                                            <constraint firstAttribute="height" constant="52" id="oM3-G2-Ln0"/>
                                            <constraint firstItem="gpQ-tN-00l" firstAttribute="leading" secondItem="BGd-dP-Fm1" secondAttribute="leading" id="y7T-ig-j6q"/>
                                            <constraint firstAttribute="trailing" secondItem="e5r-4M-rq9" secondAttribute="trailing" constant="16" id="zem-W5-fgL"/>
                                        </constraints>
                                    </view>
                                    <tableView hidden="YES" clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="default" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="JjQ-QY-0LT">
                                        <rect key="frame" x="0.0" y="52" width="375" height="595"/>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <view key="tableHeaderView" contentMode="scaleToFill" id="NcH-5o-NND">
                                            <rect key="frame" x="0.0" y="0.0" width="375" height="71"/>
                                            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                            <subviews>
                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Wlr-pZ-c7a" customClass="RoundView" customModule="Beta_Cinemas" customModuleProvider="target">
                                                    <rect key="frame" x="16" y="20" width="343" height="51"/>
                                                    <subviews>
                                                        <button opaque="NO" contentMode="scaleToFill" selected="YES" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="gz7-ja-2YG">
                                                            <rect key="frame" x="0.0" y="0.0" width="171.5" height="51"/>
                                                            <color key="backgroundColor" red="0.0**********2352941" green="0.34901960784313724" blue="0.61568627450980395" alpha="1" colorSpace="calibratedRGB"/>
                                                            <fontDescription key="fontDescription" name="Oswald-Bold" family="Oswald" pointSize="16"/>
                                                            <state key="normal" title="GIÁ VÉ PHIM 2D">
                                                                <color key="titleColor" red="0.28627450980392155" green="0.29803921568627451" blue="0.38431372549019605" alpha="1" colorSpace="calibratedRGB"/>
                                                            </state>
                                                            <state key="selected">
                                                                <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            </state>
                                                            <connections>
                                                                <action selector="price2DPressed:" destination="tzr-tg-BOM" eventType="touchUpInside" id="r3i-t0-HKR"/>
                                                            </connections>
                                                        </button>
                                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="DXF-PP-mK2">
                                                            <rect key="frame" x="171.5" y="0.0" width="171.5" height="51"/>
                                                            <fontDescription key="fontDescription" name="Oswald-Bold" family="Oswald" pointSize="16"/>
                                                            <state key="normal" title="GIÁ VÉ PHIM 3D">
                                                                <color key="titleColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="calibratedRGB"/>
                                                            </state>
                                                            <state key="selected">
                                                                <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            </state>
                                                            <connections>
                                                                <action selector="price3DPressed:" destination="tzr-tg-BOM" eventType="touchUpInside" id="qF6-jo-6ur"/>
                                                            </connections>
                                                        </button>
                                                    </subviews>
                                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <constraints>
                                                        <constraint firstItem="gz7-ja-2YG" firstAttribute="leading" secondItem="Wlr-pZ-c7a" secondAttribute="leading" id="8QV-pb-T5H"/>
                                                        <constraint firstItem="DXF-PP-mK2" firstAttribute="leading" secondItem="gz7-ja-2YG" secondAttribute="trailing" id="DH7-bj-1D0"/>
                                                        <constraint firstAttribute="bottom" secondItem="DXF-PP-mK2" secondAttribute="bottom" id="KOz-EJ-i4L"/>
                                                        <constraint firstItem="gz7-ja-2YG" firstAttribute="top" secondItem="Wlr-pZ-c7a" secondAttribute="top" id="cQO-s0-pzg"/>
                                                        <constraint firstItem="DXF-PP-mK2" firstAttribute="top" secondItem="Wlr-pZ-c7a" secondAttribute="top" id="eUr-UM-hBE"/>
                                                        <constraint firstItem="DXF-PP-mK2" firstAttribute="width" secondItem="gz7-ja-2YG" secondAttribute="width" id="i1b-av-P28"/>
                                                        <constraint firstAttribute="trailing" secondItem="DXF-PP-mK2" secondAttribute="trailing" id="m1l-tV-7T5"/>
                                                        <constraint firstAttribute="bottom" secondItem="gz7-ja-2YG" secondAttribute="bottom" id="wB2-RF-nSR"/>
                                                    </constraints>
                                                    <userDefinedRuntimeAttributes>
                                                        <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                                            <real key="value" value="1"/>
                                                        </userDefinedRuntimeAttribute>
                                                        <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                                            <color key="value" red="0.59215686274509804" green="0.59215686274509804" blue="0.59215686274509804" alpha="1" colorSpace="calibratedRGB"/>
                                                        </userDefinedRuntimeAttribute>
                                                    </userDefinedRuntimeAttributes>
                                                </view>
                                            </subviews>
                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            <constraints>
                                                <constraint firstItem="Wlr-pZ-c7a" firstAttribute="leading" secondItem="NcH-5o-NND" secondAttribute="leading" constant="16" id="Fff-QN-fqB"/>
                                                <constraint firstItem="Wlr-pZ-c7a" firstAttribute="top" secondItem="NcH-5o-NND" secondAttribute="top" constant="20" id="OSK-i0-V1c"/>
                                                <constraint firstAttribute="bottom" secondItem="Wlr-pZ-c7a" secondAttribute="bottom" id="UNp-87-aAh"/>
                                                <constraint firstAttribute="trailing" secondItem="Wlr-pZ-c7a" secondAttribute="trailing" constant="16" id="fZb-Kb-UCE"/>
                                            </constraints>
                                        </view>
                                        <prototypes>
                                            <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" reuseIdentifier="TableViewCell" rowHeight="136" id="CmP-Vs-dIV">
                                                <rect key="frame" x="0.0" y="121" width="375" height="136"/>
                                                <autoresizingMask key="autoresizingMask"/>
                                                <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="CmP-Vs-dIV" id="7tm-6u-8oU">
                                                    <rect key="frame" x="0.0" y="0.0" width="375" height="136"/>
                                                    <autoresizingMask key="autoresizingMask"/>
                                                    <subviews>
                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="sWp-no-9qo">
                                                            <rect key="frame" x="16" y="0.0" width="343" height="136"/>
                                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        </view>
                                                    </subviews>
                                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <constraints>
                                                        <constraint firstAttribute="trailing" secondItem="sWp-no-9qo" secondAttribute="trailing" constant="16" id="5Fr-Vm-QYT"/>
                                                        <constraint firstAttribute="bottom" secondItem="sWp-no-9qo" secondAttribute="bottom" id="Ki6-Jt-pB0"/>
                                                        <constraint firstItem="sWp-no-9qo" firstAttribute="leading" secondItem="7tm-6u-8oU" secondAttribute="leading" constant="16" id="ifM-uT-QHs"/>
                                                        <constraint firstItem="sWp-no-9qo" firstAttribute="top" secondItem="7tm-6u-8oU" secondAttribute="top" id="wdc-S5-TrA"/>
                                                    </constraints>
                                                </tableViewCellContentView>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            </tableViewCell>
                                            <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" reuseIdentifier="TableViewCell2" rowHeight="117" id="Ewy-B2-fXF">
                                                <rect key="frame" x="0.0" y="257" width="375" height="117"/>
                                                <autoresizingMask key="autoresizingMask"/>
                                                <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="Ewy-B2-fXF" id="qpE-l1-rkv">
                                                    <rect key="frame" x="0.0" y="0.0" width="375" height="117"/>
                                                    <autoresizingMask key="autoresizingMask"/>
                                                    <subviews>
                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="y1d-bu-hi7" customClass="RoundView" customModule="Beta_Cinemas" customModuleProvider="target">
                                                            <rect key="frame" x="16" y="0.0" width="343" height="117"/>
                                                            <subviews>
                                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="xjA-WP-sHd">
                                                                    <rect key="frame" x="1" y="0.0" width="341" height="117"/>
                                                                    <subviews>
                                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="65.000" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="JYc-Yb-xnf">
                                                                            <rect key="frame" x="20" y="10" width="301" height="97"/>
                                                                            <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="20"/>
                                                                            <color key="textColor" red="0.0**********" green="0.34901960780000002" blue="0.61568627450000002" alpha="1" colorSpace="calibratedRGB"/>
                                                                            <nil key="highlightedColor"/>
                                                                        </label>
                                                                    </subviews>
                                                                    <color key="backgroundColor" red="0.97254901960784312" green="0.97254901960784312" blue="0.97254901960784312" alpha="1" colorSpace="calibratedRGB"/>
                                                                    <constraints>
                                                                        <constraint firstAttribute="bottom" secondItem="JYc-Yb-xnf" secondAttribute="bottom" constant="10" id="3Qs-tD-w5e"/>
                                                                        <constraint firstItem="JYc-Yb-xnf" firstAttribute="leading" secondItem="xjA-WP-sHd" secondAttribute="leading" constant="20" id="a0b-tv-La7"/>
                                                                        <constraint firstAttribute="trailing" secondItem="JYc-Yb-xnf" secondAttribute="trailing" constant="20" id="a1v-V0-Atc"/>
                                                                        <constraint firstItem="JYc-Yb-xnf" firstAttribute="top" secondItem="xjA-WP-sHd" secondAttribute="top" constant="10" id="oHp-gN-nqO"/>
                                                                    </constraints>
                                                                </view>
                                                            </subviews>
                                                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            <constraints>
                                                                <constraint firstAttribute="trailing" secondItem="xjA-WP-sHd" secondAttribute="trailing" constant="1" id="Jpm-9U-BQ5"/>
                                                                <constraint firstItem="xjA-WP-sHd" firstAttribute="top" secondItem="y1d-bu-hi7" secondAttribute="top" id="ZA2-11-HaJ"/>
                                                                <constraint firstAttribute="bottom" secondItem="xjA-WP-sHd" secondAttribute="bottom" id="g1S-YU-WdE"/>
                                                                <constraint firstItem="xjA-WP-sHd" firstAttribute="leading" secondItem="y1d-bu-hi7" secondAttribute="leading" constant="1" id="hYh-RX-rvN"/>
                                                            </constraints>
                                                            <userDefinedRuntimeAttributes>
                                                                <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                                                    <color key="value" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="calibratedRGB"/>
                                                                </userDefinedRuntimeAttribute>
                                                                <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                                                    <real key="value" value="1"/>
                                                                </userDefinedRuntimeAttribute>
                                                            </userDefinedRuntimeAttributes>
                                                        </view>
                                                    </subviews>
                                                    <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <constraints>
                                                        <constraint firstAttribute="trailing" secondItem="y1d-bu-hi7" secondAttribute="trailing" constant="16" id="JQw-DR-bnv"/>
                                                        <constraint firstAttribute="bottom" secondItem="y1d-bu-hi7" secondAttribute="bottom" id="kdG-tU-hnb"/>
                                                        <constraint firstItem="y1d-bu-hi7" firstAttribute="top" secondItem="qpE-l1-rkv" secondAttribute="top" id="usl-Pe-wg4"/>
                                                        <constraint firstItem="y1d-bu-hi7" firstAttribute="leading" secondItem="qpE-l1-rkv" secondAttribute="leading" constant="16" id="vDs-vP-Agz"/>
                                                    </constraints>
                                                </tableViewCellContentView>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            </tableViewCell>
                                        </prototypes>
                                        <connections>
                                            <outlet property="delegate" destination="tzr-tg-BOM" id="8m1-97-QOy"/>
                                        </connections>
                                    </tableView>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="83E-jx-ygK">
                                        <rect key="frame" x="0.0" y="52" width="375" height="595"/>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" red="0.97254901960784312" green="0.97254901960784312" blue="0.97254901960784312" alpha="1" colorSpace="calibratedRGB"/>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="JjQ-QY-0LT" secondAttribute="trailing" id="1Vj-9E-Q3G"/>
                                    <constraint firstAttribute="bottom" secondItem="83E-jx-ygK" secondAttribute="bottom" id="4Ir-nA-Cho"/>
                                    <constraint firstItem="BGd-dP-Fm1" firstAttribute="top" secondItem="o75-Tb-rMw" secondAttribute="top" id="5NI-V7-OBU"/>
                                    <constraint firstItem="83E-jx-ygK" firstAttribute="top" secondItem="BGd-dP-Fm1" secondAttribute="bottom" id="6kA-SZ-Jba"/>
                                    <constraint firstAttribute="bottom" secondItem="JjQ-QY-0LT" secondAttribute="bottom" id="7eg-e1-cal"/>
                                    <constraint firstAttribute="trailing" secondItem="83E-jx-ygK" secondAttribute="trailing" id="8dA-6M-5I5"/>
                                    <constraint firstItem="83E-jx-ygK" firstAttribute="leading" secondItem="o75-Tb-rMw" secondAttribute="leading" id="AQX-5T-2wC"/>
                                    <constraint firstAttribute="trailing" secondItem="BGd-dP-Fm1" secondAttribute="trailing" id="Wyc-0R-ns7"/>
                                    <constraint firstItem="BGd-dP-Fm1" firstAttribute="leading" secondItem="o75-Tb-rMw" secondAttribute="leading" id="e4e-mf-vAa"/>
                                    <constraint firstItem="JjQ-QY-0LT" firstAttribute="top" secondItem="BGd-dP-Fm1" secondAttribute="bottom" id="f1Y-QA-Wo3"/>
                                    <constraint firstItem="JjQ-QY-0LT" firstAttribute="leading" secondItem="o75-Tb-rMw" secondAttribute="leading" id="fKR-0k-CPE"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="Gg0-Fp-p0X"/>
                        <color key="backgroundColor" white="0.0" alpha="0.5" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="o75-Tb-rMw" firstAttribute="top" secondItem="Gg0-Fp-p0X" secondAttribute="top" id="Z3Y-Sq-JTu"/>
                            <constraint firstItem="Gg0-Fp-p0X" firstAttribute="trailing" secondItem="o75-Tb-rMw" secondAttribute="trailing" id="aYl-q4-fYL"/>
                            <constraint firstItem="Gg0-Fp-p0X" firstAttribute="bottom" secondItem="o75-Tb-rMw" secondAttribute="bottom" id="s4N-1x-Dsc"/>
                            <constraint firstItem="o75-Tb-rMw" firstAttribute="leading" secondItem="Gg0-Fp-p0X" secondAttribute="leading" id="wWW-aY-k0b"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="btPrice2D" destination="gz7-ja-2YG" id="277-uw-8Lq"/>
                        <outlet property="btPrice3D" destination="DXF-PP-mK2" id="t87-Oa-m7l"/>
                        <outlet property="mainView" destination="83E-jx-ygK" id="XMZ-mh-QDf"/>
                        <outlet property="tableView" destination="JjQ-QY-0LT" id="Kbt-oJ-0vZ"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="zGt-cr-S9x" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="480.80000000000001" y="902.69865067466276"/>
        </scene>
        <!--Select Region View Controller-->
        <scene sceneID="8q3-08-hTc">
            <objects>
                <viewController storyboardIdentifier="SelectRegionViewController" id="HxT-Ng-Rbh" customClass="SelectRegionViewController" customModule="Beta_Cinemas" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Bch-k2-ANO">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="qlU-g7-DZm">
                                <rect key="frame" x="0.0" y="20" width="375" height="647"/>
                                <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <connections>
                                    <outlet property="delegate" destination="HxT-Ng-Rbh" id="dgG-os-wyQ"/>
                                </connections>
                            </tableView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="Jpv-FK-Flh"/>
                        <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstItem="qlU-g7-DZm" firstAttribute="leading" secondItem="Jpv-FK-Flh" secondAttribute="leading" id="Ufk-Qt-lc5"/>
                            <constraint firstItem="Jpv-FK-Flh" firstAttribute="trailing" secondItem="qlU-g7-DZm" secondAttribute="trailing" id="gmF-nl-mKc"/>
                            <constraint firstItem="Jpv-FK-Flh" firstAttribute="bottom" secondItem="qlU-g7-DZm" secondAttribute="bottom" id="oOI-JA-Lgi"/>
                            <constraint firstItem="qlU-g7-DZm" firstAttribute="top" secondItem="Jpv-FK-Flh" secondAttribute="top" id="woQ-NQ-er6"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="tableView" destination="qlU-g7-DZm" id="bCU-nh-LMK"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="hpa-qM-4DU" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1153" y="978"/>
        </scene>
        <!--Confirm Book Age View Controller-->
        <scene sceneID="VOI-Li-LN3">
            <objects>
                <viewController storyboardIdentifier="ConfirmBookAgeViewController" id="dwc-gX-V1I" customClass="ConfirmBookAgeViewController" customModule="Beta_Cinemas" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="Bt7-fF-Sfh">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="justified" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="MNp-g1-4xa">
                                <rect key="frame" x="20" y="40" width="335" height="607"/>
                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="7Tl-wU-bSC"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="MNp-g1-4xa" firstAttribute="top" secondItem="7Tl-wU-bSC" secondAttribute="top" constant="20" id="3Ms-uT-69M"/>
                            <constraint firstItem="MNp-g1-4xa" firstAttribute="leading" secondItem="7Tl-wU-bSC" secondAttribute="leading" constant="20" id="CSJ-gE-apI"/>
                            <constraint firstItem="7Tl-wU-bSC" firstAttribute="trailing" secondItem="MNp-g1-4xa" secondAttribute="trailing" constant="20" id="UKa-q9-D0G"/>
                            <constraint firstItem="7Tl-wU-bSC" firstAttribute="bottom" secondItem="MNp-g1-4xa" secondAttribute="bottom" constant="20" id="y91-Lt-mbw"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="lbMessage" destination="MNp-g1-4xa" id="YdU-Vy-CqA"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="ZU5-rL-usD" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1885.5999999999999" y="782.15892053973016"/>
        </scene>
        <!--Confirm Vip Zoom View Controller-->
        <scene sceneID="DIk-1H-z17">
            <objects>
                <viewController storyboardIdentifier="ConfirmVipZoomViewController" id="bH0-px-I7B" customClass="ConfirmVipZoomViewController" customModule="Beta_Cinemas" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="n20-9K-AKl">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="xh2-tA-B0x">
                                <rect key="frame" x="20" y="40" width="335" height="607"/>
                                <subviews>
                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="dome_zoom" translatesAutoresizingMaskIntoConstraints="NO" id="VXs-4h-LTj">
                                        <rect key="frame" x="0.0" y="0.0" width="335" height="223.5"/>
                                        <constraints>
                                            <constraint firstAttribute="width" secondItem="VXs-4h-LTj" secondAttribute="height" multiplier="300:200" id="65o-Vw-ev2"/>
                                        </constraints>
                                    </imageView>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" alignment="top" spacing="8" translatesAutoresizingMaskIntoConstraints="NO" id="ltO-xh-I2b">
                                        <rect key="frame" x="0.0" y="239.5" width="335" height="367.5"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="252" text="BẠN ĐANG ĐẶT VỀ TẠI PHÒNG CHILLAX." textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="fPk-ck-dwV">
                                                <rect key="frame" x="0.0" y="0.0" width="335" height="20.5"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="17"/>
                                                <color key="textColor" red="0.1725490093" green="0.30196079609999998" blue="0.50588238240000005" alpha="1" colorSpace="custom" customColorSpace="displayP3"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Nâng tầm trải nghiệm với ghế ngồi êm ái, âm thanh sống động cùng máy chiếu laser sắc nét." textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="jsZ-nh-5Et">
                                                <rect key="frame" x="0.0" y="28.5" width="335" height="339"/>
                                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                <color key="textColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="fPk-ck-dwV" secondAttribute="trailing" id="2rP-ik-zgT"/>
                                            <constraint firstItem="jsZ-nh-5Et" firstAttribute="leading" secondItem="ltO-xh-I2b" secondAttribute="leading" id="4NY-B2-2FU"/>
                                            <constraint firstAttribute="trailing" secondItem="jsZ-nh-5Et" secondAttribute="trailing" id="IpS-66-nSy"/>
                                            <constraint firstItem="fPk-ck-dwV" firstAttribute="leading" secondItem="ltO-xh-I2b" secondAttribute="leading" id="iz8-3U-dGE"/>
                                        </constraints>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                                <constraints>
                                    <constraint firstItem="ltO-xh-I2b" firstAttribute="top" secondItem="VXs-4h-LTj" secondAttribute="bottom" constant="16" id="3XR-cd-6Jf"/>
                                    <constraint firstAttribute="bottom" secondItem="ltO-xh-I2b" secondAttribute="bottom" id="Su3-0c-Gq0"/>
                                    <constraint firstAttribute="trailing" secondItem="VXs-4h-LTj" secondAttribute="trailing" id="dmc-Bl-44A"/>
                                    <constraint firstItem="ltO-xh-I2b" firstAttribute="leading" secondItem="xh2-tA-B0x" secondAttribute="leading" id="mrR-DR-g4k"/>
                                    <constraint firstAttribute="trailing" secondItem="ltO-xh-I2b" secondAttribute="trailing" id="oPb-NT-Gpu"/>
                                    <constraint firstItem="VXs-4h-LTj" firstAttribute="top" secondItem="xh2-tA-B0x" secondAttribute="top" id="t4O-HL-fQ9"/>
                                    <constraint firstItem="VXs-4h-LTj" firstAttribute="leading" secondItem="xh2-tA-B0x" secondAttribute="leading" id="wZZ-8X-CN2"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="dqB-Mh-2sv"/>
                        <color key="backgroundColor" systemColor="systemBackgroundColor"/>
                        <constraints>
                            <constraint firstItem="dqB-Mh-2sv" firstAttribute="trailing" secondItem="xh2-tA-B0x" secondAttribute="trailing" constant="20" id="1aW-yO-8nb"/>
                            <constraint firstItem="dqB-Mh-2sv" firstAttribute="bottom" secondItem="xh2-tA-B0x" secondAttribute="bottom" constant="20" id="3l9-WH-88I"/>
                            <constraint firstItem="xh2-tA-B0x" firstAttribute="leading" secondItem="dqB-Mh-2sv" secondAttribute="leading" constant="20" id="Cpq-BX-qFt"/>
                            <constraint firstItem="xh2-tA-B0x" firstAttribute="top" secondItem="dqB-Mh-2sv" secondAttribute="top" constant="20" id="jhD-Ew-pum"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="messageZoomLabel" destination="jsZ-nh-5Et" id="851-2f-wUb"/>
                        <outlet property="titleZoomLabel" destination="fPk-ck-dwV" id="oZu-oh-Q53"/>
                        <outlet property="zoomImage" destination="VXs-4h-LTj" id="ZCw-Rc-xqR"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="bkB-jF-h0v" userLabel="First Responder" customClass="UIResponder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="2559" y="782"/>
        </scene>
        <!--Choose Seat View Controller-->
        <scene sceneID="wXD-5D-xaG">
            <objects>
                <viewController storyboardIdentifier="ChooseSeatViewController" id="op1-6U-V9F" customClass="ChooseSeatViewController" customModule="Beta_Cinemas" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="4Mh-L3-PDo">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="667"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" minimumZoomScale="0.25" maximumZoomScale="2" translatesAutoresizingMaskIntoConstraints="NO" id="8pS-PJ-Yeh">
                                <rect key="frame" x="0.0" y="20" width="375" height="541"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="d8D-du-weY">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="812.5"/>
                                        <subviews>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Xz5-sO-KSH">
                                                <rect key="frame" x="0.0" y="0.0" width="375" height="150"/>
                                                <subviews>
                                                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="OHd-NA-UkY">
                                                        <rect key="frame" x="0.0" y="0.0" width="375" height="150"/>
                                                        <constraints>
                                                            <constraint firstAttribute="width" secondItem="OHd-NA-UkY" secondAttribute="height" multiplier="375:150" id="ydV-1f-IF2"/>
                                                        </constraints>
                                                    </imageView>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="8dl-Tf-wxx" customClass="GradientView" customModule="Beta_Cinemas" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="0.0" width="375" height="150"/>
                                                        <subviews>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="SkB-u0-Zav">
                                                                <rect key="frame" x="20" y="47" width="335" height="56.5"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Pacific Rim: Trỗi Dậy" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Dav-Si-36w">
                                                                        <rect key="frame" x="0.0" y="0.0" width="335" height="36"/>
                                                                        <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="24"/>
                                                                        <color key="textColor" red="0.**********" green="0.**********" blue="0.15686274510000001" alpha="0.85999999999999999" colorSpace="custom" customColorSpace="sRGB"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="2D - LT  |  Võ thuật, Viễn Tưởng  |  135 phút " textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="I5Z-q4-Iwa">
                                                                        <rect key="frame" x="0.0" y="36" width="335" height="20.5"/>
                                                                        <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                                        <color key="textColor" red="0.**********" green="0.**********" blue="0.15686274510000001" alpha="0.85999999999999999" colorSpace="custom" customColorSpace="sRGB"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstItem="I5Z-q4-Iwa" firstAttribute="top" secondItem="Dav-Si-36w" secondAttribute="bottom" id="9a4-Le-LgG"/>
                                                                    <constraint firstItem="Dav-Si-36w" firstAttribute="leading" secondItem="SkB-u0-Zav" secondAttribute="leading" id="HOP-Zo-Qpi"/>
                                                                    <constraint firstAttribute="bottom" secondItem="I5Z-q4-Iwa" secondAttribute="bottom" id="OJv-SY-rGz"/>
                                                                    <constraint firstItem="I5Z-q4-Iwa" firstAttribute="leading" secondItem="SkB-u0-Zav" secondAttribute="leading" id="U1k-6A-oDD"/>
                                                                    <constraint firstAttribute="trailing" secondItem="Dav-Si-36w" secondAttribute="trailing" id="boh-vY-2bz"/>
                                                                    <constraint firstItem="Dav-Si-36w" firstAttribute="top" secondItem="SkB-u0-Zav" secondAttribute="top" id="mc5-KU-gJR"/>
                                                                    <constraint firstAttribute="trailing" secondItem="I5Z-q4-Iwa" secondAttribute="trailing" id="toZ-Tg-WNE"/>
                                                                </constraints>
                                                            </view>
                                                        </subviews>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstItem="SkB-u0-Zav" firstAttribute="centerX" secondItem="8dl-Tf-wxx" secondAttribute="centerX" id="G92-2M-PN6"/>
                                                            <constraint firstItem="SkB-u0-Zav" firstAttribute="leading" secondItem="8dl-Tf-wxx" secondAttribute="leading" constant="20" id="XoI-WQ-7Tk"/>
                                                            <constraint firstItem="SkB-u0-Zav" firstAttribute="centerY" secondItem="8dl-Tf-wxx" secondAttribute="centerY" id="xu2-V3-tVH"/>
                                                        </constraints>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="point" keyPath="startPoint">
                                                                <point key="value" x="0.5" y="0.0"/>
                                                            </userDefinedRuntimeAttribute>
                                                            <userDefinedRuntimeAttribute type="point" keyPath="endPoint">
                                                                <point key="value" x="0.5" y="1"/>
                                                            </userDefinedRuntimeAttribute>
                                                        </userDefinedRuntimeAttributes>
                                                    </view>
                                                </subviews>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="trailing" secondItem="OHd-NA-UkY" secondAttribute="trailing" id="23S-UN-OAS"/>
                                                    <constraint firstAttribute="bottom" secondItem="8dl-Tf-wxx" secondAttribute="bottom" id="6qO-Fq-w6P"/>
                                                    <constraint firstAttribute="bottom" secondItem="OHd-NA-UkY" secondAttribute="bottom" id="JDV-Tf-YqA"/>
                                                    <constraint firstItem="8dl-Tf-wxx" firstAttribute="leading" secondItem="Xz5-sO-KSH" secondAttribute="leading" id="LYF-ok-LhP"/>
                                                    <constraint firstItem="OHd-NA-UkY" firstAttribute="leading" secondItem="Xz5-sO-KSH" secondAttribute="leading" id="lhy-ue-xxh"/>
                                                    <constraint firstItem="OHd-NA-UkY" firstAttribute="top" secondItem="Xz5-sO-KSH" secondAttribute="top" id="ozV-dd-JAz"/>
                                                    <constraint firstItem="8dl-Tf-wxx" firstAttribute="top" secondItem="Xz5-sO-KSH" secondAttribute="top" id="sbi-hd-JQw"/>
                                                    <constraint firstAttribute="trailing" secondItem="8dl-Tf-wxx" secondAttribute="trailing" id="tED-0A-1kj"/>
                                                </constraints>
                                            </view>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="W1q-oh-NkL">
                                                <rect key="frame" x="0.0" y="150" width="375" height="361.5"/>
                                                <subviews>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="1OU-Qs-ts7">
                                                        <rect key="frame" x="0.0" y="0.0" width="187.5" height="361.5"/>
                                                        <subviews>
                                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="FYQ-r2-aLo">
                                                                <rect key="frame" x="0.0" y="0.0" width="187.5" height="361.5"/>
                                                                <subviews>
                                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="2gj-xj-FHC">
                                                                        <rect key="frame" x="0.0" y="0.0" width="187.5" height="50"/>
                                                                        <subviews>
                                                                            <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_empty_vip_seat" translatesAutoresizingMaskIntoConstraints="NO" id="4Ym-A1-Ztf">
                                                                                <rect key="frame" x="24" y="8" width="24" height="24"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="width" constant="24" id="5EX-J9-491"/>
                                                                                    <constraint firstAttribute="height" constant="24" id="965-vj-TIq"/>
                                                                                </constraints>
                                                                            </imageView>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Ghế trống" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="JLG-Gv-aLX" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                                                                <rect key="frame" x="59" y="8" width="120.5" height="27"/>
                                                                                <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                                                <color key="textColor" red="0.**********" green="0.**********" blue="0.15686274510000001" alpha="1" colorSpace="calibratedRGB"/>
                                                                                <nil key="highlightedColor"/>
                                                                                <userDefinedRuntimeAttributes>
                                                                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Seat.Empty"/>
                                                                                </userDefinedRuntimeAttributes>
                                                                            </label>
                                                                        </subviews>
                                                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <constraints>
                                                                            <constraint firstItem="4Ym-A1-Ztf" firstAttribute="leading" secondItem="2gj-xj-FHC" secondAttribute="leading" constant="24" id="7BI-W1-11s"/>
                                                                            <constraint firstAttribute="trailing" secondItem="JLG-Gv-aLX" secondAttribute="trailing" constant="8" id="DHI-Kz-gp5"/>
                                                                            <constraint firstItem="JLG-Gv-aLX" firstAttribute="leading" secondItem="4Ym-A1-Ztf" secondAttribute="trailing" constant="11" id="NAo-Hf-WNH"/>
                                                                            <constraint firstItem="4Ym-A1-Ztf" firstAttribute="top" secondItem="2gj-xj-FHC" secondAttribute="top" constant="8" id="OLr-c5-7st"/>
                                                                            <constraint firstItem="JLG-Gv-aLX" firstAttribute="top" secondItem="4Ym-A1-Ztf" secondAttribute="top" id="heS-Gw-8K8"/>
                                                                            <constraint firstAttribute="bottom" secondItem="JLG-Gv-aLX" secondAttribute="bottom" constant="15" id="zoV-IT-SSV"/>
                                                                        </constraints>
                                                                    </view>
                                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="GvK-ck-SM6">
                                                                        <rect key="frame" x="0.0" y="50" width="187.5" height="51"/>
                                                                        <subviews>
                                                                            <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_process_vip_seat" translatesAutoresizingMaskIntoConstraints="NO" id="qwi-Tf-0Rj">
                                                                                <rect key="frame" x="24" y="8" width="24" height="24"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="height" constant="24" id="D4C-WP-3x7"/>
                                                                                    <constraint firstAttribute="width" constant="24" id="Psx-3i-Azo"/>
                                                                                </constraints>
                                                                            </imageView>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Ghế đang chọn" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="rPX-xA-dWy" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                                                                <rect key="frame" x="59" y="8" width="120.5" height="28"/>
                                                                                <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                                                <color key="textColor" red="0.**********" green="0.**********" blue="0.15686274510000001" alpha="1" colorSpace="calibratedRGB"/>
                                                                                <nil key="highlightedColor"/>
                                                                                <userDefinedRuntimeAttributes>
                                                                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Seat.Selecting"/>
                                                                                </userDefinedRuntimeAttributes>
                                                                            </label>
                                                                        </subviews>
                                                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <constraints>
                                                                            <constraint firstItem="qwi-Tf-0Rj" firstAttribute="leading" secondItem="GvK-ck-SM6" secondAttribute="leading" constant="24" id="BIv-WH-mky"/>
                                                                            <constraint firstAttribute="bottom" secondItem="rPX-xA-dWy" secondAttribute="bottom" constant="15" id="NmP-Cg-keL"/>
                                                                            <constraint firstItem="qwi-Tf-0Rj" firstAttribute="top" secondItem="GvK-ck-SM6" secondAttribute="top" constant="8" id="eyF-dQ-S0G"/>
                                                                            <constraint firstAttribute="trailing" secondItem="rPX-xA-dWy" secondAttribute="trailing" constant="8" id="gA0-7y-Scz"/>
                                                                            <constraint firstItem="rPX-xA-dWy" firstAttribute="top" secondItem="qwi-Tf-0Rj" secondAttribute="top" id="jX0-wr-ihz"/>
                                                                            <constraint firstItem="rPX-xA-dWy" firstAttribute="leading" secondItem="qwi-Tf-0Rj" secondAttribute="trailing" constant="11" id="zdB-gl-OVG"/>
                                                                        </constraints>
                                                                    </view>
                                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="8iL-wG-aCQ">
                                                                        <rect key="frame" x="0.0" y="101" width="187.5" height="150"/>
                                                                        <subviews>
                                                                            <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_select_vip_seat" translatesAutoresizingMaskIntoConstraints="NO" id="b0z-tb-YIh">
                                                                                <rect key="frame" x="24" y="8" width="24" height="24"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="height" constant="24" id="BDP-HS-FfJ"/>
                                                                                    <constraint firstAttribute="width" constant="24" id="mjH-Cd-mPa"/>
                                                                                </constraints>
                                                                            </imageView>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Ghế đã chọn" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Xet-RG-TfT" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                                                                <rect key="frame" x="59" y="8" width="120.5" height="127"/>
                                                                                <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                                                <color key="textColor" red="0.**********" green="0.**********" blue="0.15686274510000001" alpha="1" colorSpace="calibratedRGB"/>
                                                                                <nil key="highlightedColor"/>
                                                                                <userDefinedRuntimeAttributes>
                                                                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Seat.Selected"/>
                                                                                </userDefinedRuntimeAttributes>
                                                                            </label>
                                                                        </subviews>
                                                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <constraints>
                                                                            <constraint firstItem="b0z-tb-YIh" firstAttribute="top" secondItem="8iL-wG-aCQ" secondAttribute="top" constant="8" id="ZwD-Dm-Jl5"/>
                                                                            <constraint firstAttribute="bottom" secondItem="Xet-RG-TfT" secondAttribute="bottom" constant="15" id="bbe-AV-uHZ"/>
                                                                            <constraint firstItem="Xet-RG-TfT" firstAttribute="top" secondItem="b0z-tb-YIh" secondAttribute="top" id="hDo-6I-lMb"/>
                                                                            <constraint firstItem="b0z-tb-YIh" firstAttribute="leading" secondItem="8iL-wG-aCQ" secondAttribute="leading" constant="24" id="hgR-fA-M3v"/>
                                                                            <constraint firstItem="Xet-RG-TfT" firstAttribute="leading" secondItem="b0z-tb-YIh" secondAttribute="trailing" constant="11" id="sJe-lM-tA3"/>
                                                                            <constraint firstAttribute="trailing" secondItem="Xet-RG-TfT" secondAttribute="trailing" constant="8" id="zxx-ju-pJR"/>
                                                                        </constraints>
                                                                    </view>
                                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="tyX-ma-Tkm">
                                                                        <rect key="frame" x="0.0" y="251" width="187.5" height="50"/>
                                                                        <subviews>
                                                                            <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_sold_vip_seat" translatesAutoresizingMaskIntoConstraints="NO" id="enQ-iu-bS1">
                                                                                <rect key="frame" x="24" y="8" width="24" height="24"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="height" constant="24" id="i7r-SX-ytq"/>
                                                                                    <constraint firstAttribute="width" constant="24" id="sKj-f7-h9C"/>
                                                                                </constraints>
                                                                            </imageView>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Ghế đã bán" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="DXa-dW-2xv" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                                                                <rect key="frame" x="59" y="8" width="120.5" height="27"/>
                                                                                <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                                                <color key="textColor" red="0.**********" green="0.**********" blue="0.15686274510000001" alpha="1" colorSpace="calibratedRGB"/>
                                                                                <nil key="highlightedColor"/>
                                                                                <userDefinedRuntimeAttributes>
                                                                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Seat.Sold"/>
                                                                                </userDefinedRuntimeAttributes>
                                                                            </label>
                                                                        </subviews>
                                                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <constraints>
                                                                            <constraint firstItem="DXa-dW-2xv" firstAttribute="leading" secondItem="enQ-iu-bS1" secondAttribute="trailing" constant="11" id="OWX-Rf-yqX"/>
                                                                            <constraint firstAttribute="bottom" secondItem="DXa-dW-2xv" secondAttribute="bottom" constant="15" id="Phn-Zv-Xro"/>
                                                                            <constraint firstItem="enQ-iu-bS1" firstAttribute="leading" secondItem="tyX-ma-Tkm" secondAttribute="leading" constant="24" id="UH3-7p-uRP"/>
                                                                            <constraint firstItem="enQ-iu-bS1" firstAttribute="top" secondItem="tyX-ma-Tkm" secondAttribute="top" constant="8" id="d7S-8R-EkD"/>
                                                                            <constraint firstItem="DXa-dW-2xv" firstAttribute="top" secondItem="enQ-iu-bS1" secondAttribute="top" id="heM-FE-0dT"/>
                                                                            <constraint firstAttribute="trailing" secondItem="DXa-dW-2xv" secondAttribute="trailing" constant="8" id="mqJ-pu-ISg"/>
                                                                        </constraints>
                                                                    </view>
                                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="MKn-b7-van">
                                                                        <rect key="frame" x="0.0" y="301" width="187.5" height="60.5"/>
                                                                        <subviews>
                                                                            <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_set_vip_seat" translatesAutoresizingMaskIntoConstraints="NO" id="vQj-CF-cap">
                                                                                <rect key="frame" x="24" y="8" width="24" height="24"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="width" constant="24" id="EX6-j9-spY"/>
                                                                                    <constraint firstAttribute="height" constant="24" id="IPN-u3-cBt"/>
                                                                                </constraints>
                                                                            </imageView>
                                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Ghế đã đặt truớc" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="dks-ye-YZf" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                                                                <rect key="frame" x="59" y="8" width="120.5" height="37.5"/>
                                                                                <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                                                <color key="textColor" red="0.**********" green="0.**********" blue="0.15686274510000001" alpha="1" colorSpace="calibratedRGB"/>
                                                                                <nil key="highlightedColor"/>
                                                                                <userDefinedRuntimeAttributes>
                                                                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Seat.Booked"/>
                                                                                </userDefinedRuntimeAttributes>
                                                                            </label>
                                                                        </subviews>
                                                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <constraints>
                                                                            <constraint firstItem="vQj-CF-cap" firstAttribute="top" secondItem="MKn-b7-van" secondAttribute="top" constant="8" id="Hj0-np-Fwm"/>
                                                                            <constraint firstAttribute="bottom" secondItem="dks-ye-YZf" secondAttribute="bottom" constant="15" id="PKG-W2-WMR"/>
                                                                            <constraint firstItem="dks-ye-YZf" firstAttribute="top" secondItem="vQj-CF-cap" secondAttribute="top" id="QP4-yS-xUR"/>
                                                                            <constraint firstAttribute="trailing" secondItem="dks-ye-YZf" secondAttribute="trailing" constant="8" id="hMa-Wt-Ixo"/>
                                                                            <constraint firstItem="dks-ye-YZf" firstAttribute="leading" secondItem="vQj-CF-cap" secondAttribute="trailing" constant="11" id="oLm-z4-1m0"/>
                                                                            <constraint firstItem="vQj-CF-cap" firstAttribute="leading" secondItem="MKn-b7-van" secondAttribute="leading" constant="24" id="v6X-K7-CSH"/>
                                                                        </constraints>
                                                                    </view>
                                                                </subviews>
                                                            </stackView>
                                                        </subviews>
                                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstItem="FYQ-r2-aLo" firstAttribute="top" secondItem="1OU-Qs-ts7" secondAttribute="top" id="6dW-Ey-Qak"/>
                                                            <constraint firstAttribute="trailing" secondItem="FYQ-r2-aLo" secondAttribute="trailing" id="brC-qD-zmm"/>
                                                            <constraint firstAttribute="bottom" secondItem="FYQ-r2-aLo" secondAttribute="bottom" id="qbG-M2-OBD"/>
                                                            <constraint firstItem="FYQ-r2-aLo" firstAttribute="leading" secondItem="1OU-Qs-ts7" secondAttribute="leading" id="sBN-r3-5V1"/>
                                                        </constraints>
                                                    </view>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="JD3-44-iQr">
                                                        <rect key="frame" x="187.5" y="0.0" width="187.5" height="361.5"/>
                                                        <subviews>
                                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" distribution="fillEqually" translatesAutoresizingMaskIntoConstraints="NO" id="YCO-mz-ItN">
                                                                <rect key="frame" x="0.0" y="0.0" width="187.5" height="361.5"/>
                                                                <subviews>
                                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="FVq-4Q-jQ0">
                                                                        <rect key="frame" x="0.0" y="0.0" width="187.5" height="120.5"/>
                                                                        <subviews>
                                                                            <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_empty_normal_seat" translatesAutoresizingMaskIntoConstraints="NO" id="AZr-aj-7Ea">
                                                                                <rect key="frame" x="24" y="48.5" width="24" height="24"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="height" constant="24" id="gVJ-Td-Adp"/>
                                                                                    <constraint firstAttribute="width" constant="24" id="qOv-jT-vUH"/>
                                                                                </constraints>
                                                                            </imageView>
                                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="s5H-Yo-b68">
                                                                                <rect key="frame" x="68" y="38" width="111.5" height="44.5"/>
                                                                                <subviews>
                                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Ghế thường" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="KDj-kh-5u5" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                                                                        <rect key="frame" x="0.0" y="0.0" width="111.5" height="24"/>
                                                                                        <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="16"/>
                                                                                        <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="calibratedRGB"/>
                                                                                        <nil key="highlightedColor"/>
                                                                                        <userDefinedRuntimeAttributes>
                                                                                            <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Seat.Normal"/>
                                                                                        </userDefinedRuntimeAttributes>
                                                                                    </label>
                                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="45.000đ" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="iqj-aj-xd5">
                                                                                        <rect key="frame" x="0.0" y="24" width="111.5" height="20.5"/>
                                                                                        <fontDescription key="fontDescription" name="SourceSansPro-Bold" family="Source Sans Pro" pointSize="16"/>
                                                                                        <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="calibratedRGB"/>
                                                                                        <nil key="highlightedColor"/>
                                                                                    </label>
                                                                                </subviews>
                                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                <constraints>
                                                                                    <constraint firstItem="iqj-aj-xd5" firstAttribute="top" secondItem="KDj-kh-5u5" secondAttribute="bottom" id="6cr-vl-egh"/>
                                                                                    <constraint firstItem="KDj-kh-5u5" firstAttribute="leading" secondItem="s5H-Yo-b68" secondAttribute="leading" id="LhO-T5-wHs"/>
                                                                                    <constraint firstItem="KDj-kh-5u5" firstAttribute="top" secondItem="s5H-Yo-b68" secondAttribute="top" id="NRd-gM-2CL"/>
                                                                                    <constraint firstAttribute="trailing" secondItem="KDj-kh-5u5" secondAttribute="trailing" id="XCp-bv-rvN"/>
                                                                                    <constraint firstItem="iqj-aj-xd5" firstAttribute="leading" secondItem="s5H-Yo-b68" secondAttribute="leading" id="XcL-sn-TWf"/>
                                                                                    <constraint firstAttribute="trailing" secondItem="iqj-aj-xd5" secondAttribute="trailing" id="lZT-R5-5XN"/>
                                                                                    <constraint firstAttribute="bottom" secondItem="iqj-aj-xd5" secondAttribute="bottom" id="zGh-ap-J2R"/>
                                                                                </constraints>
                                                                            </view>
                                                                        </subviews>
                                                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <constraints>
                                                                            <constraint firstItem="AZr-aj-7Ea" firstAttribute="leading" secondItem="FVq-4Q-jQ0" secondAttribute="leading" constant="24" id="4bX-jc-rl3"/>
                                                                            <constraint firstItem="s5H-Yo-b68" firstAttribute="leading" secondItem="AZr-aj-7Ea" secondAttribute="trailing" constant="20" id="ewg-bt-XMW"/>
                                                                            <constraint firstItem="AZr-aj-7Ea" firstAttribute="centerY" secondItem="FVq-4Q-jQ0" secondAttribute="centerY" id="ktw-Q2-Dt7"/>
                                                                            <constraint firstAttribute="trailing" secondItem="s5H-Yo-b68" secondAttribute="trailing" constant="8" id="ns3-md-6tt"/>
                                                                            <constraint firstItem="s5H-Yo-b68" firstAttribute="centerY" secondItem="FVq-4Q-jQ0" secondAttribute="centerY" id="z8h-sG-Y8f"/>
                                                                        </constraints>
                                                                    </view>
                                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="eNQ-f2-1a4">
                                                                        <rect key="frame" x="0.0" y="120.5" width="187.5" height="120.5"/>
                                                                        <subviews>
                                                                            <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_empty_vip_seat" translatesAutoresizingMaskIntoConstraints="NO" id="aad-Ym-Epj">
                                                                                <rect key="frame" x="24" y="48.5" width="24" height="24"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="height" constant="24" id="sWb-b3-V4k"/>
                                                                                    <constraint firstAttribute="width" constant="24" id="waz-st-zGq"/>
                                                                                </constraints>
                                                                            </imageView>
                                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="BcH-kv-kcu">
                                                                                <rect key="frame" x="68" y="38" width="111.5" height="44.5"/>
                                                                                <subviews>
                                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Ghế VIP" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="AcM-Hf-CMC" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                                                                        <rect key="frame" x="0.0" y="0.0" width="111.5" height="24"/>
                                                                                        <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="16"/>
                                                                                        <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="calibratedRGB"/>
                                                                                        <nil key="highlightedColor"/>
                                                                                        <userDefinedRuntimeAttributes>
                                                                                            <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Seat.Vip"/>
                                                                                        </userDefinedRuntimeAttributes>
                                                                                    </label>
                                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="45.000đ" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="xUd-ki-mvX">
                                                                                        <rect key="frame" x="0.0" y="24" width="111.5" height="20.5"/>
                                                                                        <fontDescription key="fontDescription" name="SourceSansPro-Bold" family="Source Sans Pro" pointSize="16"/>
                                                                                        <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="calibratedRGB"/>
                                                                                        <nil key="highlightedColor"/>
                                                                                    </label>
                                                                                </subviews>
                                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="bottom" secondItem="xUd-ki-mvX" secondAttribute="bottom" id="42o-UH-97g"/>
                                                                                    <constraint firstItem="xUd-ki-mvX" firstAttribute="top" secondItem="AcM-Hf-CMC" secondAttribute="bottom" id="5zv-Hz-raG"/>
                                                                                    <constraint firstAttribute="trailing" secondItem="xUd-ki-mvX" secondAttribute="trailing" id="DVo-4b-6sI"/>
                                                                                    <constraint firstAttribute="trailing" secondItem="AcM-Hf-CMC" secondAttribute="trailing" id="dUK-Nj-7ez"/>
                                                                                    <constraint firstItem="xUd-ki-mvX" firstAttribute="leading" secondItem="BcH-kv-kcu" secondAttribute="leading" id="oZ3-Zy-qbU"/>
                                                                                    <constraint firstItem="AcM-Hf-CMC" firstAttribute="top" secondItem="BcH-kv-kcu" secondAttribute="top" id="oaI-bI-xk5"/>
                                                                                    <constraint firstItem="AcM-Hf-CMC" firstAttribute="leading" secondItem="BcH-kv-kcu" secondAttribute="leading" id="wL4-Tb-xtY"/>
                                                                                </constraints>
                                                                            </view>
                                                                        </subviews>
                                                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <constraints>
                                                                            <constraint firstItem="aad-Ym-Epj" firstAttribute="centerY" secondItem="eNQ-f2-1a4" secondAttribute="centerY" id="1IP-XD-hBC"/>
                                                                            <constraint firstAttribute="trailing" secondItem="BcH-kv-kcu" secondAttribute="trailing" constant="8" id="TCF-cr-7Sx"/>
                                                                            <constraint firstItem="BcH-kv-kcu" firstAttribute="leading" secondItem="aad-Ym-Epj" secondAttribute="trailing" constant="20" id="i6B-aX-TS2"/>
                                                                            <constraint firstItem="BcH-kv-kcu" firstAttribute="centerY" secondItem="eNQ-f2-1a4" secondAttribute="centerY" id="lOM-75-zwy"/>
                                                                            <constraint firstItem="aad-Ym-Epj" firstAttribute="leading" secondItem="eNQ-f2-1a4" secondAttribute="leading" constant="24" id="set-ic-dAn"/>
                                                                        </constraints>
                                                                    </view>
                                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="h3e-Sp-trh">
                                                                        <rect key="frame" x="0.0" y="241" width="187.5" height="120.5"/>
                                                                        <subviews>
                                                                            <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_empty_couple_seat" translatesAutoresizingMaskIntoConstraints="NO" id="Vbh-42-hL1">
                                                                                <rect key="frame" x="12" y="48.5" width="48" height="24"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="height" constant="24" id="OiE-yl-XFI"/>
                                                                                    <constraint firstAttribute="width" constant="48" id="YHp-va-qrd"/>
                                                                                </constraints>
                                                                            </imageView>
                                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="rJK-1V-sit">
                                                                                <rect key="frame" x="70" y="38" width="109.5" height="44.5"/>
                                                                                <subviews>
                                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Ghế đôi" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="hhj-x8-6tQ" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                                                                        <rect key="frame" x="0.0" y="0.0" width="109.5" height="24"/>
                                                                                        <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="16"/>
                                                                                        <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="calibratedRGB"/>
                                                                                        <nil key="highlightedColor"/>
                                                                                        <userDefinedRuntimeAttributes>
                                                                                            <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Seat.Couple"/>
                                                                                        </userDefinedRuntimeAttributes>
                                                                                    </label>
                                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="90.000đ" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="aoZ-92-w8D">
                                                                                        <rect key="frame" x="0.0" y="24" width="109.5" height="20.5"/>
                                                                                        <fontDescription key="fontDescription" name="SourceSansPro-Bold" family="Source Sans Pro" pointSize="16"/>
                                                                                        <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="calibratedRGB"/>
                                                                                        <nil key="highlightedColor"/>
                                                                                    </label>
                                                                                </subviews>
                                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="trailing" secondItem="aoZ-92-w8D" secondAttribute="trailing" id="BN0-Vg-V2W"/>
                                                                                    <constraint firstAttribute="trailing" secondItem="hhj-x8-6tQ" secondAttribute="trailing" id="D2I-H5-Kly"/>
                                                                                    <constraint firstItem="hhj-x8-6tQ" firstAttribute="top" secondItem="rJK-1V-sit" secondAttribute="top" id="Fzt-km-yrX"/>
                                                                                    <constraint firstAttribute="bottom" secondItem="aoZ-92-w8D" secondAttribute="bottom" id="GtI-Ol-Q8k"/>
                                                                                    <constraint firstItem="aoZ-92-w8D" firstAttribute="top" secondItem="hhj-x8-6tQ" secondAttribute="bottom" id="Rzq-uz-H8k"/>
                                                                                    <constraint firstItem="hhj-x8-6tQ" firstAttribute="leading" secondItem="rJK-1V-sit" secondAttribute="leading" id="Yp2-Dn-Uoy"/>
                                                                                    <constraint firstItem="aoZ-92-w8D" firstAttribute="leading" secondItem="rJK-1V-sit" secondAttribute="leading" id="kZQ-04-781"/>
                                                                                </constraints>
                                                                            </view>
                                                                        </subviews>
                                                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <constraints>
                                                                            <constraint firstItem="Vbh-42-hL1" firstAttribute="leading" secondItem="h3e-Sp-trh" secondAttribute="leading" constant="12" id="Cy3-ji-Z1N"/>
                                                                            <constraint firstItem="rJK-1V-sit" firstAttribute="leading" secondItem="Vbh-42-hL1" secondAttribute="trailing" constant="10" id="YIl-gS-bv2"/>
                                                                            <constraint firstItem="rJK-1V-sit" firstAttribute="centerY" secondItem="h3e-Sp-trh" secondAttribute="centerY" id="ppW-ro-4tY"/>
                                                                            <constraint firstItem="Vbh-42-hL1" firstAttribute="centerY" secondItem="h3e-Sp-trh" secondAttribute="centerY" id="ulJ-SK-dPf"/>
                                                                            <constraint firstAttribute="trailing" secondItem="rJK-1V-sit" secondAttribute="trailing" constant="8" id="v7t-yW-kr6"/>
                                                                        </constraints>
                                                                    </view>
                                                                </subviews>
                                                            </stackView>
                                                        </subviews>
                                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstAttribute="bottom" secondItem="YCO-mz-ItN" secondAttribute="bottom" id="1Wb-Ww-BTw"/>
                                                            <constraint firstAttribute="trailing" secondItem="YCO-mz-ItN" secondAttribute="trailing" id="718-uc-E0q"/>
                                                            <constraint firstItem="YCO-mz-ItN" firstAttribute="leading" secondItem="JD3-44-iQr" secondAttribute="leading" id="IjV-Hz-6tx"/>
                                                            <constraint firstItem="YCO-mz-ItN" firstAttribute="top" secondItem="JD3-44-iQr" secondAttribute="top" id="d1z-hC-Kk0"/>
                                                        </constraints>
                                                    </view>
                                                </subviews>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstItem="JD3-44-iQr" firstAttribute="width" secondItem="1OU-Qs-ts7" secondAttribute="width" id="1pf-1L-5pj"/>
                                                    <constraint firstAttribute="bottom" secondItem="1OU-Qs-ts7" secondAttribute="bottom" id="P7b-gC-puE"/>
                                                    <constraint firstItem="1OU-Qs-ts7" firstAttribute="leading" secondItem="W1q-oh-NkL" secondAttribute="leading" id="WuZ-MY-k9t"/>
                                                    <constraint firstItem="JD3-44-iQr" firstAttribute="leading" secondItem="1OU-Qs-ts7" secondAttribute="trailing" id="boh-Od-8DX"/>
                                                    <constraint firstItem="1OU-Qs-ts7" firstAttribute="top" secondItem="W1q-oh-NkL" secondAttribute="top" id="c9K-Fz-RGQ"/>
                                                    <constraint firstItem="JD3-44-iQr" firstAttribute="top" secondItem="W1q-oh-NkL" secondAttribute="top" id="cd7-bc-b1q"/>
                                                    <constraint firstAttribute="bottom" secondItem="JD3-44-iQr" secondAttribute="bottom" id="fZd-Np-luv"/>
                                                    <constraint firstAttribute="trailing" secondItem="JD3-44-iQr" secondAttribute="trailing" id="pRp-Jm-LkO"/>
                                                </constraints>
                                            </view>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="0F1-3H-kkG">
                                                <rect key="frame" x="0.0" y="511.5" width="375" height="1"/>
                                                <color key="backgroundColor" red="0.72156862749999995" green="0.72156862749999995" blue="0.72156862749999995" alpha="1" colorSpace="calibratedRGB"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="1" id="82l-CU-4hT"/>
                                                </constraints>
                                            </view>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="MWC-s3-NsA">
                                                <rect key="frame" x="0.0" y="512.5" width="375" height="300"/>
                                                <subviews>
                                                    <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" minimumZoomScale="0.5" maximumZoomScale="1.2" translatesAutoresizingMaskIntoConstraints="NO" id="Lps-61-sBU">
                                                        <rect key="frame" x="0.0" y="0.0" width="375" height="300"/>
                                                        <subviews>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Pa6-MM-Ivt">
                                                                <rect key="frame" x="0.0" y="0.0" width="375" height="300"/>
                                                                <subviews>
                                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="B5C-Kj-TXd">
                                                                        <rect key="frame" x="0.0" y="0.0" width="375" height="60"/>
                                                                        <subviews>
                                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="agE-Za-NV4">
                                                                                <rect key="frame" x="13" y="10" width="349" height="41"/>
                                                                                <subviews>
                                                                                    <imageView hidden="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_screen" translatesAutoresizingMaskIntoConstraints="NO" id="puC-pF-Hzc">
                                                                                        <rect key="frame" x="0.0" y="0.0" width="349" height="41"/>
                                                                                    </imageView>
                                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="MÀN HÌNH CHIẾU" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="inc-9o-cKI" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                                                                        <rect key="frame" x="106" y="16.5" width="137" height="24"/>
                                                                                        <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="19"/>
                                                                                        <color key="textColor" red="0.28627450980392155" green="0.29803921568627451" blue="0.38431372549019605" alpha="1" colorSpace="calibratedRGB"/>
                                                                                        <nil key="highlightedColor"/>
                                                                                        <userDefinedRuntimeAttributes>
                                                                                            <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Cinema.Screen"/>
                                                                                        </userDefinedRuntimeAttributes>
                                                                                    </label>
                                                                                </subviews>
                                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                <constraints>
                                                                                    <constraint firstItem="puC-pF-Hzc" firstAttribute="top" secondItem="agE-Za-NV4" secondAttribute="top" id="Md3-qb-3tu"/>
                                                                                    <constraint firstItem="puC-pF-Hzc" firstAttribute="leading" secondItem="agE-Za-NV4" secondAttribute="leading" id="gt2-vR-QU6"/>
                                                                                    <constraint firstAttribute="trailing" secondItem="puC-pF-Hzc" secondAttribute="trailing" id="n6Y-dn-gAE"/>
                                                                                    <constraint firstAttribute="bottom" secondItem="puC-pF-Hzc" secondAttribute="bottom" id="tZ9-Yk-YKQ"/>
                                                                                    <constraint firstItem="inc-9o-cKI" firstAttribute="centerX" secondItem="agE-Za-NV4" secondAttribute="centerX" id="xlz-gA-6Yq"/>
                                                                                    <constraint firstItem="inc-9o-cKI" firstAttribute="centerY" secondItem="agE-Za-NV4" secondAttribute="centerY" constant="8" id="z0M-zM-7rv"/>
                                                                                </constraints>
                                                                            </view>
                                                                        </subviews>
                                                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <constraints>
                                                                            <constraint firstItem="agE-Za-NV4" firstAttribute="top" secondItem="B5C-Kj-TXd" secondAttribute="top" constant="10" id="4Tl-FL-zbf"/>
                                                                            <constraint firstItem="agE-Za-NV4" firstAttribute="centerX" secondItem="B5C-Kj-TXd" secondAttribute="centerX" id="YCh-bq-2Kg"/>
                                                                            <constraint firstAttribute="height" constant="60" id="jsM-6E-MIC"/>
                                                                            <constraint firstAttribute="width" secondItem="B5C-Kj-TXd" secondAttribute="height" multiplier="375:60" id="jv7-bs-ChL"/>
                                                                        </constraints>
                                                                        <variation key="default">
                                                                            <mask key="constraints">
                                                                                <exclude reference="jv7-bs-ChL"/>
                                                                            </mask>
                                                                        </variation>
                                                                    </view>
                                                                    <collectionView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" scrollEnabled="NO" dataMode="prototypes" translatesAutoresizingMaskIntoConstraints="NO" id="df2-58-CVE">
                                                                        <rect key="frame" x="0.0" y="60" width="375" height="240"/>
                                                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                        <constraints>
                                                                            <constraint firstAttribute="width" constant="375" id="A5x-dV-3JU"/>
                                                                            <constraint firstAttribute="height" constant="240" id="K6A-Hs-c86"/>
                                                                        </constraints>
                                                                        <collectionViewLayout key="collectionViewLayout" id="gFI-wj-bJd" customClass="ColumnLayout" customModule="Beta_Cinemas" customModuleProvider="target"/>
                                                                        <cells>
                                                                            <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" reuseIdentifier="SeatCollectionViewCell" id="kiH-7H-5bc" customClass="SeatCollectionViewCell" customModule="Beta_Cinemas" customModuleProvider="target">
                                                                                <rect key="frame" x="0.0" y="0.0" width="50" height="50"/>
                                                                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                                                                <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" insetsLayoutMarginsFromSafeArea="NO">
                                                                                    <rect key="frame" x="0.0" y="0.0" width="50" height="50"/>
                                                                                    <autoresizingMask key="autoresizingMask"/>
                                                                                    <subviews>
                                                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="yqA-8o-bZM">
                                                                                            <rect key="frame" x="0.0" y="0.0" width="50" height="50"/>
                                                                                            <subviews>
                                                                                                <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_empty_normal_seat" translatesAutoresizingMaskIntoConstraints="NO" id="YGn-tb-FX1">
                                                                                                    <rect key="frame" x="2" y="2" width="46" height="46"/>
                                                                                                </imageView>
                                                                                                <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="15" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="aXw-pB-qGF">
                                                                                                    <rect key="frame" x="0.0" y="4" width="50" height="34"/>
                                                                                                    <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="12"/>
                                                                                                    <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                                    <nil key="highlightedColor"/>
                                                                                                </label>
                                                                                            </subviews>
                                                                                            <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                                            <constraints>
                                                                                                <constraint firstItem="aXw-pB-qGF" firstAttribute="top" secondItem="yqA-8o-bZM" secondAttribute="top" constant="4" id="CnK-5n-eh6"/>
                                                                                                <constraint firstAttribute="bottom" secondItem="aXw-pB-qGF" secondAttribute="bottom" constant="12" id="Fpw-ro-42G"/>
                                                                                                <constraint firstAttribute="trailing" secondItem="aXw-pB-qGF" secondAttribute="trailing" id="GZP-qP-RzK"/>
                                                                                                <constraint firstAttribute="bottom" secondItem="YGn-tb-FX1" secondAttribute="bottom" constant="2" id="cQN-ts-L5U"/>
                                                                                                <constraint firstAttribute="trailing" secondItem="YGn-tb-FX1" secondAttribute="trailing" constant="2" id="irB-gG-zqh"/>
                                                                                                <constraint firstItem="YGn-tb-FX1" firstAttribute="leading" secondItem="yqA-8o-bZM" secondAttribute="leading" constant="2" id="jj3-Z7-aWu"/>
                                                                                                <constraint firstItem="aXw-pB-qGF" firstAttribute="leading" secondItem="yqA-8o-bZM" secondAttribute="leading" id="mdW-c8-dqa"/>
                                                                                                <constraint firstItem="YGn-tb-FX1" firstAttribute="top" secondItem="yqA-8o-bZM" secondAttribute="top" constant="2" id="x4t-8o-hKb"/>
                                                                                            </constraints>
                                                                                        </view>
                                                                                    </subviews>
                                                                                </view>
                                                                                <constraints>
                                                                                    <constraint firstAttribute="trailing" secondItem="yqA-8o-bZM" secondAttribute="trailing" id="0hL-0L-PZC"/>
                                                                                    <constraint firstAttribute="bottom" secondItem="yqA-8o-bZM" secondAttribute="bottom" id="7qb-ss-iRO"/>
                                                                                    <constraint firstItem="yqA-8o-bZM" firstAttribute="top" secondItem="kiH-7H-5bc" secondAttribute="top" id="wra-bs-qGa"/>
                                                                                    <constraint firstItem="yqA-8o-bZM" firstAttribute="leading" secondItem="kiH-7H-5bc" secondAttribute="leading" id="y7R-QL-MwL"/>
                                                                                </constraints>
                                                                                <connections>
                                                                                    <outlet property="ivSeat" destination="YGn-tb-FX1" id="9oD-C5-Gib"/>
                                                                                    <outlet property="lbSeatNumber" destination="aXw-pB-qGF" id="3Hc-pd-NFH"/>
                                                                                </connections>
                                                                            </collectionViewCell>
                                                                        </cells>
                                                                        <connections>
                                                                            <outlet property="dataSource" destination="op1-6U-V9F" id="sXG-IL-mXB"/>
                                                                            <outlet property="delegate" destination="op1-6U-V9F" id="GEF-sx-NUm"/>
                                                                        </connections>
                                                                    </collectionView>
                                                                </subviews>
                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstItem="B5C-Kj-TXd" firstAttribute="leading" secondItem="Pa6-MM-Ivt" secondAttribute="leading" id="0Td-kI-53Y"/>
                                                                    <constraint firstAttribute="bottom" secondItem="df2-58-CVE" secondAttribute="bottom" id="GDU-2L-cD0"/>
                                                                    <constraint firstAttribute="trailing" secondItem="df2-58-CVE" secondAttribute="trailing" id="JrD-pQ-g4d"/>
                                                                    <constraint firstItem="B5C-Kj-TXd" firstAttribute="top" secondItem="Pa6-MM-Ivt" secondAttribute="top" id="dJu-p3-REh"/>
                                                                    <constraint firstAttribute="trailing" secondItem="B5C-Kj-TXd" secondAttribute="trailing" id="u9P-jI-9XZ"/>
                                                                    <constraint firstItem="df2-58-CVE" firstAttribute="leading" secondItem="Pa6-MM-Ivt" secondAttribute="leading" id="uXV-8T-fMN"/>
                                                                    <constraint firstItem="df2-58-CVE" firstAttribute="top" secondItem="B5C-Kj-TXd" secondAttribute="bottom" id="xRt-zF-Jtn"/>
                                                                </constraints>
                                                            </view>
                                                        </subviews>
                                                        <constraints>
                                                            <constraint firstItem="Pa6-MM-Ivt" firstAttribute="leading" secondItem="Lps-61-sBU" secondAttribute="leading" id="14U-aq-AMb"/>
                                                            <constraint firstAttribute="bottom" secondItem="Pa6-MM-Ivt" secondAttribute="bottom" id="59Q-TK-VGz"/>
                                                            <constraint firstItem="Pa6-MM-Ivt" firstAttribute="top" secondItem="Lps-61-sBU" secondAttribute="top" id="cfJ-p7-ASm"/>
                                                            <constraint firstAttribute="trailing" secondItem="Pa6-MM-Ivt" secondAttribute="trailing" id="d6e-XC-4op"/>
                                                        </constraints>
                                                        <connections>
                                                            <outlet property="delegate" destination="op1-6U-V9F" id="y9h-h7-Mln"/>
                                                        </connections>
                                                    </scrollView>
                                                </subviews>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="300" id="5HM-k8-bnn"/>
                                                    <constraint firstAttribute="trailing" secondItem="Lps-61-sBU" secondAttribute="trailing" id="V00-H5-fbo"/>
                                                    <constraint firstItem="Lps-61-sBU" firstAttribute="leading" secondItem="MWC-s3-NsA" secondAttribute="leading" id="n2K-60-2YY"/>
                                                    <constraint firstItem="Lps-61-sBU" firstAttribute="top" secondItem="MWC-s3-NsA" secondAttribute="top" id="syO-Lc-g4t"/>
                                                    <constraint firstAttribute="bottom" secondItem="Lps-61-sBU" secondAttribute="bottom" id="tI0-Lk-4hf"/>
                                                </constraints>
                                            </view>
                                        </subviews>
                                    </stackView>
                                </subviews>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="d8D-du-weY" secondAttribute="trailing" id="4EF-ew-b0I"/>
                                    <constraint firstItem="d8D-du-weY" firstAttribute="top" secondItem="8pS-PJ-Yeh" secondAttribute="top" id="URa-Ko-JVw"/>
                                    <constraint firstAttribute="bottom" secondItem="d8D-du-weY" secondAttribute="bottom" id="c1K-92-XKd"/>
                                    <constraint firstItem="d8D-du-weY" firstAttribute="leading" secondItem="8pS-PJ-Yeh" secondAttribute="leading" id="hio-us-Lc9"/>
                                    <constraint firstItem="d8D-du-weY" firstAttribute="width" secondItem="8pS-PJ-Yeh" secondAttribute="width" id="kjL-mu-AhP"/>
                                </constraints>
                                <connections>
                                    <outlet property="delegate" destination="op1-6U-V9F" id="grX-fd-vyS"/>
                                </connections>
                            </scrollView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Oho-nd-DhF">
                                <rect key="frame" x="0.0" y="581" width="375" height="86"/>
                                <subviews>
                                    <imageView userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="btnBookingdetailWhite" translatesAutoresizingMaskIntoConstraints="NO" id="qe8-3r-PyI">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="86"/>
                                    </imageView>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="SiF-w7-SN6" customClass="LocalizableButton" customModule="Beta_Cinemas" customModuleProvider="target">
                                        <rect key="frame" x="260" y="9.5" width="115" height="76.5"/>
                                        <constraints>
                                            <constraint firstAttribute="width" secondItem="SiF-w7-SN6" secondAttribute="height" multiplier="116:77" id="Uyw-Qk-YMO"/>
                                        </constraints>
                                        <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="22"/>
                                        <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <state key="normal" title="Tiếp tục" backgroundImage="btnBookingdetailBlue"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Seat.Continue"/>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="continueButtonPressed:" destination="op1-6U-V9F" eventType="touchUpInside" id="sga-5M-rHG"/>
                                        </connections>
                                    </button>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Y8B-tb-6Xs">
                                        <rect key="frame" x="0.0" y="0.0" width="126" height="86"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Ghế đã chọn" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="tgF-A7-Y1L" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                                <rect key="frame" x="6" y="10" width="114" height="24"/>
                                                <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="16"/>
                                                <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="calibratedRGB"/>
                                                <nil key="highlightedColor"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Seat.SelectedSeats"/>
                                                </userDefinedRuntimeAttributes>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" text="C4, C5, D5, C" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="l9f-ij-Qw1">
                                                <rect key="frame" x="5" y="37" width="116" height="43"/>
                                                <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="20"/>
                                                <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="calibratedRGB"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="wfU-w3-gLT">
                                                <rect key="frame" x="0.0" y="34" width="126" height="52"/>
                                                <connections>
                                                    <action selector="showAllSeatsPressed:" destination="op1-6U-V9F" eventType="touchUpInside" id="f6Q-AZ-Kfn"/>
                                                </connections>
                                            </button>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="l9f-ij-Qw1" secondAttribute="trailing" constant="5" id="1Xv-KL-cfZ"/>
                                            <constraint firstItem="tgF-A7-Y1L" firstAttribute="leading" secondItem="Y8B-tb-6Xs" secondAttribute="leading" constant="6" id="4jh-c2-lSP"/>
                                            <constraint firstAttribute="trailing" secondItem="wfU-w3-gLT" secondAttribute="trailing" id="7HW-uy-2FP"/>
                                            <constraint firstItem="l9f-ij-Qw1" firstAttribute="leading" secondItem="Y8B-tb-6Xs" secondAttribute="leading" constant="5" id="99a-aK-Td4"/>
                                            <constraint firstItem="tgF-A7-Y1L" firstAttribute="top" secondItem="Y8B-tb-6Xs" secondAttribute="top" constant="10" id="Heu-df-LS2"/>
                                            <constraint firstItem="wfU-w3-gLT" firstAttribute="top" secondItem="tgF-A7-Y1L" secondAttribute="bottom" id="IU0-MG-nFB"/>
                                            <constraint firstAttribute="trailing" secondItem="tgF-A7-Y1L" secondAttribute="trailing" constant="6" id="OB2-Hk-cj1"/>
                                            <constraint firstItem="l9f-ij-Qw1" firstAttribute="top" secondItem="tgF-A7-Y1L" secondAttribute="bottom" constant="3" id="QSl-K7-MM7"/>
                                            <constraint firstAttribute="bottom" secondItem="l9f-ij-Qw1" secondAttribute="bottom" constant="6" id="jCt-hj-JqM"/>
                                            <constraint firstAttribute="bottom" secondItem="wfU-w3-gLT" secondAttribute="bottom" id="n8d-uD-zie"/>
                                            <constraint firstItem="wfU-w3-gLT" firstAttribute="leading" secondItem="Y8B-tb-6Xs" secondAttribute="leading" id="xhX-wq-nmR"/>
                                        </constraints>
                                    </view>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Sjy-i7-44g">
                                        <rect key="frame" x="126" y="0.0" width="126" height="86"/>
                                        <subviews>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="NO9-KV-4FP">
                                                <rect key="frame" x="0.0" y="16" width="1" height="54"/>
                                                <color key="backgroundColor" red="0.59215686274509804" green="0.59215686274509804" blue="0.59215686274509804" alpha="1" colorSpace="calibratedRGB"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" constant="1" id="B6l-eg-bjE"/>
                                                </constraints>
                                            </view>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Tổng tiền" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="YRc-eU-fMG" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                                <rect key="frame" x="6" y="10" width="114" height="24"/>
                                                <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="16"/>
                                                <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="calibratedRGB"/>
                                                <nil key="highlightedColor"/>
                                                <userDefinedRuntimeAttributes>
                                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Seat.TotalBill"/>
                                                </userDefinedRuntimeAttributes>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" fixedFrame="YES" text="90.000đ" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="pei-qO-ZMi">
                                                <rect key="frame" x="6" y="46" width="117.5" height="24"/>
                                                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                                <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="20"/>
                                                <color key="textColor" red="0.0**********" green="0.34901960780000002" blue="0.61568627450000002" alpha="1" colorSpace="calibratedRGB"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstItem="YRc-eU-fMG" firstAttribute="leading" secondItem="Sjy-i7-44g" secondAttribute="leading" constant="6" id="29S-Lh-gAk"/>
                                            <constraint firstAttribute="trailing" secondItem="YRc-eU-fMG" secondAttribute="trailing" constant="6" id="CuR-3x-K9h"/>
                                            <constraint firstAttribute="bottom" secondItem="NO9-KV-4FP" secondAttribute="bottom" constant="16" id="YNv-VW-Xmo"/>
                                            <constraint firstItem="YRc-eU-fMG" firstAttribute="top" secondItem="Sjy-i7-44g" secondAttribute="top" constant="10" id="gf1-1v-muB"/>
                                            <constraint firstItem="NO9-KV-4FP" firstAttribute="top" secondItem="Sjy-i7-44g" secondAttribute="top" constant="16" id="hHk-YO-unJ"/>
                                            <constraint firstItem="NO9-KV-4FP" firstAttribute="leading" secondItem="Sjy-i7-44g" secondAttribute="leading" id="uPr-Bl-eYl"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="qe8-3r-PyI" firstAttribute="top" secondItem="Oho-nd-DhF" secondAttribute="top" id="2D7-9X-COk"/>
                                    <constraint firstItem="SiF-w7-SN6" firstAttribute="leading" secondItem="Sjy-i7-44g" secondAttribute="trailing" constant="8" id="8jl-Mq-Koh"/>
                                    <constraint firstItem="SiF-w7-SN6" firstAttribute="top" secondItem="Oho-nd-DhF" secondAttribute="top" constant="9.5" id="CcY-yT-QFc"/>
                                    <constraint firstAttribute="bottom" secondItem="Sjy-i7-44g" secondAttribute="bottom" id="FkR-aJ-m4e"/>
                                    <constraint firstAttribute="trailing" secondItem="qe8-3r-PyI" secondAttribute="trailing" id="G9r-OK-XHS"/>
                                    <constraint firstItem="Y8B-tb-6Xs" firstAttribute="top" secondItem="Oho-nd-DhF" secondAttribute="top" id="N7B-CD-ZY5"/>
                                    <constraint firstAttribute="bottom" secondItem="SiF-w7-SN6" secondAttribute="bottom" id="Nkf-rk-Ojd"/>
                                    <constraint firstItem="Y8B-tb-6Xs" firstAttribute="leading" secondItem="Oho-nd-DhF" secondAttribute="leading" id="Qm7-ej-IRC"/>
                                    <constraint firstItem="Sjy-i7-44g" firstAttribute="leading" secondItem="Y8B-tb-6Xs" secondAttribute="trailing" id="SqL-rw-2IG"/>
                                    <constraint firstAttribute="bottom" secondItem="Y8B-tb-6Xs" secondAttribute="bottom" id="UZt-8l-dgB"/>
                                    <constraint firstAttribute="height" constant="86" id="cDi-Oo-o2R"/>
                                    <constraint firstAttribute="trailing" secondItem="SiF-w7-SN6" secondAttribute="trailing" id="cxi-yW-gQx"/>
                                    <constraint firstItem="Sjy-i7-44g" firstAttribute="width" secondItem="Y8B-tb-6Xs" secondAttribute="width" id="ftE-gp-P5k"/>
                                    <constraint firstItem="Sjy-i7-44g" firstAttribute="top" secondItem="Oho-nd-DhF" secondAttribute="top" id="pJj-RP-j3l"/>
                                    <constraint firstItem="qe8-3r-PyI" firstAttribute="leading" secondItem="Oho-nd-DhF" secondAttribute="leading" id="qLh-of-n66"/>
                                    <constraint firstAttribute="bottom" secondItem="qe8-3r-PyI" secondAttribute="bottom" id="vbu-FG-9Lc"/>
                                </constraints>
                            </view>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="10:00" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="qmz-1x-qJO">
                                <rect key="frame" x="0.0" y="561" width="375" height="20"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="20" id="U8l-Ag-jAX"/>
                                </constraints>
                                <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="15"/>
                                <nil key="textColor"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="H0R-zn-M0q"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="H0R-zn-M0q" firstAttribute="trailing" secondItem="8pS-PJ-Yeh" secondAttribute="trailing" id="1yt-mb-D6a"/>
                            <constraint firstItem="H0R-zn-M0q" firstAttribute="bottom" secondItem="Oho-nd-DhF" secondAttribute="bottom" id="2At-B3-ezB"/>
                            <constraint firstItem="Oho-nd-DhF" firstAttribute="trailing" secondItem="H0R-zn-M0q" secondAttribute="trailing" id="6AY-k9-xCf"/>
                            <constraint firstItem="qmz-1x-qJO" firstAttribute="top" secondItem="8pS-PJ-Yeh" secondAttribute="bottom" id="7I4-JP-nWk"/>
                            <constraint firstItem="H0R-zn-M0q" firstAttribute="trailing" secondItem="qmz-1x-qJO" secondAttribute="trailing" id="A7y-Mu-jHw"/>
                            <constraint firstItem="Oho-nd-DhF" firstAttribute="leading" secondItem="H0R-zn-M0q" secondAttribute="leading" id="AsP-KJ-lre"/>
                            <constraint firstItem="qmz-1x-qJO" firstAttribute="leading" secondItem="H0R-zn-M0q" secondAttribute="leading" id="Wtx-mF-NYb"/>
                            <constraint firstItem="qmz-1x-qJO" firstAttribute="bottom" secondItem="Oho-nd-DhF" secondAttribute="top" id="ZMl-ch-5XR"/>
                            <constraint firstItem="8pS-PJ-Yeh" firstAttribute="leading" secondItem="H0R-zn-M0q" secondAttribute="leading" id="aSe-ba-6A1"/>
                            <constraint firstItem="8pS-PJ-Yeh" firstAttribute="top" secondItem="H0R-zn-M0q" secondAttribute="top" id="h2N-SX-2WT"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="collectionView" destination="df2-58-CVE" id="taj-xE-xDU"/>
                        <outlet property="collectionViewHeight" destination="K6A-Hs-c86" id="mQG-cs-CVO"/>
                        <outlet property="collectionViewWidth" destination="A5x-dV-3JU" id="6ZX-fK-IBx"/>
                        <outlet property="columnLayout" destination="gFI-wj-bJd" id="jKk-Ug-JgO"/>
                        <outlet property="ivFilmBanner" destination="OHd-NA-UkY" id="Ima-x8-jaM"/>
                        <outlet property="lbCouplePrice" destination="aoZ-92-w8D" id="bgn-gf-mSm"/>
                        <outlet property="lbFilmName" destination="Dav-Si-36w" id="Bzb-iF-IcX"/>
                        <outlet property="lbFilmOption" destination="I5Z-q4-Iwa" id="lnF-af-mOw"/>
                        <outlet property="lbNormalPrice" destination="iqj-aj-xd5" id="tvK-82-Mwz"/>
                        <outlet property="lbSelectedSeats" destination="l9f-ij-Qw1" id="9uR-AT-eUb"/>
                        <outlet property="lbTotalPrice" destination="pei-qO-ZMi" id="K8t-1M-LoH"/>
                        <outlet property="lbVipPrice" destination="xUd-ki-mvX" id="NMA-fB-LSz"/>
                        <outlet property="mainView" destination="d8D-du-weY" id="4cb-TA-WuC"/>
                        <outlet property="screenView" destination="B5C-Kj-TXd" id="2HY-uY-Wfv"/>
                        <outlet property="scrollView" destination="8pS-PJ-Yeh" id="sEZ-9M-E8A"/>
                        <outlet property="seatContainer" destination="Pa6-MM-Ivt" id="pHH-V7-Ucz"/>
                        <outlet property="seatHeight" destination="5HM-k8-bnn" id="qau-kG-CxJ"/>
                        <outlet property="seatScrollView" destination="Lps-61-sBU" id="kgZ-gW-8lI"/>
                        <outlet property="timeLeftBottomConstraint" destination="ZMl-ch-5XR" id="u49-vT-JA0"/>
                        <outlet property="timeLeftLabel" destination="qmz-1x-qJO" id="qpA-pi-xVI"/>
                        <outlet property="vHeaderGradient" destination="8dl-Tf-wxx" id="OGD-hN-mxU"/>
                        <outlet property="vPrice" destination="Oho-nd-DhF" id="z3d-Y5-Peb"/>
                        <outlet property="vPriceBottom" destination="2At-B3-ezB" id="qfd-ys-Tle"/>
                        <outlet property="vSeatsList" destination="MWC-s3-NsA" id="iHE-7m-BgR"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="8LC-Ge-RSu" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1973.5999999999999" y="108.39580209895054"/>
        </scene>
    </scenes>
    <designables>
        <designable name="AcM-Hf-CMC">
            <size key="intrinsicContentSize" width="46" height="24"/>
        </designable>
        <designable name="DXa-dW-2xv">
            <size key="intrinsicContentSize" width="75.5" height="20.5"/>
        </designable>
        <designable name="JLG-Gv-aLX">
            <size key="intrinsicContentSize" width="66" height="20.5"/>
        </designable>
        <designable name="KDj-kh-5u5">
            <size key="intrinsicContentSize" width="66" height="24"/>
        </designable>
        <designable name="M1w-8X-qnp">
            <size key="intrinsicContentSize" width="82" height="45"/>
        </designable>
        <designable name="NVo-W7-IQ4">
            <size key="intrinsicContentSize" width="74" height="44"/>
        </designable>
        <designable name="SiF-w7-SN6">
            <size key="intrinsicContentSize" width="116" height="80"/>
        </designable>
        <designable name="Xet-RG-TfT">
            <size key="intrinsicContentSize" width="83.5" height="20.5"/>
        </designable>
        <designable name="YRc-eU-fMG">
            <size key="intrinsicContentSize" width="53.5" height="24"/>
        </designable>
        <designable name="aOZ-xQ-DS3">
            <size key="intrinsicContentSize" width="72" height="45"/>
        </designable>
        <designable name="dks-ye-YZf">
            <size key="intrinsicContentSize" width="111" height="20.5"/>
        </designable>
        <designable name="gj7-NC-xJg">
            <size key="intrinsicContentSize" width="69" height="33"/>
        </designable>
        <designable name="hhj-x8-6tQ">
            <size key="intrinsicContentSize" width="44" height="24"/>
        </designable>
        <designable name="inc-9o-cKI">
            <size key="intrinsicContentSize" width="137" height="24"/>
        </designable>
        <designable name="rPX-xA-dWy">
            <size key="intrinsicContentSize" width="100.5" height="20.5"/>
        </designable>
        <designable name="tgF-A7-Y1L">
            <size key="intrinsicContentSize" width="71.5" height="24"/>
        </designable>
    </designables>
    <resources>
        <image name="bg3.png" width="320" height="568"/>
        <image name="btnBookingdetailBlue" width="116" height="80"/>
        <image name="btnBookingdetailWhite" width="375" height="93"/>
        <image name="dome_zoom" width="900" height="600"/>
        <image name="icCall" width="24" height="25"/>
        <image name="icClose" width="24" height="24"/>
        <image name="icSesiontime" width="25" height="25"/>
        <image name="icTicket" width="40" height="20"/>
        <image name="ic_empty_couple_seat" width="48" height="25"/>
        <image name="ic_empty_normal_seat" width="24" height="24"/>
        <image name="ic_empty_vip_seat" width="24" height="24"/>
        <image name="ic_process_vip_seat" width="24" height="24"/>
        <image name="ic_screen" width="349" height="41"/>
        <image name="ic_select_vip_seat" width="24" height="24"/>
        <image name="ic_set_vip_seat" width="24" height="24"/>
        <image name="ic_sold_vip_seat" width="24" height="24"/>
        <systemColor name="systemBackgroundColor">
            <color white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
        </systemColor>
    </resources>
</document>

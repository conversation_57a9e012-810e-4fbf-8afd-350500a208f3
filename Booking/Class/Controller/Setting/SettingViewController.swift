//
//  SettingViewController.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 3/14/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit
import CoreLocation

extension TableSectionTag {
    static let language = TableSectionTag(value: 100)
    static let other = TableSectionTag(value: 101)
}

extension TableItemTag {
    static let vietnam = TableItemTag(value: 98)
    static let english = TableItemTag(value: 99)
    static let notify = TableItemTag(value: 100)
    static let location = TableItemTag(value: 101)
    static let qa = TableItemTag(value: 102)
    static let version = TableItemTag(value: 103)
    static let policy = TableItemTag(value: 104)
    static let paymentPolicy = TableItemTag(value: 105)
    static let securePolicy = TableItemTag(value: 106)
    static let companyInfo = TableItemTag(value: 107)
}

class SettingViewController: BaseViewController {
    @IBOutlet weak var tableView: UITableView!

    let dataSource = SimpleTableViewDataSource()

    fileprivate let switchCellId = "SwitchTableCell"
    fileprivate let checkboxCellId = "CheckboxTableCell"
    fileprivate let settingCellId = "SettingTableCell"
    fileprivate let titleHeaderId = "TitleHeaderView"
    fileprivate var locationCell: SwitchTableCell!
    
    override func viewDidLoad() {
        super.viewDidLoad()

        localizableTitle = "Setting.Title"

        tableView.dataSource = dataSource
        dataSource.register(tableView: tableView, cellId: switchCellId)
        dataSource.register(tableView: tableView, cellId: checkboxCellId)
        dataSource.register(tableView: tableView, cellId: settingCellId)
        tableView.register(UINib(nibName: titleHeaderId, bundle: nil), forHeaderFooterViewReuseIdentifier: titleHeaderId)

        initData()
        
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        self.navigationController?.setTransparent(false)
    }

    deinit {
        print("Setting deinit")
    }

    func initData() {
        var sections = [TableSection]()
        // language
        sections.append(TableSection(title: "Language".localized.uppercased(), items: [TableItem(title: "Setting.VietNam".localized, cellId: checkboxCellId, accessoryType: Utils.shared.isEng() ? .none : .checkmark, tag: .vietnam), TableItem(title: "Setting.English".localized, cellId: checkboxCellId, accessoryType: !Utils.shared.isEng() ? .none : .checkmark, tag: .english)], tag: .language))
        // other
        let notifyCell = SwitchTableCell.nib()
        let notifyItem = TableItem(title: "Setting.Notify".localized, tag: .notify)
        notifyItem.cell = notifyCell
        notifyCell.onSwitchChangeValue = { isOn in

        }

        locationCell = SwitchTableCell.nib()
        let locationItem = TableItem(title: "Setting.Location".localized, tag: .location)
        locationItem.cell = locationCell
        locationCell.onSwitchChangeValue = { [weak self] isOn in
            self?.changeLocation(enable: isOn)
        }
        locationCell.switchView.isOn = LocationManager.shared.isEnable
        print("location enable: \(LocationManager.shared.isEnable)")

        let qaItem = TableItem(title: "Setting.FAQ".localized, content: nil, cellId: settingCellId, tag: .qa)
        let versionItem = TableItem(title: "Setting.Version".localized, content: UIDevice.current.appVersion, cellId: settingCellId, tag: .version)
        let policyItem = TableItem(title: "Setting.Policy".localized, content: nil, cellId: settingCellId, tag: .policy)
        let paymentPolicyItem = TableItem(title: "Setting.PaymentPolicy".localized, content: nil, cellId: settingCellId, tag: .paymentPolicy)
        let securePolicyItem = TableItem(title: "Setting.SecurePolicy".localized, content: nil, cellId: settingCellId, tag: .securePolicy)
        let companyInfoItem = TableItem(title: "Setting.CompanyInfo".localized, content: nil, cellId: settingCellId, tag: .companyInfo)

        sections.append(TableSection(title: "Other".localized.uppercased(), items: [notifyItem, locationItem, qaItem, versionItem, policyItem, paymentPolicyItem, securePolicyItem, companyInfoItem], tag: .other))

        dataSource.addSection(sections)
    }

    override func localizationDidChange() {
        super.localizationDidChange()
        dataSource.removeAll()
        initData()
        tableView.reloadData()
    }

    func changeLocation(enable: Bool) {
        if !enable {
            LocationManager.shared.isEnable = false
            locationCell.switchView.setOn(false, animated: true)
            return
        }

        let status = CLLocationManager.authorizationStatus()
        switch status {
        case .authorizedAlways, .authorizedWhenInUse:
            LocationManager.shared.isEnable = true
            locationCell.switchView.setOn(enable, animated: true)
        case .notDetermined:
            LocationManager.shared.isEnable = enable
            LocationManager.shared.startTracking { _ in
                self.locationCell.switchView.setOn(LocationManager.shared.isEnable, animated: true)
            }
        case .denied, .restricted:
            LocationManager.shared.openLocationAlert()
            locationCell.switchView.setOn(false, animated: true)
        }
    }

    
}

extension SettingViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        let headerView = tableView.dequeueReusableHeaderFooterView(withIdentifier: titleHeaderId)
        headerView?.updateViewWithSection(dataSource[section])
        return headerView
    }

    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        return 50
    }

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: false)

        let section = dataSource[indexPath.section]
        let item = section[indexPath.row]

        if section.tag == .language {
            if item.tag == .vietnam{
                guard LanguageManager.shared.getLanguage() == .English else {return}
                LanguageManager.shared.setLanguage(.Vietnam)
                item.setAccessoryType(.checkmark)
                let preItem = section[indexPath.row + 1]
                preItem.setAccessoryType(.none)
            }else{
                guard LanguageManager.shared.getLanguage() == .Vietnam else {return}
                LanguageManager.shared.setLanguage(.English)
                item.setAccessoryType(.checkmark)
                let preItem = section[indexPath.row - 1]
                preItem.setAccessoryType(.none)
            }
            section.updateTitle(title: "Language".localized.uppercased())
            NotificationCenter.default.post(name: NSNotification.Name.ChangeLocalization, object: nil)
        } else {
            if item.tag == .qa {
                let vc = UIStoryboard.setting[.faq]
                show(vc, sender: nil)
            } else if item.tag == .version {
                let vc = UIStoryboard.setting[.versionInfo]
                self.show(vc, sender: nil)
            }else{
                var type: OtherType = .Term
                if item.tag == .policy{
                    type = .Term
                }else if item.tag == .paymentPolicy{
                    type = .PolicyPayment
                }else if item.tag == .securePolicy{
                    type = .Security
                }else if item.tag == .companyInfo{
                    type = .CompanyInfo
                }
                let vc = UIStoryboard.setting[.other] as! OtherViewController
                vc.type = type
                self.show(vc, sender: nil)
            }
        }
    }
}

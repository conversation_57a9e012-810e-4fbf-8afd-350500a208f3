//
//  LogoutViewController.swift
//  Booking
//
//  Created by <PERSON><PERSON> Vu on 3/10/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit
import ActiveLabel
import SwiftDate
import IQKeyboardManagerSwift
import PopupDialog
import ReCaptcha
import RxSwift

enum Gender: Int, CustomStringConvertible{
    var description: String{
        let en = Utils.shared.isEng()
        switch self {
        case .Male:
            return en ? "Male" : "Nam"
        case .Female:
            return en ? "Female" : "Nữ"
        case .Other:
            return en ? "Other" : "Khác"
        }
    }
    
    case Male = 1
    case Female = 2
    case Other = 3
}

class RegisterViewController: BaseViewController {
    @IBOutlet weak var nameTextField: InputTextField!
    @IBOutlet weak var firstNameTextField: InputTextField!
    @IBOutlet weak var lastNameTextField: InputTextField!
    @IBOutlet weak var emailTextField: InputTextField!
    @IBOutlet weak var passwordTextField: InputTextField!
    @IBOutlet weak var repasswordTextField: InputTextField!
    @IBOutlet weak var phoneTextField: InputTextField!
    @IBOutlet weak var cmndTextField: InputTextField!
    @IBOutlet weak var birthdayTextField: DateTextField!

    @IBOutlet weak var genderTextField: PickerTextField!
    @IBOutlet weak var cityTextField: PickerTextField!
    @IBOutlet weak var districtTextField: PickerTextField!
    @IBOutlet weak var addressTextField: InputTextField!

    @IBOutlet weak var receiveCardCheckbox: UIButton!
    @IBOutlet weak var acceptCheckbox: UIButton!
    @IBOutlet weak var policyConfirmLabel: ActiveLabel!
    @IBOutlet weak var btnRegister: GradientButton!
    @IBOutlet weak var scrollView: UIScrollView!
    
    private var listCity: [CityModel]?
    private var listDistrict: [CityModel]?
    let genders = [Gender.Male, .Female, .Other]
    var genderSelected: Gender?
    var citySelected: CityModel?
    var districtSelected: CityModel?

    private var recaptcha: ReCaptcha!

    private var locale: Locale = Locale.current
    private var endpoint = ReCaptcha.Endpoint.default

    private struct Constants {
        static let webViewTag = 123
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        localizableTitle = "Register.Title"
        // Do any additional setup after loading the view.
        initPolicyLabel()
        initCityPicker()
        setupGenderData()
        birthdayTextField.maxDate = Date()
        self.getListCity()
        setupReCaptcha()
    }

    func initPolicyLabel() {
        let term = "Register.Term".localized
        let policy = "Register.Policy".localized
        let termAndPolicy = "Register.ConfirmTermAndPolicyText".localized

        let termType = ActiveType.custom(pattern: "\\s" + term + "\\b")
        let policyType = ActiveType.custom(pattern: "\\s" + policy + "\\b")
        policyConfirmLabel.enabledTypes = [termType, policyType]
        policyConfirmLabel.customColor[termType] = .linkText
        policyConfirmLabel.customColor[policyType] = .linkText

        policyConfirmLabel.highlightFontName = UIFont.fontName(.SourceSansPro, style: .SemiBold)
        policyConfirmLabel.highlightFontSize = policyConfirmLabel.font.pointSize
        policyConfirmLabel.text = termAndPolicy
        policyConfirmLabel.adjustsFontSizeToFitWidth = true

        policyConfirmLabel.handleCustomTap(for: termType) { [weak self] text in
            let vc = UIStoryboard.setting[.other] as! OtherViewController
            vc.type = OtherType.Term
            self?.show(vc)
        }

        policyConfirmLabel.handleCustomTap(for: policyType) { [weak self] text in
            let vc = UIStoryboard.setting[.other] as! OtherViewController
            vc.type = OtherType.Security
            self?.show(vc)
        }
    }

    override func localizationDidChange() {
        super.localizationDidChange()
        initPolicyLabel()
    }
    
    func initCityPicker(){
        /*
        cityTextField.didSelectItemAtIndex = {[weak self] index in
            guard let `self` = self, let cities = self.listCity else {return}
            let city = cities[index]
            self.citySelected = city
            self.getListDistrict(city.Id)
        }
        
        districtTextField.didSelectItemAtIndex = {[weak self] index in
            guard let `self` = self, let districts = self.listDistrict else {return}
            self.districtSelected = districts[index]
        }
 */
    }
    
    func setupGenderData(){
        genderTextField.listItem = genders.map{$0.description}
        genderTextField.didSelectItemAtIndex = { [weak self] index in
            guard let `self` = self else {return}
            self.genderSelected = self.genders[index]
        }
    }

    private func setupReCaptcha() {
        recaptcha = try! ReCaptcha(endpoint: endpoint, locale: locale)
        recaptcha.configureWebView { [weak self] webview in
            webview.frame = self?.view.bounds ?? CGRect.zero
            webview.tag = Constants.webViewTag
        }
    }

    private func captcha(completion: @escaping (String) -> Void) {
        recaptcha.rx.validate(on: view)
            .subscribe(onNext: { (token) in
                let webview = self.view.viewWithTag(Constants.webViewTag)
                webview?.removeFromSuperview()
                completion(token)
            }, onError: { (error) in
                print(error.localizedDescription)
                let webview = self.view.viewWithTag(Constants.webViewTag)
                webview?.removeFromSuperview()
                UIAlertController.showAlert(self, message: error.localizedDescription)
            }, onCompleted: {
                print("completed")
            }) {
                print("disposed")
            }.disposed(by: disposeBag)
    }
}

// Actions
extension RegisterViewController {
    @IBAction func acceptCheckboxPressed(_ sender: UIButton) {
        sender.isSelected = !sender.isSelected
//        btnRegister.isEnabled = sender.isSelected
    }

    @IBAction func createCardBoxPressed(_ sender: UIButton) {
        sender.isSelected = !sender.isSelected
    }

    @IBAction func registerButtonPressed(_ sender: UIButton) {
        view.endEditing(true)
        if acceptCheckbox.isSelected != true {
            self.showAlert(message: "Alert.UserNotAcceptTerm".localized)
            return
        }
        if self.validate(){
            self.captcha { [weak self] (token) in
                self?.register(token: token)
            }
        }
    }
}

extension RegisterViewController{
    private func validate() -> Bool{
        if nameTextField.text?.isEmpty != false {
            UIAlertController.showAlert(self, message: "Alert.NameNotEmpty".localized, handler: { _ in
//                self.firstNameTextField.becomeFirstResponder()
            })
            return false
        }

        /*
        if lastNameTextField.text?.isEmpty != false {
            UIAlertController.showAlert(self, message: "Alert.LastNameNotEmpty".localized, handler: { _ in
//                self.lastNameTextField.becomeFirstResponder()
            })
            return false
        } */
        
        if emailTextField.text?.isEmpty != false {
            UIAlertController.showAlert(self, message: "Alert.EmailNotEmpty".localized, handler: { _ in
//                self.emailTextField.becomeFirstResponder()
            })
            return false
        }

        if emailTextField.text?.isValidEmail() != true {
            UIAlertController.showAlert(self, message: "Alert.EmailInvalid".localized, handler: { _ in
//                self.emailTextField.becomeFirstResponder()
            })
            return false
        }
        
        if passwordTextField.text?.isEmpty != false {
            UIAlertController.showAlert(self, message: "Alert.PasswordNotEmpty".localized, handler: { _ in
//                self.passwordTextField.becomeFirstResponder()
            })
            return false
        }

        if passwordTextField.text?.isValidPassword() != true {
            UIAlertController.showAlert(self, message: "Alert.PasswordInvalid".localized, handler: { _ in
//                self.passwordTextField.becomeFirstResponder()
            })
            return false
        }

        if let text = passwordTextField.text, let repass = repasswordTextField.text, text != repass{
            UIAlertController.showAlert(self, message: "Alert.PasswordNotMatch".localized, handler: { _ in
//                self.repasswordTextField.becomeFirstResponder()
            })
            return false
        }
        
        if phoneTextField.text?.isEmpty != false {
            UIAlertController.showAlert(self, message: "Alert.PhoneNotEmpty".localized, handler: { _ in
//                self.phoneTextField.becomeFirstResponder()
            })
            return false
        }

        if phoneTextField.text?.removeAllSpaces().isPhone() != true {
            UIAlertController.showAlert(self, message: "Alert.PhoneInvalid".localized, handler: { _ in
//                self.phoneTextField.becomeFirstResponder()
            })
            return false
        }
        /*
        if cmndTextField.text?.isEmpty != false {
            UIAlertController.showAlert(self, message: "Alert.IDNotEmpty".localized, handler: { _ in
//                self.cmndTextField.becomeFirstResponder()
            })
            return false
        }

        if cmndTextField.text?.isValidPersonalId() != true {
            UIAlertController.showAlert(self, message: "Alert.IDInvalid".localized, handler: { _ in
//                self.cmndTextField.becomeFirstResponder()
            })
            return false
        } */
        
//        if birthdayTextField.text?.isEmpty != false {
//            UIAlertController.showAlert(self, message: "Alert.BithdayNotEmpty".localized, handler: { _ in
////                self.birthdayTextField.becomeFirstResponder()
//            })
//            return false
//        }

        if let birthdate = birthdayTextField.text?.toDate("dd/MM/yyyy"), Date().years(from: birthdate.date) < 12 {
            UIAlertController.showAlert(self, message: "Alert.BirthdayInvalid".localized, handler: { _ in
//                self.birthdayTextField.becomeFirstResponder()
            })
            return false
        }
        
        return true
    }
    
    private func register(token: String){
        let model = RegisterModel(name: nameTextField.text!, email: emailTextField.text!, password: passwordTextField.text!, phone: phoneTextField.text!.removeAllSpaces(), birthDay: birthdayTextField.getDate() ?? "", gender: self.genderSelected?.rawValue)
        model.token = token
        self.showLoading()
        Tracking.shared.authBegin(method: "sign-up")
        AccountProvider.rx.request(.register(model)).mapObject(DDKCResponse<UserModel>.self)
            
            .subscribe(onNext:{[weak self] response in
                guard let `self` = self else {return}
                self.dismissLoading()
                guard let user = response.Object else{
                    print("Data wrong")
                    self.showAlert(message: response.Message)
                    return
                }
                if response.isSuccess(){
                    Tracking.shared.authComplete(method: "email")
                    let vc = UIStoryboard.authen[.registerResult] as! RegisterResultViewController
                    vc.user = user
                    let popup = self.createPopup(vc, gestureDismissal: false)

                    let okButton = PopupDialogButton(title: "Bt.OK".localized) {
                        self.navigationController?.popViewController(animated: true)
                    }
                    
                    popup.addButton(okButton)
                    self.present(popup, animated: true, completion: nil)
//                    self.showAlert(message: "RegisterSuccess".localized) { _ in
//                        self.navigationController?.popViewController(animated: true)
//                    }
                }else{
                    UIAlertController.showAlert(self, message: response.Message ?? "")
                }
            }).disposed(by: disposeBag)
    }
    
    private func getListCity(){ /*
        self.showLoading()
        CityProvider.rx.request(.listCity).mapObject(DDKCResponse<CityModel>.self)
            
            .subscribe(onNext:{[weak self] response in
                self?.dismissLoading()
                guard let items = response.ListObject else {return}
                self?.cityTextField.listItem = items.map{$0.Name ?? ""}
                self?.listCity = items
            }).disposed(by: disposeBag) */
    }
    
    private func getListDistrict(_ cityId: String?){
        /*
        guard let id = cityId else {
            return
        }
        self.showLoading()
        CityProvider.rx.request(.listDistrict(id)).mapObject(DDKCResponse<CityModel>.self)
            
            .subscribe(onNext:{[weak self] response in
                self?.dismissLoading()
                guard let items = response.ListObject else {return}
                self?.districtTextField.listItem = items.map{$0.Name ?? ""}
                self?.listDistrict = items
            }).disposed(by: disposeBag) */
    }
}

extension RegisterViewController: UITextFieldDelegate {
    func textFieldShouldReturn(_ textField: UITextField) -> Bool {
        if textField == phoneTextField || textField == genderTextField {
            registerButtonPressed(btnRegister)
        } else {
            IQKeyboardManager.shared.goNext()
        }
        return true
    }
}


//
//  RegisterResultViewController.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 5/27/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

class RegisterResultViewController: BaseViewController {
    @IBOutlet weak var lbCardTitle: LocalizableLabel!
    @IBOutlet weak var lbCardNumber: UILabel!
    var user: UserModel?

    override func viewDidLoad() {
        super.viewDidLoad()

        localizableTitle = "Register.Title"

        if let card = user?.CardNumber {
            lbCardNumber.text = card
        } else {
            lbCardTitle.isHidden = true
            lbCardNumber.text = nil
        }

        // remove Register VC from navigation controller
        if var listVC = self.navigationController?.viewControllers {
            if let confirmIndex = listVC.index(where: { $0 is RegisterViewController }){
                listVC.remove(at: confirmIndex)
                self.navigationController?.viewControllers = listVC
            }
        }
    }

    override func didReceiveMemoryWarning() {
        super.didReceiveMemoryWarning()
        // Dispose of any resources that can be recreated.
    }
    

    /*
    // MARK: - Navigation

    // In a storyboard-based application, you will often want to do a little preparation before navigation
    override func prepare(for segue: UIStoryboardSegue, sender: Any?) {
        // Get the new view controller using segue.destinationViewController.
        // Pass the selected object to the new view controller.
    }
    */

}

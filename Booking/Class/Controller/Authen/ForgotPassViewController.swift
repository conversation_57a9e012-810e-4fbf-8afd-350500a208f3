//
//  ForgotPassViewController.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 3/10/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit

class ForgotPassViewController: BaseViewController {

    @IBOutlet weak var tfEmail: InputTextField!
    override func viewDidLoad() {
        super.viewDidLoad()

        localizableTitle = "ForgotPass.Title"

        tfEmail.becomeFirstResponder()
    }

    override func didReceiveMemoryWarning() {
        super.didReceiveMemoryWarning()
        // Dispose of any resources that can be recreated.
    }
    
    
    @IBAction func didTapSend(_ sender: Any) {
        if self.validate(){
            self.send()
        }
    }
    
    private func send(){
        self.showLoading()
        AccountProvider.rx.request(.forgotPass(tfEmail.text!)).mapObject(DDKCResponse<ConfirmModel>.self)
            
            .subscribe(onNext: {[weak self] (response) in
                guard let `self` = self else {return}
                self.dismissLoading()
                guard let confirm = response.Object else{
                    print("Data wrong")
                    self.flashError(title: "Alert.Error".localized, message: "Alert.ErrorServer".localized)
                    return
                }
                
                if let confirm = confirm.Result, confirm{
                    self.flashSuccess()
                    self.navigationController?.popViewController(animated: true)
                }else{
                    self.flashError(title: "Alert.Error".localized, message: response.Message ?? "Alert.ErrorServer".localized)
                }
            }).disposed(by: disposeBag)
    }
    
    private func validate() -> Bool{
        if let text = tfEmail.text, text.isEmpty{
            UIAlertController.showAlert(self, message: "Alert.EmailNotEmpty".localized)
            return false
        }
        
        if let text = tfEmail.text, !text.isValidEmail(){
            UIAlertController.showAlert(self, message: "Alert.EmailInvalid".localized)
            return false
        }
        
        return true
    }

}

extension ForgotPassViewController: UITextFieldDelegate {
    func textFieldShouldReturn(_ textField: UITextField) -> Bool {
        if textField == tfEmail {
            didTapSend(textField)
        }
        return true
    }
}

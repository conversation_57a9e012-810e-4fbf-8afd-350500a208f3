//
//  LoginViewController.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 3/10/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit
import FBSDKCoreKit
import FBSDKLoginKit
import IQKeyboardManagerSwift
import Re<PERSON><PERSON>tcha
import RxSwift
import AuthenticationServices

class LoginViewController: BaseViewController {
    @IBOutlet weak var tfEmail: InputTextField!
    @IBOutlet weak var tfPassword: InputTextField!
    
    @IBOutlet weak var btForgotPass: LocalizableButton!

    var showMemberVC: Bool = false

    private struct Constants {
        static let webViewTag = 123
    }

    private var recaptcha: ReCaptcha?

    private var locale: Locale = Locale.current
    private var endpoint = ReCaptcha.Endpoint.default
    
    override func viewDidLoad() {
        super.viewDidLoad()

        localizableTitle = "Login.Title"

        updateButtonTitles()
        setupReCaptcha()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        self.navigationController?.isNavigationBarHidden = false
    }

    func updateButtonTitles() {
        let attributedText = NSAttributedString(string: btForgotPass.localizableString?.localized ?? "", attributes: [.font: UIFont(fontName: .SourceSansPro, size: 16), .foregroundColor: UIColor.linkText, .underlineStyle: NSNumber(value: Int8(NSUnderlineStyle.styleSingle.rawValue))])
        btForgotPass.setAttributedTitle(attributedText, for: .normal)
    }

    override func didReceiveMemoryWarning() {
        super.didReceiveMemoryWarning()
    }

    override func localizationDidChange() {
        super.localizationDidChange()
        updateButtonTitles()
    }
    
    @IBAction func didTapLogin(_ sender: Any) {
        view.endEditing(true)

        if self.validate(){
//            captcha { [weak self] (token) in
//                guard let self = self else {
//                    return
//                }
//                self.resetCaptcha()
                self.login(email: self.tfEmail.text!, pass: self.tfPassword.text!, token: "")
//            }
        }
    }
    
    
    @IBAction func didTapLoginFB(_ sender: Any) {
      let loginManager = LoginManager()
      loginManager.logOut()
      loginManager.logIn(permissions: ["public_profile", "email"], from: self) {[weak self] result, error in
          if let error = error {
              self?.showAlert(message: "Alert.LoginFBFailed".localized)
          } else if let result = result, result.isCancelled {
              print("Cancelled")
          } else if let grantedPermissions = result?.grantedPermissions, let token = result?.token {
              guard grantedPermissions.contains("email") else {
                self?.showAlert(message: "Alert.LoginFBWithNoEmail".localized)
                return
              }
              self?.loginFB(token.tokenString, captchaToken: "")
          } else {
              // no-op
          }
      }
    }
    
    @IBAction func didTapLoginApple(_ sender: Any) {
        if #available(iOS 13.0, *) {
            self.showLoading()
            let appleIDProvider = ASAuthorizationAppleIDProvider()
            let request = appleIDProvider.createRequest()
            request.requestedScopes = [.fullName, .email]
            let authorizationController = ASAuthorizationController(authorizationRequests: [request])
            authorizationController.delegate = self
            authorizationController.presentationContextProvider = self
            authorizationController.performRequests()
        } else {
            self.showAlert(message: "Alert.LoginAppleUnsupported".localized)
        }
    }

    private func resetCaptcha() {
        recaptcha = nil
        setupReCaptcha()
    }

    private func setupReCaptcha() {
        recaptcha = try? ReCaptcha(endpoint: endpoint, locale: locale)
        recaptcha?.configureWebView { [weak self] webview in
            webview.frame = self?.view.bounds ?? CGRect.zero
            webview.tag = Constants.webViewTag
        }
    }

    private func captcha(completion: @escaping (String) -> Void) {

        recaptcha?.rx.validate(on: view)
            .subscribe(onNext: { (token) in
                let webview = self.view.viewWithTag(Constants.webViewTag)
                webview?.removeFromSuperview()
                completion(token)
            }, onError: { (error) in
                print(error.localizedDescription)
                let webview = self.view.viewWithTag(Constants.webViewTag)
                webview?.removeFromSuperview()
                UIAlertController.showAlert(self, message: error.localizedDescription)
            }, onCompleted: {
                print("completed")
            }) {
                print("disposed")
        }.disposed(by: disposeBag)
    }
    
}

@available(iOS 13.0, *)
extension LoginViewController: ASAuthorizationControllerDelegate, ASAuthorizationControllerPresentationContextProviding {
    func authorizationController(controller: ASAuthorizationController, didCompleteWithAuthorization authorization: ASAuthorization) {
        self.dismissLoading()
        if let appleIDCredential = authorization.credential as? ASAuthorizationAppleIDCredential {
            guard let appleIDToken = appleIDCredential.identityToken else {
                self.showAlert(message: "Alert.LoginAppleFailed".localized)
                return
            }
            guard let idTokenString = String(data: appleIDToken, encoding: .utf8) else {
                self.showAlert(message: "Alert.LoginAppleFailed".localized)
                return
            }
            let userFirstName = appleIDCredential.fullName?.givenName ?? ""
            let userLastName = appleIDCredential.fullName?.familyName ?? ""
            let userEmail = appleIDCredential.email ?? ""
            self.loginApple(idTokenString, fullName: "\(userFirstName) \(userLastName)", email: userEmail, captchaToken: "")
        }
    }
    
    func authorizationController(controller: ASAuthorizationController, didCompleteWithError error: Error) {
        self.dismissLoading()
        self.showAlert(message: "Alert.LoginAppleFailed".localized)
    }
    
    func presentationAnchor(for controller: ASAuthorizationController) -> ASPresentationAnchor {
        self.dismissLoading()
        return self.view.window!
    }
}

extension LoginViewController{
    private func validate() -> Bool{
        if let text = tfEmail.text, text.isEmpty{
            UIAlertController.showAlert(self, message: "Alert.EmailNotEmpty".localized)
            return false
        }
        
        if let text = tfPassword.text, text.isEmpty{
            UIAlertController.showAlert(self, message: "Alert.PasswordNotEmpty".localized)
            return false
        }
        
        return true
    }

    func openUpdateView() {
        let vc = UIStoryboard.authen[.updatePass]
        self.navigationController?.pushViewController(vc, animated: true)
    }

    private func storeAccount(email: String?, pass: String?) {
        let userDefault = UserDefaults.standard
        userDefault.set(email, forKey: DefaultKey.userName.rawValue)
        userDefault.set(pass, forKey: DefaultKey.password.rawValue)
        userDefault.synchronize()
    }
    
    private func loginFB(_ token: String, captchaToken: String){
        self.showLoading()
        Tracking.shared.authBegin(method: "facebook")
        AccountProvider.rx.request(.loginFB(token, captchaToken)).mapObject(DDKCResponse<UserModel>.self)
            .subscribe(onNext:{[weak self] response in
                guard let object = response.Object else{
                    self?.dismissLoading()
                    self?.showAlert(message: response.Message)
                    print("Data wrong")
                    return
                }
                if response.isSuccess(){
                    self?.accountDeleteValidate(user: object, handler: {
                        print("Token: \(object.Token ?? "")")
                        Global.shared.user = object
                        self?.getProfile(id: object.UserId)
                        Tracking.shared.authComplete(method: "facebook")
                    })
                } else {
                    self?.showAlert(message: "Alert.LoginFailed".localized)
                }
                
                }, onError: { [weak self] _ in
                    self?.showAlert(message: "Alert.LoginFailed".localized)
                    self?.dismissLoading()
            }).disposed(by: disposeBag)
    }
    
    private func loginApple(_ token: String, fullName: String, email: String, captchaToken: String){
        self.showLoading()
        Tracking.shared.authBegin(method: "apple")
        AccountProvider.rx.request(.loginApple(token, fullName, email, captchaToken)).mapObject(DDKCResponse<UserModel>.self)
            .subscribe(onNext:{[weak self] response in
                guard let object = response.Object else{
                    self?.dismissLoading()
                    self?.showAlert(message: response.Message)
                    print("Data wrong")
                    return
                }
                if response.isSuccess(){
                    self?.accountDeleteValidate(user: object, handler: {
                        print("Token: \(object.Token ?? "")")
                        Global.shared.user = object
                        self?.getProfile(id: object.UserId)
                        Tracking.shared.authComplete(method: "apple")
                    })
                } else {
                    self?.showAlert(message: "Alert.LoginFailed".localized)
                }
                
                }, onError: { [weak self] _ in
                    self?.showAlert(message: "Alert.LoginFailed".localized)
                    self?.dismissLoading()
            }).disposed(by: disposeBag)
    }
    
    private func login(email: String, pass: String, token: String){
        self.showLoading()
        Tracking.shared.authBegin(method: "email")
        let model = LoginModel(userName: email, password: pass, token: token)
        AccountProvider.rx.request(.login(model)).mapObject(DDKCResponse<UserModel>.self)
            .subscribe(onNext:{[weak self] response in
                guard let object = response.Object else{
                    self?.dismissLoading()
                    self?.showAlert(message: response.Message)
                    print("Data wrong")
                    return
                }
                if response.isSuccess(){
                    self?.accountDeleteValidate(user: object, handler: {
                        print("Token: \(object.Token ?? "")")
                        Global.shared.user = object
                        self?.storeAccount(email: email, pass: pass)
                        self?.getProfile(id: object.UserId, email: email)
                        Tracking.shared.authComplete(method: "email")
                    })
                } else {
                    self?.dismissLoading()
                    self?.showAlert(message: "Alert.LoginFailed".localized)
                }
                
                }, onError: { [weak self] _ in
                    self?.showAlert(message: "Alert.LoginFailed".localized)
                    self?.dismissLoading()
            }).disposed(by: disposeBag)
    }
    
    private func accountDeleteValidate(user: UserModel, handler: ()->Void){
        let preferences = UserDefaults.standard
        let currentKey = "delete_account"
        let currentValue =  preferences.string(forKey: currentKey)
        if let userId = user.UserId, currentValue == userId {
            UIAlertController.showAlert(self,message: "Alert.LoginDeleteAccount".localized)
            self.dismissLoading()
        }else{
            handler()
        }
    }
    
    private func getProfile(id: String?, email: String? = nil){
        guard let userId = id else {
            self.dismissLoading()
            self.flashError(title: "Alert.Error".localized, message: "Alert.LoginFailed".localized)
            return
        }
        self.registerDeviceToken(userId)
        AccountProvider.rx.request(.getProfileById(userId)).mapObject(DDKCResponse<UserModel>.self)
            
            .subscribe(onNext:{ [weak self] response in
                self?.dismissLoading()
                guard let object = response.Object else{
                    print("Data wrong")
                    self?.showAlert(message: "Alert.LoginFailed".localized)
                    return
                }
                if response.isSuccess(){
                    print("Name: \(object.FullName ?? "")")
                    if let user = Global.shared.user{
                        let fullUser = object
                        fullUser.Token = user.Token
                        fullUser.UserId = user.UserId
                        Global.shared.saveUser(fullUser)
                    }
                    if Global.shared.user?.IsUpdatedFacebookPassword == false ||
                        Global.shared.user?.IsUpdatedApplePassword == false {
                        self?.openUpdateView()
                        return
                    }
                    if self?.navigationController?.viewControllers.count == 1 {
                        let slideMenu = self?.slideMenuVC
                        if self?.showMemberVC == true {
                            slideMenu?.openView(.member, shouldSelect: true)
                        } else {
                            slideMenu?.openView(.home, shouldSelect: true)
                        }
                    } else {
                        self?.navigationController?.popViewController(animated: true)
                    }
                } else {
                    self?.showAlert(message: "Alert.LoginFailed".localized)
                }
                
            }, onError: { [weak self] _ in
                self?.showAlert(message: "Alert.LoginFailed".localized)
                self?.dismissLoading()
            }).disposed(by: disposeBag)
    }
 
    private func registerDeviceToken(_ accountId: String) {
        guard let token: String = UserDefaults.standard.object(forKey: DefaultKey.deviceToken.rawValue) as? String,
        let deviceId = UIDevice.current.identifierForVendor?.uuidString else {
            return
        }
        AccountProvider.rx.request(.registerDeviceToken(deviceId, accountId, token)).mapObject(DDKCResponse<RegisterDeviceToken>.self)
        .subscribe().disposed(by: disposeBag)
    }
}

extension LoginViewController: UITextFieldDelegate {
    func textFieldShouldReturn(_ textField: UITextField) -> Bool {
        if textField == tfPassword {
            didTapLogin(textField)
        } else {
            IQKeyboardManager.shared.goNext()
        }
        return true
    }
}

//
//  MyTabbarButton.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 9/20/19.
//  Copyright © 2019 ddkc. All rights reserved.
//

import Foundation
import UIKit

class MyTabBarItem: UIButton {
    var itemHeight: CGFloat = 0
    var lock = false

    private let iconImageView = MyUIMaker.makeImageView(contentMode: .scaleAspectFit)
    private let textLabel = MyUIMaker.makeLabel(font: UIFont.init(fontName: .SourceSansPro, style: .Bold, size: 10),
                                                color: .black, numberOfLines: 2, alignment: .center)
    private var icon: UIImage!
    private var selectedIcon: UIImage!
    private var title: String!

    convenience init(icon: UIImage, selectedIcon: UIImage, title: String) {
        self.init()
        translatesAutoresizingMaskIntoConstraints = false
        self.icon = icon
        self.selectedIcon = selectedIcon
        self.title = title
        iconImageView.image = icon
        textLabel.text = title.localized
        textLabel.font = UIFont.init(fontName: .SourceSansPro, style: .Bold, size: 10)
        textLabel.textColor = 0x223849.toColor.withAlphaComponent(0.55)
        setupView()
    }

    private func setupView() {
        addSubviews(views: iconImageView, textLabel)
        iconImageView.top(toView: self, space: 4)
        iconImageView.centerX(toView: self)
        iconImageView.width(22)
        iconImageView.square()

        let labelTopConstant: CGFloat = (textLabel.text?.contains("\n") ?? false) ? 24 : 28
        textLabel.top(toView: iconImageView, space: labelTopConstant)
        textLabel.centerX(toView: self)
    }

    func update(_ isSelected: Bool) {
        if isSelected {
            iconImageView.image = selectedIcon
            textLabel.textColor = 0x03599d.toColor
        } else {
            iconImageView.image = icon
            textLabel.textColor = 0x223849.toColor.withAlphaComponent(0.55)
        }
    }

    func reload() {
        textLabel.text = title.localized
    }
}


class MyTabBar: UITabBar {
    var My_items = [MyTabBarItem]()
    convenience init(items: [MyTabBarItem]) {
        self.init()
        My_items = items
        translatesAutoresizingMaskIntoConstraints = false
        setupView()
    }

    func setupView() {
        backgroundColor = .white
        if My_items.count == 0 { return }

        let line = UIView()
        line.translatesAutoresizingMaskIntoConstraints = false
        line.backgroundColor = .lightGray
        line.height(0.5)

        addSubviews(views: line)
        line.horizontal(toView: self)
        line.top(toView: self)

        var horizontalConstraints = "H:|"
        let itemWidth: CGFloat = screenWidth / CGFloat(My_items.count)
        for i in 0 ..< My_items.count {
            let item = My_items[i]
            addSubviews(views: item)
            if item.itemHeight == 0 {
                item.vertical(toView: self)
            }
            else {
                item.bottom(toView: self)
                item.height(item.itemHeight)
            }
            item.width(itemWidth)
            horizontalConstraints += String(format: "[v%d]", i)

        }

        horizontalConstraints += "|"
        addConstraints(withFormat: horizontalConstraints, arrayOf: My_items)
    }

    func reload() {
        for item in My_items {
            item.reload()
        }
    }
}

class MyTabController: UITabBarController {
    var My_tabBar: MyTabBar!

    private var My_tabBarHeight: CGFloat = 60

    override func viewDidLoad() {
        super.viewDidLoad()
        tabBar.isHidden = true
        setupView()
    }

    func setupView() {}

    func setTabBar(items: [MyTabBarItem], height: CGFloat = 60) {
        guard items.count > 0 else { return }

        My_tabBar = MyTabBar(items: items)
        guard let bar = My_tabBar else { return }
        bar.My_items.first?.update(true)

        view.addSubviews(views: bar)
        bar.horizontal(toView: view)
        bar.bottomSafe(toView: view)
        My_tabBarHeight = height
        bar.height(My_tabBarHeight)
        for i in 0 ..< items.count {
            items[i].tag = i
            items[i].addTarget(self, action: #selector(switchTab), for: .touchUpInside)
        }
    }

    @objc func switchTab(button: UIButton) {
        let newIndex = button.tag
        changeTab(from: selectedIndex, to: newIndex)
    }

    private func changeTab(from fromIndex: Int, to toIndex: Int) {
        My_tabBar.My_items[fromIndex].update(false)
        My_tabBar.My_items[toIndex].update(true)
        selectedIndex = toIndex
        tabBar.isHidden = true
        My_tabBar.isHidden = false
    }

    func changeTab(_ index: Int) {
      if selectedIndex == index {
        if let nav = viewControllers?[index] as? UINavigationController {
          nav.popToRootViewController(animated: false)
        }
      }
        changeTab(from: selectedIndex, to: index)
    }
}

//
//  MainTabContainer.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 8/2/19.
//  Copyright © 2019 ddkc. All rights reserved.
//

import Foundation
import UIKit
import RxSwift

final class MainTabContainer: MyTabController {
    let disposeBag = DisposeBag()

    deinit {
        NotificationCenter.default.removeObserver(self)
    }

    override func viewDidLoad() {
        super.viewDidLoad()

        getAppParams()

        NotificationCenter.default.addObserver(self, selector: #selector(localizationDidChange), name: .ChangeLocalization, object: nil)
    }

    @objc func localizationDidChange(){
        My_tabBar.reload()
    }

    private func getAppParams() {
        AccountProvider.rx.request(.appParams).mapObject(DDKCResponse<AppParams>.self).subscribe(onNext: { (response) in
            if response.isSuccess() {
                Global.shared.appParams = response.ListObject
                if !UserDefaults.standard.bool(forKey: Global.shared.appVersion?.key ?? "") {
                    self.checkUpdateStatus()
                }
            }
        }).disposed(by: disposeBag)
    }

    private func checkUpdateStatus() {
        guard let version = Global.shared.appVersion, version.status == true else {
            return
        }
        let appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String
        if appVersion == version.value {
            return
        }

        guard let message = version.message, let highlight = version.messageHighLight else {
            return
        }

        let alert = UIAlertController(title: nil, message: "", preferredStyle: .alert)

        let contents = message.replacingOccurrences(of: highlight, with: "\(highlight)#")

        let strings = contents.components(separatedBy: "#")
        let attributedString = NSMutableAttributedString()

        attributedString.string(strings[0], font: UIFont(fontName: .SourceSansPro, style: .Bold, size: 16))
        attributedString.string(strings[1], font: UIFont(fontName: .SourceSansPro, style: .Regular, size: 16))

        alert.setValue(attributedString, forKey: "attributedMessage")

        if version.canClose == true {
            alert.addAction(UIAlertAction(title: "Bt.Cancel".localized, style: .cancel))
            UserDefaults.standard.set(true, forKey: version.key)
        }

        let okAction = UIAlertAction(title: "Bt.Yes".localized, style: .default, handler: {_ in
            self.gotoAppStore()
        })
        alert.addAction(okAction)
        present(alert, animated: true, completion: nil)

    }

    private func gotoAppStore() {
        if let url = URL(string: "itms-apps://itunes.apple.com/app/id1403107666"),
            UIApplication.shared.canOpenURL(url)
        {
            if #available(iOS 10.0, *) {
                UIApplication.shared.open(url, options: [:], completionHandler: nil)
            } else {
                UIApplication.shared.openURL(url)
            }
        }
    }

    override func setupView() {
        let tabItem1 = MyTabBarItem(icon: UIImage(named: "tab1")!, selectedIcon: UIImage(named: "tab1_selected")!, title:  "Tab1")
        let tabItem2 = MyTabBarItem(icon: UIImage(named: "tab2")!, selectedIcon: UIImage(named: "tab2_selected")!, title:  "Tab2")
        let tabItem3 = MyTabBarItem(icon: UIImage(named: "tab3")!, selectedIcon: UIImage(named: "tab3_selected")!, title:  "Tab3")
        let tabItem4 = MyTabBarItem(icon: UIImage(named: "tab4")!, selectedIcon: UIImage(named: "tab4_selected")!, title:  "Tab4")
        let tabItem5 = MyTabBarItem(icon: UIImage(named: "tab5")!, selectedIcon: UIImage(named: "tab5_selected")!, title:  "Tab5")

        setTabBar(items: [tabItem1, tabItem2, tabItem3, tabItem4, tabItem5])

        let homeVC = NewHomeViewController()
        let tab1 = BaseNavigationViewController(rootViewController: homeVC)


        let listCinemaVC = CinemasViewController()
        listCinemaVC.isBooking = true
        let tab2 = BaseNavigationViewController(rootViewController: listCinemaVC)


        let voucherVC = MyVoucherViewController()
        let tab3 = BaseNavigationViewController(rootViewController: voucherVC)


        let newAndDealsVC = UIStoryboard.home[.newsAndDeals] as! NewsAndDealsViewController
        let tab4 = BaseNavigationViewController(rootViewController: newAndDealsVC)


        let tab5 = BaseNavigationViewController(rootViewController: TabOtherViewController())
        

        self.viewControllers = [tab1, tab2, tab3, tab4, tab5]
    }

    private func initViews() {
        let homeVC = NewHomeViewController()
        let tab1 = BaseNavigationViewController(rootViewController: homeVC)
        tab1.tabBarItem.image = UIImage(named: "tab1")
        tab1.tabBarItem.selectedImage = UIImage(named: "tab1_selected")
        tab1.tabBarItem.title = "Tab1".localized

        let viewTabBar = tab1.tabBarItem.value(forKey: "view") as? UIView
        let label = viewTabBar?.subviews[1] as? UILabel
        label?.numberOfLines = 0

        let listCinemaVC = CinemasViewController()
        listCinemaVC.isBooking = true
        let tab2 = BaseNavigationViewController(rootViewController: listCinemaVC)
        tab2.tabBarItem.image = UIImage(named: "tab2")
        tab2.tabBarItem.selectedImage = UIImage(named: "tab2_selected")
        tab2.tabBarItem.title = "Tab2".localized

        let voucherVC = MyVoucherViewController()
        let tab3 = BaseNavigationViewController(rootViewController: voucherVC)
        tab3.tabBarItem.image = UIImage(named: "tab3")
        tab3.tabBarItem.selectedImage = UIImage(named: "tab3_selected")
        tab3.tabBarItem.title = "Tab3".localized

        let newAndDealsVC = UIStoryboard.home[.newsAndDeals] as! NewsAndDealsViewController
        let tab4 = BaseNavigationViewController(rootViewController: newAndDealsVC)
        tab4.tabBarItem.image = UIImage(named: "tab4")
        tab4.tabBarItem.selectedImage = UIImage(named: "tab4_selected")
        tab4.tabBarItem.title = "Tab4".localized

        let tab5 = BaseNavigationViewController(rootViewController: TabOtherViewController())
        tab5.tabBarItem.image = UIImage(named: "tab5")
        tab5.tabBarItem.selectedImage = UIImage(named: "tab5_selected")
        tab5.tabBarItem.title = "Tab5".localized

        self.viewControllers = [tab1, tab2, tab3, tab4, tab5]
    }

}

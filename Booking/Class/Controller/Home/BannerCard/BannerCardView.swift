//
//  BannerCardView.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 4/4/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import AlamofireImage

class BannerCardView: UIView {
    @IBOutlet weak var imvAgeOption: UIImageView!
    @IBOutlet weak var imvTop: UIImageView!
    @IBOutlet weak var lbName: UILabel!
    @IBOutlet weak var lbType: UILabel!
    @IBOutlet weak var imvBG: UIImageView!
    @IBOutlet weak var lbFormat: UILabel!
    @IBOutlet weak var btPlay: UIButton!

    var onPlayButtonPressed: (() -> Void)?
    
    
    static func nib() -> BannerCardView {
        return Bundle.main.loadNibNamed(self.className, owner: self, options: nil)?.first as! BannerCardView
    }

    override func awakeFromNib() {
        super.awakeFromNib()

//        btPlay.isEnabled = false
        btPlay.addTarget(self, action: #selector(playButtonPressed(_:)), for: .touchUpInside)
    }
    
    func fillData(film: FilmModel){
        
        lbName.text = film.getName()
        lbFormat.text = film.getFormatName()
        lbType.text = "\(film.getFilmGenre() ?? "") | \(film.Duration ?? 0) \("Home.Minute".localized)"
        
        let imageURL = Config.BaseURLResource + (film.MainPosterUrl ?? "")
        if let url = URL(string: imageURL) {
            imvBG.af_setImage(withURL: url, placeholderImage: #imageLiteral(resourceName: "bg1.png"))
        }

        if film.FilmRestrictAgeName == FilmModel.RestrictAge.c13 {
            imvAgeOption.image = #imageLiteral(resourceName: "ic_c13")
        } else if film.FilmRestrictAgeName == FilmModel.RestrictAge.c16 {
            imvAgeOption.image = #imageLiteral(resourceName: "ic_c16")
        } else if film.FilmRestrictAgeName == FilmModel.RestrictAge.c18 {
            imvAgeOption.image = #imageLiteral(resourceName: "ic_c18")
        } else if film.FilmRestrictAgeName == FilmModel.RestrictAge.p {
            imvAgeOption.image = #imageLiteral(resourceName: "ic_p")
        } else {
            imvAgeOption.image = nil
        }

    }

    func fitIn(width: CGFloat) {
        var frame = self.frame
        frame.size.width = width
        self.frame = frame
        self.autoFitSize()
    }

    @IBAction func playButtonPressed(_ sender: Any) {
        onPlayButtonPressed?()
    }
}

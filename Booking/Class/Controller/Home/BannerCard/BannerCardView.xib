<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14113" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14088"/>
        <capability name="Aspect ratio constraints" minToolsVersion="5.1"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Oswald-Bold.ttf">
            <string>Oswald-Bold</string>
        </array>
        <array key="Oswald-Regular.ttf">
            <string>Oswald-Regular</string>
        </array>
        <array key="SourceSansPro-Regular.ttf">
            <string>SourceSansPro-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <view contentMode="scaleToFill" id="iN0-l3-epB" customClass="BannerCardView" customModule="Booking_dev" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="272" height="416"/>
            <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
            <subviews>
                <view clipsSubviews="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="fUr-du-CDS" customClass="RoundView" customModule="Booking_dev" customModuleProvider="target">
                    <rect key="frame" x="0.0" y="0.0" width="272" height="416"/>
                    <subviews>
                        <imageView userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="bg1.png" translatesAutoresizingMaskIntoConstraints="NO" id="VRJ-nu-iWj">
                            <rect key="frame" x="0.0" y="0.0" width="272" height="416"/>
                        </imageView>
                        <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_c16" translatesAutoresizingMaskIntoConstraints="NO" id="BEK-0M-pxP">
                            <rect key="frame" x="8" y="9" width="56" height="27"/>
                            <constraints>
                                <constraint firstAttribute="width" constant="56" id="Vq7-Gh-1qn"/>
                                <constraint firstAttribute="height" constant="27" id="bbc-Dt-Mh8"/>
                            </constraints>
                        </imageView>
                        <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_top_1" translatesAutoresizingMaskIntoConstraints="NO" id="lzc-q9-vZg">
                            <rect key="frame" x="216" y="8" width="48" height="73"/>
                            <constraints>
                                <constraint firstAttribute="height" constant="73" id="34m-Mw-Kh1"/>
                                <constraint firstAttribute="width" constant="48" id="gXI-tq-kK1"/>
                            </constraints>
                        </imageView>
                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="aiK-uh-ZCX" customClass="RoundButton" customModule="Booking_dev" customModuleProvider="target">
                            <rect key="frame" x="106" y="178" width="60" height="60"/>
                            <constraints>
                                <constraint firstAttribute="width" secondItem="aiK-uh-ZCX" secondAttribute="height" id="iYB-km-C5k"/>
                                <constraint firstAttribute="width" constant="60" id="yw4-BT-UP1"/>
                            </constraints>
                            <state key="normal" image="ic_play"/>
                        </button>
                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Wla-7u-0LK" customClass="GradientView" customModule="Booking_dev" customModuleProvider="target">
                            <rect key="frame" x="0.0" y="296.5" width="272" height="119.5"/>
                            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <userDefinedRuntimeAttributes>
                                <userDefinedRuntimeAttribute type="point" keyPath="startPoint">
                                    <point key="value" x="0.5" y="0.0"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="point" keyPath="endPoint">
                                    <point key="value" x="0.5" y="1"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="color" keyPath="firstColor">
                                    <color key="value" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </userDefinedRuntimeAttribute>
                                <userDefinedRuntimeAttribute type="color" keyPath="secondColor">
                                    <color key="value" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                </userDefinedRuntimeAttribute>
                            </userDefinedRuntimeAttributes>
                        </view>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="252" verticalHuggingPriority="251" text="STAR WARS 8: THE LAST JEDI" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Qu9-2N-Unn">
                            <rect key="frame" x="8" y="326.5" width="155" height="59.5"/>
                            <fontDescription key="fontDescription" name="Oswald-Bold" family="Oswald" pointSize="20"/>
                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="2" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="DZC-OJ-sH5">
                            <rect key="frame" x="8" y="387.5" width="256" height="20.5"/>
                            <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <nil key="highlightedColor"/>
                        </label>
                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" horizontalCompressionResistancePriority="751" text="(2D - PD)" textAlignment="right" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="32K-y9-Df6">
                            <rect key="frame" x="171" y="343.5" width="93" height="27"/>
                            <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="18"/>
                            <color key="textColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            <nil key="highlightedColor"/>
                        </label>
                    </subviews>
                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    <constraints>
                        <constraint firstAttribute="bottom" secondItem="DZC-OJ-sH5" secondAttribute="bottom" constant="8" id="3nN-Tk-15Y"/>
                        <constraint firstAttribute="trailing" secondItem="32K-y9-Df6" secondAttribute="trailing" constant="8" id="8A1-jN-HHJ"/>
                        <constraint firstItem="DZC-OJ-sH5" firstAttribute="leading" secondItem="fUr-du-CDS" secondAttribute="leading" constant="8" id="ALA-VE-dPl"/>
                        <constraint firstAttribute="trailing" secondItem="Wla-7u-0LK" secondAttribute="trailing" id="BQF-v4-vbx"/>
                        <constraint firstItem="VRJ-nu-iWj" firstAttribute="leading" secondItem="fUr-du-CDS" secondAttribute="leading" id="BnT-ig-Iqg"/>
                        <constraint firstItem="aiK-uh-ZCX" firstAttribute="centerY" secondItem="fUr-du-CDS" secondAttribute="centerY" id="FgH-GV-kh7"/>
                        <constraint firstAttribute="bottom" secondItem="Wla-7u-0LK" secondAttribute="bottom" id="GNw-e3-iEx"/>
                        <constraint firstItem="BEK-0M-pxP" firstAttribute="leading" secondItem="fUr-du-CDS" secondAttribute="leading" constant="8" id="IGt-Yd-2na"/>
                        <constraint firstAttribute="trailing" secondItem="VRJ-nu-iWj" secondAttribute="trailing" id="Kxj-Lv-FXD"/>
                        <constraint firstItem="VRJ-nu-iWj" firstAttribute="centerY" secondItem="fUr-du-CDS" secondAttribute="centerY" id="Lnn-r5-11a"/>
                        <constraint firstAttribute="trailing" secondItem="DZC-OJ-sH5" secondAttribute="trailing" constant="8" id="MbS-mV-1ip"/>
                        <constraint firstItem="32K-y9-Df6" firstAttribute="leading" secondItem="Qu9-2N-Unn" secondAttribute="trailing" constant="8" id="Mzn-yt-cqJ"/>
                        <constraint firstAttribute="trailing" secondItem="lzc-q9-vZg" secondAttribute="trailing" constant="8" id="RQW-zq-emc"/>
                        <constraint firstItem="aiK-uh-ZCX" firstAttribute="centerX" secondItem="fUr-du-CDS" secondAttribute="centerX" id="S6t-0l-pDW"/>
                        <constraint firstItem="32K-y9-Df6" firstAttribute="centerY" secondItem="Qu9-2N-Unn" secondAttribute="centerY" id="TNQ-3F-LeI"/>
                        <constraint firstItem="Qu9-2N-Unn" firstAttribute="leading" secondItem="fUr-du-CDS" secondAttribute="leading" constant="8" id="Yxh-ZD-DbL"/>
                        <constraint firstItem="BEK-0M-pxP" firstAttribute="top" secondItem="fUr-du-CDS" secondAttribute="top" constant="9" id="auE-PA-mOA"/>
                        <constraint firstItem="Qu9-2N-Unn" firstAttribute="top" secondItem="Wla-7u-0LK" secondAttribute="top" constant="30" id="edN-ef-M9x"/>
                        <constraint firstItem="lzc-q9-vZg" firstAttribute="top" secondItem="fUr-du-CDS" secondAttribute="top" constant="8" id="fil-0D-oPc"/>
                        <constraint firstAttribute="width" secondItem="fUr-du-CDS" secondAttribute="height" multiplier="272:416" priority="999" id="kJ9-P0-S4S"/>
                        <constraint firstItem="DZC-OJ-sH5" firstAttribute="top" secondItem="Qu9-2N-Unn" secondAttribute="bottom" constant="2" id="kyc-WH-lG9"/>
                        <constraint firstItem="Wla-7u-0LK" firstAttribute="leading" secondItem="fUr-du-CDS" secondAttribute="leading" id="pp8-YD-jx1"/>
                        <constraint firstItem="VRJ-nu-iWj" firstAttribute="top" secondItem="fUr-du-CDS" secondAttribute="top" id="sD0-yX-6aG"/>
                    </constraints>
                    <userDefinedRuntimeAttributes>
                        <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                            <real key="value" value="8"/>
                        </userDefinedRuntimeAttribute>
                        <userDefinedRuntimeAttribute type="number" keyPath="shadowRadius">
                            <real key="value" value="8"/>
                        </userDefinedRuntimeAttribute>
                        <userDefinedRuntimeAttribute type="point" keyPath="shadowOffset">
                            <point key="value" x="0.0" y="6"/>
                        </userDefinedRuntimeAttribute>
                        <userDefinedRuntimeAttribute type="number" keyPath="shadowOpacity">
                            <real key="value" value="0.29999999999999999"/>
                        </userDefinedRuntimeAttribute>
                        <userDefinedRuntimeAttribute type="color" keyPath="shadowColor">
                            <color key="value" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        </userDefinedRuntimeAttribute>
                    </userDefinedRuntimeAttributes>
                </view>
            </subviews>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <constraints>
                <constraint firstAttribute="trailing" secondItem="fUr-du-CDS" secondAttribute="trailing" id="4XR-mI-U2Q"/>
                <constraint firstItem="fUr-du-CDS" firstAttribute="leading" secondItem="iN0-l3-epB" secondAttribute="leading" id="KaP-Si-983"/>
                <constraint firstItem="fUr-du-CDS" firstAttribute="top" secondItem="iN0-l3-epB" secondAttribute="top" id="YlE-iq-J4g"/>
                <constraint firstAttribute="bottom" secondItem="fUr-du-CDS" secondAttribute="bottom" id="cyK-dY-kRE"/>
            </constraints>
            <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
            <connections>
                <outlet property="btPlay" destination="aiK-uh-ZCX" id="eMT-Sq-qTp"/>
                <outlet property="imvAgeOption" destination="BEK-0M-pxP" id="7w7-IB-OS0"/>
                <outlet property="imvBG" destination="VRJ-nu-iWj" id="c4P-rd-bYu"/>
                <outlet property="imvTop" destination="lzc-q9-vZg" id="Iph-qG-jaE"/>
                <outlet property="lbFormat" destination="32K-y9-Df6" id="bRG-La-cNn"/>
                <outlet property="lbName" destination="Qu9-2N-Unn" id="Jst-Tl-4dO"/>
                <outlet property="lbType" destination="DZC-OJ-sH5" id="Yvd-yW-hWA"/>
            </connections>
            <point key="canvasLocation" x="24" y="-10"/>
        </view>
    </objects>
    <resources>
        <image name="bg1.png" width="320" height="568"/>
        <image name="ic_c16" width="56" height="27"/>
        <image name="ic_play" width="60" height="60"/>
        <image name="ic_top_1" width="48" height="73"/>
    </resources>
</document>

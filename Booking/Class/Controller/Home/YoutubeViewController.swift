//
//  YoutubeViewController.swift
//  Booking-dev
//
//  Created by <PERSON><PERSON> on 5/25/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit
import youtube_ios_player_helper

class YoutubeViewController: BaseViewController {
    @IBOutlet weak var youtubePlayer: YTPlayerView!

    var videoURL: String?

    override func viewDidLoad() {
        super.viewDidLoad()

        // Do any additional setup after loading the view.
        youtubePlayer.delegate = self
        youtubePlayer.webView?.backgroundColor = .black
        loadVideo()
    }

    func loadVideo() {
        youtubePlayer.isHidden = true
        guard let videoURL = videoURL else {
            self.showAlert(message: "Alert.NoFilmTrailerURL".localized)
            return
        }
        if videoURL.isYoutubeURL {
            let queryItems = URLComponents(string: videoURL)?.queryItems
            if let id = queryItems?.filter({$0.name == "v"}).first?.value {
                self.showLoading()
                self.youtubePlayer.load(withVideoId: id, playerVars: ["playsinline": 0])
                return
            }
        }
        self.showAlert(message: "Alert.FilmTrailerLoadError".localized)
    }
    @IBAction func closeButtonPressed(_ sender: Any) {
        dismiss(animated: true, completion: nil)
    }
}

extension YoutubeViewController: YTPlayerViewDelegate {
    func playerViewDidBecomeReady(_ playerView: YTPlayerView) {
        playerView.playVideo()
        self.dismissLoading()
        playerView.isHidden = false
    }

    func playerView(_ playerView: YTPlayerView, receivedError error: YTPlayerError) {
        self.showAlert(message: "Alert.FilmTrailerLoadError".localized)
    }

    func playerViewPreferredWebViewBackgroundColor(_ playerView: YTPlayerView) -> UIColor {
        return .black
    }
}

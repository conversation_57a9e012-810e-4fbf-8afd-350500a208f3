<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.Storyboard.XIB" version="3.0" toolsVersion="32700.99.1234" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina5_9" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="22684"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Oswald-Bold.ttf">
            <string>Oswald-Bold</string>
        </array>
        <array key="Oswald-ExtraLight.ttf">
            <string>Oswald-ExtraLight</string>
        </array>
        <array key="Oswald-Regular.ttf">
            <string>Oswald-Regular</string>
        </array>
        <array key="SourceSansPro-Regular.ttf">
            <string>SourceSansPro-Regular</string>
        </array>
    </customFonts>
    <scenes>
        <!--HomeViewController-->
        <scene sceneID="Tbq-SO-gEn">
            <objects>
                <viewController storyboardIdentifier="HomeViewController" id="DwD-6Y-uMb" userLabel="HomeViewController" customClass="HomeViewController" customModule="Beta_Cinemas" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="dLe-zI-yju">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="1506"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="MFB-jB-eyR" customClass="GradientImageView" customModule="Beta_Cinemas" customModuleProvider="target">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="50"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="wwX-cy-Se2" customClass="ScrollPager" customModule="Beta_Cinemas" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="10" width="375" height="40"/>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="color" keyPath="textColor">
                                                <color key="value" red="0.72549019609999998" green="0.72549019609999998" blue="0.72549019609999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="number" keyPath="indicatorWidth">
                                                <real key="value" value="21"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="boolean" keyPath="indicatorSizeMatchesTitle" value="NO"/>
                                            <userDefinedRuntimeAttribute type="boolean" keyPath="indicatorIsAtBottom" value="YES"/>
                                            <userDefinedRuntimeAttribute type="color" keyPath="selectedTextColor">
                                                <color key="value" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="color" keyPath="indicatorColor">
                                                <color key="value" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <outlet property="delegate" destination="DwD-6Y-uMb" id="DW9-em-3VT"/>
                                        </connections>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <color key="tintColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="50" id="01E-CB-Of1"/>
                                    <constraint firstAttribute="trailing" secondItem="wwX-cy-Se2" secondAttribute="trailing" id="D9r-Rr-yAu"/>
                                    <constraint firstItem="wwX-cy-Se2" firstAttribute="leading" secondItem="MFB-jB-eyR" secondAttribute="leading" id="Pj0-dl-fDB"/>
                                    <constraint firstAttribute="bottom" secondItem="wwX-cy-Se2" secondAttribute="bottom" id="Smh-RP-YEz"/>
                                    <constraint firstItem="wwX-cy-Se2" firstAttribute="top" secondItem="MFB-jB-eyR" secondAttribute="top" constant="10" id="jgL-q2-6cd"/>
                                </constraints>
                            </view>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="zFr-A3-0yR">
                                <rect key="frame" x="0.0" y="50" width="375" height="1456"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="U2u-iD-bos">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="1031"/>
                                        <subviews>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="NSK-M4-t8i" customClass="GradientImageView" customModule="Beta_Cinemas" customModuleProvider="target">
                                                <rect key="frame" x="0.0" y="0.0" width="375" height="200"/>
                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="200" id="diR-rc-ZEy"/>
                                                </constraints>
                                            </view>
                                            <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" spacing="32" translatesAutoresizingMaskIntoConstraints="NO" id="3fD-Sy-HAH">
                                                <rect key="frame" x="0.0" y="0.0" width="375" height="1031"/>
                                                <subviews>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="7s4-Hd-6UX" userLabel="BannerView">
                                                        <rect key="frame" x="0.0" y="0.0" width="375" height="503"/>
                                                        <subviews>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="fha-oB-Eez">
                                                                <rect key="frame" x="0.0" y="53" width="375" height="436"/>
                                                                <color key="backgroundColor" red="0.**********" green="0.**********" blue="0.15686274510000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                            </view>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="WVT-EW-XZs" customClass="iCarousel">
                                                                <rect key="frame" x="0.0" y="23" width="375" height="416"/>
                                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="width" secondItem="WVT-EW-XZs" secondAttribute="height" multiplier="375:416" id="Xlt-ip-V4Q"/>
                                                                </constraints>
                                                                <connections>
                                                                    <outlet property="dataSource" destination="DwD-6Y-uMb" id="dgr-c8-KuP"/>
                                                                    <outlet property="delegate" destination="DwD-6Y-uMb" id="QSj-uJ-LOU"/>
                                                                </connections>
                                                            </view>
                                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="lNk-2v-C0e" customClass="RoundView" customModule="Beta_Cinemas" customModuleProvider="target">
                                                                <rect key="frame" x="67.666666666666686" y="447" width="240" height="56"/>
                                                                <subviews>
                                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="30. 03. 2018" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="fma-qw-zxn">
                                                                        <rect key="frame" x="0.0" y="0.0" width="240" height="56"/>
                                                                        <fontDescription key="fontDescription" name="Oswald-ExtraLight" family="Oswald" pointSize="30"/>
                                                                        <color key="textColor" red="0.0**********" green="0.34901960780000002" blue="0.61568627450000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                        <nil key="highlightedColor"/>
                                                                    </label>
                                                                </subviews>
                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="trailing" secondItem="fma-qw-zxn" secondAttribute="trailing" id="MXj-Nv-C9W"/>
                                                                    <constraint firstAttribute="bottom" secondItem="fma-qw-zxn" secondAttribute="bottom" id="QJa-oX-g4d"/>
                                                                    <constraint firstItem="fma-qw-zxn" firstAttribute="leading" secondItem="lNk-2v-C0e" secondAttribute="leading" id="bfR-EJ-PBh"/>
                                                                    <constraint firstItem="fma-qw-zxn" firstAttribute="top" secondItem="lNk-2v-C0e" secondAttribute="top" id="zJO-KC-gtx"/>
                                                                </constraints>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                                        <real key="value" value="4"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="shadowOpacity">
                                                                        <real key="value" value="0.20000000000000001"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="point" keyPath="shadowOffset">
                                                                        <point key="value" x="0.0" y="8"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="shadowRadius">
                                                                        <real key="value" value="4"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="color" keyPath="shadowColor">
                                                                        <color key="value" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                </userDefinedRuntimeAttributes>
                                                            </view>
                                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="sSf-Qo-0ig" customClass="GradientButton" customModule="Beta_Cinemas" customModuleProvider="target">
                                                                <rect key="frame" x="67.666666666666686" y="447" width="240" height="56"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="56" id="Sjq-cw-8ol"/>
                                                                    <constraint firstAttribute="width" constant="240" id="rAe-1F-6Ym"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="24"/>
                                                                <state key="normal" title="MUA VÉ"/>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                                        <real key="value" value="4"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="shadowOpacity">
                                                                        <real key="value" value="0.29999999999999999"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="point" keyPath="shadowOffset">
                                                                        <point key="value" x="0.0" y="8"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="shadowRadius">
                                                                        <real key="value" value="20"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="color" keyPath="shadowColor">
                                                                        <color key="value" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Home.BuyTicket"/>
                                                                </userDefinedRuntimeAttributes>
                                                                <connections>
                                                                    <action selector="buyTicketBtPressed:" destination="DwD-6Y-uMb" eventType="touchUpInside" id="WuR-io-fKH"/>
                                                                </connections>
                                                            </button>
                                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="Dj7-wB-BBE">
                                                                <rect key="frame" x="67.666666666666686" y="447" width="240" height="56"/>
                                                                <connections>
                                                                    <action selector="buyTicketBtPressed:" destination="DwD-6Y-uMb" eventType="touchUpInside" id="XK4-1M-uMM"/>
                                                                </connections>
                                                            </button>
                                                        </subviews>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstItem="fha-oB-Eez" firstAttribute="bottom" secondItem="WVT-EW-XZs" secondAttribute="bottom" constant="50" id="2PO-Nd-2Gw"/>
                                                            <constraint firstItem="WVT-EW-XZs" firstAttribute="top" secondItem="7s4-Hd-6UX" secondAttribute="top" constant="23" id="3S5-Vd-lAF"/>
                                                            <constraint firstAttribute="bottom" secondItem="sSf-Qo-0ig" secondAttribute="bottom" id="518-Yy-YiD"/>
                                                            <constraint firstItem="fha-oB-Eez" firstAttribute="trailing" secondItem="WVT-EW-XZs" secondAttribute="trailing" id="8SI-m3-iDB"/>
                                                            <constraint firstItem="sSf-Qo-0ig" firstAttribute="leading" secondItem="Dj7-wB-BBE" secondAttribute="leading" id="Aoz-2U-mNM"/>
                                                            <constraint firstItem="sSf-Qo-0ig" firstAttribute="centerX" secondItem="7s4-Hd-6UX" secondAttribute="centerX" id="FWF-8q-mhm"/>
                                                            <constraint firstItem="sSf-Qo-0ig" firstAttribute="top" secondItem="Dj7-wB-BBE" secondAttribute="top" id="N2i-hJ-X1f"/>
                                                            <constraint firstItem="sSf-Qo-0ig" firstAttribute="top" secondItem="lNk-2v-C0e" secondAttribute="top" id="NR5-QB-Kfv"/>
                                                            <constraint firstItem="sSf-Qo-0ig" firstAttribute="leading" secondItem="lNk-2v-C0e" secondAttribute="leading" id="POD-Pc-gpe"/>
                                                            <constraint firstAttribute="trailing" secondItem="WVT-EW-XZs" secondAttribute="trailing" id="RVx-TZ-LGL"/>
                                                            <constraint firstItem="fha-oB-Eez" firstAttribute="leading" secondItem="WVT-EW-XZs" secondAttribute="leading" id="RvW-zQ-yNe"/>
                                                            <constraint firstItem="sSf-Qo-0ig" firstAttribute="bottom" secondItem="Dj7-wB-BBE" secondAttribute="bottom" id="UaS-vC-6aM"/>
                                                            <constraint firstItem="sSf-Qo-0ig" firstAttribute="trailing" secondItem="lNk-2v-C0e" secondAttribute="trailing" id="aBE-z5-IOX"/>
                                                            <constraint firstItem="fha-oB-Eez" firstAttribute="top" secondItem="WVT-EW-XZs" secondAttribute="top" constant="30" id="eou-ve-e4L"/>
                                                            <constraint firstItem="sSf-Qo-0ig" firstAttribute="top" secondItem="WVT-EW-XZs" secondAttribute="bottom" constant="8" id="h8T-Uk-2l7"/>
                                                            <constraint firstItem="WVT-EW-XZs" firstAttribute="leading" secondItem="7s4-Hd-6UX" secondAttribute="leading" id="kda-hJ-ETE"/>
                                                            <constraint firstItem="sSf-Qo-0ig" firstAttribute="trailing" secondItem="Dj7-wB-BBE" secondAttribute="trailing" id="o6n-XD-X6U"/>
                                                            <constraint firstItem="sSf-Qo-0ig" firstAttribute="bottom" secondItem="lNk-2v-C0e" secondAttribute="bottom" id="to4-sD-EkV"/>
                                                        </constraints>
                                                    </view>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="DZC-iW-6z0" customClass="RoundView" customModule="Beta_Cinemas" customModuleProvider="target">
                                                        <rect key="frame" x="0.0" y="535" width="375" height="64"/>
                                                        <subviews>
                                                            <textField opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" placeholder="Tìm rạp gần bạn..." textAlignment="natural" minimumFontSize="17" translatesAutoresizingMaskIntoConstraints="NO" id="WTa-DS-Jmm" customClass="RoundTextField" customModule="Beta_Cinemas" customModuleProvider="target">
                                                                <rect key="frame" x="16" y="0.0" width="343" height="64"/>
                                                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <color key="tintColor" red="0.72549019609999998" green="0.72549019609999998" blue="0.72549019609999998" alpha="1" colorSpace="calibratedRGB"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="64" id="Tbb-1b-3zv"/>
                                                                </constraints>
                                                                <color key="textColor" red="0.72549019607843135" green="0.72549019607843135" blue="0.72549019607843135" alpha="1" colorSpace="calibratedRGB"/>
                                                                <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="20"/>
                                                                <textInputTraits key="textInputTraits"/>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="color" keyPath="shadowColor">
                                                                        <color key="value" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="shadowOpacity">
                                                                        <real key="value" value="0.10000000000000001"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="point" keyPath="shadowOffset">
                                                                        <point key="value" x="0.0" y="4"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="shadowRadius">
                                                                        <real key="value" value="8"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                                        <real key="value" value="8"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="horizontalSpace">
                                                                        <real key="value" value="16"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Home.SearchAroundYou"/>
                                                                </userDefinedRuntimeAttributes>
                                                            </textField>
                                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="glV-Vk-F9u">
                                                                <rect key="frame" x="0.0" y="0.0" width="375" height="64"/>
                                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <connections>
                                                                    <action selector="searchButtonPressed:" destination="DwD-6Y-uMb" eventType="touchUpInside" id="44c-lp-Qp0"/>
                                                                </connections>
                                                            </button>
                                                        </subviews>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstItem="glV-Vk-F9u" firstAttribute="leading" secondItem="DZC-iW-6z0" secondAttribute="leading" id="Ef6-dX-aLI"/>
                                                            <constraint firstItem="glV-Vk-F9u" firstAttribute="top" secondItem="DZC-iW-6z0" secondAttribute="top" id="SFd-ys-4va"/>
                                                            <constraint firstAttribute="bottom" secondItem="glV-Vk-F9u" secondAttribute="bottom" id="TTk-pa-i5c"/>
                                                            <constraint firstAttribute="trailing" secondItem="glV-Vk-F9u" secondAttribute="trailing" id="UXy-E4-Wkq"/>
                                                            <constraint firstItem="WTa-DS-Jmm" firstAttribute="leading" secondItem="DZC-iW-6z0" secondAttribute="leading" constant="16" id="Xv0-W9-c93"/>
                                                            <constraint firstItem="WTa-DS-Jmm" firstAttribute="top" secondItem="DZC-iW-6z0" secondAttribute="top" id="boO-67-DG7"/>
                                                            <constraint firstAttribute="trailing" secondItem="WTa-DS-Jmm" secondAttribute="trailing" constant="16" id="j77-ex-dVu"/>
                                                            <constraint firstItem="WTa-DS-Jmm" firstAttribute="centerY" secondItem="DZC-iW-6z0" secondAttribute="centerY" id="y89-gn-kml"/>
                                                        </constraints>
                                                        <userDefinedRuntimeAttributes>
                                                            <userDefinedRuntimeAttribute type="color" keyPath="shadowColor">
                                                                <color key="value" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                            </userDefinedRuntimeAttribute>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="shadowOpacity">
                                                                <real key="value" value="0.10000000000000001"/>
                                                            </userDefinedRuntimeAttribute>
                                                            <userDefinedRuntimeAttribute type="point" keyPath="shadowOffset">
                                                                <point key="value" x="0.0" y="4"/>
                                                            </userDefinedRuntimeAttribute>
                                                            <userDefinedRuntimeAttribute type="number" keyPath="shadowRadius">
                                                                <real key="value" value="8"/>
                                                            </userDefinedRuntimeAttribute>
                                                        </userDefinedRuntimeAttributes>
                                                    </view>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="2on-3e-oVx">
                                                        <rect key="frame" x="0.0" y="631" width="375" height="400"/>
                                                        <subviews>
                                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="KHUYẾN MÃI MỚI" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="9ai-cs-Xz8" customClass="LocalizableLabel" customModule="Beta_Cinemas" customModuleProvider="target">
                                                                <rect key="frame" x="8.0000000000000071" y="8" width="126.66666666666669" height="30"/>
                                                                <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="20"/>
                                                                <color key="textColor" red="0.28627450980392155" green="0.29803921568627451" blue="0.38431372549019605" alpha="1" colorSpace="calibratedRGB"/>
                                                                <nil key="highlightedColor"/>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Home.BigDeals"/>
                                                                </userDefinedRuntimeAttributes>
                                                            </label>
                                                            <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="sFw-5P-9fW" customClass="RoundButton" customModule="Beta_Cinemas" customModuleProvider="target">
                                                                <rect key="frame" x="299" y="11" width="68" height="24"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="24" id="kvf-ef-PrS"/>
                                                                    <constraint firstAttribute="width" constant="68" id="vUF-h7-n2Q"/>
                                                                </constraints>
                                                                <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                                <state key="normal" title="Tất cả">
                                                                    <color key="titleColor" red="0.0**********" green="0.34901960780000002" blue="0.61568627450000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                </state>
                                                                <userDefinedRuntimeAttributes>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="borderWidth">
                                                                        <real key="value" value="1"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                                        <real key="value" value="13"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="color" keyPath="borderColor">
                                                                        <color key="value" red="0.0**********2352941" green="0.34901960784313724" blue="0.61568627450980395" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                                    </userDefinedRuntimeAttribute>
                                                                    <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Home.All"/>
                                                                </userDefinedRuntimeAttributes>
                                                                <connections>
                                                                    <action selector="showAllNewsButtonPressed:" destination="DwD-6Y-uMb" eventType="touchUpInside" id="wpj-VW-YHo"/>
                                                                </connections>
                                                            </button>
                                                            <view alpha="0.29999999999999999" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="I2M-hE-xQf" userLabel="lineView">
                                                                <rect key="frame" x="134.66666666666663" y="34" width="177.33333333333337" height="1"/>
                                                                <color key="backgroundColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="calibratedRGB"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="1" id="fEh-Z8-605"/>
                                                                </constraints>
                                                            </view>
                                                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" scrollEnabled="NO" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="UeP-Iq-KWe">
                                                                <rect key="frame" x="0.0" y="45" width="375" height="345"/>
                                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                                <constraints>
                                                                    <constraint firstAttribute="height" constant="345" id="HjC-FW-Uxw"/>
                                                                </constraints>
                                                                <connections>
                                                                    <outlet property="dataSource" destination="DwD-6Y-uMb" id="Umt-fp-qXP"/>
                                                                    <outlet property="delegate" destination="DwD-6Y-uMb" id="qjV-CV-EM3"/>
                                                                </connections>
                                                            </tableView>
                                                        </subviews>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstItem="UeP-Iq-KWe" firstAttribute="leading" secondItem="2on-3e-oVx" secondAttribute="leading" id="2nG-CV-Vg4"/>
                                                            <constraint firstItem="9ai-cs-Xz8" firstAttribute="top" secondItem="2on-3e-oVx" secondAttribute="top" constant="8" id="5Lu-nZ-bTz"/>
                                                            <constraint firstItem="9ai-cs-Xz8" firstAttribute="leading" secondItem="2on-3e-oVx" secondAttribute="leading" constant="8" id="8Z0-qn-uOn"/>
                                                            <constraint firstItem="9ai-cs-Xz8" firstAttribute="top" secondItem="2on-3e-oVx" secondAttribute="top" constant="8" id="DQ2-wF-4I8"/>
                                                            <constraint firstItem="UeP-Iq-KWe" firstAttribute="top" secondItem="I2M-hE-xQf" secondAttribute="bottom" constant="10" id="Pcs-Ba-VYE"/>
                                                            <constraint firstAttribute="trailing" secondItem="UeP-Iq-KWe" secondAttribute="trailing" id="PqF-Ex-as7"/>
                                                            <constraint firstAttribute="bottom" secondItem="UeP-Iq-KWe" secondAttribute="bottom" constant="10" id="kHS-L2-mGD"/>
                                                            <constraint firstItem="I2M-hE-xQf" firstAttribute="leading" secondItem="9ai-cs-Xz8" secondAttribute="trailing" id="q0A-Vt-d9L"/>
                                                            <constraint firstItem="sFw-5P-9fW" firstAttribute="bottom" secondItem="I2M-hE-xQf" secondAttribute="bottom" id="raT-j2-nZs"/>
                                                            <constraint firstItem="sFw-5P-9fW" firstAttribute="centerY" secondItem="9ai-cs-Xz8" secondAttribute="centerY" id="u0C-yh-MCw"/>
                                                            <constraint firstAttribute="trailing" secondItem="sFw-5P-9fW" secondAttribute="trailing" constant="8" id="xQq-r3-UV7"/>
                                                            <constraint firstItem="sFw-5P-9fW" firstAttribute="leading" secondItem="I2M-hE-xQf" secondAttribute="trailing" constant="-13" id="yQd-at-YB4"/>
                                                        </constraints>
                                                    </view>
                                                </subviews>
                                                <constraints>
                                                    <constraint firstAttribute="trailing" secondItem="7s4-Hd-6UX" secondAttribute="trailing" id="43q-UJ-jnh"/>
                                                    <constraint firstAttribute="trailing" secondItem="DZC-iW-6z0" secondAttribute="trailing" id="4ok-eh-tnG"/>
                                                    <constraint firstItem="DZC-iW-6z0" firstAttribute="leading" secondItem="3fD-Sy-HAH" secondAttribute="leading" id="8zH-TB-xGe"/>
                                                    <constraint firstItem="7s4-Hd-6UX" firstAttribute="top" secondItem="3fD-Sy-HAH" secondAttribute="top" id="g7T-vV-IaB"/>
                                                    <constraint firstAttribute="trailing" secondItem="2on-3e-oVx" secondAttribute="trailing" id="j6j-Ec-nIA"/>
                                                    <constraint firstItem="2on-3e-oVx" firstAttribute="top" secondItem="DZC-iW-6z0" secondAttribute="bottom" constant="32" id="jLA-a8-esW"/>
                                                    <constraint firstItem="DZC-iW-6z0" firstAttribute="top" secondItem="7s4-Hd-6UX" secondAttribute="bottom" constant="32" id="kd7-vc-NQT"/>
                                                    <constraint firstAttribute="bottom" secondItem="2on-3e-oVx" secondAttribute="bottom" id="ttX-pK-DRQ"/>
                                                    <constraint firstItem="2on-3e-oVx" firstAttribute="leading" secondItem="3fD-Sy-HAH" secondAttribute="leading" id="vXr-wZ-beQ"/>
                                                    <constraint firstItem="7s4-Hd-6UX" firstAttribute="leading" secondItem="3fD-Sy-HAH" secondAttribute="leading" id="y2h-cq-2iM"/>
                                                </constraints>
                                            </stackView>
                                        </subviews>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="trailing" secondItem="3fD-Sy-HAH" secondAttribute="trailing" id="Lmd-Sf-k2t"/>
                                            <constraint firstItem="NSK-M4-t8i" firstAttribute="leading" secondItem="U2u-iD-bos" secondAttribute="leading" id="V8J-Si-dgF"/>
                                            <constraint firstAttribute="bottom" secondItem="3fD-Sy-HAH" secondAttribute="bottom" id="VRM-i7-btO"/>
                                            <constraint firstItem="3fD-Sy-HAH" firstAttribute="top" secondItem="U2u-iD-bos" secondAttribute="top" id="eeu-MJ-7lB"/>
                                            <constraint firstAttribute="trailing" secondItem="NSK-M4-t8i" secondAttribute="trailing" id="fxa-0v-3qu"/>
                                            <constraint firstItem="3fD-Sy-HAH" firstAttribute="leading" secondItem="U2u-iD-bos" secondAttribute="leading" id="gWw-OG-ciD"/>
                                            <constraint firstItem="NSK-M4-t8i" firstAttribute="top" secondItem="U2u-iD-bos" secondAttribute="top" id="mzd-lm-Crz"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="bottom" secondItem="U2u-iD-bos" secondAttribute="bottom" id="O8S-ez-RpF"/>
                                    <constraint firstAttribute="trailing" secondItem="U2u-iD-bos" secondAttribute="trailing" id="PpZ-xB-urk"/>
                                    <constraint firstItem="U2u-iD-bos" firstAttribute="width" secondItem="zFr-A3-0yR" secondAttribute="width" id="RoB-Md-SMB"/>
                                    <constraint firstItem="U2u-iD-bos" firstAttribute="top" secondItem="zFr-A3-0yR" secondAttribute="top" id="qki-EV-xkS"/>
                                    <constraint firstItem="U2u-iD-bos" firstAttribute="leading" secondItem="zFr-A3-0yR" secondAttribute="leading" id="zYC-pC-goh"/>
                                </constraints>
                                <connections>
                                    <outlet property="delegate" destination="DwD-6Y-uMb" id="VwN-hC-owr"/>
                                </connections>
                            </scrollView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="CeQ-wc-jn5"/>
                        <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                        <constraints>
                            <constraint firstItem="MFB-jB-eyR" firstAttribute="trailing" secondItem="CeQ-wc-jn5" secondAttribute="trailing" id="10U-Az-cQT"/>
                            <constraint firstAttribute="trailing" secondItem="zFr-A3-0yR" secondAttribute="trailing" id="8uy-lw-28B"/>
                            <constraint firstItem="zFr-A3-0yR" firstAttribute="leading" secondItem="dLe-zI-yju" secondAttribute="leading" id="KwA-uf-i7U"/>
                            <constraint firstItem="MFB-jB-eyR" firstAttribute="leading" secondItem="CeQ-wc-jn5" secondAttribute="leading" id="giZ-Uj-wwe"/>
                            <constraint firstAttribute="bottom" secondItem="zFr-A3-0yR" secondAttribute="bottom" id="o45-24-C45"/>
                            <constraint firstItem="zFr-A3-0yR" firstAttribute="top" secondItem="MFB-jB-eyR" secondAttribute="bottom" id="pEG-dC-zzh"/>
                            <constraint firstItem="MFB-jB-eyR" firstAttribute="top" secondItem="CeQ-wc-jn5" secondAttribute="top" id="xnW-80-qKr"/>
                        </constraints>
                        <edgeInsets key="layoutMargins" top="0.0" left="0.0" bottom="0.0" right="0.0"/>
                    </view>
                    <navigationItem key="navigationItem" id="aqx-Ye-PlN"/>
                    <freeformSimulatedSizeMetrics key="simulatedDestinationMetrics"/>
                    <size key="freeformSize" width="375" height="1600"/>
                    <connections>
                        <outlet property="bannerBackgroundView" destination="fha-oB-Eez" id="8Id-HV-5OW"/>
                        <outlet property="bannerCarousel" destination="WVT-EW-XZs" id="4CD-NF-LAa"/>
                        <outlet property="btBuyTicket" destination="sSf-Qo-0ig" id="FMg-zY-SNN"/>
                        <outlet property="lbFilmDate" destination="fma-qw-zxn" id="f8z-a2-hgo"/>
                        <outlet property="searchCinemaView" destination="DZC-iW-6z0" id="Shr-50-ZDw"/>
                        <outlet property="searchTextField" destination="WTa-DS-Jmm" id="LeJ-yr-MfJ"/>
                        <outlet property="tbPromotion" destination="UeP-Iq-KWe" id="rE9-bf-qA0"/>
                        <outlet property="tbPromotionHeight" destination="HjC-FW-Uxw" id="S1X-bG-nIK"/>
                        <outlet property="topicTab" destination="wwX-cy-Se2" id="Jdt-4V-UP2"/>
                        <outlet property="vFilmDate" destination="lNk-2v-C0e" id="ncf-BJ-dni"/>
                        <outlet property="vTopicTab" destination="MFB-jB-eyR" id="I0k-HD-pYz"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="wmu-do-L5p" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="215.19999999999999" y="-11.694152923538232"/>
        </scene>
        <!--Notification View Controller-->
        <scene sceneID="W7U-MY-oU1">
            <objects>
                <viewController storyboardIdentifier="NotificationViewController" id="irD-pg-Xqm" customClass="NotificationViewController" customModule="Beta_Cinemas" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="GRV-eY-Ljh">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="812"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="jG7-mf-NZi">
                                <rect key="frame" x="0.0" y="50" width="375" height="728"/>
                                <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="calibratedRGB"/>
                                <connections>
                                    <outlet property="dataSource" destination="irD-pg-Xqm" id="662-xg-tbd"/>
                                    <outlet property="delegate" destination="irD-pg-Xqm" id="OXJ-D6-0E2"/>
                                </connections>
                            </tableView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="leW-yn-cwa"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="jG7-mf-NZi" firstAttribute="top" secondItem="leW-yn-cwa" secondAttribute="top" id="H7k-lb-flx"/>
                            <constraint firstItem="leW-yn-cwa" firstAttribute="trailing" secondItem="jG7-mf-NZi" secondAttribute="trailing" id="SOU-hH-jDW"/>
                            <constraint firstItem="jG7-mf-NZi" firstAttribute="leading" secondItem="leW-yn-cwa" secondAttribute="leading" id="Vcj-Ee-mDF"/>
                            <constraint firstItem="leW-yn-cwa" firstAttribute="bottom" secondItem="jG7-mf-NZi" secondAttribute="bottom" id="cE8-QK-fRe"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="tableView" destination="jG7-mf-NZi" id="tEf-yV-5kB"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Em4-Wl-L3h" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-471.19999999999999" y="665.76354679802955"/>
        </scene>
        <!--News Detail View Controller-->
        <scene sceneID="r8w-Xq-php">
            <objects>
                <viewController storyboardIdentifier="NewsDetailViewController" extendedLayoutIncludesOpaqueBars="YES" automaticallyAdjustsScrollViewInsets="NO" id="UEH-Ol-orm" customClass="NewsDetailViewController" customModule="Beta_Cinemas" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="TVj-L0-EUl">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="812"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <scrollView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="OPd-RT-leo">
                                <rect key="frame" x="0.0" y="50" width="375" height="664"/>
                                <subviews>
                                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="OLd-ui-51C">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="654.66666666666663"/>
                                        <subviews>
                                            <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="bg1.png" translatesAutoresizingMaskIntoConstraints="NO" id="E37-sv-xL1">
                                                <rect key="frame" x="0.0" y="0.0" width="375" height="292"/>
                                                <constraints>
                                                    <constraint firstAttribute="width" secondItem="E37-sv-xL1" secondAttribute="height" multiplier="375:292" id="bUv-Bh-mMR"/>
                                                </constraints>
                                            </imageView>
                                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="4Yd-OA-3YD">
                                                <rect key="frame" x="0.0" y="292" width="375" height="362.66666666666674"/>
                                                <subviews>
                                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="252" text="Beta's Day - thứ 4 xem phim cực đã chỉ 45k" textAlignment="center" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="iFO-5b-aya">
                                                        <rect key="frame" x="32" y="12.000000000000004" width="311" height="59.333333333333343"/>
                                                        <fontDescription key="fontDescription" name="Oswald-Bold" family="Oswald" pointSize="20"/>
                                                        <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                        <nil key="highlightedColor"/>
                                                    </label>
                                                    <textView clipsSubviews="YES" contentMode="scaleToFill" scrollEnabled="NO" delaysContentTouches="NO" editable="NO" textAlignment="justified" translatesAutoresizingMaskIntoConstraints="NO" id="Pve-L2-TGQ">
                                                        <rect key="frame" x="16" y="83.333333333333314" width="343" height="267.33333333333331"/>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                                        <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                                                        <dataDetectorType key="dataDetectorTypes" phoneNumber="YES" link="YES"/>
                                                        <connections>
                                                            <outlet property="delegate" destination="UEH-Ol-orm" id="qhW-G2-Wxf"/>
                                                        </connections>
                                                    </textView>
                                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="f77-nS-x4C" customClass="DRPLoadingSpinner">
                                                        <rect key="frame" x="172.66666666666666" y="79.333333333333314" width="30" height="30"/>
                                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                        <constraints>
                                                            <constraint firstAttribute="width" constant="30" id="Jck-0B-kDP"/>
                                                            <constraint firstAttribute="height" constant="30" id="eu4-LF-eoJ"/>
                                                        </constraints>
                                                    </view>
                                                </subviews>
                                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                <constraints>
                                                    <constraint firstAttribute="bottom" secondItem="Pve-L2-TGQ" secondAttribute="bottom" constant="12" id="2G8-qS-Aty"/>
                                                    <constraint firstItem="Pve-L2-TGQ" firstAttribute="top" secondItem="iFO-5b-aya" secondAttribute="bottom" constant="12" id="8ka-v4-pfS"/>
                                                    <constraint firstAttribute="trailing" secondItem="Pve-L2-TGQ" secondAttribute="trailing" constant="16" id="LcK-O0-eaf"/>
                                                    <constraint firstItem="iFO-5b-aya" firstAttribute="top" secondItem="4Yd-OA-3YD" secondAttribute="top" constant="12" id="QCY-4a-Ynm"/>
                                                    <constraint firstItem="Pve-L2-TGQ" firstAttribute="leading" secondItem="4Yd-OA-3YD" secondAttribute="leading" constant="16" id="X5S-Mw-dkn"/>
                                                    <constraint firstItem="iFO-5b-aya" firstAttribute="leading" secondItem="4Yd-OA-3YD" secondAttribute="leading" constant="32" id="Zal-I8-qr6"/>
                                                    <constraint firstAttribute="trailing" secondItem="iFO-5b-aya" secondAttribute="trailing" constant="32" id="iEp-pt-WKf"/>
                                                    <constraint firstItem="f77-nS-x4C" firstAttribute="top" secondItem="iFO-5b-aya" secondAttribute="bottom" constant="8" id="kAw-wp-Tdc"/>
                                                    <constraint firstItem="f77-nS-x4C" firstAttribute="centerX" secondItem="4Yd-OA-3YD" secondAttribute="centerX" id="rPS-XK-PzH"/>
                                                </constraints>
                                            </view>
                                        </subviews>
                                    </stackView>
                                </subviews>
                                <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="calibratedRGB"/>
                                <constraints>
                                    <constraint firstItem="OLd-ui-51C" firstAttribute="top" secondItem="OPd-RT-leo" secondAttribute="top" id="Ie3-nA-SQ3"/>
                                    <constraint firstItem="OLd-ui-51C" firstAttribute="width" secondItem="OPd-RT-leo" secondAttribute="width" id="Kb1-lH-Nhe"/>
                                    <constraint firstAttribute="trailing" secondItem="OLd-ui-51C" secondAttribute="trailing" id="T6A-nC-jMz"/>
                                    <constraint firstAttribute="bottom" secondItem="OLd-ui-51C" secondAttribute="bottom" id="TvZ-cH-29R"/>
                                    <constraint firstItem="OLd-ui-51C" firstAttribute="leading" secondItem="OPd-RT-leo" secondAttribute="leading" id="sUG-99-Gfr"/>
                                </constraints>
                            </scrollView>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="TZi-DR-rRn">
                                <rect key="frame" x="0.0" y="714" width="375" height="64"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="qr0-zS-S7A" customClass="LocalizableButton" customModule="Beta_Cinemas" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="0.0" width="375" height="64"/>
                                        <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="24"/>
                                        <state key="normal" title="CHIA SẺ">
                                            <color key="titleColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        </state>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="NewsDetail.Share"/>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <action selector="shareNewsButtonPressed:" destination="UEH-Ol-orm" eventType="touchUpInside" id="iBO-Qa-GX8"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" red="0.0**********" green="0.34901960780000002" blue="0.61568627450000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="qr0-zS-S7A" secondAttribute="trailing" id="4ay-Ei-yIG"/>
                                    <constraint firstAttribute="height" constant="64" id="N9u-Gp-wB7"/>
                                    <constraint firstItem="qr0-zS-S7A" firstAttribute="leading" secondItem="TZi-DR-rRn" secondAttribute="leading" id="Pfp-Mc-uuv"/>
                                    <constraint firstAttribute="bottom" secondItem="qr0-zS-S7A" secondAttribute="bottom" id="e7U-AQ-Mo3"/>
                                    <constraint firstItem="qr0-zS-S7A" firstAttribute="top" secondItem="TZi-DR-rRn" secondAttribute="top" id="qHd-Am-YgZ"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="09B-Gh-L58"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="TZi-DR-rRn" firstAttribute="leading" secondItem="09B-Gh-L58" secondAttribute="leading" id="5cR-c3-QWa"/>
                            <constraint firstItem="OPd-RT-leo" firstAttribute="top" secondItem="09B-Gh-L58" secondAttribute="top" id="YL2-Kk-Ssw"/>
                            <constraint firstItem="OPd-RT-leo" firstAttribute="leading" secondItem="09B-Gh-L58" secondAttribute="leading" id="aps-OS-cwS"/>
                            <constraint firstItem="TZi-DR-rRn" firstAttribute="trailing" secondItem="09B-Gh-L58" secondAttribute="trailing" id="oDy-C5-TIh"/>
                            <constraint firstItem="09B-Gh-L58" firstAttribute="bottom" secondItem="TZi-DR-rRn" secondAttribute="bottom" id="xI0-1j-HmZ"/>
                            <constraint firstItem="TZi-DR-rRn" firstAttribute="top" secondItem="OPd-RT-leo" secondAttribute="bottom" id="zFE-so-b7E"/>
                            <constraint firstItem="09B-Gh-L58" firstAttribute="trailing" secondItem="OPd-RT-leo" secondAttribute="trailing" id="zgb-31-aNE"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="lbTitle" destination="iFO-5b-aya" id="yRt-nQ-hU4"/>
                        <outlet property="loadingView" destination="f77-nS-x4C" id="ZOh-RT-Ryi"/>
                        <outlet property="newBannerImageView" destination="E37-sv-xL1" id="BZP-gj-j5e"/>
                        <outlet property="scrollView" destination="OPd-RT-leo" id="Vdv-bo-aMo"/>
                        <outlet property="shareButton" destination="qr0-zS-S7A" id="sPJ-SV-81c"/>
                        <outlet property="tvContent" destination="Pve-L2-TGQ" id="72d-37-A8e"/>
                        <outlet property="vBottom" destination="TZi-DR-rRn" id="YlJ-9Y-6So"/>
                        <outlet property="viewShareHeight" destination="N9u-Gp-wB7" id="shh-fi-83u"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Npg-5j-UCt" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="803" y="900"/>
        </scene>
        <!--Youtube View Controller-->
        <scene sceneID="KQp-n9-2va">
            <objects>
                <viewController storyboardIdentifier="YoutubeViewController" id="luq-DD-NW1" customClass="YoutubeViewController" customModule="Beta_Cinemas" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="li4-VW-Td4">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="812"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="7kt-ix-YLQ" customClass="YTPlayerView">
                                <rect key="frame" x="0.0" y="100" width="375" height="678"/>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="HZr-Gg-hBr">
                                <rect key="frame" x="0.0" y="50" width="375" height="50"/>
                                <subviews>
                                    <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" buttonType="system" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="FVe-u1-sjc">
                                        <rect key="frame" x="341" y="13" width="24" height="24"/>
                                        <constraints>
                                            <constraint firstAttribute="height" constant="24" id="rLK-MX-SgU"/>
                                            <constraint firstAttribute="width" constant="24" id="zgC-tV-68x"/>
                                        </constraints>
                                        <color key="tintColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <state key="normal" image="icClose"/>
                                        <connections>
                                            <action selector="closeButtonPressed:" destination="luq-DD-NW1" eventType="touchUpInside" id="l80-Q2-Dey"/>
                                        </connections>
                                    </button>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstAttribute="height" constant="50" id="9Go-1z-wJg"/>
                                    <constraint firstItem="FVe-u1-sjc" firstAttribute="centerY" secondItem="HZr-Gg-hBr" secondAttribute="centerY" id="rIR-wX-WHy"/>
                                    <constraint firstAttribute="trailing" secondItem="FVe-u1-sjc" secondAttribute="trailing" constant="10" id="wof-Em-aQt"/>
                                </constraints>
                            </view>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="9IG-zs-31t"/>
                        <color key="backgroundColor" white="0.0" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="9IG-zs-31t" firstAttribute="trailing" secondItem="HZr-Gg-hBr" secondAttribute="trailing" id="GOg-tj-m2V"/>
                            <constraint firstItem="7kt-ix-YLQ" firstAttribute="top" secondItem="HZr-Gg-hBr" secondAttribute="bottom" id="SUE-pB-reA"/>
                            <constraint firstItem="7kt-ix-YLQ" firstAttribute="leading" secondItem="9IG-zs-31t" secondAttribute="leading" id="hM4-dH-NVH"/>
                            <constraint firstItem="9IG-zs-31t" firstAttribute="trailing" secondItem="7kt-ix-YLQ" secondAttribute="trailing" id="j2K-nb-9vU"/>
                            <constraint firstItem="HZr-Gg-hBr" firstAttribute="leading" secondItem="9IG-zs-31t" secondAttribute="leading" id="m83-vc-ZqW"/>
                            <constraint firstItem="9IG-zs-31t" firstAttribute="bottom" secondItem="7kt-ix-YLQ" secondAttribute="bottom" id="pCf-xy-WKI"/>
                            <constraint firstItem="HZr-Gg-hBr" firstAttribute="top" secondItem="9IG-zs-31t" secondAttribute="top" id="pmd-Cj-5vF"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="youtubePlayer" destination="7kt-ix-YLQ" id="7Ch-oo-qPc"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Pi2-LE-LPu" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-619" y="1493"/>
        </scene>
        <!--Top View Controller-->
        <scene sceneID="SrX-UT-S1x">
            <objects>
                <viewController storyboardIdentifier="TopViewController" id="7Cn-hj-DtF" customClass="TopViewController" customModule="Beta_Cinemas" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="slg-jB-pAV">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="812"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <viewLayoutGuide key="safeArea" id="liV-fU-Tzl"/>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    </view>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="UtD-5b-DyT" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="142" y="1361"/>
        </scene>
        <!--Notification Detail View Controller-->
        <scene sceneID="FPi-yv-SeU">
            <objects>
                <viewController storyboardIdentifier="NotificationDetailViewController" id="hBn-iF-fle" customClass="NotificationDetailViewController" customModule="Beta_Cinemas" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="am4-yw-QgO">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="812"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <textView clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="scaleToFill" maximumZoomScale="2.5" editable="NO" textAlignment="natural" translatesAutoresizingMaskIntoConstraints="NO" id="KCo-WW-qJz">
                                <rect key="frame" x="0.0" y="50" width="375" height="728"/>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <fontDescription key="fontDescription" type="system" pointSize="14"/>
                                <textInputTraits key="textInputTraits" autocapitalizationType="sentences"/>
                                <dataDetectorType key="dataDetectorTypes" phoneNumber="YES" link="YES"/>
                            </textView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="Fgh-ml-JEc"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="Fgh-ml-JEc" firstAttribute="trailing" secondItem="KCo-WW-qJz" secondAttribute="trailing" id="4OK-zy-5hI"/>
                            <constraint firstItem="KCo-WW-qJz" firstAttribute="top" secondItem="Fgh-ml-JEc" secondAttribute="top" id="ZjW-kE-Q7q"/>
                            <constraint firstItem="KCo-WW-qJz" firstAttribute="leading" secondItem="Fgh-ml-JEc" secondAttribute="leading" id="ubm-w3-qLj"/>
                            <constraint firstItem="Fgh-ml-JEc" firstAttribute="bottom" secondItem="KCo-WW-qJz" secondAttribute="bottom" id="zdk-Sx-CMH"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="tvContent" destination="KCo-WW-qJz" id="8Cn-8S-Iu4"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="POZ-Vb-FU2" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="642" y="1947"/>
        </scene>
        <!--NewsAndDealsViewController-->
        <scene sceneID="WWp-TP-KbO">
            <objects>
                <viewController storyboardIdentifier="NewsAndDealsViewController" id="I8d-BF-CGd" userLabel="NewsAndDealsViewController" customClass="NewsAndDealsViewController" customModule="Beta_Cinemas" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="SUT-0o-8kU">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="812"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="dh0-Lk-y2B">
                                <rect key="frame" x="0.0" y="50" width="375" height="68"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="MLS-Q6-Rtq" customClass="ScrollPager" customModule="Beta_Cinemas" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="18" width="375" height="50"/>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="color" keyPath="textColor">
                                                <color key="value" red="0.72549019609999998" green="0.72549019609999998" blue="0.72549019609999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="color" keyPath="selectedTextColor">
                                                <color key="value" red="0.**********" green="0.**********" blue="0.15686274510000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="color" keyPath="indicatorColor">
                                                <color key="value" red="0.**********" green="0.**********" blue="0.15686274510000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="boolean" keyPath="indicatorIsAtBottom" value="YES"/>
                                            <userDefinedRuntimeAttribute type="boolean" keyPath="indicatorSizeMatchesTitle" value="NO"/>
                                            <userDefinedRuntimeAttribute type="number" keyPath="indicatorWidth">
                                                <real key="value" value="21"/>
                                            </userDefinedRuntimeAttribute>
                                            <userDefinedRuntimeAttribute type="number" keyPath="indicatorHeight">
                                                <real key="value" value="2"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                        <connections>
                                            <outlet property="delegate" destination="I8d-BF-CGd" id="0dy-Zf-0c2"/>
                                        </connections>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="calibratedRGB"/>
                                <constraints>
                                    <constraint firstAttribute="trailing" secondItem="MLS-Q6-Rtq" secondAttribute="trailing" id="0hu-fP-0jS"/>
                                    <constraint firstItem="MLS-Q6-Rtq" firstAttribute="leading" secondItem="dh0-Lk-y2B" secondAttribute="leading" id="MUO-y9-jYA"/>
                                    <constraint firstAttribute="height" constant="68" id="blH-dI-P9t"/>
                                    <constraint firstAttribute="bottom" secondItem="MLS-Q6-Rtq" secondAttribute="bottom" id="fPC-U2-HLI"/>
                                    <constraint firstItem="MLS-Q6-Rtq" firstAttribute="top" secondItem="dh0-Lk-y2B" secondAttribute="top" constant="18" id="yZn-aR-T9h"/>
                                </constraints>
                            </view>
                            <containerView opaque="NO" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="AT1-sS-q1E">
                                <rect key="frame" x="0.0" y="118" width="375" height="660"/>
                                <connections>
                                    <segue destination="tff-51-iOK" kind="embed" id="eFx-Qu-nnC"/>
                                </connections>
                            </containerView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="1ca-Mz-Ih0"/>
                        <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="calibratedRGB"/>
                        <constraints>
                            <constraint firstItem="1ca-Mz-Ih0" firstAttribute="trailing" secondItem="AT1-sS-q1E" secondAttribute="trailing" id="22C-St-vwF"/>
                            <constraint firstItem="dh0-Lk-y2B" firstAttribute="top" secondItem="1ca-Mz-Ih0" secondAttribute="top" id="MZA-HR-FMN"/>
                            <constraint firstItem="dh0-Lk-y2B" firstAttribute="leading" secondItem="1ca-Mz-Ih0" secondAttribute="leading" id="Rqt-A0-0WH"/>
                            <constraint firstItem="AT1-sS-q1E" firstAttribute="top" secondItem="dh0-Lk-y2B" secondAttribute="bottom" id="U69-Cn-QO0"/>
                            <constraint firstItem="1ca-Mz-Ih0" firstAttribute="bottom" secondItem="AT1-sS-q1E" secondAttribute="bottom" id="XjK-ld-UWK"/>
                            <constraint firstItem="1ca-Mz-Ih0" firstAttribute="trailing" secondItem="dh0-Lk-y2B" secondAttribute="trailing" id="hsF-nn-K2q"/>
                            <constraint firstItem="AT1-sS-q1E" firstAttribute="leading" secondItem="1ca-Mz-Ih0" secondAttribute="leading" id="wOo-u1-Moo"/>
                        </constraints>
                    </view>
                    <extendedEdge key="edgesForExtendedLayout" bottom="YES"/>
                    <connections>
                        <outlet property="tabPager" destination="MLS-Q6-Rtq" id="e77-xS-JjV"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="J3J-Qd-Aq0" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1356" y="645"/>
        </scene>
        <!--List News View Controller-->
        <scene sceneID="3mD-QO-UcX">
            <objects>
                <viewController storyboardIdentifier="ListNewsViewController" id="Nls-0K-DGt" customClass="ListNewsViewController" customModule="Beta_Cinemas" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="fFS-xL-5xd">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="812"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="thu-yP-r4T">
                                <rect key="frame" x="0.0" y="50" width="375" height="728"/>
                                <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="calibratedRGB"/>
                                <connections>
                                    <outlet property="dataSource" destination="Nls-0K-DGt" id="V6D-P2-lEk"/>
                                    <outlet property="delegate" destination="Nls-0K-DGt" id="5am-vB-gZt"/>
                                </connections>
                            </tableView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="hI6-jA-jcM"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="hI6-jA-jcM" firstAttribute="bottom" secondItem="thu-yP-r4T" secondAttribute="bottom" id="9KE-u3-j5K"/>
                            <constraint firstAttribute="trailing" secondItem="thu-yP-r4T" secondAttribute="trailing" id="ScQ-jk-BR9"/>
                            <constraint firstItem="thu-yP-r4T" firstAttribute="leading" secondItem="hI6-jA-jcM" secondAttribute="leading" id="UTq-4m-Ygh"/>
                            <constraint firstItem="thu-yP-r4T" firstAttribute="top" secondItem="hI6-jA-jcM" secondAttribute="top" id="zM1-Pq-z3z"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="tableView" destination="thu-yP-r4T" id="uao-tF-X8N"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Qan-hV-TYR" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="2092" y="644"/>
        </scene>
        <!--Slide Menu View Controller-->
        <scene sceneID="Odb-dU-wkh">
            <objects>
                <viewController storyboardIdentifier="SlideMenuViewController" id="zTq-Gk-fgw" customClass="SlideMenuViewController" customModule="Beta_Cinemas" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="hWJ-PL-WHg">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="812"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" delaysContentTouches="NO" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="qfO-B7-HTp">
                                <rect key="frame" x="168.66666666666663" y="50" width="206.33333333333337" height="728"/>
                                <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <edgeInsets key="layoutMargins" top="0.0" left="0.0" bottom="0.0" right="0.0"/>
                                <view key="tableHeaderView" contentMode="scaleToFill" id="R2c-oq-Nz6">
                                    <rect key="frame" x="0.0" y="0.0" width="206.33333333333337" height="216"/>
                                    <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                                    <subviews>
                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="center" contentVerticalAlignment="center" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="i6X-eL-h50">
                                            <rect key="frame" x="174.33333333333334" y="67" width="24" height="24"/>
                                            <constraints>
                                                <constraint firstAttribute="width" secondItem="i6X-eL-h50" secondAttribute="height" multiplier="1:1" id="W0W-Po-v8m"/>
                                                <constraint firstAttribute="width" constant="24" id="ZIh-4K-zQF"/>
                                            </constraints>
                                            <state key="normal" image="icClose"/>
                                            <connections>
                                                <action selector="closeButtonPressed:" destination="zTq-Gk-fgw" eventType="touchUpInside" id="H09-xx-P0A"/>
                                            </connections>
                                        </button>
                                        <button opaque="NO" contentMode="scaleToFill" verticalCompressionResistancePriority="749" contentHorizontalAlignment="left" contentVerticalAlignment="center" reversesTitleShadowWhenHighlighted="YES" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="1gB-kj-RbL" customClass="LocalizableButton" customModule="Beta_Cinemas" customModuleProvider="target">
                                            <rect key="frame" x="0.0" y="101.00000000000001" width="206.33333333333334" height="57.666666666666671"/>
                                            <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="20"/>
                                            <state key="normal" title="ĐẶT VÉ THEO PHIM">
                                                <color key="titleColor" red="0.28627450980392155" green="0.29803921568627451" blue="0.38431372549019605" alpha="1" colorSpace="calibratedRGB"/>
                                            </state>
                                            <state key="selected">
                                                <color key="titleColor" red="0.0**********" green="0.34901960780000002" blue="0.61568627450000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            </state>
                                            <state key="highlighted">
                                                <color key="titleColor" red="0.0**********" green="0.34901960780000002" blue="0.61568627450000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            </state>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Menu.BookByMovie"/>
                                            </userDefinedRuntimeAttributes>
                                            <connections>
                                                <action selector="bookingByMoviePressed:" destination="zTq-Gk-fgw" eventType="touchUpInside" id="0yP-Ak-UKh"/>
                                            </connections>
                                        </button>
                                        <button opaque="NO" contentMode="scaleToFill" contentHorizontalAlignment="left" contentVerticalAlignment="center" showsTouchWhenHighlighted="YES" lineBreakMode="middleTruncation" translatesAutoresizingMaskIntoConstraints="NO" id="fLs-ww-qYY" customClass="LocalizableButton" customModule="Beta_Cinemas" customModuleProvider="target">
                                            <rect key="frame" x="0.0" y="158.66666666666666" width="206.33333333333334" height="57.333333333333343"/>
                                            <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="20"/>
                                            <state key="normal" title="ĐẶT VÉ THEO RẠP">
                                                <color key="titleColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="calibratedRGB"/>
                                            </state>
                                            <state key="selected">
                                                <color key="titleColor" red="0.0**********" green="0.34901960780000002" blue="0.61568627450000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            </state>
                                            <state key="highlighted">
                                                <color key="titleColor" red="0.0**********" green="0.34901960780000002" blue="0.61568627450000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                            </state>
                                            <userDefinedRuntimeAttributes>
                                                <userDefinedRuntimeAttribute type="string" keyPath="localizableString" value="Menu.BookByTheater"/>
                                            </userDefinedRuntimeAttributes>
                                            <connections>
                                                <action selector="bookingByCinemaPressed:" destination="zTq-Gk-fgw" eventType="touchUpInside" id="DfM-ot-0Rw"/>
                                            </connections>
                                        </button>
                                        <view alpha="0.10000000000000001" contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="s6W-MU-xpV">
                                            <rect key="frame" x="0.0" y="215" width="206.33333333333334" height="1"/>
                                            <color key="backgroundColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="calibratedRGB"/>
                                            <constraints>
                                                <constraint firstAttribute="height" constant="1" id="w63-0h-vKD"/>
                                            </constraints>
                                        </view>
                                    </subviews>
                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                    <constraints>
                                        <constraint firstAttribute="bottom" secondItem="s6W-MU-xpV" secondAttribute="bottom" id="12W-Rt-nal"/>
                                        <constraint firstItem="fLs-ww-qYY" firstAttribute="height" secondItem="1gB-kj-RbL" secondAttribute="height" id="A1o-Za-w0b"/>
                                        <constraint firstAttribute="bottom" secondItem="fLs-ww-qYY" secondAttribute="bottom" id="CS5-yj-ed3"/>
                                        <constraint firstAttribute="trailing" secondItem="s6W-MU-xpV" secondAttribute="trailing" id="KRP-b2-oXD"/>
                                        <constraint firstItem="i6X-eL-h50" firstAttribute="top" secondItem="R2c-oq-Nz6" secondAttribute="top" constant="67" id="OSG-5d-g4k"/>
                                        <constraint firstItem="1gB-kj-RbL" firstAttribute="top" secondItem="i6X-eL-h50" secondAttribute="bottom" constant="10" id="Oub-Q1-4GA"/>
                                        <constraint firstItem="s6W-MU-xpV" firstAttribute="leading" secondItem="R2c-oq-Nz6" secondAttribute="leading" id="SoO-rT-dGN"/>
                                        <constraint firstItem="fLs-ww-qYY" firstAttribute="top" secondItem="1gB-kj-RbL" secondAttribute="bottom" id="eDY-Ei-jdE"/>
                                        <constraint firstAttribute="trailing" secondItem="fLs-ww-qYY" secondAttribute="trailing" id="fBs-8g-QvG"/>
                                        <constraint firstAttribute="trailing" secondItem="i6X-eL-h50" secondAttribute="trailing" constant="8" id="m8h-NV-DwG"/>
                                        <constraint firstItem="1gB-kj-RbL" firstAttribute="leading" secondItem="R2c-oq-Nz6" secondAttribute="leading" id="p7K-pu-5sh"/>
                                        <constraint firstItem="fLs-ww-qYY" firstAttribute="leading" secondItem="R2c-oq-Nz6" secondAttribute="leading" id="prZ-DL-WiL"/>
                                        <constraint firstAttribute="trailing" secondItem="1gB-kj-RbL" secondAttribute="trailing" id="xHI-nq-XVI"/>
                                    </constraints>
                                </view>
                                <prototypes>
                                    <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" selectionStyle="default" indentationWidth="0.0" reuseIdentifier="MenuItemCell" rowHeight="104" id="IRq-95-TgC" customClass="MenuItemCell" customModule="Beta_Cinemas" customModuleProvider="target">
                                        <rect key="frame" x="0.0" y="266" width="206.33333333333337" height="104"/>
                                        <autoresizingMask key="autoresizingMask"/>
                                        <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="IRq-95-TgC" id="apX-gw-v4b">
                                            <rect key="frame" x="0.0" y="0.0" width="206.33333333333337" height="104"/>
                                            <autoresizingMask key="autoresizingMask"/>
                                            <subviews>
                                                <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="yka-Tz-kFs">
                                                    <rect key="frame" x="0.0" y="0.0" width="206.33333333333334" height="104"/>
                                                    <subviews>
                                                        <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="4Yo-SI-afg">
                                                            <rect key="frame" x="0.0" y="45" width="26" height="26"/>
                                                            <constraints>
                                                                <constraint firstAttribute="width" constant="26" id="9Ju-pM-jmd"/>
                                                                <constraint firstAttribute="height" constant="26" id="TKC-Ir-mIM"/>
                                                            </constraints>
                                                        </imageView>
                                                        <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="JFB-VV-6Db">
                                                            <rect key="frame" x="34" y="18" width="31" height="80"/>
                                                            <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="16"/>
                                                            <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="calibratedRGB"/>
                                                            <nil key="highlightedColor"/>
                                                        </label>
                                                        <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="93l-W5-sXl" customClass="RoundView" customModule="Beta_Cinemas" customModuleProvider="target">
                                                            <rect key="frame" x="39" y="102" width="21" height="2"/>
                                                            <color key="backgroundColor" red="0.0**********" green="0.34901960780000002" blue="0.61568627450000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                            <constraints>
                                                                <constraint firstAttribute="width" constant="21" id="Fuj-6i-XCu"/>
                                                                <constraint firstAttribute="height" constant="2" id="dPJ-7r-ggS"/>
                                                            </constraints>
                                                            <userDefinedRuntimeAttributes>
                                                                <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                                    <real key="value" value="1.5"/>
                                                                </userDefinedRuntimeAttribute>
                                                            </userDefinedRuntimeAttributes>
                                                        </view>
                                                    </subviews>
                                                    <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                                    <constraints>
                                                        <constraint firstItem="JFB-VV-6Db" firstAttribute="centerY" secondItem="4Yo-SI-afg" secondAttribute="centerY" id="5wT-UU-TMg"/>
                                                        <constraint firstItem="93l-W5-sXl" firstAttribute="centerX" secondItem="JFB-VV-6Db" secondAttribute="centerX" id="LAs-tE-r3d"/>
                                                        <constraint firstAttribute="bottom" secondItem="93l-W5-sXl" secondAttribute="bottom" id="Oaz-RH-UzH"/>
                                                        <constraint firstItem="JFB-VV-6Db" firstAttribute="leading" secondItem="4Yo-SI-afg" secondAttribute="trailing" constant="8" id="Wje-ie-Pyj"/>
                                                        <constraint firstItem="JFB-VV-6Db" firstAttribute="top" secondItem="yka-Tz-kFs" secondAttribute="top" constant="18" id="X0A-Ku-aJi"/>
                                                        <constraint firstItem="4Yo-SI-afg" firstAttribute="leading" secondItem="yka-Tz-kFs" secondAttribute="leading" id="a2l-ZC-Un3"/>
                                                        <constraint firstItem="93l-W5-sXl" firstAttribute="top" secondItem="JFB-VV-6Db" secondAttribute="bottom" constant="4" id="kGm-u9-3sU"/>
                                                    </constraints>
                                                </view>
                                            </subviews>
                                            <constraints>
                                                <constraint firstItem="yka-Tz-kFs" firstAttribute="leading" secondItem="apX-gw-v4b" secondAttribute="leading" id="I8m-LC-jup"/>
                                                <constraint firstAttribute="bottom" secondItem="yka-Tz-kFs" secondAttribute="bottom" id="ICf-3g-ZEx"/>
                                                <constraint firstItem="yka-Tz-kFs" firstAttribute="top" secondItem="apX-gw-v4b" secondAttribute="top" id="NT9-4R-t1Q"/>
                                                <constraint firstAttribute="trailing" secondItem="yka-Tz-kFs" secondAttribute="trailing" id="SWo-xu-Qgv"/>
                                            </constraints>
                                        </tableViewCellContentView>
                                        <edgeInsets key="layoutMargins" top="0.0" left="0.0" bottom="0.0" right="0.0"/>
                                        <connections>
                                            <outlet property="ivIcon" destination="4Yo-SI-afg" id="fMT-3l-6xt"/>
                                            <outlet property="lbTitle" destination="JFB-VV-6Db" id="Z5O-iE-ebv"/>
                                            <outlet property="vUnderline" destination="93l-W5-sXl" id="rz7-bf-9Lf"/>
                                        </connections>
                                    </tableViewCell>
                                </prototypes>
                                <connections>
                                    <outlet property="dataSource" destination="zTq-Gk-fgw" id="pdp-bP-6Kf"/>
                                    <outlet property="delegate" destination="zTq-Gk-fgw" id="jaq-nZ-J2Q"/>
                                </connections>
                            </tableView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="qJk-wY-qc5"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="qJk-wY-qc5" firstAttribute="trailing" secondItem="qfO-B7-HTp" secondAttribute="trailing" id="1Rg-iN-Cin"/>
                            <constraint firstItem="qfO-B7-HTp" firstAttribute="width" secondItem="qJk-wY-qc5" secondAttribute="width" multiplier="0.55" id="nNp-ta-bv6"/>
                            <constraint firstItem="qJk-wY-qc5" firstAttribute="bottom" secondItem="qfO-B7-HTp" secondAttribute="bottom" id="pN1-38-FMa"/>
                            <constraint firstItem="qfO-B7-HTp" firstAttribute="top" secondItem="qJk-wY-qc5" secondAttribute="top" id="w75-up-FAs"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="btRegisterByMovie" destination="1gB-kj-RbL" id="60u-rj-bKo"/>
                        <outlet property="btRegisterByTheater" destination="fLs-ww-qYY" id="tRF-sE-QsU"/>
                        <outlet property="tableView" destination="qfO-B7-HTp" id="75z-kf-9Dm"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="Hdn-f8-7Qr" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="871.20000000000005" y="-12.143928035982009"/>
        </scene>
        <!--Page View Controller-->
        <scene sceneID="4gR-la-f0s">
            <objects>
                <pageViewController autoresizesArchivedViewToFullSize="NO" transitionStyle="scroll" navigationOrientation="horizontal" spineLocation="none" id="tff-51-iOK" sceneMemberID="viewController"/>
                <placeholder placeholderIdentifier="IBFirstResponder" id="xsc-wq-N5A" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="1769" y="-31"/>
        </scene>
        <!--Base Navigation View Controller-->
        <scene sceneID="acg-Vo-Bsa">
            <objects>
                <navigationController storyboardIdentifier="HomeNavigationController" automaticallyAdjustsScrollViewInsets="NO" id="2Lj-u3-4ly" customClass="BaseNavigationViewController" customModule="Beta_Cinemas" customModuleProvider="target" sceneMemberID="viewController">
                    <toolbarItems/>
                    <navigationBar key="navigationBar" opaque="NO" contentMode="scaleToFill" insetsLayoutMarginsFromSafeArea="NO" translucent="NO" id="cuu-be-guJ">
                        <rect key="frame" x="0.0" y="50" width="375" height="44"/>
                        <autoresizingMask key="autoresizingMask"/>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                    </navigationBar>
                    <nil name="viewControllers"/>
                    <connections>
                        <segue destination="DwD-6Y-uMb" kind="relationship" relationship="rootViewController" id="dh1-Ty-Jni"/>
                    </connections>
                </navigationController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="kuY-Mg-UVg" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-471" y="-11"/>
        </scene>
        <!--Recruitment View Controller-->
        <scene sceneID="yIw-Ol-AAk">
            <objects>
                <viewController storyboardIdentifier="RecruitmentViewController" id="nlq-43-WXL" customClass="RecruitmentViewController" customModule="Beta_Cinemas" customModuleProvider="target" sceneMemberID="viewController">
                    <view key="view" contentMode="scaleToFill" id="txH-Br-gy3">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="812"/>
                        <autoresizingMask key="autoresizingMask" widthSizable="YES" heightSizable="YES"/>
                        <subviews>
                            <tableView clipsSubviews="YES" contentMode="scaleToFill" alwaysBounceVertical="YES" dataMode="prototypes" style="plain" separatorStyle="none" rowHeight="-1" estimatedRowHeight="-1" sectionHeaderHeight="28" sectionFooterHeight="28" translatesAutoresizingMaskIntoConstraints="NO" id="sco-1v-9Kc">
                                <rect key="frame" x="0.0" y="50" width="375" height="728"/>
                                <color key="backgroundColor" red="0.95294117649999999" green="0.95294117649999999" blue="0.95294117649999999" alpha="1" colorSpace="calibratedRGB"/>
                                <connections>
                                    <outlet property="dataSource" destination="nlq-43-WXL" id="AI5-fF-0GD"/>
                                    <outlet property="delegate" destination="nlq-43-WXL" id="8WP-xM-E8E"/>
                                </connections>
                            </tableView>
                        </subviews>
                        <viewLayoutGuide key="safeArea" id="zxb-Yt-TE2"/>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="zxb-Yt-TE2" firstAttribute="bottom" secondItem="sco-1v-9Kc" secondAttribute="bottom" id="5JO-PR-cJ7"/>
                            <constraint firstItem="sco-1v-9Kc" firstAttribute="top" secondItem="zxb-Yt-TE2" secondAttribute="top" id="Lkr-vF-mRE"/>
                            <constraint firstItem="sco-1v-9Kc" firstAttribute="leading" secondItem="zxb-Yt-TE2" secondAttribute="leading" id="ZnL-4D-C8W"/>
                            <constraint firstItem="zxb-Yt-TE2" firstAttribute="trailing" secondItem="sco-1v-9Kc" secondAttribute="trailing" id="tyk-MG-9Mq"/>
                        </constraints>
                    </view>
                    <connections>
                        <outlet property="tableView" destination="sco-1v-9Kc" id="UAE-Os-MGN"/>
                    </connections>
                </viewController>
                <placeholder placeholderIdentifier="IBFirstResponder" id="e7W-9G-LID" userLabel="First Responder" sceneMemberID="firstResponder"/>
            </objects>
            <point key="canvasLocation" x="-471.19999999999999" y="665.76354679802955"/>
        </scene>
    </scenes>
    <designables>
        <designable name="1gB-kj-RbL">
            <size key="intrinsicContentSize" width="138" height="42"/>
        </designable>
        <designable name="9ai-cs-Xz8">
            <size key="intrinsicContentSize" width="126.66666666666667" height="29.666666666666668"/>
        </designable>
        <designable name="fLs-ww-qYY">
            <size key="intrinsicContentSize" width="129" height="42"/>
        </designable>
        <designable name="qr0-zS-S7A">
            <size key="intrinsicContentSize" width="71" height="48"/>
        </designable>
        <designable name="sFw-5P-9fW">
            <size key="intrinsicContentSize" width="40" height="33"/>
        </designable>
        <designable name="sSf-Qo-0ig">
            <size key="intrinsicContentSize" width="69" height="48"/>
        </designable>
    </designables>
    <resources>
        <image name="bg1.png" width="320" height="568"/>
        <image name="icClose" width="24" height="24"/>
    </resources>
</document>

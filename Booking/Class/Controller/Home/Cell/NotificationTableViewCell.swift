//
//  NotificationTableViewCell.swift
//  Booking
//
//  Created by <PERSON><PERSON>u on 4/13/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit
import DRPLoadingSpinner
import RxSwift
import WebKit

protocol NotificationWebDelegate: class {
    func notificationDidLoadWebView(_ indexPath: IndexPath)
}

class NotificationTableViewCell: UITableViewCell {

    @IBOutlet weak var roundView: RoundView!
    @IBOutlet weak var titleLabel: UILabel!
    @IBOutlet weak var detailView: UIView!
    @IBOutlet weak var dateLabel: UILabel!
    
    @IBOutlet weak var detailViewHeight: NSLayoutConstraint!
    @IBOutlet weak var loadingView: DRPLoadingSpinner!
    @IBOutlet weak var arrowDownImageView: UIImageView!
    @IBOutlet weak var detailLabel: UILabel!
    
    @IBOutlet weak var dateLabelHeight: NSLayoutConstraint!
    
    
    fileprivate var news: NewNotification?
    var indexPath: IndexPath!

    fileprivate var webview: WKWebView!

    weak var delegate: NotificationWebDelegate?
    
    var isOpen: Bool = false {
        didSet {
            detailView.isHidden = !isOpen
            self.arrowDownImageView.transform = CGAffineTransform.identity
            UIView.animate(withDuration: 0.3) {
                self.arrowDownImageView.transform = CGAffineTransform(rotationAngle: self.isOpen ? .pi / 2 : 0)
            }
        }
    }
    
    override func awakeFromNib() {
        super.awakeFromNib()
        detailView.isHidden = true

//        self.roundView.backgroundColor = UIColor.init("0xFFE6E6")
//        let configuration = WKWebViewConfiguration()
//        webview = WKWebView(frame: detailView.bounds, configuration: configuration)
//        webview.autoresizingMask = [.flexibleWidth, .flexibleHeight]
//        webview.navigationDelegate = self
//        detailView.addSubview(webview)
//
//        webview.scrollView.addObserver(self, forKeyPath: "contentSize", options: .new, context: nil)
//
//        detailView.bringSubview(toFront: loadingView)
    }

    deinit {
//        webview.scrollView.removeObserver(self, forKeyPath: "contentSize")
    }

    func showLoading() {
        self.loadingView.isHidden = false
        self.loadingView.startAnimating()
    }

    func hideLoading() {
        self.loadingView.isHidden = true
        self.loadingView.stopAnimating()
    }

    override func updateViewWithItem(_ tbItem: TableItem, indexPath: IndexPath) {
        self.indexPath = indexPath
        guard let item = tbItem.data as? NewNotification else {
            titleLabel.text = tbItem.title
            if let str = tbItem.detailAttributed {
                detailLabel.attributedText = str
            } else {
                detailLabel.text = tbItem.content
            }
            dateLabel.text = nil
            arrowDownImageView.transform = CGAffineTransform(rotationAngle: tbItem.isOpen ? .pi : 0)
            detailView.isHidden = !tbItem.isOpen
            return
        }
        self.news = item

        titleLabel.text = item.Title
//        detailLabel.attributedText = item.Noi_dung_chi_tiet?.first?.ParagraphData?.ParagraphContent?.htmlToAttributedString(14)
//        if let html = item.Noi_dung_chi_tiet?.first?.ParagraphData?.ParagraphContent {
//            webview.loadHTMLString(html, baseURL: URL(string: Config.BaseURL))
//        }
//        dateLabel.text = item.getStartDateString()
        dateLabelHeight.constant = 0
        self.contentView.updateConstraints()
        
        if let status = item.ReadStatus, !status {
//            self.roundView.backgroundColor = UIColor.init("0xFFE6E6")
            self.titleLabel.font = UIFont(fontName: .SourceSansPro, style: .Bold, size: 16)
        } else {
//            self.roundView.backgroundColor = UIColor.white
            self.titleLabel.font = UIFont(fontName: .SourceSansPro, style: .Regular, size: 16)
        }

        arrowDownImageView.transform = CGAffineTransform(rotationAngle: tbItem.isOpen ? .pi : 0)
        detailView.isHidden = !tbItem.isOpen
    }
    
    override func setSelected(_ selected: Bool, animated: Bool) {
        
    }
    
    override func setHighlighted(_ highlighted: Bool, animated: Bool) {
        
    }

    override func observeValue(forKeyPath keyPath: String?, of object: Any?, change: [NSKeyValueChangeKey : Any]?, context: UnsafeMutableRawPointer?) {
        if keyPath == "contentSize" {
            let height = webview.scrollView.contentSize.height
            if height != detailViewHeight.constant {
                detailViewHeight.constant = height
                delegate?.notificationDidLoadWebView(self.indexPath)
            }
        }
    }
}

extension NotificationTableViewCell: WKNavigationDelegate {
    
}

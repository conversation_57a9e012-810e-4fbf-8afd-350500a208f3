<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14490.70" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina4_7" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14490.49"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="SourceSansPro-Regular.ttf">
            <string>SourceSansPro-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell clipsSubviews="YES" contentMode="scaleToFill" preservesSuperviewLayoutMargins="YES" selectionStyle="default" indentationWidth="10" reuseIdentifier="NotificationTableViewCell" rowHeight="185" id="GWV-Og-Khw" customClass="NotificationTableViewCell" customModule="Booking_dev" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="375" height="185"/>
            <autoresizingMask key="autoresizingMask"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" preservesSuperviewLayoutMargins="YES" insetsLayoutMarginsFromSafeArea="NO" tableViewCell="GWV-Og-Khw" id="SX3-z7-U13">
                <rect key="frame" x="0.0" y="0.0" width="375" height="184.5"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <stackView opaque="NO" contentMode="scaleToFill" axis="vertical" translatesAutoresizingMaskIntoConstraints="NO" id="R4c-D6-6Ur">
                        <rect key="frame" x="0.0" y="0.0" width="375" height="184.5"/>
                        <subviews>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="uUN-c4-L4j">
                                <rect key="frame" x="0.0" y="0.0" width="375" height="134.5"/>
                                <subviews>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="Y6C-cb-oSg" customClass="RoundView" customModule="Booking_dev" customModuleProvider="target">
                                        <rect key="frame" x="8" y="8" width="359" height="89.5"/>
                                        <subviews>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="249" text="Quà tặng từ Mr &amp; Mrs Grey-Special gift from Mr &amp; Mrs Grey" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="7vp-Of-oxD">
                                                <rect key="frame" x="12" y="15" width="285" height="40.5"/>
                                                <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                                <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="30/03/2018" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="RhX-Uf-M3P">
                                                <rect key="frame" x="12" y="63.5" width="285" height="18"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="18" id="jCm-gx-92T"/>
                                                </constraints>
                                                <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="14"/>
                                                <color key="textColor" red="0.01176470588" green="0.34901960780000002" blue="0.61568627450000002" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                                <nil key="highlightedColor"/>
                                            </label>
                                            <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_arrow_right" translatesAutoresizingMaskIntoConstraints="NO" id="MQk-mL-aEQ">
                                                <rect key="frame" x="317" y="29" width="32" height="32"/>
                                                <constraints>
                                                    <constraint firstAttribute="height" constant="32" id="DgT-8j-ueQ"/>
                                                    <constraint firstAttribute="width" constant="32" id="OqI-XF-GEk"/>
                                                </constraints>
                                            </imageView>
                                        </subviews>
                                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="bottom" secondItem="RhX-Uf-M3P" secondAttribute="bottom" constant="8" id="Aqa-Go-WXM"/>
                                            <constraint firstItem="RhX-Uf-M3P" firstAttribute="top" secondItem="7vp-Of-oxD" secondAttribute="bottom" constant="8" id="MfY-OY-4xq"/>
                                            <constraint firstItem="7vp-Of-oxD" firstAttribute="top" secondItem="Y6C-cb-oSg" secondAttribute="top" constant="15" id="W32-d4-fS7"/>
                                            <constraint firstItem="MQk-mL-aEQ" firstAttribute="leading" secondItem="7vp-Of-oxD" secondAttribute="trailing" constant="20" id="brk-AY-rYD"/>
                                            <constraint firstItem="RhX-Uf-M3P" firstAttribute="leading" secondItem="7vp-Of-oxD" secondAttribute="leading" id="kEa-qi-Tac"/>
                                            <constraint firstItem="7vp-Of-oxD" firstAttribute="leading" secondItem="Y6C-cb-oSg" secondAttribute="leading" constant="12" id="mOq-02-OUL"/>
                                            <constraint firstItem="RhX-Uf-M3P" firstAttribute="trailing" secondItem="7vp-Of-oxD" secondAttribute="trailing" id="oRX-LR-xZN"/>
                                            <constraint firstItem="MQk-mL-aEQ" firstAttribute="centerY" secondItem="Y6C-cb-oSg" secondAttribute="centerY" id="ssv-3J-Wi0"/>
                                            <constraint firstAttribute="trailing" secondItem="MQk-mL-aEQ" secondAttribute="trailing" constant="10" id="yMi-rR-KmQ"/>
                                        </constraints>
                                        <userDefinedRuntimeAttributes>
                                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                                <real key="value" value="2"/>
                                            </userDefinedRuntimeAttribute>
                                        </userDefinedRuntimeAttributes>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="Y6C-cb-oSg" firstAttribute="leading" secondItem="uUN-c4-L4j" secondAttribute="leading" constant="8" id="3sY-J3-loR"/>
                                    <constraint firstAttribute="trailing" secondItem="Y6C-cb-oSg" secondAttribute="trailing" constant="8" id="ISE-Tg-Xan"/>
                                    <constraint firstItem="Y6C-cb-oSg" firstAttribute="top" secondItem="uUN-c4-L4j" secondAttribute="top" constant="8" id="dOE-Zn-a2k"/>
                                    <constraint firstAttribute="bottom" relation="greaterThanOrEqual" secondItem="Y6C-cb-oSg" secondAttribute="bottom" constant="8" id="zXc-MB-ywG"/>
                                </constraints>
                            </view>
                            <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="X9Q-Eq-ZDe">
                                <rect key="frame" x="0.0" y="134.5" width="375" height="50"/>
                                <subviews>
                                    <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Tặng gối tình yêu khi mua 2 vé xem phim Năm Mươi Sắc Thái." textAlignment="justified" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="EUs-3Q-qcM">
                                        <rect key="frame" x="16" y="0.0" width="343" height="40.5"/>
                                        <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                        <color key="textColor" red="0.1176470588" green="0.1215686275" blue="0.15686274510000001" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                        <nil key="highlightedColor"/>
                                    </label>
                                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="nrk-kf-w6Y" customClass="DRPLoadingSpinner">
                                        <rect key="frame" x="172.5" y="10" width="30" height="30"/>
                                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                        <constraints>
                                            <constraint firstAttribute="width" constant="30" id="6hp-eO-gWi"/>
                                            <constraint firstAttribute="height" constant="30" id="C4p-7P-19d"/>
                                        </constraints>
                                    </view>
                                </subviews>
                                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                                <constraints>
                                    <constraint firstItem="EUs-3Q-qcM" firstAttribute="leading" secondItem="X9Q-Eq-ZDe" secondAttribute="leading" constant="16" id="4c6-jO-6iW"/>
                                    <constraint firstAttribute="bottom" relation="greaterThanOrEqual" secondItem="nrk-kf-w6Y" secondAttribute="bottom" constant="10" id="EK1-QE-QXD"/>
                                    <constraint firstAttribute="trailing" secondItem="EUs-3Q-qcM" secondAttribute="trailing" constant="16" id="On5-Qw-GYa"/>
                                    <constraint firstAttribute="bottom" secondItem="EUs-3Q-qcM" secondAttribute="bottom" priority="250" constant="8" id="W0x-Th-TRp"/>
                                    <constraint firstAttribute="height" priority="750" constant="50" id="mOH-Qj-vSB"/>
                                    <constraint firstItem="EUs-3Q-qcM" firstAttribute="top" secondItem="X9Q-Eq-ZDe" secondAttribute="top" id="n6k-tX-XrP"/>
                                    <constraint firstItem="nrk-kf-w6Y" firstAttribute="centerX" secondItem="X9Q-Eq-ZDe" secondAttribute="centerX" id="qQd-hz-pys"/>
                                    <constraint firstItem="nrk-kf-w6Y" firstAttribute="top" secondItem="X9Q-Eq-ZDe" secondAttribute="top" constant="10" id="sO8-Y5-otc"/>
                                </constraints>
                                <variation key="default">
                                    <mask key="constraints">
                                        <exclude reference="mOH-Qj-vSB"/>
                                    </mask>
                                </variation>
                            </view>
                        </subviews>
                    </stackView>
                </subviews>
                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                <constraints>
                    <constraint firstItem="R4c-D6-6Ur" firstAttribute="leading" secondItem="SX3-z7-U13" secondAttribute="leading" id="2kH-ht-apO"/>
                    <constraint firstAttribute="trailing" secondItem="R4c-D6-6Ur" secondAttribute="trailing" id="Icr-8O-Rf7"/>
                    <constraint firstAttribute="bottom" secondItem="R4c-D6-6Ur" secondAttribute="bottom" id="eBP-CD-hPV"/>
                    <constraint firstItem="R4c-D6-6Ur" firstAttribute="top" secondItem="SX3-z7-U13" secondAttribute="top" id="wly-PW-Vr8"/>
                </constraints>
            </tableViewCellContentView>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <viewLayoutGuide key="safeArea" id="ZUc-jq-L8K"/>
            <connections>
                <outlet property="arrowDownImageView" destination="MQk-mL-aEQ" id="JxE-x3-GYp"/>
                <outlet property="dateLabel" destination="RhX-Uf-M3P" id="GXb-Ak-3JH"/>
                <outlet property="dateLabelHeight" destination="jCm-gx-92T" id="z6h-GL-Hn9"/>
                <outlet property="detailLabel" destination="EUs-3Q-qcM" id="j7W-8p-Ug6"/>
                <outlet property="detailView" destination="X9Q-Eq-ZDe" id="snf-Df-SQG"/>
                <outlet property="detailViewHeight" destination="mOH-Qj-vSB" id="igM-1o-c54"/>
                <outlet property="loadingView" destination="nrk-kf-w6Y" id="0VA-7h-zA9"/>
                <outlet property="roundView" destination="Y6C-cb-oSg" id="8lz-fQ-WBr"/>
                <outlet property="titleLabel" destination="7vp-Of-oxD" id="AM0-3d-RLR"/>
            </connections>
        </tableViewCell>
    </objects>
    <resources>
        <image name="ic_arrow_right" width="26" height="26"/>
    </resources>
</document>

<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14490.70" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait">
        <adaptation id="fullscreen"/>
    </device>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14490.49"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="SourceSansPro-Regular.ttf">
            <string>SourceSansPro-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <tableViewCell contentMode="scaleToFill" selectionStyle="default" indentationWidth="10" rowHeight="124" id="KGk-i7-Jjw" customClass="NotificationCell" customModule="Booking_dev" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="320" height="124"/>
            <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
            <tableViewCellContentView key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" tableViewCell="KGk-i7-Jjw" id="H2p-sc-9uM">
                <rect key="frame" x="0.0" y="0.0" width="320" height="123.5"/>
                <autoresizingMask key="autoresizingMask"/>
                <subviews>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="LTq-jK-jIU" customClass="RoundView" customModule="Booking_dev" customModuleProvider="target">
                        <rect key="frame" x="8" y="8" width="304" height="107.5"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="249" text="Quà tặng từ Mr &amp; Mrs Grey-Special gift from Mr &amp; Mrs Grey" textAlignment="natural" lineBreakMode="tailTruncation" numberOfLines="0" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="Px2-wj-n2L">
                                <rect key="frame" x="12" y="16" width="230" height="49.5"/>
                                <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="16"/>
                                <color key="textColor" red="0.28627450980000002" green="0.29803921570000003" blue="0.38431372549999998" alpha="1" colorSpace="custom" customColorSpace="sRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <imageView userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_arrow_right" translatesAutoresizingMaskIntoConstraints="NO" id="wZh-qZ-0hb">
                                <rect key="frame" x="262" y="38" width="32" height="32"/>
                                <constraints>
                                    <constraint firstAttribute="width" constant="32" id="Gyr-hC-yDc"/>
                                    <constraint firstAttribute="height" constant="32" id="Pv4-6L-Oce"/>
                                </constraints>
                            </imageView>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="natural" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="u8w-hJ-0CI">
                                <rect key="frame" x="12" y="73.5" width="230" height="18"/>
                                <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="14"/>
                                <color key="textColor" red="0.24705882352941178" green="0.71764705882352942" blue="0.97647058823529409" alpha="1" colorSpace="calibratedRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <color key="backgroundColor" white="1" alpha="1" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="Px2-wj-n2L" firstAttribute="leading" secondItem="LTq-jK-jIU" secondAttribute="leading" constant="12" id="Ick-Ei-AxQ"/>
                            <constraint firstItem="Px2-wj-n2L" firstAttribute="top" secondItem="LTq-jK-jIU" secondAttribute="top" constant="16" id="X0S-bx-Rm4"/>
                            <constraint firstItem="u8w-hJ-0CI" firstAttribute="top" secondItem="Px2-wj-n2L" secondAttribute="bottom" constant="8" id="aCu-7Y-Ksq"/>
                            <constraint firstItem="u8w-hJ-0CI" firstAttribute="leading" secondItem="Px2-wj-n2L" secondAttribute="leading" id="bvT-Ar-M8i"/>
                            <constraint firstItem="u8w-hJ-0CI" firstAttribute="trailing" secondItem="Px2-wj-n2L" secondAttribute="trailing" id="cWG-Cv-Sma"/>
                            <constraint firstItem="wZh-qZ-0hb" firstAttribute="centerY" secondItem="LTq-jK-jIU" secondAttribute="centerY" id="nTo-w9-BGT"/>
                            <constraint firstAttribute="bottom" secondItem="u8w-hJ-0CI" secondAttribute="bottom" constant="16" id="ope-BK-bD3"/>
                            <constraint firstItem="wZh-qZ-0hb" firstAttribute="leading" secondItem="Px2-wj-n2L" secondAttribute="trailing" constant="20" id="pZ7-xG-clT"/>
                            <constraint firstAttribute="trailing" secondItem="wZh-qZ-0hb" secondAttribute="trailing" constant="10" id="sWP-KK-jM0"/>
                        </constraints>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="2"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </view>
                </subviews>
                <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                <constraints>
                    <constraint firstAttribute="bottom" secondItem="LTq-jK-jIU" secondAttribute="bottom" constant="8" id="8Jv-MR-vzJ"/>
                    <constraint firstItem="LTq-jK-jIU" firstAttribute="leading" secondItem="H2p-sc-9uM" secondAttribute="leading" constant="8" id="FpL-iB-lBG"/>
                    <constraint firstAttribute="trailing" secondItem="LTq-jK-jIU" secondAttribute="trailing" constant="8" id="Jva-pD-PfM"/>
                    <constraint firstItem="LTq-jK-jIU" firstAttribute="top" secondItem="H2p-sc-9uM" secondAttribute="top" constant="8" id="N2C-Y2-UpV"/>
                </constraints>
            </tableViewCellContentView>
            <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
            <viewLayoutGuide key="safeArea" id="njF-e1-oar"/>
            <inset key="separatorInset" minX="1000" minY="0.0" maxX="0.0" maxY="0.0"/>
            <connections>
                <outlet property="dateLabel" destination="u8w-hJ-0CI" id="cty-i2-qPJ"/>
                <outlet property="titleLabel" destination="Px2-wj-n2L" id="bPp-Nz-PAC"/>
            </connections>
            <point key="canvasLocation" x="131.8840579710145" y="179.46428571428569"/>
        </tableViewCell>
    </objects>
    <resources>
        <image name="ic_arrow_right" width="26" height="26"/>
    </resources>
</document>

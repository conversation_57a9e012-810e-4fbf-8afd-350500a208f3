<?xml version="1.0" encoding="UTF-8"?>
<document type="com.apple.InterfaceBuilder3.CocoaTouch.XIB" version="3.0" toolsVersion="14868" targetRuntime="iOS.CocoaTouch" propertyAccessControl="none" useAutolayout="YES" useTraitCollections="YES" useSafeAreas="YES" colorMatched="YES">
    <device id="retina6_1" orientation="portrait" appearance="light"/>
    <dependencies>
        <deployment identifier="iOS"/>
        <plugIn identifier="com.apple.InterfaceBuilder.IBCocoaTouchPlugin" version="14824"/>
        <capability name="Safe area layout guides" minToolsVersion="9.0"/>
        <capability name="documents saved in the Xcode 8 format" minToolsVersion="8.0"/>
    </dependencies>
    <customFonts key="customFonts">
        <array key="Oswald-Regular.ttf">
            <string>Oswald-Regular</string>
        </array>
        <array key="SourceSansPro-Regular.ttf">
            <string>SourceSansPro-Regular</string>
        </array>
    </customFonts>
    <objects>
        <placeholder placeholderIdentifier="IBFilesOwner" id="-1" userLabel="File's Owner"/>
        <placeholder placeholderIdentifier="IBFirstResponder" id="-2" customClass="UIResponder"/>
        <collectionViewCell opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center" id="gTV-IL-0wX" customClass="HomeCollectionViewCell" customModule="Booking_dev" customModuleProvider="target">
            <rect key="frame" x="0.0" y="0.0" width="178" height="234"/>
            <autoresizingMask key="autoresizingMask"/>
            <view key="contentView" opaque="NO" clipsSubviews="YES" multipleTouchEnabled="YES" contentMode="center">
                <rect key="frame" x="0.0" y="0.0" width="178" height="234"/>
                <autoresizingMask key="autoresizingMask" flexibleMaxX="YES" flexibleMaxY="YES"/>
                <subviews>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleToFill" horizontalHuggingPriority="251" verticalHuggingPriority="251" translatesAutoresizingMaskIntoConstraints="NO" id="Ffs-Ap-nZC" customClass="RoundImageView" customModule="Booking_dev" customModuleProvider="target">
                        <rect key="frame" x="0.0" y="0.0" width="178" height="164"/>
                        <userDefinedRuntimeAttributes>
                            <userDefinedRuntimeAttribute type="number" keyPath="cornerRadius">
                                <real key="value" value="5"/>
                            </userDefinedRuntimeAttribute>
                        </userDefinedRuntimeAttributes>
                    </imageView>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_c13" translatesAutoresizingMaskIntoConstraints="NO" id="X9d-Bb-nAZ">
                        <rect key="frame" x="4" y="4" width="34" height="16"/>
                        <constraints>
                            <constraint firstAttribute="width" constant="34" id="YNu-TF-wkf"/>
                            <constraint firstAttribute="height" constant="16" id="hVO-Hl-kQ8"/>
                        </constraints>
                    </imageView>
                    <view contentMode="scaleToFill" translatesAutoresizingMaskIntoConstraints="NO" id="y3f-cr-186">
                        <rect key="frame" x="0.0" y="172" width="178" height="62"/>
                        <subviews>
                            <label opaque="NO" userInteractionEnabled="NO" alpha="0.55000000000000004" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="120 phut" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="F4i-OD-uaP">
                                <rect key="frame" x="66.5" y="41" width="45" height="16"/>
                                <fontDescription key="fontDescription" name="SourceSansPro-Regular" family="Source Sans Pro" pointSize="12"/>
                                <color key="textColor" red="0.13333333333333333" green="0.2196078431372549" blue="0.28627450980392155" alpha="1" colorSpace="calibratedRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Ác Quỷ Trong Gương" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="jaz-SU-ajB">
                                <rect key="frame" x="8" y="20" width="162" height="21"/>
                                <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="14"/>
                                <color key="textColor" red="0.1333333333" green="0.21960784310000001" blue="0.28627450980000002" alpha="1" colorSpace="calibratedRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                            <label opaque="NO" userInteractionEnabled="NO" contentMode="left" horizontalHuggingPriority="251" verticalHuggingPriority="251" text="Label" textAlignment="center" lineBreakMode="tailTruncation" baselineAdjustment="alignBaselines" adjustsFontSizeToFit="NO" translatesAutoresizingMaskIntoConstraints="NO" id="kqM-uy-MqK">
                                <rect key="frame" x="8" y="0.0" width="162" height="18"/>
                                <fontDescription key="fontDescription" name="Oswald-Regular" family="Oswald" pointSize="12"/>
                                <color key="textColor" red="0.24705882352941178" green="0.71764705882352942" blue="0.97647058823529409" alpha="1" colorSpace="calibratedRGB"/>
                                <nil key="highlightedColor"/>
                            </label>
                        </subviews>
                        <color key="backgroundColor" white="0.0" alpha="0.0" colorSpace="custom" customColorSpace="genericGamma22GrayColorSpace"/>
                        <constraints>
                            <constraint firstItem="kqM-uy-MqK" firstAttribute="leading" secondItem="y3f-cr-186" secondAttribute="leading" constant="8" id="IR9-yh-ne1"/>
                            <constraint firstItem="F4i-OD-uaP" firstAttribute="centerX" secondItem="y3f-cr-186" secondAttribute="centerX" id="MKi-3O-P8e"/>
                            <constraint firstAttribute="height" constant="62" id="MXm-E7-yrj"/>
                            <constraint firstItem="jaz-SU-ajB" firstAttribute="leading" secondItem="y3f-cr-186" secondAttribute="leading" constant="8" id="Mmv-i9-d2B"/>
                            <constraint firstItem="kqM-uy-MqK" firstAttribute="top" secondItem="y3f-cr-186" secondAttribute="top" id="bx5-qB-cGE"/>
                            <constraint firstAttribute="trailing" secondItem="kqM-uy-MqK" secondAttribute="trailing" constant="8" id="hS6-Hp-4OG"/>
                            <constraint firstItem="F4i-OD-uaP" firstAttribute="top" secondItem="jaz-SU-ajB" secondAttribute="bottom" id="ieu-uH-Ha6"/>
                            <constraint firstItem="jaz-SU-ajB" firstAttribute="top" secondItem="y3f-cr-186" secondAttribute="top" constant="20" id="uXe-B9-f9f"/>
                            <constraint firstAttribute="trailing" secondItem="jaz-SU-ajB" secondAttribute="trailing" constant="8" id="yCS-Pz-kxh"/>
                        </constraints>
                    </view>
                    <imageView clipsSubviews="YES" userInteractionEnabled="NO" contentMode="scaleAspectFit" horizontalHuggingPriority="251" verticalHuggingPriority="251" image="ic_hot" translatesAutoresizingMaskIntoConstraints="NO" id="sDs-Ec-iSF">
                        <rect key="frame" x="138" y="0.0" width="40" height="40"/>
                        <constraints>
                            <constraint firstAttribute="height" constant="40" id="Hp2-pf-zWE"/>
                            <constraint firstAttribute="width" constant="40" id="NTh-iz-DKq"/>
                        </constraints>
                    </imageView>
                </subviews>
            </view>
            <constraints>
                <constraint firstAttribute="trailing" secondItem="sDs-Ec-iSF" secondAttribute="trailing" id="2b1-d9-mLO"/>
                <constraint firstAttribute="trailing" secondItem="Ffs-Ap-nZC" secondAttribute="trailing" id="4Sj-QO-cGE"/>
                <constraint firstItem="Ffs-Ap-nZC" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="96z-nK-SyB"/>
                <constraint firstItem="X9d-Bb-nAZ" firstAttribute="top" secondItem="Ffs-Ap-nZC" secondAttribute="top" constant="4" id="GIn-bi-4sv"/>
                <constraint firstAttribute="trailing" secondItem="y3f-cr-186" secondAttribute="trailing" id="M2a-lC-fcI"/>
                <constraint firstAttribute="bottom" secondItem="y3f-cr-186" secondAttribute="bottom" id="YKh-FR-Qti"/>
                <constraint firstItem="sDs-Ec-iSF" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="YT3-yk-kpO"/>
                <constraint firstItem="y3f-cr-186" firstAttribute="leading" secondItem="gTV-IL-0wX" secondAttribute="leading" id="Yaa-oT-yjs"/>
                <constraint firstItem="X9d-Bb-nAZ" firstAttribute="leading" secondItem="Ffs-Ap-nZC" secondAttribute="leading" constant="4" id="jho-Va-iTm"/>
                <constraint firstItem="y3f-cr-186" firstAttribute="top" secondItem="Ffs-Ap-nZC" secondAttribute="bottom" constant="8" id="luO-S9-KpY"/>
                <constraint firstItem="Ffs-Ap-nZC" firstAttribute="top" secondItem="gTV-IL-0wX" secondAttribute="top" id="zqb-NI-r7W"/>
            </constraints>
            <viewLayoutGuide key="safeArea" id="ZTg-uK-7eu"/>
            <size key="customSize" width="178" height="234"/>
            <connections>
                <outlet property="cImageView" destination="X9d-Bb-nAZ" id="vqd-hp-BOM"/>
                <outlet property="dateLabel" destination="kqM-uy-MqK" id="OoX-h3-tBq"/>
                <outlet property="durationLabel" destination="F4i-OD-uaP" id="Q3d-gM-R6A"/>
                <outlet property="hotImageView" destination="sDs-Ec-iSF" id="h9l-Me-l6m"/>
                <outlet property="nameLabel" destination="jaz-SU-ajB" id="tcz-yE-6aX"/>
                <outlet property="nameTop" destination="uXe-B9-f9f" id="51l-0w-OjT"/>
                <outlet property="posterImageView" destination="Ffs-Ap-nZC" id="KNj-Ga-rVf"/>
            </connections>
            <point key="canvasLocation" x="224.63768115942031" y="214.28571428571428"/>
        </collectionViewCell>
    </objects>
    <resources>
        <image name="ic_c13" width="56" height="27"/>
        <image name="ic_hot" width="40" height="40"/>
    </resources>
</document>

//
//  NewsDetailViewController.swift
//  Booking
//
//  Created by <PERSON><PERSON>u on 3/14/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit                             
import AlamofireImage
import DRPLoadingSpinner

enum NewType {
    case none
    case news(NewsModel)
    case notification(NewNotification)
    case voucherFromList(FreeVoucher)
    case voucherFromDirectLink(String, Bool)
    case recruitment(NewsModel)
    var isVoucher: Bool {
        switch self {
        case .voucherFromList, .voucherFromDirectLink:
            return true
        default:
            return false
        }
    }
}

class NewsDetailViewController: StickyHeaderViewController {
    @IBOutlet weak var newBannerImageView: UIImageView!
    @IBOutlet weak var lbTitle: UILabel!
    @IBOutlet weak var scrollView: UIScrollView!
    @IBOutlet weak var vBottom: UIView!
    @IBOutlet weak var loadingView: DRPLoadingSpinner!
    @IBOutlet weak var tvContent: UITextView!
    @IBOutlet weak var viewShareHeight: NSLayoutConstraint!
    @IBOutlet weak var shareButton: UIButton!

    private let staticSchemas = "betacineplex://"
    private var news: NewsModel?
    private var newNotification: NewNotification?
    private var voucher: FreeVoucher?
    private var voucherNewId: String?

    var type: NewType = .none

    override func viewDidLoad() {
        super.viewDidLoad()
        navigationController?.setTransparent(false)

        if #available(iOS 11.0, *) {
            self.scrollView.contentInsetAdjustmentBehavior = .never
        } else {
            self.automaticallyAdjustsScrollViewInsets = false
        }
        
        tvContent.textAlignment = .justified

        scrollView.delegate = self
        scrollView.maximumZoomScale = 2.5

        configureData()

    }

    override func barTransparent() -> Bool {
        return false
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()

        scrollView.frame = self.view.bounds
        let bottom = UIEdgeInsets(top: 0, left: 0, bottom: vBottom.frame.height, right: 0)
        scrollView.contentInset = bottom
        scrollView.scrollIndicatorInsets = bottom

        newBannerImageView.addBottomRoundedEdge()
    }

    func configureData() {
        switch type {
        case .news(let news):
            self.news = news
            localizableTitle = "NewsDetail.Title"
            fillNewData()
        case .recruitment(let news):
            self.news = news
            localizableTitle = "recruiment_info"
            fillNewData()
        case .notification(let notification):
            self.newNotification = notification
            localizableTitle = "Notification.Title"
            viewShareHeight.constant = 0
            shareButton.isHidden = true
            fillNotificationData()
        case .voucherFromList(let voucher):
            self.voucher = voucher
            localizableTitle = "free_voucher"
            fillFreeVoucherData()
            shareButton.setTitle("get_voucher".localized.uppercased(), for: .normal)
        case .voucherFromDirectLink(let id, let isGot):
            self.voucherNewId = id
            localizableTitle = "free_voucher"
            getContent(newId: id)
            vBottom.isHidden = isGot
            shareButton.setTitle("get_voucher".localized.uppercased(), for: .normal)
        default:
            break
        }
    }
    
    func reloadData(for id: Int) {
        getContents(id)
    }

    private func fillNotificationData() {
        guard let id = newNotification?.NotificationCampaignId else {
            return
        }
        self.getContents(id)
    }
    
    private func fillNewData(){
        guard let news = news else {
            return
        }
        if let url = news.Duong_dan_anh_dai_dien{
            self.newBannerImageView.af_setImage(withURL: URL(string: url)!)
        }
        lbTitle.text = news.Tieu_de
        self.getContent(newId: news.StorylineID)
    }

    private func fillFreeVoucherData() {
        guard let voucher = self.voucher else {
            return
        }
        if let urlString = voucher.duong_dan_anh_dai_dien, let url = URL(string: urlString) {
            newBannerImageView.af_setImage(withURL: url)
        }
        lbTitle.text = voucher.tieu_de

        if let content = voucher.Noi_dung_chi_tiet?.first?.ParagraphData?.ParagraphContent, let attributed = content.htmlToAttributedString(14){
            let titleParagraphStyle = NSMutableParagraphStyle()
            titleParagraphStyle.alignment = .justified
            let attributedString = NSMutableAttributedString(attributedString: attributed)
            attributedString.addAttributes([.paragraphStyle: titleParagraphStyle], range: NSMakeRange(0, attributed.string.count))
            self.tvContent.attributedText = attributedString
        }
    }
    
    private func getContents(_ id: Int) {
        
        self.showLoading()
        EcmProvider.rx.request(.getNotificationDetail(id)).mapObject(DDKCResponse<NotificationDetail>.self)
            
            .subscribe(onNext:{[weak self] response in
                guard let `self` = self else {return}
                self.dismissLoading()
                guard let object = response.Object else{
                    print("Wrong data")
                    return
                }
                print(object)
                if let urlString = object.ImageThumb, let url = URL(string: urlString){
                    self.newBannerImageView.af_setImage(withURL: url)
                }
                self.lbTitle.text = object.Title
                if let content = object.Content, let attributed = content.htmlToAttributedString(14) {
                    let titleParagraphStyle = NSMutableParagraphStyle()
                    titleParagraphStyle.alignment = .justified
                    let attributedString = NSMutableAttributedString(attributedString: attributed)
                    attributedString.addAttributes([.paragraphStyle: titleParagraphStyle], range: NSMakeRange(0, attributed.string.count))
                    self.tvContent.attributedText = attributedString

                }
                
                self.updateReadNotification()
            }).disposed(by: disposeBag)
    }
    
    private func updateReadNotification() {
        guard let id = newNotification?.Id, let code = newNotification?.ScreenCode else {
            return
        }
        EcmProvider.rx.request(.updateRead(id, code)).subscribe().disposed(by: disposeBag)
    }
    
    private func getContent(newId: String?){
        guard let id = newId else {
            self.dismissLoading()
            return
        }
        self.loadingView.startAnimating()
        EcmProvider.rx.request(.getNewWithId(id, nil, nil)).mapObject(DDKCResponse<NewsModel>.self)
            .subscribe(onNext:{[weak self] response in
                guard let `self` = self else {return}
                self.dismissLoading()
                self.handlerResponse(response, success: {
                    guard let object = response.Object else{
                        print("Wrong data")
                        self.loadingView.stopAnimating()
                        self.loadingView.isHidden = true
                        return
                    }
                    if let attributed = object.contents.htmlToAttributedString(14){
                        let titleParagraphStyle = NSMutableParagraphStyle()
                        titleParagraphStyle.alignment = .justified
                        let attributedString = NSMutableAttributedString(attributedString: attributed)
                        attributedString.addAttributes([.paragraphStyle: titleParagraphStyle], range: NSMakeRange(0, attributed.string.count))
                        self.tvContent.attributedText = attributedString
                    }

                    if let url = object.Duong_dan_anh_dai_dien{
                        self.newBannerImageView.af_setImage(withURL: URL(string: url)!)
                    }
                    self.lbTitle.text = object.Tieu_de

                    if let url = object.NewsURI {
                        self.news?.NewsURI = url
                    }

                    if case .voucherFromDirectLink = self.type {
                        self.news = object
                    }

                    self.loadingView.stopAnimating()
                    self.loadingView.isHidden = true
                }, error: {
                    self.loadingView.stopAnimating()
                    self.loadingView.isHidden = true
                })
            }).disposed(by: disposeBag)
    }

    private func getVoucher() {
        var id = voucher?.storylineID
        if id == nil {
            id = news?.StorylineID
        }
        guard let _id = id else {
            return
        }
        self.showLoading()
        VoucherProvider.rx.request(.getFreeVoucher(_id)).mapObject(DDKCResponse<VoucherGot>.self)
            .subscribe(onNext:{[weak self] response in
                guard let `self` = self else {return}
                self.dismissLoading()

                guard let object = response.Object else {
                    print("Wrong data")
                    self.dismissLoading()
                    return
                }

                if let firstMessage = object.firstMessage, let firstHighlight = object.firstMessageHighLight, let secondMessage = object.secondMessage, let secondHighlight = object.secondMessageHighLight {
                    UIAlertController.showAttributeMessage(self, messages: [firstMessage, firstHighlight, secondMessage, secondHighlight])
                }
            }).disposed(by: disposeBag)
    }

    @IBAction func shareNewsButtonPressed(_ sender: Any) {
        if type.isVoucher {
            getVoucher()
            return
        }
        share(data: [news?.getNewsURL()].compactMap{$0})
    }
}

extension NewsDetailViewController {

    private func handlerClick(in URL: URL) {
        let paths = URL.absoluteString.replacingOccurrences(of: staticSchemas, with: "").components(separatedBy: "/")
        if paths.count > 1 {
            if let index = Int(paths[0]), let type = RouteType(rawValue: index) {
                RouteManager(vc: self, type: type, params: [paths[1]]).route()
            }
        }
    }
}

extension NewsDetailViewController: UITextViewDelegate {
    @available(iOS, deprecated: 10.0)
    func textView(_ textView: UITextView, shouldInteractWith URL: URL, in characterRange: NSRange) -> Bool {
        print("\(URL.absoluteString)")
        guard URL.absoluteString.starts(with: staticSchemas) else {
            return true
        }
        handlerClick(in: URL)

        return false
    }

    @available(iOS 10.0, *)
    func textView(_ textView: UITextView, shouldInteractWith URL: URL, in characterRange: NSRange, interaction: UITextItemInteraction) -> Bool {
        print("\(URL.absoluteString)")
        guard URL.absoluteString.starts(with: staticSchemas) else {
            return true
        }
        handlerClick(in: URL)

        return false
    }
}

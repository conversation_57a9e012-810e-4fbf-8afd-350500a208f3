//
//  Config.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 3/10/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation

struct Config {
    #if BETA_DEV
    static let BaseURL = "http://dev.api.betacorp.vn/"
    static let BaseURLResource = "http://dev.files.tms.betacorp.vn/"
    static let BaseURLImage = "http://dev.api.betacorp.vn/files/"
    static let PriceURL = "http://dev.betacineplex.vn/gia-ve/gia-ve-d.htm"
    static let SignalRURL = "http://dev.betacineplex.vn/signalr/hubs"
    static let BaseURLWeb = "http://dev.betacineplex.vn"
    static let MixpanelToken = "7c957cceb74932260d4c3f8d63bdf590"
    static let GoogleServiceInfo = "GoogleServiceDev"
    #elseif BETA_PRO
    static let BaseURL = "https://api.betacorp.vn/"
    static let BaseURLResource = "https://files.betacorp.vn/files/"
    static let BaseURLImage = "https://files.betacorp.vn/files/"
    static let PriceURL = "https://www.betacinemas.vn/gia-ve/gia-ve-d.htm"
    static let SignalRURL = "https://www.betacinemas.vn/signalr/hubs"
    static let BaseURLWeb = "https://www.betacinemas.vn"
    static let MixpanelToken = "f9846dae54c28e43efcc22bee8df21f1"
    static let GoogleServiceInfo = "GoogleServiceProd"
    #elseif TEST
    static let BaseURL = "http://dev.api.betacorp.vn/"
    static let BaseURLResource = "http://dev.files.tms.betacorp.vn/"
    static let BaseURLImage = "http://dev.files.betacorp.vn/"
    static let PriceURL = "https://dev.cinemas.betacorp.vn/gia-ve/gia-ve-d.htm"
    static let SignalRURL = "https://dev.cinemas.betacorp.vn"
    static let BaseURLWeb = "dev.cinemas.betacorp.vn"
    static let MixpanelToken = "7c957cceb74932260d4c3f8d63bdf590"
    static let GoogleServiceInfo = "GoogleServiceTest"
    #endif

  static let PageSize = 1000
    
  static let TimeExpired = 10 * 60.0 //10 minutes

  static let gmsApiKey = "AIzaSyBeUDaR8kcZHPBZh1wqVPPmz1NMSPpUNCw"
}

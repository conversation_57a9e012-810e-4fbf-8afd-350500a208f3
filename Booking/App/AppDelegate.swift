//
//  AppDelegate.swift
//  ProjectBase
//
//  Created by <PERSON><PERSON> on 3/7/18.
//  Copyright © 2018 Me. All rights reserved.
//

import UIKit
import FBSDKCoreKit
import IQKeyboardManagerSwift
import GoogleMaps
import Fabric
import Crashlytics
import RxSwift

@UIApplicationMain
class AppDelegate: UIResponder, UIApplicationDelegate {
    var firebaseToken: String = ""
    var window: UIWindow?
    var topNavigationController: BaseNavigationViewController?
    var disposeBag = DisposeBag()
    private static var sharedInstance: AppDelegate?
    static var shared: AppDelegate {
        get {
            return sharedInstance!
        }
    }

    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplicationLaunchOptionsKey: Any]?) -> Bool {
        Fabric.with([Crashlytics.self])
        AppDelegate.sharedInstance = self
        IQKeyboardManager.shared.enable = true
        IQKeyboardManager.shared.enabledTouchResignedClasses = [BaseViewController.self]
        UserDefaults.standard.register(defaults: [DefaultKey.locationEnable.rawValue: true])

        ApplicationDelegate.shared.application(application, didFinishLaunchingWithOptions: launchOptions)
        
        initFirebase()
        initNotification()
        initAppearances()
        initAppRoute()
        initMixpanel()
        
        GMSServices.provideAPIKey(Config.gmsApiKey)
        
//        if let notification = launchOptions?[.remoteNotification] as? [String: AnyObject] {
//            let data = notification["data"] as! [String: AnyObject]
//
//            if let code = data["sreenCode"] as? String, let id = data["refId"] as? String, let codeInt = Int(code) {
//                DispatchQueue.main.asyncAfter(deadline: DispatchTime.now() + 0.5) {
//                    self.handlerNotification(withId: codeInt, id: id, in: application)
//                }
//            }
//        }
        
        return true
    }

    func applicationWillResignActive(_ application: UIApplication) {
        
    }

    func applicationDidEnterBackground(_ application: UIApplication) {
        application.applicationIconBadgeNumber = 0
    }

    func applicationWillEnterForeground(_ application: UIApplication) {
        // Called as part of the transition from the background to the active state; here you can undo many of the changes made on entering the background.
    }

    func applicationDidBecomeActive(_ application: UIApplication) {
    }

    func applicationWillTerminate(_ application: UIApplication) {
        // Called when the application is about to terminate. Save data if appropriate. See also applicationDidEnterBackground:.
    }

    func application(_ app: UIApplication, open url: URL, options: [UIApplicationOpenURLOptionsKey : Any] = [:]) -> Bool {
        if let orderId = url.valueOf("orderId"),
           let resultCode = url.valueOf("resultCode"),
           let requestId = url.valueOf("requestId"),
           let transId = url.valueOf("transId"),
           let message = url.valueOf("message"),
           let responseTime = url.valueOf("responseTime"),
           let payType = url.valueOf("payType"),
           let extraData = url.valueOf("extraData"),
           let partnerCode = url.valueOf("partnerCode") {
            NotificationCenter.default.post(name: NSNotification.Name.CheckMomoOrderStatus, object: (orderId, resultCode, requestId, transId, message, responseTime, payType, extraData, partnerCode))
            return false;
        }
        if let appid = url.valueOf("appid"),
           let appTransId = url.valueOf("apptransid"),
           let pmcid = url.valueOf("pmcid"),
           let bankCode = url.valueOf("bankcode"),
           let amount = url.valueOf("amount"),
           let discountAmount = url.valueOf("discountamount"),
           let status = url.valueOf("status"),
           let checksum = url.valueOf("checksum") {
            NotificationCenter.default.post(name: NSNotification.Name.CheckZaloPayOrderStatus, object: (appid, appTransId, pmcid, bankCode, amount, discountAmount, status, checksum))
            return false;
        }
        let handled = ApplicationDelegate.shared.application(app, open: url, options: options)
        return handled
    }
}


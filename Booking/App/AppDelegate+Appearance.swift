//
//  AppDelegate+Appearance.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 4/4/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import UIKit
import PopupDialog

extension AppDelegate {
    func initAppearances() {
        UINavigationBar.appearance().tintColor = .white

        let containerAppearance = PopupDialogContainerView.appearance()
        containerAppearance.cornerRadius = 12

        let dialogAppearance = PopupDialogDefaultView.appearance()
        dialogAppearance.backgroundColor      = .white
        dialogAppearance.titleFont            = UIFont(fontName: .<PERSON>, style: .Medium, size: 17)
        dialogAppearance.titleColor           = UIColor.inputText
        dialogAppearance.titleTextAlignment   = .center
        dialogAppearance.messageFont          = UIFont(fontName: .Oswald, style: .Medium, size: 24)
        dialogAppearance.messageColor         = UIColor.selected
        dialogAppearance.messageTextAlignment = .center

        let buttonAppearance = DefaultButton.appearance()
        // Default button
        buttonAppearance.titleFont      = .boldSystemFont(ofSize: 17)
        buttonAppearance.titleColor     = UIColor(red: 0, green: 122, blue: 255)
        buttonAppearance.buttonColor    = .clear
        buttonAppearance.separatorColor = UIColor(white: 0.9, alpha: 1)

        // Below, only the differences are highlighted

        // Cancel button
        CancelButton.appearance().titleColor = UIColor(red: 0, green: 122, blue: 255)
        CancelButton.appearance().titleFont = .systemFont(ofSize: 17)

        // Destructive button
        DestructiveButton.appearance().titleColor = .red

        UITabBarItem.appearance().setTitleTextAttributes([NSAttributedString.Key.font: UIFont(fontName: .Oswald, size: 12)], for: .normal)
    }
}

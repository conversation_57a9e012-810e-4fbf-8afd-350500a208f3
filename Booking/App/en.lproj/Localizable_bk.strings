/* 
  Localizable.strings
  Booking

  Created by <PERSON><PERSON> on 4/4/18.
  Copyright © 2018 ddkc. All rights reserved.
*/

"HelloWorld" = "Hello World";

"Bt.OK" = "OK";
"Bt.Yes" = "Yes";
"Bt.SEND" = "SEND";
"Bt.Confirm" = "CONFIRM";
"Bt.Update" = "UPDATE";
"Bt.Cancel" = "Cancel";
"Bt.Retry" = "Retry";
"Bt.CaptureImage" = "Capture new photo";
"Bt.ChooseFromLibrary" = "From photo library";
"Bt.REGISTER" = "REGISTER";
"Bt.LocationSetting" = "Open Settings";
"Bt.Close" = "Close";

"Menu.Home" = "Home";
"Menu.Member" = "Member BETA";
"Menu.Cenima" = "Cinema BETA";
"Menu.NewsAndHotDeals" = "News and hot deals";
"Menu.Barcode" = "Barcode";
"Menu.Recruitment" = "Recruitment";
"Menu.Notification" = "Notifications";
"Menu.Setting" = "Setting";
"Menu.Price" = "Price";

"Register.ConfirmTermAndPolicyText" = "Tôi cam kết tuân theo chính sách bảo mật và điều khoản sử dụng của Betacineplex.";
"Register.Term" = "chính sách bảo mật";
"Register.Policy" = "điều khoản sử dụng";
"Register.Title" = "Register";
"Register.RequiredInfo" = "REQUIRED INFORMATION";
"Register.Name" = "Full name";
"Register.FirstName" = "First name";
"Register.LastName" = "Last name";
"Register.Email" = "Email";
"Register.Password" = "Password";
"Register.RePassword" = "Confirm password";
"Register.Phone" = "Phone number";
"Register.CMND" = "CMND/ Hộ chiếu";
"Register.Birthday" = "Birthday";
"Register.AdditionalInfo" = "ADDITIONAL INFORMATION";
"Register.Gender" = "Gender";
"Register.City" = "Province/ City";
"Register.District" = "State/ District";
"Register.Address" = "Address";
"Register.Register" = "REGISTER";
"RegisterSuccess" = "Register successfully!";
"Register.Success" = "REGISTER SUCCESS";
"Register.YouReceivedCardNumber" = "You received the card number is";
"Register.ReceiveMemberCard" = "Receive online member card";

"Login.Title" = "Login";
"Login.Email" = "Email or Username";
"Login.Password" = "Password";
"Login.ForgotPassword" = "Forgot Password";
"Login.Login" = "LOGIN";
"Login.LoginWithFacebook" = "LOGIN WITH FACEBOOK";
"Or" = "Or";
"Login.RegisterBETAAccount" = "Register Beta Cineplex Account";

"ForgotPass.Title" = "Forgot Password";
"ForgotPass.GuideTitle" = "New password will be sent\nto your Email inbox!";
"ForgotPass.Email" = "Email";

"Home.Minute" = "Minute";
"Home.UpComing" = "UP COMING";
"Home.NowShowing" = "NOW SHOWING";
"Home.Special" = "SPECIAL";
"Home.BuyTicket" = "BUY TICKET";
"Home.SearchAroundYou" = "Search cinema near you...";
"Home.BigDeals" = "BIG DEALS";
"Home.All" = "All";
"Home.Hi" = "Hi";

"FilmDetail.Director" = "Director";
"FilmDetail.Actor" = "Actors";
"FilmDetail.Type" = "Type";
"FilmDetail.Duration" = "Duration";
"FilmDetail.Language" = "Language";
"FilmDetail.DateShow" = "Start Date";
"FilmDetail.Title" = "Booking by film";
"FilmDetail.Share" = "SHARE";
"FilmDetail.BuyTicket" = "BUY TICKET";
"Film.SelectRegion" = "Select region";

"Alert.EmailNotEmpty" = "Email is not empty";
"Alert.EmailInvalid" = "Email is invalid";
"Alert.PasswordNotEmpty" = "Password is not empty";
"Alert.NewPasswordNotEmpty" = "New Password is not empty";
"Alert.ConfirmPasswordNotEmpty" = "Confirm Password is not empty";
"Alert.ConfirmPasswordInvalid" = "Confirm Password invalid";
"Alert.NameNotEmpty" = "Name is not empty";
"Alert.FirstNameNotEmpty" = "First name is not empty";
"Alert.LastNameNotEmpty" = "Last name is not empty";
"Alert.PhoneNotEmpty" = "Phone is not empty";
"Alert.PhoneInvalid" = "Phone is invalid";
"Alert.IDNotEmpty" = "ID is not empty";
"Alert.IDInvalid" = "ID is invalid";
"Alert.BithdayNotEmpty" = "Birthday is not empty";
"Alert.BirthdayInvalid" = "Birthday is invalid";
"Alert.PasswordNotMatch" = "Password is not match";
"Alert.PasswordInvalid" = "Password must be longer than or equal to 6 characters";
"Alert.VoucherCodeNotEmpty" = "Voucher Code is not empty";
"Alert.PinCodeNotEmpty" = "Pin Code is not empty";
"Alert.CouponCodeNotEmpty" = "Coupon Code is not empty";
"Alert.NoFilmTrailerURL" = "Cannot load the film trailer!";
"Alert.FilmTrailerLoadError" = "Cannot load the film trailer!";
"Alert.UserNotAcceptTerm" = "You must accept our term and policy";
"Alert.ChangePassFailed" = "Change password failed!";
"Alert.Success" = "Success";
"Alert.Error" = "Error";
"Alert.LoginFailed" = "Login failed";
"Alert.PaymentFailed" = "Your payment has failed! Please try again later.";
"Alert.BookingSeatFailed" = "Cannot book these seats! Please choose another seat!";
"Alert.PaymentSuccess" = "Booking success!";

"CinemaList.ChooseByArea" = "Choose by area";
"CinemaList.NearCinema" = "Cinemas near you";

// setting
"Language" = "Language";
"Other" = "Other";
"Setting.Notify" = "Notification";
"Setting.Location" = "Location";
"Setting.FAQ" = "F.A.Q";
"Setting.Version" = "Version";
"Setting.Policy" = "Term of use";
"Setting.PaymentPolicy" = "Payment Policy";
"Setting.SecurePolicy" = "Secure Policy";
"Setting.CompanyInfo" = "Company Information";
"Setting.VietNam" = "Vietnamese";
"Setting.English" = "English";
"Setting.Title" = "Setting";

"ListCinema.Title" = "BETA Cinemas";
"Cinema.Screen" = "SCREEN";

"Notification.Title" = "Notifications";

"FAQ.Title" = "F.A.Q";

"AppVersion.Title" = "App version";
"App.NewVersionAvailable" = "New version available";
"App.UsingLatestVersion" = "You are using the latest version";
"Alert.CheckNewVersionFailed" = "Check app version failed!";
"App.VersionSupport" = "Support iOS XXX and above";
"App.CurrentVersion" = "Current version";

"NewsAndDeals.Title" = "News and Hot Deals";
"NewsAndDeals.Promotion" = "HOT DEALS";
"NewsAndDeals.News" = "NEWS";

"NewsDetail.Title" = "News and Hot Deals";
"NewsDetail.Share" = "SHARE";

"Member.Title" = "Beta Member";
"Member.AccountInfo" = "Account information";
"Member.ChangePass" = "Change password";
"Member.MemberCard" = "Member card";
"Member.BetaPoint" = "BETA Point";
"Member.PreferentialCard" = "Preferential Card";
"Member.TransactionHistory" = "Transaction history";
"Member.ChooseImage" = "Choose";
"Member.MemberCard" = "Member Card";
"Member.TotalSpent" = "Total spent";
"Member.TotalPoint" = "Point";
"Member.Logout" = "Log out";
"Member.SpentToVip" = "Bạn cần tích luỹ thêm XXX để nâng hạng VIP";

"Confirm.Password" = "Password";
"Confirm.GuideText" = "For secure, please enter your password";

"ChangePass.Title" = "Change password";
"ChangePass.EnterCurrentPass" = "Enter current password";
"ChangePass.CurrentPass" = "Current password";
"ChangePass.EnterNewPass" = "Enter new password";
"ChangePass.NewPass" = "New password";
"ChangePass.ReNewPass" = "Confirm new password";

"AccountInfo.Title" = "Account Information";
"AccountInfo.BasicInfo" = "BASIC INFORMATION";
"AccountInfo.ContactInfo" = "CONTACT INFORMATION";

"BetaPoint.Title" = "BETA Point";
"BetaPoint.TotalPoint" = "Total point";
"BetaPoint.UsedPoint" = "Used point";
"BetaPoint.CurrentPoint" = "Current point";

"TransactionHistory.Title" = "Transaction History";
"TransactionHistory.GuideText" = "Hiển thị giao dịch trong 3 tháng gần nhất. Vui lòng truy cập website để xem toàn bộ lịch sử giao dịch";
"TransactionHistory.Point" = "Point";

"TransactionDetail.Cinema" = "Cinema";
"TransactionDetail.ShowDate" = "Date";
"TransactionDetail.ShowTime" = "Time";
"TransactionDetail.ShowRoom" = "Room";
"TransactionDetail.Seat" = "Seat ({SEAT_COUNT})";
"TransactionDetail.Combo" = "Combo ({COMBO_COUNT})";
"TransactionDetail.Cash" = "Cash";
"TransactionDetail.Voucher" = "Voucher";
"TransactionDetail.Point" = "Point";
"TransactionDetail.Card" = "Card";
"TransactionDetail.TotalMoney" = "Total";
"TransactionDetail.Title" = "Transaction Detail";
"TransactionDetail.GuideText" = "Vui lòng đưa mã số này đến quầy vé Beta để nhận vé";
"TransactionDetail.NoticeText" = "Lưu ý: Beta không chấp nhận hoàn tiền hoặc đổi vé đã thanh toán thành công trên Website và Ứng dụng Beta";
"TransactionDetail.Notice" = "Lưu ý:";

"NoPhotoPermissionTitle" = "Photo Access Permission Denied!";
"NoPhotoPermissionMsg" = "Please allow app to access the Photo Library!";

"VoucherCoupon.Title" = "Preferential Card";
"BetaVoucher" = "BETA Voucher";
"BetaCoupon" = "BETA Coupon";

"Voucher.Title" = "BETA Voucher";
"Voucher.Info" = "VOUCHER INFORMATION";
"Voucher.Code" = "Voucher Code";
"Voucher.PIN" = "PIN Code";
"YourVoucher" = "YOUR VOUCHER";
"Voucher.CardType" = "Card type";
"Voucher.CardCode" = "Card number";
"Voucher.ExpireDate" = "Expire Date";

"Coupon.Title" = "BETA Coupon";
"Coupon.Info" = "COUPON INFORMATION";
"Coupon.Code" = "Coupon Code";
"Coupon.PIN" = "PIN Code";
"YourCoupon" = "YOUR COUPON";

"Subtitles" = "Subtitles";
"Dubbing" = "Dubbing";
"Sub" = "SUB";
"Dub" = "DUB";
"AgeRestrict.C13" = "Chỉ dành cho người trên 13 tuổi";
"AgeRestrict.C16" = "Chỉ dành cho người trên 16 tuổi";
"AgeRestrict.C18" = "Chỉ dành cho người trên 18 tuổi";

"CompanyInfo.Title" = "Company Information";
"SecurePolicy.Title" = "Secure Policy";
"PaymentPolicy.Title" = "Payment Policy";
"TermOfUse.Title" = "Terms of use";

"BookingByFilm.Title" = "Booking by movie";
"Film.NowShowing" = "NOW SHOWING";
"Film.Special" = "SPECIAL";

"Today" = "Today";

"Address" = "Address";
"Bt.Guide" = "Guide";

"CinemaDetail.Title" = "Beta Cinema";
"NoRoutesFound" = "Cannot find any available routes!";
"LocationNoPermission" = "Permission is not allowed! Please open Location Service settings and allow app using location access!";
"RouteInMap.Title" = "Select your app";
"OpenInAppleMap" = "Open in Apple Map";
"OpenInGoogleMap" = "Open in Google Map";

"MemberCard.Title" = "Member Card";
"MemberCard.CardName" = "Card name";
"MemberCard.CardNumber" = "Card number";
"MemberCard.CardDate" = "Registration";
"MemberCard.UsingCard" = "(Using)";

"empty" = "empty";

"Recruitment.Title" = "Recruitment";
"Seat.Empty" = "Empty seat";
"Seat.Selecting" = "Selecting seat";
"Seat.Selected" = "Selected seat";
"Seat.Sold" = "Sold seat";
"Seat.Booked" = "Booked seat";
"Seat.Normal" = "Normal seat";
"Seat.Vip" = "VIP seat";
"Seat.Couple" = "Couple seat";

"Seat.Continue" = "Continue";
"Seat.SelectedSeats" = "Selected seats";
"Seat.TotalBill" = "Total";
"Seat.MaxSelectedSeats" = "You can just choose 8 seats at a time";

"ConfirmAge.Message" = "Tôi xác nhận mua vé cho người xem từ XXX và hiểu rằng BETA sẽ không hoàn lại tiền nếu không chứng thực được độ tuổi của khán giả. Tham khảo quy định của Cục Điện Ảnh.";
"AgeAbove13" = "13 tuổi trở lên";
"AgeAbove16" = "16 tuổi trở lên";
"AgeAbove18" = "18 tuổi trở lên";
"ConfirmAge.Rule" = "quy định";
"Payment.Title" = "Payment";

//
//  AppDelegate+Initial.swift
//  Booking
//
//  Created by <PERSON><PERSON> on 3/10/18.
//  Copyright © 2018 ddkc. All rights reserved.
//

import Foundation
import UserNotifications
import RxSwift
import Mixpanel
import FirebaseMessaging
import FirebaseCore


extension AppDelegate {    
    var rootViewController: UIViewController? {
        return window?.rootViewController
    }

    var topVC: UIViewController? {
        return rootViewController?.topMostViewController()
    }

    var tabbarVC: MainTabContainer? {
        return rootViewController as? MainTabContainer
    }

    func initAppRoute(){
        if self.window == nil {
            self.window = UIWindow(frame: UIScreen.main.bounds)
        }

        let rootVC = MainTabContainer()

        window?.rootViewController = rootVC
        window?.makeKeyAndVisible()
    }

    
    func initMixpanel(){
        Tracking.shared.setToken()
    }
    
    func initFirebase(){
//        let filePath = Bundle.main.path(forResource: Config.GoogleServiceInfo, ofType: "plist")!
//        let options = FirebaseOptions(contentsOfFile: filePath)
//        FirebaseApp.configure(options: options!)
        FirebaseApp.configure()
    }
    
    func initNotification(){
        registerForPushNotifications()
        Messaging.messaging().delegate = self
    }

    func application(_ application: UIApplication, didReceiveRemoteNotification userInfo: [AnyHashable : Any]) {
        guard application.applicationState != .active else {
            return
        }
        
        print(userInfo)
        guard let data = userInfo["data"] as? [String: AnyObject] else { return }
        print(data)

        if let id = data["id"] as? Int, id != 0 {
            handlerOldNotification(id, in: application)
        } else if let code = data["sreenCode"] as? String, let id = data["refId"] as? String, let codeInt = Int(code) {
            handlerNotification(withId: codeInt, id: id, in: application)
        }
    }

    func application(_ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
        let tokenParts = deviceToken.map { data -> String in
            return String(format: "%02.2hhx", data)
        }

        let token = tokenParts.joined()
        print("Device Token: \(token)")
        
//        UIAlertController.showAlert(self.rootViewController!.topMostViewController()!, message: token)
        UserDefaults.standard.set(token, forKey: DefaultKey.deviceToken.rawValue)
        UserDefaults.standard.synchronize()
        
        if let accountId = Global.shared.user?.UserId,
            let deviceId = UIDevice.current.identifierForVendor?.uuidString {
            let _ = AccountProvider.rx.request(.registerDeviceToken(deviceId, accountId, token)).mapObject(DDKCResponse<RegisterDeviceToken>.self).asObservable().subscribe()
        }
        
    }

    func application(_ application: UIApplication, didFailToRegisterForRemoteNotificationsWithError error: Error) {
        print("Remote Notification Error: \(error)")
    }
}

extension AppDelegate: MessagingDelegate, UNUserNotificationCenterDelegate {

    //MessagingDelegate
    func messaging(_ messaging: Messaging, didReceiveRegistrationToken fcmToken: String?) {
        self.firebaseToken = fcmToken!
        print("Firebase token: \(fcmToken)")
    }

    //UNUserNotificationCenterDelegate
    func application(_ application: UIApplication, didReceiveRemoteNotification userInfo: [AnyHashable : Any], fetchCompletionHandler completionHandler: @escaping (UIBackgroundFetchResult) -> Void) {
        print("APNs received with: \(userInfo)")
     }
    
    
    func registerForPushNotifications() {
        if #available(iOS 10.0, *) {
            UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .sound, .badge]) {
                (granted, error) in
                print("Permission granted: \(granted)")
                guard granted else { return }
                DispatchQueue.main.async {
                    self.getNotificationSettings()
                }
            }
            UNUserNotificationCenter.current().delegate = self
        } else {
            let notificationTypes: UIUserNotificationType = [UIUserNotificationType.alert, UIUserNotificationType.badge, UIUserNotificationType.sound]
            let pushNotificationSettings = UIUserNotificationSettings(types: notificationTypes, categories: nil)
            UIApplication.shared.registerUserNotificationSettings(pushNotificationSettings)
            UIApplication.shared.registerForRemoteNotifications()
        }
    }

    func getNotificationSettings() {
        if #available(iOS 10.0, *) {
            UNUserNotificationCenter.current().getNotificationSettings { (settings) in
                print("Notification settings: \(settings)")
                guard settings.authorizationStatus == .authorized else { return }
                DispatchQueue.main.async {
                    UIApplication.shared.registerForRemoteNotifications()
                }
            }
        } else {
            // Fallback on earlier versions
        }
    }
    
    @available(iOS 10.0, *)
    func userNotificationCenter(_ center: UNUserNotificationCenter, willPresent notification: UNNotification, withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void) {
        completionHandler([.alert, .badge, .sound])
    }
    
    func userNotificationCenter(_ center: UNUserNotificationCenter,
                                     didReceive response: UNNotificationResponse,
                                     withCompletionHandler completionHandler: @escaping () -> Void) {
       completionHandler()
        
        let userInfo = response.notification.request.content.userInfo
        guard let screenCode = userInfo["ScreenCode"] as! String? else { return }
        
        guard let screenCode = Int( userInfo["ScreenCode"] as! String) else { return }
        
        let refId = userInfo["RefId"] as? String ?? ""
        guard let topVC = rootViewController?.topMostViewController() as? BaseViewController else {
            return
        }

        guard  let type = RouteType(rawValue: screenCode) else {
            return
        }

        RouteManager(vc: topVC, type: type, params: [refId]).route()

    }
}

extension AppDelegate {

    func gotoHome() {
        guard let rootVC = rootViewController as? MainTabContainer else {
            return
        }
        rootVC.changeTab(0)
    }

    func gotoMyVoucher() {
        guard let rootVC = rootViewController as? MainTabContainer else {
            return
        }
        if rootVC.selectedIndex == 2 {
            topVC?.navigationController?.popToRootViewController(animated: false)
        } else {
            rootVC.changeTab(2)
        }
    }

    func gotoLogin() {
        guard let topVC = UIApplication.shared.topMostViewController() else {
            return
        }

        let vc = UIStoryboard.authen[.login] as! LoginViewController
        topVC.navigationController?.pushViewController(vc, animated: true)
    }

    func handlerOldNotification(_ id: Int, in application: UIApplication) {
        guard let topVC = application.topMostViewController() as? BaseViewController else {
            return
        }

        if topVC is NewsDetailViewController {
            let newsDetailVC = topVC as! NewsDetailViewController
            newsDetailVC.reloadData(for: id)
            return
        }

        let vc = UIStoryboard.home[.newsDetail] as! NewsDetailViewController
        let newNotification = NewNotification(id)
        vc.type = NewType.notification(newNotification)
        vc.isFromRouter = true
        topVC.navigationController?.pushViewController(vc, animated: true)
    }

    func handlerNotification(withId code: Int, id: String, in application: UIApplication) {

        guard let topVC = application.topMostViewController() as? BaseViewController else {
            return
        }
        
        guard  let type = RouteType(rawValue: code) else {
            return
        }

        RouteManager(vc: topVC, type: type, params: [id]).route()
    }
}

extension UIViewController {
    func topMostViewController() -> UIViewController {

        if let presented = self.presentedViewController {
            return presented.topMostViewController()
        }

        if let navigation = self as? UINavigationController {
            return navigation.visibleViewController?.topMostViewController() ?? navigation
        }

        if let tab = self as? UITabBarController {
            return tab.selectedViewController?.topMostViewController() ?? tab
        }

        return self
    }
}

extension UIApplication {
    func topMostViewController() -> UIViewController? {
        return self.keyWindow?.rootViewController?.topMostViewController()
    }
}

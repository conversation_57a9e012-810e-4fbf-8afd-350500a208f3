import Foundation
import UIKit
import FirebaseRemoteConfig

struct AppIconRemoteKeys {
    static let iconName = "AppIcon_IOS_Name"
}

final class AppIconManager {
    static let shared = AppIconManager()
    private lazy var remoteConfig = RemoteConfig.remoteConfig()
    private let userDefaultsKey = "AppIcon"

    init() {
        remoteConfig.setDefaults([
            AppIconRemoteKeys.iconName: "" as NSObject
        ])
    }

    func updateAppIcon() {
        let settings = RemoteConfigSettings()
        settings.minimumFetchInterval = 0
        remoteConfig.configSettings = settings
        remoteConfig.fetchAndActivate { [weak self] _, _ in
            self?.evaluateAndApplyIcon()
        }
    }

    private func evaluateAndApplyIcon() {
        let remoteIconName = remoteConfig[AppIconRemoteKeys.iconName].stringValue ?? ""
        let currentIcon = UIApplication.shared.alternateIconName

        // Nếu remoteIconName là rỗng → revert về icon mặc định
        if remoteIconName.isEmpty {
            if currentIcon != nil {
                resetToDefaultIcon()
            }
            return
        }

        // Nếu icon hiện tại khác remote → tiến hành đổi
        if currentIcon != remoteIconName {
            setIcon(named: remoteIconName)
        }
    }

    private func setIcon(named name: String) {
        let app = UIApplication.shared
        if #available(iOS 10.3, *) {
            if app.supportsAlternateIcons {
                app.setAlternateIconName(name) { [weak self] error in
                    guard let self = self else { return }
                    if let error = error {
                        print("❌ Failed to change icon: \(error.localizedDescription)")
                    } else {
                        print("✅ Changed icon successfully to: \(name)")
                        UserDefaults.standard.setValue(name, forKey: userDefaultsKey)
                    }
                }
            }
        }
    }

    private func resetToDefaultIcon() {
        UIApplication.shared.setAlternateIconName(nil) { [weak self] error in
            guard let self = self else { return }
            if let error = error {
                print("❌ Failed to reset to default icon: \(error.localizedDescription)")
            } else {
                print("✅ Reverted to default icon.")
                UserDefaults.standard.removeObject(forKey: userDefaultsKey)
            }
        }
    }
}

import 'dart:io';

/// <PERSON>ript to setup environment-specific Facebook configurations
/// This helps separate dev and production Facebook settings
void main(List<String> args) {
  print('🔧 Setting up Facebook Environment Configurations');
  print('==================================================');

  if (args.isEmpty) {
    print('Usage: dart scripts/setup_facebook_environments.dart <environment>');
    print('Environments: dev, staging, prod');
    exit(1);
  }

  final environment = args[0].toLowerCase();
  
  switch (environment) {
    case 'dev':
      setupDevEnvironment();
      break;
    case 'staging':
      setupStagingEnvironment();
      break;
    case 'prod':
      setupProdEnvironment();
      break;
    default:
      print('❌ Unknown environment: $environment');
      print('Available: dev, staging, prod');
      exit(1);
  }
}

void setupDevEnvironment() {
  print('🔧 Setting up DEV environment...');
  
  // Update Android configuration for dev
  updateAndroidConfig(
    appId: '367174740769877', // Same app ID but different package
    clientToken: '********************************',
    packageSuffix: '.dev',
    appName: 'Beta Cinemas Dev',
  );
  
  // Update iOS configuration for dev
  updateiOSConfig(
    appId: '367174740769877',
    clientToken: '********************************',
    bundleId: 'com.beta.betacineplex.dev',
    displayName: 'Beta Cinemas Dev',
  );
  
  // Update environment file
  updateEnvironmentFile('development', {
    'API_URL': 'http://dev.api.betacorp.vn/',
    'FACEBOOK_APP_ID': '367174740769877',
    'FACEBOOK_CLIENT_TOKEN': '********************************',
    'APP_PACKAGE': 'com.beta.betacineplex.dev',
  });
  
  print('✅ DEV environment configured');
  print('📝 Next steps:');
  print('   1. Run: flutter clean && flutter pub get');
  print('   2. Generate key hash: bash scripts/generate_facebook_key_hash.sh');
  print('   3. Add key hash to Facebook Developer Console for package: com.beta.betacineplex.dev');
}

void setupStagingEnvironment() {
  print('🔧 Setting up STAGING environment...');
  
  updateAndroidConfig(
    appId: '367174740769877',
    clientToken: '********************************',
    packageSuffix: '.staging',
    appName: 'Beta Cinemas Staging',
  );
  
  updateiOSConfig(
    appId: '367174740769877',
    clientToken: '********************************',
    bundleId: 'com.beta.betacineplex.staging',
    displayName: 'Beta Cinemas Staging',
  );
  
  updateEnvironmentFile('staging', {
    'API_URL': 'https://staging-api.betacorp.vn/',
    'FACEBOOK_APP_ID': '367174740769877',
    'FACEBOOK_CLIENT_TOKEN': '********************************',
    'APP_PACKAGE': 'com.beta.betacineplex.staging',
  });
  
  print('✅ STAGING environment configured');
}

void setupProdEnvironment() {
  print('🔧 Setting up PRODUCTION environment...');
  
  updateAndroidConfig(
    appId: '367174740769877',
    clientToken: '********************************',
    packageSuffix: '',
    appName: 'Beta Cinemas',
  );
  
  updateiOSConfig(
    appId: '367174740769877',
    clientToken: '********************************',
    bundleId: 'com.beta.betacineplex',
    displayName: 'Beta Cinemas',
  );
  
  updateEnvironmentFile('production', {
    'API_URL': 'https://api.betacorp.vn',
    'FACEBOOK_APP_ID': '367174740769877',
    'FACEBOOK_CLIENT_TOKEN': '********************************',
    'APP_PACKAGE': 'com.beta.betacineplex',
  });
  
  print('✅ PRODUCTION environment configured');
}

void updateAndroidConfig({
  required String appId,
  required String clientToken,
  required String packageSuffix,
  required String appName,
}) {
  print('📱 Updating Android configuration...');
  
  // Update strings.xml
  final stringsFile = File('android/app/src/main/res/values/strings.xml');
  if (stringsFile.existsSync()) {
    final content = '''<?xml version="1.0" encoding="utf-8"?>
<resources>
    <!-- App name -->
    <string name="app_name">$appName</string>
    
    <!-- Facebook configuration -->
    <string name="facebook_app_id">$appId</string>
    <string name="facebook_client_token">$clientToken</string>
    <string name="fb_login_protocol_scheme">fb$appId</string>
    
    <!-- Facebook display name -->
    <string name="facebook_display_name">Betacineplex</string>
</resources>''';
    
    stringsFile.writeAsStringSync(content);
    print('   ✅ Updated strings.xml');
  }
  
  // Note: build.gradle applicationIdSuffix should be configured manually
  // or through flavor-specific configurations
  print('   📝 Remember to update build.gradle applicationIdSuffix: "$packageSuffix"');
}

void updateiOSConfig({
  required String appId,
  required String clientToken,
  required String bundleId,
  required String displayName,
}) {
  print('🍎 Updating iOS configuration...');
  
  final infoPlistFile = File('ios/Runner/Info.plist');
  if (infoPlistFile.existsSync()) {
    String content = infoPlistFile.readAsStringSync();
    
    // Update Facebook configuration in Info.plist
    // This is a simplified approach - in production, you might want to use proper plist parsing
    content = content.replaceAll(
      RegExp(r'<key>FacebookAppID</key>\s*<string>.*?</string>'),
      '<key>FacebookAppID</key>\n  <string>$appId</string>'
    );
    
    content = content.replaceAll(
      RegExp(r'<key>FacebookClientToken</key>\s*<string>.*?</string>'),
      '<key>FacebookClientToken</key>\n  <string>$clientToken</string>'
    );
    
    content = content.replaceAll(
      RegExp(r'<key>FacebookDisplayName</key>\s*<string>.*?</string>'),
      '<key>FacebookDisplayName</key>\n  <string>$displayName</string>'
    );
    
    // Update URL scheme
    content = content.replaceAll(
      RegExp(r'<string>fb\d+</string>'),
      '<string>fb$appId</string>'
    );
    
    infoPlistFile.writeAsStringSync(content);
    print('   ✅ Updated Info.plist');
  }
  
  print('   📝 Remember to update bundle identifier to: $bundleId');
}

void updateEnvironmentFile(String env, Map<String, String> config) {
  print('🌍 Updating environment file...');
  
  final envFile = File('.env.$env');
  final content = config.entries
      .map((entry) => "${entry.key}='${entry.value}'")
      .join('\n') + '\n';
  
  envFile.writeAsStringSync(content);
  print('   ✅ Updated .env.$env');
}

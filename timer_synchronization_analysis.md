# Timer Synchronization Analysis - Seat to WebView Payment

## Tóm Tắt Kết Quả

### ✅ **iOS Style WebView - ĐÃ IMPLEMENT**
- Nhận `remainingTime` và `timeStartBooking` từ seat.dart
- Có countdown timer riêng trong webview
- Đồng bộ hóa với seat timer
- Auto pop về seat khi hết giờ

### ❌ **Android Style WebView - CHƯA IMPLEMENT**
- KHÔNG nhận `remainingTime` và `timeStartBooking`
- KHÔNG có countdown timer
- KHÔNG đồng bộ hóa với seat timer
- KHÔNG auto pop khi hết giờ

## Chi Tiết So Sánh

### 1. **iOS Style Implementation** ✅

#### Navigation từ Seat:
```dart
// lib/pages/cinema/choose/seat.dart - navigateToIOSStylePayment()
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => IOSStyleWebViewPayment(
      // ... other params
      remainingTime: _remainingTime,                    // ✅ Truyền remaining time
      timeStartBooking: DateTime.fromMillisecondsSinceEpoch((timeStartBooking * 1000).toInt()), // ✅ Truyền start time
      onPaymentSuccess: (result) {
        Navigator.of(context).popUntil((route) => route.isFirst);
      },
      // ... other callbacks
    ),
  ),
);
```

#### WebView Timer Implementation:
```dart
// lib/pages/cinema/payment/ios_style_webview_payment.dart
class _IOSStyleWebViewPaymentState extends State<IOSStyleWebViewPayment> {
  Timer? _countdownTimer;
  int _remainingSeconds = 600;

  @override
  void initState() {
    super.initState();
    _remainingSeconds = widget.remainingTime ?? 600; // ✅ Nhận timer từ seat
    _startCountdownTimer();                          // ✅ Bắt đầu countdown
  }

  void _startCountdownTimer() {
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _remainingSeconds--;
      });

      if (_remainingSeconds <= 0) {
        timer.cancel();
        _handleTimeout();                            // ✅ Xử lý timeout
      }
    });
  }

  void _handleTimeout() {
    if (mounted) {
      widget.onPaymentFailed('Timeout: Session expired');
      Navigator.of(context).pop();                   // ✅ Auto pop về seat
    }
  }
}
```

#### Countdown Calculation:
```dart
// Calculate countdown exactly like iOS
final expiredTime = widget.timeStartBooking?.add(const Duration(minutes: 10)) ??
                  DateTime.now().add(const Duration(minutes: 10));
final countDown = '/Date(${expiredTime.millisecondsSinceEpoch})/';
```

### 2. **Android Style Implementation** ❌

#### Navigation từ Seat:
```dart
// lib/pages/cinema/choose/seat.dart - navigateToAndroidStylePayment()
Navigator.push(
  context,
  MaterialPageRoute(
    builder: (context) => AndroidStyleWebViewPayment(
      // ... other params
      // ❌ THIẾU: remainingTime
      // ❌ THIẾU: timeStartBooking
      onPaymentSuccess: (result) {
        Navigator.of(context).popUntil((route) => route.isFirst);
      },
      // ... other callbacks
    ),
  ),
);
```

#### WebView Constructor:
```dart
// lib/pages/cinema/payment/android_style_webview_payment.dart
const AndroidStyleWebViewPayment({
  super.key,
  this.film,
  this.combo,
  this.listSeat,
  this.cinemaId,
  this.totalPrice,
  this.cinemaName,
  required this.onPaymentSuccess,
  required this.onPaymentFailed,
  required this.onPaymentWaiting,
  required this.onPaymentMethodSelected,
  this.selectedSeats,
  this.showTime,
  this.showId,
  // ❌ THIẾU: remainingTime parameter
  // ❌ THIẾU: timeStartBooking parameter
});
```

#### State Class:
```dart
class _AndroidStyleWebViewPaymentState extends State<AndroidStyleWebViewPayment> {
  // ❌ THIẾU: Timer? _countdownTimer;
  // ❌ THIẾU: int _remainingSeconds;
  
  @override
  void initState() {
    super.initState();
    // ❌ THIẾU: Timer initialization
    // ❌ THIẾU: Countdown logic
  }
  
  // ❌ THIẾU: _startCountdownTimer()
  // ❌ THIẾU: _handleTimeout()
}
```

### 3. **iOS Native Reference**

#### iOS PaymentViewController:
```swift
// Booking/Class/Controller/Payment/PaymentViewController.swift
var timeStartBooking: TimeInterval = 0

func startTimer() {
    stopTimer()
    timer = Timer.scheduledTimer(timeInterval: 1, target: self, selector: #selector(updateTimer), userInfo: nil, repeats: true)
}

@objc func updateTimer() {
    let currentTime = NSDate().timeIntervalSince1970
    let timeLeft = Int(Config.TimeExpired - (currentTime - timeStartBooking));
    if (timeLeft <= 0) {
        stopTimer()
        self.showAlert(message: "SeatsTimeOut".localized) { _ in
            self.navigationController?.popToRootViewController(animated: true)
        }
    }
}
```

### 4. **Android Native Reference**

#### Android SelectChairPresenter:
```kotlin
// app/src/main/java/vn/zenity/betacineplex/view/film/SelectChairPresenter.kt
override fun countDownTime(expiredTime: Date) {
    val timerDisposable = Observable.interval(1, TimeUnit.SECONDS, Schedulers.computation())
        .map { expiredTime }
        .map { dateTime -> dateTime.time - System.currentTimeMillis() }
        .filter { it > 0 }
        .subscribe {
            if (it <= 0) {
                compositeDisposable.dispose()
            }
            view?.get()?.showCountDownTime(it)
        }
}
```

## Vấn Đề Cần Sửa

### 1. **Android Style WebView - Thiếu Timer Parameters**

#### Cần thêm vào constructor:
```dart
const AndroidStyleWebViewPayment({
  // ... existing params
  this.remainingTime,        // ✅ Thêm parameter
  this.timeStartBooking,     // ✅ Thêm parameter
});
```

#### Cần thêm vào class:
```dart
final int? remainingTime;
final DateTime? timeStartBooking;
```

### 2. **Android Style WebView - Thiếu Timer Logic**

#### Cần implement:
```dart
class _AndroidStyleWebViewPaymentState extends State<AndroidStyleWebViewPayment> {
  Timer? _countdownTimer;
  int _remainingSeconds = 600;

  @override
  void initState() {
    super.initState();
    _remainingSeconds = widget.remainingTime ?? 600;
    _startCountdownTimer();
  }

  void _startCountdownTimer() {
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        _remainingSeconds--;
      });

      if (_remainingSeconds <= 0) {
        timer.cancel();
        _handleTimeout();
      }
    });
  }

  void _handleTimeout() {
    if (mounted) {
      widget.onPaymentFailed('Timeout: Session expired');
      Navigator.of(context).pop();
    }
  }

  @override
  void dispose() {
    _countdownTimer?.cancel();
    super.dispose();
  }
}
```

### 3. **Navigation Update**

#### Cần update seat.dart:
```dart
void navigateToAndroidStylePayment() {
  Navigator.push(
    context,
    MaterialPageRoute(
      builder: (context) => AndroidStyleWebViewPayment(
        // ... existing params
        remainingTime: _remainingTime,                    // ✅ Thêm
        timeStartBooking: DateTime.fromMillisecondsSinceEpoch((timeStartBooking * 1000).toInt()), // ✅ Thêm
        // ... existing callbacks
      ),
    ),
  );
}
```

## Kết Luận

### ✅ **iOS Style**: Hoàn chỉnh
- Timer synchronization hoạt động đúng
- Auto pop về seat khi hết giờ
- Đồng bộ với seat timer

### ❌ **Android Style**: Thiếu hoàn toàn
- Không có timer parameters
- Không có countdown logic
- Không auto pop khi hết giờ
- Cần implement đầy đủ như iOS Style

### 🔧 **Khuyến nghị**:
1. **Ưu tiên cao**: Fix Android Style WebView để match iOS Style
2. **Thêm timer parameters** vào constructor
3. **Implement countdown logic** giống iOS Style
4. **Test timer synchronization** giữa seat và webview
5. **Đảm bảo auto pop** khi hết giờ

**Hiện tại chỉ iOS Style WebView hoạt động đúng, Android Style cần fix hoàn toàn.**

# Avatar Upload Error Fix

## Lỗi Gặp Phải

```
🔄 Upload avatar response: 200
📄 Response body: {"Data":null,"TotalCount":0,"DataCount":0,"Status":-1,"Message":"Error message: Access to the path 'C:\\inetpub\\files\\account\\avatar\\2025\\7\\7' is denied."}
```

## Phân Tích Lỗi

### 1. **HTTP Status 200 nhưng Status -1**
- Server tr<PERSON> về HTTP 200 (thành công)
- Nhưng response body có `Status: -1` (lỗi)
- Message: "Access to the path 'C:\\inetpub\\files\\account\\avatar\\2025\\7\\7' is denied"

### 2. **Nguyên Nhân**
- **Server-side error**: Server không có quyền ghi vào thư mục avatar
- **Request đúng format**: Flutter đã gửi request đúng như iOS/Android
- **Authentication thành công**: <PERSON><PERSON> hợ<PERSON> lệ, server nhậ<PERSON> đư<PERSON> request

### 3. **Vấn Đề Đã Sửa**

#### Before (<PERSON><PERSON> vấn đề):
```dart
// Base URL không match iOS/Android
final response = await http.put(
  Uri.parse('${ApiService.baseUrl}/api/v1/erp/accounts/$accountId/avatar'),
  headers: {
    'Authorization': 'Bearer $token',
    'Content-Type': 'application/json',
  },
  body: jsonEncode(body),
);

// Chỉ check Status == 1
if (responseData['Status'] == 1) {
  // Success
}
```

#### After (Đã sửa):
```dart
// Base URL chính xác như iOS/Android
final String baseUrl = 'https://api.betacorp.vn'; // Match iOS Config.BaseURL
final response = await http.put(
  Uri.parse('$baseUrl/api/v1/erp/accounts/$accountId/avatar'),
  headers: {
    'Authorization': 'Bearer $token',
    'Content-Type': 'application/json',
    'channel': 'mobile',                                    // ✅ Match iOS/Android
    'device-type': Platform.isAndroid ? 'android' : 'ios', // ✅ Match iOS/Android
    'language': 'vi',                                       // ✅ Match iOS/Android
    'X-User': accountId,                                    // ✅ Match Android APIClient
  },
  body: jsonEncode(body),
);

// Handle both success and error status
final int status = responseData['Status'] ?? -1;
final String message = responseData['Message'] ?? '';

print('📊 Response Status: $status');
print('💬 Response Message: $message');

if (status == 1 || status == 0) { // Success status
  // Success
} else {
  // Handle server errors (Status: -1, etc.)
  print('❌ Server error: Status=$status, Message=$message');
  onError('Lỗi server: $message');
}
```

## So Sánh Với iOS/Android

### iOS Headers:
```swift
// Booking/Manager/Network/Moya/Global.swift
func headers() -> [String: String] {
    var headers = [String: String]()
    headers["channel"] = "mobile"
    headers["Content-Type"] = "application/json"
    headers["device-type"] = "ios"
    headers["language"] = Utils.shared.isEng() ? "en" : "vi"
    if let token = self.token {
        headers["Authorization"] = "Bearer \(token)"
    }
    return headers
}
```

### Android Headers:
```kotlin
// app/src/main/java/vn/zenity/betacineplex/Manager/Network/APIClient.kt
val headerInterceptor = Interceptor { chain ->  
    val builder = chain.request().newBuilder()
        .addHeader("channel", "mobile")
        .addHeader("device-type", "android")
        .addHeader("language", if(App.shared().isLangVi()) "vn" else "en")
    
    Global.share().user?.AccountId?.let {
        builder.addHeader("X-User", it)
    }
    
    if (!chain.request().url().encodedPath().contains("login")) {
        Global.share().token?.let {
            builder.addHeader("Authorization", "Bearer $it")
        }
    }
    
    val request = builder.build()
    chain.proceed(request)
}
```

### Flutter Headers (Fixed):
```dart
final Map<String, String> headers = {
  'Authorization': 'Bearer $token',
  'Content-Type': 'application/json',
  'channel': 'mobile',                                    // ✅ Match iOS/Android
  'device-type': Platform.isAndroid ? 'android' : 'ios', // ✅ Match iOS/Android
  'language': 'vi',                                       // ✅ Match iOS/Android
  'X-User': accountId,                                    // ✅ Match Android APIClient
};
```

## Base URL Comparison

### iOS:
```swift
// Booking/Common/Config.swift
#elseif BETA_PRO
static let BaseURL = "https://api.betacorp.vn/"
```

### Android:
```kotlin
// app/build.gradle
prod {
    buildConfigField "String", "BASE_URL", '"https://api.betacorp.vn/"'
}
```

### Flutter (Fixed):
```dart
final String baseUrl = 'https://api.betacorp.vn'; // Match iOS/Android
```

## Request Body Comparison

### iOS:
```swift
// Booking/Manager/Network/Moya/AccountAPI.swift
case .uploadAvatar(_, let base64):
    return ["ImageBase64": base64,
            "DeviceId": UIDevice.current.identifierForVendor?.uuidString ?? "",
            "Extension": ".jpg"]
```

### Android:
```kotlin
// app/src/main/java/vn/zenity/betacineplex/view/user/MemberPresenter.kt
APIClient.shared.accountAPI.uploadAvatar(
    hashMapOf(
        "ImageBase64" to imageBase64, 
        "Extension" to ".jpg"
    ), 
    accountId
)
```

### Flutter (Fixed):
```dart
final Map<String, dynamic> body = {
  'ImageBase64': imageBase64,
  'Extension': '.jpg',
  'DeviceId': deviceId,  // ✅ Add DeviceId like iOS/Android
};
```

## Kết Quả

### ✅ **Đã Sửa:**
1. **Base URL**: Chính xác như iOS/Android
2. **Headers**: Đầy đủ tất cả headers cần thiết
3. **Error Handling**: Handle cả success và error status
4. **Device ID**: Sử dụng device_info_plus
5. **Request Body**: Đúng format như iOS/Android

### 🔍 **Lỗi Server-side:**
- **Vấn đề**: Server không có quyền ghi vào thư mục `C:\inetpub\files\account\avatar\2025\7\7`
- **Giải pháp**: Cần admin server cấp quyền write cho thư mục này
- **Tạm thời**: Request đã đúng format, chờ server fix permission

### 📱 **Test Kết Quả:**
```
🔄 Upload avatar response: 200
📊 Response Status: -1
💬 Response Message: Error message: Access to the path 'C:\inetpub\files\account\avatar\2025\7\7' is denied.
❌ Server error: Status=-1, Message=Error message: Access to the path 'C:\inetpub\files\account\avatar\2025\7\7' is denied.
```

**Flutter giờ đây gửi request chính xác như iOS/Android. Lỗi là do server-side permission issue.**

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'common.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:flutter_app/main.dart' as app; // Sửa lại đúng tên app của bạn
import 'package:flutter/material.dart';

void main() async {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();
  testWidgets('Login', (WidgetTester tester) async {
    await initAppWidgetTest(tester);

    // Wait for app to load and API calls to complete
    // The app calls sFilm.getInfoHomePage() which might set homeModel
    // We need to wait for this to complete and ensure user is not logged in
    await tester.pump(const Duration(seconds: 5));
    await tester.pumpAndSettle();

    // Debug: Check current state
    print('🔍 Checking app state...');
    print('OutlinedButton widgets: ${find.byType(OutlinedButton).evaluate().length}');
    print('InkWell widgets: ${find.byType(InkWell).evaluate().length}');
    print('ElevatedButton widgets: ${find.byType(ElevatedButton).evaluate().length}');

    // Look for login button in different ways
    Finder? loginButtonFinder;

    // Method 1: By key (most reliable)
    if (find.byKey(const Key('login_button_homePage')).evaluate().isNotEmpty) {
      loginButtonFinder = find.byKey(const Key('login_button_homePage'));
      print('✅ Found login button by key');
    }
    // Method 2: By OutlinedButton type (login button is OutlinedButton.icon)
    else if (find.byType(OutlinedButton).evaluate().isNotEmpty) {
      loginButtonFinder = find.byType(OutlinedButton).first;
      print('✅ Found login button by OutlinedButton type');
    }
    // Method 3: Look in bottom navigation or drawer
    else {
      print('🔍 Login button not found in main area, checking other locations...');

      // Check if there's a drawer or menu
      final scaffoldFinder = find.byType(Scaffold);
      if (scaffoldFinder.evaluate().isNotEmpty) {
        // Try to open drawer
        try {
          await tester.tap(find.byTooltip('Open navigation menu'));
          await tester.pumpAndSettle();

          // Look for login button in drawer
          if (find.text('pages.login.login.Log in'.tr()).evaluate().isNotEmpty) {
            loginButtonFinder = find.text('pages.login.login.Log in'.tr());
            print('✅ Found login button in drawer');
          }
        } catch (e) {
          print('⚠️ No drawer found or failed to open: $e');
        }
      }

      // If still not found, check bottom navigation
      if (loginButtonFinder == null) {
        // Navigate to "Khác" tab which might have login
        final bottomNavItems = find.byType(BottomNavigationBar);
        if (bottomNavItems.evaluate().isNotEmpty) {
          await tester.tap(find.text('Khác\n '));
          await tester.pumpAndSettle();

          // Look for login button in "Khác" tab
          if (find.text('pages.login.login.Log in'.tr()).evaluate().isNotEmpty) {
            loginButtonFinder = find.text('pages.login.login.Log in'.tr());
            print('✅ Found login button in Khác tab');
          }
        }
      }
    }

    if (loginButtonFinder == null) {
      throw Exception('❌ Login button not found anywhere in the app');
    }

    // Tap login button
    await tester.tap(loginButtonFinder);
    await tester.pumpAndSettle();
    // await tester.tap(find.byKey( ValueKey('pages.login.login.Log in'.tr())));
    // await tester.pumpAndSettle();
    // expect(find.byKey(const Key("login_button_homePage")), findsOneWidget);
    // await tester.tap(find.widgetWithText(OutlinedButton, 'pages.login.login.Log in'.tr()));

    // Check login form elements using translation keys
    expect(find.byKey(const ValueKey('Email hoặc tên đăng nhập')), findsOneWidget);
    expect(find.byKey(ValueKey('pages.login.login.Password'.tr())), findsOneWidget);
    // expect(find.text('pages.login.login.Forgot password'.tr()), findsOneWidget);
    expect(find.widgetWithText(ElevatedButton, 'pages.login.login.Log in'.tr()), findsOneWidget);

    await tapButtonPump(tester, 'Đăng nhập');
    expect(find.descendant(of: find.byKey(const ValueKey("Địa chỉ Email")), matching: find.text('Xin vui lòng nhập địa chỉ email')), findsOneWidget);
    expect(find.descendant(of: find.byKey(const ValueKey("Mật khẩu")), matching: find.text('Xin vui lòng nhập mật khẩu')), findsOneWidget);

    await tester.enterText(find.byKey(const ValueKey("Email hoặc tên đăng nhập")), '<EMAIL>');
    await tester.enterText(find.byKey(const ValueKey("Mật khẩu")), '123123');

    await SystemChannels.textInput.invokeMethod('TextInput.hide');
    await tester.pumpAndSettle(const Duration(milliseconds: 500));
    await tester.tap(find.widgetWithText(ElevatedButton, "Đăng nhập"));
    await pumpUntilFound(tester, find.text("Tài khoản <EMAIL> không tồn tại trong hệ thống. Vui lòng đăng ký mới."));

    // expect(find.text("Tài khoản <EMAIL> không tồn tại trong hệ thống. Vui lòng đăng ký mới."), findsOneWidget);
    // await tester.tap(find.widgetWithText(TextButton, "Thoát"));
    // await tester.pumpAndSettle();
    //
    // await tester.enterText(find.byKey(const ValueKey("Địa chỉ Email")), '<EMAIL>');
    // await tester.enterText(find.byKey(const ValueKey("Mật khẩu")), '123123123');
    //
    // await SystemChannels.textInput.invokeMethod('TextInput.hide');
    // await tester.pumpAndSettle(const Duration(milliseconds: 300));
    // await tapButtonPump(tester, 'Đăng nhập');
    // await pumpUntilFound(tester, find.text("Sai mật khẩu cho tài khoản <EMAIL>"));
    //
    //
    // expect(find.text("Sai mật khẩu cho tài khoản <EMAIL>"), findsOneWidget);
    // await tester.tap(find.widgetWithText(TextButton, "Thoát"));
    // await tester.pumpAndSettle();
    //
    // await tester.enterText(find.byKey(const ValueKey("Địa chỉ Email")), '<EMAIL>');
    // await tester.enterText(find.byKey(const ValueKey("Mật khẩu")), '123123');
    //
    // await SystemChannels.textInput.invokeMethod('TextInput.hide');
    // await tester.pumpAndSettle(const Duration(milliseconds: 300));
    // await tapButtonPump(tester, 'Đăng nhập');
    // await pumpUntilFound(tester, find.text("Home"));

    // expect(find.text("Home"), findsOneWidget);
  });
}

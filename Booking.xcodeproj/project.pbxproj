// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 48;
	objects = {

/* Begin PBXBuildFile section */
		6A03F38C208106F0002FC2CD /* TransparentAnimator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A03F38B208106F0002FC2CD /* TransparentAnimator.swift */; };
		6A0A8B37208571BD00679340 /* GradientImageView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A0A8B36208571BD00679340 /* GradientImageView.swift */; };
		6A11D6CB20B9D69A009668B2 /* ColumnLayout.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A11D6CA20B9D69A009668B2 /* ColumnLayout.swift */; };
		6A11D6CD20BA1567009668B2 /* SeatCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A11D6CC20BA1567009668B2 /* SeatCollectionViewCell.swift */; };
		6A11D6CF20BA3F66009668B2 /* ListSeatModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A11D6CE20BA3F66009668B2 /* ListSeatModel.swift */; };
		6A11D6D120BA4021009668B2 /* ScreenModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A11D6D020BA4021009668B2 /* ScreenModel.swift */; };
		6A11D6D320BA402A009668B2 /* SeatModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A11D6D220BA402A009668B2 /* SeatModel.swift */; };
		6A11D6D520BA446E009668B2 /* TicketType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A11D6D420BA446E009668B2 /* TicketType.swift */; };
		6A1F60132078E71A00212F7D /* View+Layer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A1F60122078E71A00212F7D /* View+Layer.swift */; };
		6A1F60152078EAB300212F7D /* View+Size.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A1F60142078EAB300212F7D /* View+Size.swift */; };
		6A22620021120E3F0029B300 /* UpdatePasswordViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AFD7002210FF59500E01D8A /* UpdatePasswordViewController.swift */; };
		6A29B7A1207C0D1500ED7F4C /* NewsDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B73C207C0D1400ED7F4C /* NewsDetailViewController.swift */; };
		6A29B7A3207C0D1500ED7F4C /* BannerCardView.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B73E207C0D1400ED7F4C /* BannerCardView.xib */; };
		6A29B7A5207C0D1500ED7F4C /* BannerCardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B73F207C0D1400ED7F4C /* BannerCardView.swift */; };
		6A29B7A7207C0D1500ED7F4C /* Home.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B740207C0D1400ED7F4C /* Home.storyboard */; };
		6A29B7A9207C0D1500ED7F4C /* HomeViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B741207C0D1400ED7F4C /* HomeViewController.swift */; };
		6A29B7AB207C0D1500ED7F4C /* NewsAndDealsViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B742207C0D1400ED7F4C /* NewsAndDealsViewController.swift */; };
		6A29B7AF207C0D1500ED7F4C /* NewsTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B745207C0D1400ED7F4C /* NewsTableViewCell.xib */; };
		6A29B7B5207C0D1500ED7F4C /* TicketBookingViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B74A207C0D1400ED7F4C /* TicketBookingViewController.swift */; };
		6A29B7B7207C0D1500ED7F4C /* GraphShitingViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B74B207C0D1400ED7F4C /* GraphShitingViewController.swift */; };
		6A29B7B9207C0D1500ED7F4C /* Film.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B74C207C0D1400ED7F4C /* Film.storyboard */; };
		6A29B7BB207C0D1500ED7F4C /* FilmDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B74D207C0D1400ED7F4C /* FilmDetailViewController.swift */; };
		6A29B7BD207C0D1500ED7F4C /* ListFilmViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B74E207C0D1400ED7F4C /* ListFilmViewController.swift */; };
		6A29B7BF207C0D1500ED7F4C /* ListAllCinemasViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B750207C0D1400ED7F4C /* ListAllCinemasViewController.swift */; };
		6A29B7C1207C0D1500ED7F4C /* CinemaDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B751207C0D1400ED7F4C /* CinemaDetailViewController.swift */; };
		6A29B7C3207C0D1500ED7F4C /* ChooseCinemasViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B752207C0D1400ED7F4C /* ChooseCinemasViewController.swift */; };
		6A29B7C5207C0D1500ED7F4C /* NearCinemaTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B754207C0D1400ED7F4C /* NearCinemaTableViewCell.swift */; };
		6A29B7C7207C0D1500ED7F4C /* AreaCinemaHeaderView.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B755207C0D1400ED7F4C /* AreaCinemaHeaderView.xib */; };
		6A29B7C9207C0D1500ED7F4C /* TitleHeaderView.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B756207C0D1400ED7F4C /* TitleHeaderView.xib */; };
		6A29B7CB207C0D1500ED7F4C /* AreaCinemaHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B757207C0D1400ED7F4C /* AreaCinemaHeaderView.swift */; };
		6A29B7CD207C0D1500ED7F4C /* TitleHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B758207C0D1400ED7F4C /* TitleHeaderView.swift */; };
		6A29B7CF207C0D1500ED7F4C /* CinemaTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B759207C0D1400ED7F4C /* CinemaTableViewCell.swift */; };
		6A29B7D1207C0D1500ED7F4C /* Cinema.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B75A207C0D1400ED7F4C /* Cinema.storyboard */; };
		6A29B7D3207C0D1500ED7F4C /* PaymentViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B75C207C0D1400ED7F4C /* PaymentViewController.swift */; };
		6A29B7D5207C0D1500ED7F4C /* Payment.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B75D207C0D1400ED7F4C /* Payment.storyboard */; };
		6A29B7D7207C0D1500ED7F4C /* ForgotPassViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B75F207C0D1400ED7F4C /* ForgotPassViewController.swift */; };
		6A29B7D9207C0D1500ED7F4C /* LoginViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B760207C0D1400ED7F4C /* LoginViewController.swift */; };
		6A29B7DB207C0D1500ED7F4C /* Authen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B761207C0D1400ED7F4C /* Authen.storyboard */; };
		6A29B7DD207C0D1500ED7F4C /* RegisterViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B762207C0D1400ED7F4C /* RegisterViewController.swift */; };
		6A29B7DF207C0D1500ED7F4C /* RewardPointsViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B764207C0D1400ED7F4C /* RewardPointsViewController.swift */; };
		6A29B7E1207C0D1500ED7F4C /* VourcherCouponViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B765207C0D1400ED7F4C /* VourcherCouponViewController.swift */; };
		6A29B7E3207C0D1500ED7F4C /* Member.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B766207C0D1400ED7F4C /* Member.storyboard */; };
		6A29B7E5207C0D1500ED7F4C /* MemberCardViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B767207C0D1400ED7F4C /* MemberCardViewController.swift */; };
		6A29B7E7207C0D1500ED7F4C /* VoucherViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B768207C0D1400ED7F4C /* VoucherViewController.swift */; };
		6A29B7E9207C0D1500ED7F4C /* ChangePassViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B769207C0D1400ED7F4C /* ChangePassViewController.swift */; };
		6A29B7EB207C0D1500ED7F4C /* CardGiftViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B76A207C0D1400ED7F4C /* CardGiftViewController.swift */; };
		6A29B7ED207C0D1500ED7F4C /* MemberViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B76B207C0D1400ED7F4C /* MemberViewController.swift */; };
		6A29B7EF207C0D1500ED7F4C /* ListFilmWatchedViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B76C207C0D1400ED7F4C /* ListFilmWatchedViewController.swift */; };
		6A29B7F1207C0D1500ED7F4C /* CouponViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B76D207C0D1400ED7F4C /* CouponViewController.swift */; };
		6A29B7F3207C0D1500ED7F4C /* TranferHistoryViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B76E207C0D1400ED7F4C /* TranferHistoryViewController.swift */; };
		6A29B7F5207C0D1500ED7F4C /* SlideMenuViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B770207C0D1400ED7F4C /* SlideMenuViewController.swift */; };
		6A29B7F7207C0D1500ED7F4C /* MenuItemCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B772207C0D1400ED7F4C /* MenuItemCell.swift */; };
		6A29B7F9207C0D1500ED7F4C /* NotificationTableCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B773207C0D1400ED7F4C /* NotificationTableCell.swift */; };
		6A29B7FB207C0D1500ED7F4C /* NotificationViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B774207C0D1400ED7F4C /* NotificationViewController.swift */; };
		6A29B7FD207C0D1500ED7F4C /* SettingViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B776207C0D1400ED7F4C /* SettingViewController.swift */; };
		6A29B7FF207C0D1500ED7F4C /* Setting.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B777207C0D1400ED7F4C /* Setting.storyboard */; };
		6A29B801207C0D1500ED7F4C /* VersionInfoViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B778207C0D1400ED7F4C /* VersionInfoViewController.swift */; };
		6A29B803207C0D1500ED7F4C /* OtherViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B779207C0D1400ED7F4C /* OtherViewController.swift */; };
		6A29B805207C0D1500ED7F4C /* ProfileViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B77A207C0D1400ED7F4C /* ProfileViewController.swift */; };
		6A29B807207C0D1500ED7F4C /* BaseViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B77C207C0D1500ED7F4C /* BaseViewController.swift */; };
		6A29B809207C0D1500ED7F4C /* BaseNavigationViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B77D207C0D1500ED7F4C /* BaseNavigationViewController.swift */; };
		6A29B80B207C0D1500ED7F4C /* BaseRequestModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B780207C0D1500ED7F4C /* BaseRequestModel.swift */; };
		6A29B80D207C0D1500ED7F4C /* LoginModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B781207C0D1500ED7F4C /* LoginModel.swift */; };
		6A29B80F207C0D1500ED7F4C /* RegisterModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B782207C0D1500ED7F4C /* RegisterModel.swift */; };
		6A29B811207C0D1500ED7F4C /* PointModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B784207C0D1500ED7F4C /* PointModel.swift */; };
		6A29B813207C0D1500ED7F4C /* FilmModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B785207C0D1500ED7F4C /* FilmModel.swift */; };
		6A29B815207C0D1500ED7F4C /* CinemaModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B786207C0D1500ED7F4C /* CinemaModel.swift */; };
		6A29B817207C0D1500ED7F4C /* CardModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B787207C0D1500ED7F4C /* CardModel.swift */; };
		6A29B819207C0D1500ED7F4C /* ListPosterUrlModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B788207C0D1500ED7F4C /* ListPosterUrlModel.swift */; };
		6A29B81B207C0D1500ED7F4C /* CityModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B789207C0D1500ED7F4C /* CityModel.swift */; };
		6A29B81D207C0D1500ED7F4C /* ListFilmGenreModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B78A207C0D1500ED7F4C /* ListFilmGenreModel.swift */; };
		6A29B81F207C0D1500ED7F4C /* UserModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B78B207C0D1500ED7F4C /* UserModel.swift */; };
		6A29B821207C0D1500ED7F4C /* ShowFilmModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B78C207C0D1500ED7F4C /* ShowFilmModel.swift */; };
		6A29B829207C0D1500ED7F4C /* NewsModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B791207C0D1500ED7F4C /* NewsModel.swift */; };
		6A29B837207C0D1500ED7F4C /* NewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B798207C0D1500ED7F4C /* NewModel.swift */; };
		6A29B839207C0D1500ED7F4C /* ShowModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B799207C0D1500ED7F4C /* ShowModel.swift */; };
		6A29B83B207C0D1500ED7F4C /* CinemaProvinceModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B79A207C0D1500ED7F4C /* CinemaProvinceModel.swift */; };
		6A29B83D207C0D1500ED7F4C /* Repository.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B79B207C0D1500ED7F4C /* Repository.swift */; };
		6A29B83F207C0D1500ED7F4C /* BaseModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B79C207C0D1500ED7F4C /* BaseModel.swift */; };
		6A29B841207C0D1500ED7F4C /* Owner.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B79D207C0D1500ED7F4C /* Owner.swift */; };
		6A29B843207C0D1500ED7F4C /* NewAndDealsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B79F207C0D1500ED7F4C /* NewAndDealsView.swift */; };
		6A29B845207C0D1500ED7F4C /* NewAndDealsView.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B7A0207C0D1500ED7F4C /* NewAndDealsView.xib */; };
		6A29B859207C0D3800ED7F4C /* Constants.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B848207C0D3800ED7F4C /* Constants.swift */; };
		6A29B85B207C0D3800ED7F4C /* Global.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B849207C0D3800ED7F4C /* Global.swift */; };
		6A29B85D207C0D3800ED7F4C /* Config.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B84A207C0D3800ED7F4C /* Config.swift */; };
		6A29B85F207C0D3800ED7F4C /* Fonts.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B84B207C0D3800ED7F4C /* Fonts.swift */; };
		6A29B861207C0D3800ED7F4C /* PickerTextField.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B84D207C0D3800ED7F4C /* PickerTextField.swift */; };
		6A29B863207C0D3800ED7F4C /* RoundView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B84E207C0D3800ED7F4C /* RoundView.swift */; };
		6A29B865207C0D3800ED7F4C /* RoundButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B84F207C0D3800ED7F4C /* RoundButton.swift */; };
		6A29B867207C0D3800ED7F4C /* RoundTextField.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B850207C0D3800ED7F4C /* RoundTextField.swift */; };
		6A29B869207C0D3800ED7F4C /* InputTextField.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B851207C0D3800ED7F4C /* InputTextField.swift */; };
		6A29B86B207C0D3800ED7F4C /* GradientButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B852207C0D3800ED7F4C /* GradientButton.swift */; };
		6A29B86D207C0D3800ED7F4C /* GradientView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B853207C0D3800ED7F4C /* GradientView.swift */; };
		6A29B86F207C0D3800ED7F4C /* RoundImageView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B854207C0D3800ED7F4C /* RoundImageView.swift */; };
		6A29B871207C0D3800ED7F4C /* DateTextField.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B855207C0D3800ED7F4C /* DateTextField.swift */; };
		6A29B873207C0D3800ED7F4C /* Images.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B856207C0D3800ED7F4C /* Images.swift */; };
		6A29B875207C0D3800ED7F4C /* Utils.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B857207C0D3800ED7F4C /* Utils.swift */; };
		6A29B877207C0D3800ED7F4C /* Color.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B858207C0D3800ED7F4C /* Color.swift */; };
		6A29B87B207C25B200ED7F4C /* FilmDescriptionTableCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B87A207C25B200ED7F4C /* FilmDescriptionTableCell.swift */; };
		6A29B87E207C2A6B00ED7F4C /* NewsAndDealsCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B87D207C2A6B00ED7F4C /* NewsAndDealsCell.swift */; };
		6A29B881207C2AB100ED7F4C /* NewsAndDealsCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B880207C2AB100ED7F4C /* NewsAndDealsCell.xib */; };
		6A29B884207CC07900ED7F4C /* DateFormat.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B883207CC07900ED7F4C /* DateFormat.swift */; };
		6A29B888207EA50F00ED7F4C /* SettingTableCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B887207EA50F00ED7F4C /* SettingTableCell.swift */; };
		6A29B88B207EA51F00ED7F4C /* SwitchTableCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B88A207EA51F00ED7F4C /* SwitchTableCell.swift */; };
		6A29B88E207EA54600ED7F4C /* CheckboxTableCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B88D207EA54600ED7F4C /* CheckboxTableCell.swift */; };
		6A29B891207EA57E00ED7F4C /* CheckboxTableCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B890207EA57E00ED7F4C /* CheckboxTableCell.xib */; };
		6A29B894207EA58C00ED7F4C /* SettingTableCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B893207EA58C00ED7F4C /* SettingTableCell.xib */; };
		6A29B897207EA59D00ED7F4C /* SwitchTableCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B896207EA59D00ED7F4C /* SwitchTableCell.xib */; };
		6A29B89A207EADCB00ED7F4C /* Device.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B899207EADCB00ED7F4C /* Device.swift */; };
		6A29B89D207EC13400ED7F4C /* FAQViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B89C207EC13300ED7F4C /* FAQViewController.swift */; };
		6A31AAFA20BA91E100DC59B3 /* RegisterResultViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A31AAF920BA91E100DC59B3 /* RegisterResultViewController.swift */; };
		6A31AAFD20BB2AED00DC59B3 /* SeatCollectionHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A31AAFB20BB2AED00DC59B3 /* SeatCollectionHeaderView.swift */; };
		6A31AAFE20BB2AED00DC59B3 /* SeatCollectionHeaderView.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6A31AAFC20BB2AED00DC59B3 /* SeatCollectionHeaderView.xib */; };
		6A34871920C149390074F58F /* CreateBookingModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A34871820C149390074F58F /* CreateBookingModel.swift */; };
		6A34871B20C1499A0074F58F /* SeatBookingModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A34871A20C1499A0074F58F /* SeatBookingModel.swift */; };
		6A3FB3B720BF93390034FC3D /* ConfirmBookAgeViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A3FB3B620BF93390034FC3D /* ConfirmBookAgeViewController.swift */; };
		6A41ECBC20CB83D100BA16FA /* CardClassModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A41ECBB20CB83D100BA16FA /* CardClassModel.swift */; };
		6A41ED2920D1215F00BA16FA /* NotificationDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A41ED2820D1215F00BA16FA /* NotificationDetailViewController.swift */; };
		6A44992D20B626BB006B37A3 /* CinemaPriceHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A44992C20B626BB006B37A3 /* CinemaPriceHeaderView.swift */; };
		6A44992F20B626C8006B37A3 /* CinemaPriceHeaderView.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6A44992E20B626C8006B37A3 /* CinemaPriceHeaderView.xib */; };
		6A4550B120816C4A00AD3031 /* TopViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A4550B020816C4A00AD3031 /* TopViewController.swift */; };
		6A4550BA2082517300AD3031 /* LocalizableProtocol.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A4550B92082517300AD3031 /* LocalizableProtocol.swift */; };
		6A4550BD2082520800AD3031 /* LocalizableLabel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A4550BC2082520800AD3031 /* LocalizableLabel.swift */; };
		6A4550C02082530200AD3031 /* LocalizableButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A4550BF2082530200AD3031 /* LocalizableButton.swift */; };
		6A48447520F59C7100273D66 /* CinemaFilmTimeTableCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A2B22C020924E9A00DA096B /* CinemaFilmTimeTableCell.swift */; };
		6A48447620F59C7100273D66 /* TopicDetailModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE16D01720810693004D9722 /* TopicDetailModel.swift */; };
		6A48447720F59C7100273D66 /* ConfirmPassViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A812F092086BBEB0079ABF6 /* ConfirmPassViewController.swift */; };
		6A48447820F59C7100273D66 /* YoutubeViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A968EAB20B7EBC100A80BE2 /* YoutubeViewController.swift */; };
		6A48447920F59C7100273D66 /* GradientView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B853207C0D3800ED7F4C /* GradientView.swift */; };
		6A48447A20F59C7100273D66 /* TicketBookingViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B74A207C0D1400ED7F4C /* TicketBookingViewController.swift */; };
		6A48447B20F59C7100273D66 /* NewsAndDealsCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B87D207C2A6B00ED7F4C /* NewsAndDealsCell.swift */; };
		6A48447C20F59C7100273D66 /* SeatCollectionHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A31AAFB20BB2AED00DC59B3 /* SeatCollectionHeaderView.swift */; };
		6A48447D20F59C7100273D66 /* LanguageManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE16D0112080F1A5004D9722 /* LanguageManager.swift */; };
		6A48447E20F59C7100273D66 /* MemberViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B76B207C0D1400ED7F4C /* MemberViewController.swift */; };
		6A48447F20F59C7100273D66 /* CinemaDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B751207C0D1400ED7F4C /* CinemaDetailViewController.swift */; };
		6A48448020F59C7100273D66 /* LoginModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B781207C0D1500ED7F4C /* LoginModel.swift */; };
		6A48448120F59C7100273D66 /* HCYoutubeParser.m in Sources */ = {isa = PBXBuildFile; fileRef = 6AC8AC7320AF4C2800DE4F5B /* HCYoutubeParser.m */; };
		6A48448220F59C7100273D66 /* BaseNavigationViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B77D207C0D1500ED7F4C /* BaseNavigationViewController.swift */; };
		6A48448320F59C7100273D66 /* Common+Ext.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* Common+Ext.swift */; };
		6A48448420F59C7100273D66 /* AppDelegate+Initial.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE1C88E32053FD11003B5B9A /* AppDelegate+Initial.swift */; };
		6A48448520F59C7100273D66 /* FilmDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B74D207C0D1400ED7F4C /* FilmDetailViewController.swift */; };
		6A48448620F59C7100273D66 /* TableViewHelper.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AA1AC842076CF220081188F /* TableViewHelper.swift */; };
		6A48448720F59C7100273D66 /* Global.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B849207C0D3800ED7F4C /* Global.swift */; };
		6A48448820F59C7100273D66 /* ForgotPassViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B75F207C0D1400ED7F4C /* ForgotPassViewController.swift */; };
		6A48448920F59C7100273D66 /* NewsAndDealsViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B742207C0D1400ED7F4C /* NewsAndDealsViewController.swift */; };
		6A48448A20F59C7100273D66 /* TopicModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE16D01420810652004D9722 /* TopicModel.swift */; };
		6A48448B20F59C7100273D66 /* RegisterResultViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A31AAF920BA91E100DC59B3 /* RegisterResultViewController.swift */; };
		6A48448C20F59C7100273D66 /* SeatBookingModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A34871A20C1499A0074F58F /* SeatBookingModel.swift */; };
		6A48448D20F59C7100273D66 /* LocalizableLabel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A4550BC2082520800AD3031 /* LocalizableLabel.swift */; };
		6A48448E20F59C7100273D66 /* FilmModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B785207C0D1500ED7F4C /* FilmModel.swift */; };
		6A48448F20F59C7100273D66 /* DDKCResult.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEBE7B1E207D09C1005A1F1D /* DDKCResult.swift */; };
		6A48449020F59C7100273D66 /* NewsDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B73C207C0D1400ED7F4C /* NewsDetailViewController.swift */; };
		6A48449120F59C7100273D66 /* PolicyModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DED214692083AA22003BBBA2 /* PolicyModel.swift */; };
		6A48449220F59C7100273D66 /* ShowFilmModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B78C207C0D1500ED7F4C /* ShowFilmModel.swift */; };
		6A48449320F59C7100273D66 /* FAQViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B89C207EC13300ED7F4C /* FAQViewController.swift */; };
		6A48449420F59C7100273D66 /* TransactionDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A812F1320870F500079ABF6 /* TransactionDetailViewController.swift */; };
		6A48449520F59C7100273D66 /* Color.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B858207C0D3800ED7F4C /* Color.swift */; };
		6A48449620F59C7100273D66 /* NotificationModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEBE7B15207CC4F5005A1F1D /* NotificationModel.swift */; };
		6A48449720F59C7100273D66 /* Fonts.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B84B207C0D3800ED7F4C /* Fonts.swift */; };
		6A48449820F59C7100273D66 /* ListPosterUrlModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B788207C0D1500ED7F4C /* ListPosterUrlModel.swift */; };
		6A48449920F59C7100273D66 /* CityAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* CityAPI.swift */; };
		6A48449A20F59C7100273D66 /* FilmDescriptionTableCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B87A207C25B200ED7F4C /* FilmDescriptionTableCell.swift */; };
		6A48449B20F59C7100273D66 /* PaymentPointViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AA13FE0208C901C007809AC /* PaymentPointViewController.swift */; };
		6A48449C20F59C7100273D66 /* FilmAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* FilmAPI.swift */; };
		6A48449D20F59C7100273D66 /* LocationManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AC8AC7720AFDD2900DE4F5B /* LocationManager.swift */; };
		6A48449E20F59C7100273D66 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE1C887B20539BC3003B5B9A /* AppDelegate.swift */; };
		6A48449F20F59C7100273D66 /* MemberCardCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A7C078A20B3730C0092E553 /* MemberCardCell.swift */; };
		6A4844A020F59C7100273D66 /* BaseRequestModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B780207C0D1500ED7F4C /* BaseRequestModel.swift */; };
		6A4844A120F59C7100273D66 /* ListFilmWatchedViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B76C207C0D1400ED7F4C /* ListFilmWatchedViewController.swift */; };
		6A4844A220F59C7100273D66 /* NewsModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B791207C0D1500ED7F4C /* NewsModel.swift */; };
		6A4844A320F59C7100273D66 /* AreaCinemaHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B757207C0D1400ED7F4C /* AreaCinemaHeaderView.swift */; };
		6A4844A420F59C7100273D66 /* Repository.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B79B207C0D1500ED7F4C /* Repository.swift */; };
		6A4844A520F59C7100273D66 /* ShowCinemaModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AC52E5220B54ED600D9BCAC /* ShowCinemaModel.swift */; };
		6A4844A620F59C7100273D66 /* VoucherViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B768207C0D1400ED7F4C /* VoucherViewController.swift */; };
		6A4844A720F59C7100273D66 /* TransactionHistoryCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A812F1020870A480079ABF6 /* TransactionHistoryCell.swift */; };
		6A4844A820F59C7100273D66 /* AccountInfoViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A812F0B2086BBF70079ABF6 /* AccountInfoViewController.swift */; };
		6A4844A920F59C7100273D66 /* SlideMenuViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B770207C0D1400ED7F4C /* SlideMenuViewController.swift */; };
		6A4844AA20F59C7100273D66 /* FilmChooseTimeViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A661B5620913C5000CFC976 /* FilmChooseTimeViewController.swift */; };
		6A4844AB20F59C7100273D66 /* ListAllCinemasViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B750207C0D1400ED7F4C /* ListAllCinemasViewController.swift */; };
		6A4844AC20F59C7100273D66 /* TransactionHistoryDetailModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE1B083A20C035A1000FFD1D /* TransactionHistoryDetailModel.swift */; };
		6A4844AD20F59C7100273D66 /* OtherViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B779207C0D1400ED7F4C /* OtherViewController.swift */; };
		6A4844AE20F59C7100273D66 /* ListFilmViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B74E207C0D1400ED7F4C /* ListFilmViewController.swift */; };
		6A4844AF20F59C7100273D66 /* SettingTableCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B887207EA50F00ED7F4C /* SettingTableCell.swift */; };
		6A4844B020F59C7100273D66 /* CinemaTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B759207C0D1400ED7F4C /* CinemaTableViewCell.swift */; };
		6A4844B120F59C7100273D66 /* CheckboxTableCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B88D207EA54600ED7F4C /* CheckboxTableCell.swift */; };
		6A4844B220F59C7100273D66 /* GraphShitingViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B74B207C0D1400ED7F4C /* GraphShitingViewController.swift */; };
		6A4844B320F59C7100273D66 /* SelectRegionViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A968EAD20B925A500A80BE2 /* SelectRegionViewController.swift */; };
		6A4844B420F59C7100273D66 /* LoginViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B760207C0D1400ED7F4C /* LoginViewController.swift */; };
		6A4844B520F59C7100273D66 /* ListSeatModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A11D6CE20BA3F66009668B2 /* ListSeatModel.swift */; };
		6A4844B620F59C7100273D66 /* Utils.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B857207C0D3800ED7F4C /* Utils.swift */; };
		6A4844B720F59C7100273D66 /* ChooseCinemasViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B752207C0D1400ED7F4C /* ChooseCinemasViewController.swift */; };
		6A4844B820F59C7100273D66 /* Storyboard+Quick.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A912F152069806C003F98B3 /* Storyboard+Quick.swift */; };
		6A4844B920F59C7100273D66 /* Image+Ext.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A812F1F2088A18C0079ABF6 /* Image+Ext.swift */; };
		6A4844BA20F59C7100273D66 /* CalendarHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AA299B0208C42680007E074 /* CalendarHeaderView.swift */; };
		6A4844BB20F59C7100273D66 /* CinemaModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B786207C0D1500ED7F4C /* CinemaModel.swift */; };
		6A4844BC20F59C7100273D66 /* CardGiftViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B76A207C0D1400ED7F4C /* CardGiftViewController.swift */; };
		6A4844BD20F59C7100273D66 /* MenuItemCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B772207C0D1400ED7F4C /* MenuItemCell.swift */; };
		6A4844BE20F59C7100273D66 /* RecruitmentViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AC52E5420B6064600D9BCAC /* RecruitmentViewController.swift */; };
		6A4844BF20F59C7100273D66 /* String+Ext.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE002F5F2074DD4C0001C63D /* String+Ext.swift */; };
		6A4844C020F59C7100273D66 /* ProfileViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B77A207C0D1400ED7F4C /* ProfileViewController.swift */; };
		6A4844C120F59C7100273D66 /* FilmTimeTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AA13FE2208C97A3007809AC /* FilmTimeTableViewCell.swift */; };
		6A4844C220F59C7100273D66 /* NewsTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE75FC17207F9A4C00604D7E /* NewsTableViewCell.swift */; };
		6A4844C320F59C7100273D66 /* CinemaProvinceModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B79A207C0D1500ED7F4C /* CinemaProvinceModel.swift */; };
		6A4844C420F59C7100273D66 /* Config.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B84A207C0D3800ED7F4C /* Config.swift */; };
		6A4844C520F59C7100273D66 /* TimeListCollectionCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A661B50208FF99F00CFC976 /* TimeListCollectionCell.swift */; };
		6A4844C620F59C7100273D66 /* PolicyContentModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DED2146C2083AA34003BBBA2 /* PolicyContentModel.swift */; };
		6A4844C720F59C7100273D66 /* AvatarImageModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AA959CE20B65DE000E6337A /* AvatarImageModel.swift */; };
		6A4844C820F59C7100273D66 /* LocalizableButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A4550BF2082530200AD3031 /* LocalizableButton.swift */; };
		6A4844C920F59C7100273D66 /* Device.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B899207EADCB00ED7F4C /* Device.swift */; };
		6A4844CA20F59C7100273D66 /* SettingViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B776207C0D1400ED7F4C /* SettingViewController.swift */; };
		6A4844CB20F59C7100273D66 /* AccountAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEB8F8A72075237A00715531 /* AccountAPI.swift */; };
		6A4844CC20F59C7100273D66 /* ListFilmGenreModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B78A207C0D1500ED7F4C /* ListFilmGenreModel.swift */; };
		6A4844CD20F59C7100273D66 /* BaseModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B79C207C0D1500ED7F4C /* BaseModel.swift */; };
		6A4844CE20F59C7100273D66 /* GradientButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B852207C0D3800ED7F4C /* GradientButton.swift */; };
		6A4844CF20F59C7100273D66 /* NearCinemaTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B754207C0D1400ED7F4C /* NearCinemaTableViewCell.swift */; };
		6A4844D020F59C7100273D66 /* Array+Ext.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A5B053420BE838F00E40BC0 /* Array+Ext.swift */; };
		6A4844D120F59C7100273D66 /* StickyHeaderViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AA14023208F371B007809AC /* StickyHeaderViewController.swift */; };
		6A4844D220F59C7100273D66 /* SeatModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A11D6D220BA402A009668B2 /* SeatModel.swift */; };
		6A4844D320F59C7100273D66 /* TranferHistoryViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B76E207C0D1400ED7F4C /* TranferHistoryViewController.swift */; };
		6A4844D420F59C7100273D66 /* TicketType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A11D6D420BA446E009668B2 /* TicketType.swift */; };
		6A4844D520F59C7100273D66 /* NotificationDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A41ED2820D1215F00BA16FA /* NotificationDetailViewController.swift */; };
		6A4844D620F59C7100273D66 /* VoucherAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* VoucherAPI.swift */; };
		6A4844D720F59C7100273D66 /* ChangePassViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B769207C0D1400ED7F4C /* ChangePassViewController.swift */; };
		6A4844D820F59C7100273D66 /* RoundTextField.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B850207C0D3800ED7F4C /* RoundTextField.swift */; };
		6A4844D920F59C7100273D66 /* FAQDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE16D01D208109ED004D9722 /* FAQDetailViewController.swift */; };
		6A4844DA20F59C7100273D66 /* CinemaPriceViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A7C078720B3701A0092E553 /* CinemaPriceViewController.swift */; };
		6A4844DB20F59C7100273D66 /* BaseViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B77C207C0D1500ED7F4C /* BaseViewController.swift */; };
		6A4844DC20F59C7100273D66 /* VersionInfoViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B778207C0D1400ED7F4C /* VersionInfoViewController.swift */; };
		6A4844DD20F59C7100273D66 /* UIAlertController+Ext.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE26416F207A62DD00844F7F /* UIAlertController+Ext.swift */; };
		6A4844DE20F59C7100273D66 /* CardModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B787207C0D1500ED7F4C /* CardModel.swift */; };
		6A4844DF20F59C7100273D66 /* Date+Ext.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEBE7B18207CDF4D005A1F1D /* Date+Ext.swift */; };
		6A4844E020F59C7100273D66 /* NewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B798207C0D1500ED7F4C /* NewModel.swift */; };
		6A4844E120F59C7100273D66 /* RoundView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B84E207C0D3800ED7F4C /* RoundView.swift */; };
		6A4844E220F59C7100273D66 /* AppDelegate+Appearance.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A9D124420740CC800D49B55 /* AppDelegate+Appearance.swift */; };
		6A4844E320F59C7100273D66 /* UserModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B78B207C0D1500ED7F4C /* UserModel.swift */; };
		6A4844E420F59C7100273D66 /* NotificationTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE16D02020810BCA004D9722 /* NotificationTableViewCell.swift */; };
		6A4844E520F59C7100273D66 /* VourcherCouponViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B765207C0D1400ED7F4C /* VourcherCouponViewController.swift */; };
		6A4844E620F59C7100273D66 /* SSASideMenu.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A9D123A2072D90C00D49B55 /* SSASideMenu.swift */; };
		6A4844E720F59C7100273D66 /* EcmAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* EcmAPI.swift */; };
		6A4844E820F59C7100273D66 /* FilmItemTableCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AA299A7208BF2A20007E074 /* FilmItemTableCell.swift */; };
		6A4844E920F59C7100273D66 /* DDKCResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE2164E72073C99B00938938 /* DDKCResponse.swift */; };
		6A4844EA20F59C7100273D66 /* PickerTextField.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B84D207C0D3800ED7F4C /* PickerTextField.swift */; };
		6A4844EB20F59C7100273D66 /* VoucherTableCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A812F22208933160079ABF6 /* VoucherTableCell.swift */; };
		6A4844EC20F59C7100273D66 /* SwitchTableCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B88A207EA51F00ED7F4C /* SwitchTableCell.swift */; };
		6A4844ED20F59C7100273D66 /* PaymentViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B75C207C0D1400ED7F4C /* PaymentViewController.swift */; };
		6A4844EE20F59C7100273D66 /* SeatCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A11D6CC20BA1567009668B2 /* SeatCollectionViewCell.swift */; };
		6A4844EF20F59C7100273D66 /* TimeListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A661B4D208FF6E600CFC976 /* TimeListView.swift */; };
		6A4844F020F59C7100273D66 /* TopViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A4550B020816C4A00AD3031 /* TopViewController.swift */; };
		6A4844F120F59C7100273D66 /* ChooseSeatViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AF06C6420A010A100A6459C /* ChooseSeatViewController.swift */; };
		6A4844F220F59C7100273D66 /* NetworkManager+Ecm.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEBE7B26207D13D8005A1F1D /* NetworkManager+Ecm.swift */; };
		6A4844F320F59C7100273D66 /* CreateBookingModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A34871820C149390074F58F /* CreateBookingModel.swift */; };
		6A4844F420F59C7100273D66 /* VoucherModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE154A7A2090EDC300A51889 /* VoucherModel.swift */; };
		6A4844F520F59C7100273D66 /* NetworkManager+Film.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEBE7B23207D0C2C005A1F1D /* NetworkManager+Film.swift */; };
		6A4844F620F59C7100273D66 /* DateTextField.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B855207C0D3800ED7F4C /* DateTextField.swift */; };
		6A4844F720F59C7100273D66 /* FilmBookingViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AA299AD208C04920007E074 /* FilmBookingViewController.swift */; };
		6A4844F820F59C7100273D66 /* VerticalLayoutButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A97191020A39962009FF4B7 /* VerticalLayoutButton.swift */; };
		6A4844F920F59C7100273D66 /* RegisterModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B782207C0D1500ED7F4C /* RegisterModel.swift */; };
		6A4844FA20F59C7100273D66 /* View+Size.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A1F60142078EAB300212F7D /* View+Size.swift */; };
		6A4844FB20F59C7100273D66 /* TitleHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B758207C0D1400ED7F4C /* TitleHeaderView.swift */; };
		6A4844FC20F59C7100273D66 /* CinemaPriceHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A44992C20B626BB006B37A3 /* CinemaPriceHeaderView.swift */; };
		6A4844FD20F59C7100273D66 /* TransparentAnimator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A03F38B208106F0002FC2CD /* TransparentAnimator.swift */; };
		6A4844FE20F59C7100273D66 /* ScrollPager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A9D123E2072E3F700D49B55 /* ScrollPager.swift */; };
		6A4844FF20F59C7100273D66 /* RoundImageView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B854207C0D3800ED7F4C /* RoundImageView.swift */; };
		6A48450020F59C7100273D66 /* Images.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B856207C0D3800ED7F4C /* Images.swift */; };
		6A48450120F59C7100273D66 /* App+Rx.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE1C88D42053E2D0003B5B9A /* App+Rx.swift */; };
		6A48450220F59C7100273D66 /* ScreenModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A11D6D020BA4021009668B2 /* ScreenModel.swift */; };
		6A48450320F59C7100273D66 /* AssetLibrary.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A812F1920889E880079ABF6 /* AssetLibrary.swift */; };
		6A48450420F59C7100273D66 /* NotificationViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B774207C0D1400ED7F4C /* NotificationViewController.swift */; };
		6A48450520F59C7100273D66 /* DashView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A812F162087C9280079ABF6 /* DashView.swift */; };
		6A48450620F59C7100273D66 /* Constants.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B848207C0D3800ED7F4C /* Constants.swift */; };
		6A48450720F59C7100273D66 /* Owner.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B79D207C0D1500ED7F4C /* Owner.swift */; };
		6A48450820F59C7100273D66 /* CinemaAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEA5BEA12073F636006D8DE1 /* CinemaAPI.swift */; };
		6A48450920F59C7100273D66 /* RewardPointsViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B764207C0D1400ED7F4C /* RewardPointsViewController.swift */; };
		6A48450A20F59C7100273D66 /* ConfirmBookAgeViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A3FB3B620BF93390034FC3D /* ConfirmBookAgeViewController.swift */; };
		6A48450B20F59C7100273D66 /* NetworkManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEBE7B1B207D01EC005A1F1D /* NetworkManager.swift */; };
		6A48450C20F59C7100273D66 /* RegisterVoucherModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE019FB520B9585500A934B0 /* RegisterVoucherModel.swift */; };
		6A48450D20F59C7100273D66 /* ShowModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B799207C0D1500ED7F4C /* ShowModel.swift */; };
		6A48450E20F59C7100273D66 /* ColumnLayout.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A11D6CA20B9D69A009668B2 /* ColumnLayout.swift */; };
		6A48450F20F59C7100273D66 /* RoundButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B84F207C0D3800ED7F4C /* RoundButton.swift */; };
		6A48451020F59C7100273D66 /* PointModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B784207C0D1500ED7F4C /* PointModel.swift */; };
		6A48451120F59C7100273D66 /* NotificationTableCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B773207C0D1400ED7F4C /* NotificationTableCell.swift */; };
		6A48451220F59C7100273D66 /* RegisterViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B762207C0D1400ED7F4C /* RegisterViewController.swift */; };
		6A48451320F59C7100273D66 /* Notification.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE4453B520F3B13B00835007 /* Notification.swift */; };
		6A48451420F59C7100273D66 /* ImagePickerController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A812F1B20889EEE0079ABF6 /* ImagePickerController.swift */; };
		6A48451520F59C7100273D66 /* MemberCardViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B767207C0D1400ED7F4C /* MemberCardViewController.swift */; };
		6A48451620F59C7100273D66 /* GradientImageView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A0A8B36208571BD00679340 /* GradientImageView.swift */; };
		6A48451720F59C7100273D66 /* BannerCardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B73F207C0D1400ED7F4C /* BannerCardView.swift */; };
		6A48451820F59C7100273D66 /* Location+Ext.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE75FC1E207F9FC200604D7E /* Location+Ext.swift */; };
		6A48451920F59C7100273D66 /* ListNewsViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AA14020208F2FA1007809AC /* ListNewsViewController.swift */; };
		6A48451A20F59C7100273D66 /* CouponViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B76D207C0D1400ED7F4C /* CouponViewController.swift */; };
		6A48451C20F59C7100273D66 /* TransactionHistoryModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE1CF99120BD9885001D89F2 /* TransactionHistoryModel.swift */; };
		6A48451D20F59C7100273D66 /* HomeViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B741207C0D1400ED7F4C /* HomeViewController.swift */; };
		6A48451E20F59C7100273D66 /* UserDefault+Quick.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE1C88E02053FBA3003B5B9A /* UserDefault+Quick.swift */; };
		6A48451F20F59C7100273D66 /* CardClassModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A41ECBB20CB83D100BA16FA /* CardClassModel.swift */; };
		6A48452020F59C7100273D66 /* UILabel+Localization.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE26416C207A56C700844F7F /* UILabel+Localization.swift */; };
		6A48452120F59C7100273D66 /* LocalizableProtocol.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A4550B92082517300AD3031 /* LocalizableProtocol.swift */; };
		6A48452220F59C7100273D66 /* InputTextField.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B851207C0D3800ED7F4C /* InputTextField.swift */; };
		6A48452320F59C7100273D66 /* CityModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B789207C0D1500ED7F4C /* CityModel.swift */; };
		6A48452420F59C7100273D66 /* NewAndDealsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B79F207C0D1500ED7F4C /* NewAndDealsView.swift */; };
		6A48452520F59C7100273D66 /* DateFormat.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B883207CC07900ED7F4C /* DateFormat.swift */; };
		6A48452620F59C7100273D66 /* CalendarHeaderViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AA299B4208C43490007E074 /* CalendarHeaderViewCell.swift */; };
		6A48452720F59C7100273D66 /* View+Layer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A1F60122078E71A00212F7D /* View+Layer.swift */; };
		6A48452C20F59C7100273D66 /* OFL.txt in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D127D20746A3700D49B55 /* OFL.txt */; };
		6A48452D20F59C7100273D66 /* Film.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B74C207C0D1400ED7F4C /* Film.storyboard */; };
		6A48452E20F59C7100273D66 /* TimeListCollectionCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6A661B53208FF9AA00CFC976 /* TimeListCollectionCell.xib */; };
		6A48452F20F59C7100273D66 /* SourceSansPro-BoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D127F20746A3700D49B55 /* SourceSansPro-BoldItalic.ttf */; };
		6A48453020F59C7100273D66 /* SourceSansPro-BlackItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D127620746A3700D49B55 /* SourceSansPro-BlackItalic.ttf */; };
		6A48453120F59C7100273D66 /* SourceSansPro-ExtraLightItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D128120746A3700D49B55 /* SourceSansPro-ExtraLightItalic.ttf */; };
		6A48453220F59C7100273D66 /* SourceSansPro-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D127820746A3700D49B55 /* SourceSansPro-Regular.ttf */; };
		6A48453320F59C7100273D66 /* Oswald-ExtraLight.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D1258207418B200D49B55 /* Oswald-ExtraLight.ttf */; };
		6A48453420F59C7100273D66 /* Oswald-SemiBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D1254207418B200D49B55 /* Oswald-SemiBold.ttf */; };
		6A48453520F59C7100273D66 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = DE1C888B20539BC3003B5B9A /* LaunchScreen.storyboard */; };
		6A48453620F59C7100273D66 /* SourceSansPro-ExtraLight.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D127E20746A3700D49B55 /* SourceSansPro-ExtraLight.ttf */; };
		6A48453720F59C7100273D66 /* VoucherTableCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6A812F25208939E60079ABF6 /* VoucherTableCell.xib */; };
		6A48453820F59C7100273D66 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = DE1C88F920540F9E003B5B9A /* <EMAIL> */; };
		6A48453920F59C7100273D66 /* SourceSansPro-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D127920746A3700D49B55 /* SourceSansPro-Bold.ttf */; };
		6A48453A20F59C7100273D66 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = DE1C88EF20540F9E003B5B9A /* <EMAIL> */; };
		6A48453B20F59C7100273D66 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = DE1C888A20539BC3003B5B9A /* Assets.xcassets */; };
		6A48453C20F59C7100273D66 /* CalendarHeaderViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6AA299B5208C43490007E074 /* CalendarHeaderViewCell.xib */; };
		6A48453D20F59C7100273D66 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = DE1C88FA20540F9E003B5B9A /* <EMAIL> */; };
		6A48453E20F59C7100273D66 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = DE1C88F320540F9E003B5B9A /* <EMAIL> */; };
		6A48453F20F59C7100273D66 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = DE1C88F820540F9E003B5B9A /* <EMAIL> */; };
		6A48454020F59C7100273D66 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = DE1C88F520540F9E003B5B9A /* <EMAIL> */; };
		6A48454120F59C7100273D66 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = DE1C88F420540F9E003B5B9A /* <EMAIL> */; };
		6A48454220F59C7100273D66 /* Member.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B766207C0D1400ED7F4C /* Member.storyboard */; };
		6A48454320F59C7100273D66 /* Oswald-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D1255207418B200D49B55 /* Oswald-Medium.ttf */; };
		6A48454420F59C7100273D66 /* CheckboxTableCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B890207EA57E00ED7F4C /* CheckboxTableCell.xib */; };
		6A48454520F59C7100273D66 /* NewsAndDealsCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B880207C2AB100ED7F4C /* NewsAndDealsCell.xib */; };
		6A48454620F59C7100273D66 /* SourceSansPro-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D127B20746A3700D49B55 /* SourceSansPro-Light.ttf */; };
		6A48454720F59C7100273D66 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = DE1C88EE20540F9E003B5B9A /* <EMAIL> */; };
		6A48454820F59C7100273D66 /* SourceSansPro-SemiBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D128020746A3700D49B55 /* SourceSansPro-SemiBold.ttf */; };
		6A48454920F59C7100273D66 /* Localizable.strings in Resources */ = {isa = PBXBuildFile; fileRef = DE002F5D2074DC6A0001C63D /* Localizable.strings */; };
		6A48454A20F59C7100273D66 /* pageDot.png in Resources */ = {isa = PBXBuildFile; fileRef = DE1C88EC20540F9E003B5B9A /* pageDot.png */; };
		6A48454B20F59C7100273D66 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = DE1C888D20539BC3003B5B9A /* Main.storyboard */; };
		6A48454C20F59C7100273D66 /* FilmTimeTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6AA13FE3208C97A3007809AC /* FilmTimeTableViewCell.xib */; };
		6A48454D20F59C7100273D66 /* Oswald-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D1256207418B200D49B55 /* Oswald-Regular.ttf */; };
		6A48454E20F59C7100273D66 /* NewAndDealsView.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B7A0207C0D1500ED7F4C /* NewAndDealsView.xib */; };
		6A48454F20F59C7100273D66 /* SwitchTableCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B896207EA59D00ED7F4C /* SwitchTableCell.xib */; };
		6A48455020F59C7100273D66 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = DE1C88ED20540F9E003B5B9A /* <EMAIL> */; };
		6A48455120F59C7100273D66 /* Authen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B761207C0D1400ED7F4C /* Authen.storyboard */; };
		6A48455220F59C7100273D66 /* Home.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B740207C0D1400ED7F4C /* Home.storyboard */; };
		6A48455320F59C7100273D66 /* SettingTableCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B893207EA58C00ED7F4C /* SettingTableCell.xib */; };
		6A48455420F59C7100273D66 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = DE1C88F220540F9E003B5B9A /* <EMAIL> */; };
		6A48455520F59C7100273D66 /* Cinema.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B75A207C0D1400ED7F4C /* Cinema.storyboard */; };
		6A48455620F59C7100273D66 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = DE1C88F120540F9E003B5B9A /* <EMAIL> */; };
		6A48455720F59C7100273D66 /* NotificationTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = DE16D02120810BCA004D9722 /* NotificationTableViewCell.xib */; };
		6A48455820F59C7100273D66 /* Payment.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B75D207C0D1400ED7F4C /* Payment.storyboard */; };
		6A48455920F59C7100273D66 /* NewsTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B745207C0D1400ED7F4C /* NewsTableViewCell.xib */; };
		6A48455A20F59C7100273D66 /* Setting.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B777207C0D1400ED7F4C /* Setting.storyboard */; };
		6A48455B20F59C7100273D66 /* TitleHeaderView.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B756207C0D1400ED7F4C /* TitleHeaderView.xib */; };
		6A48455C20F59C7100273D66 /* BannerCardView.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B73E207C0D1400ED7F4C /* BannerCardView.xib */; };
		6A48455D20F59C7100273D66 /* SourceSansPro-Black.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D127C20746A3700D49B55 /* SourceSansPro-Black.ttf */; };
		6A48455E20F59C7100273D66 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = DE1C88F720540F9E003B5B9A /* <EMAIL> */; };
		6A48455F20F59C7100273D66 /* FilmItemTableCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6AA299A8208BF2A20007E074 /* FilmItemTableCell.xib */; };
		6A48456020F59C7100273D66 /* Oswald-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D1259207418B200D49B55 /* Oswald-Light.ttf */; };
		6A48456120F59C7100273D66 /* SourceSansPro-LightItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D127A20746A3700D49B55 /* SourceSansPro-LightItalic.ttf */; };
		6A48456220F59C7100273D66 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = DE1C88F620540F9E003B5B9A /* <EMAIL> */; };
		6A48456320F59C7100273D66 /* AreaCinemaHeaderView.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B755207C0D1400ED7F4C /* AreaCinemaHeaderView.xib */; };
		6A48456420F59C7100273D66 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = DE1C88F020540F9E003B5B9A /* <EMAIL> */; };
		6A48456520F59C7100273D66 /* Oswald-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D1253207418B200D49B55 /* Oswald-Bold.ttf */; };
		6A48456620F59C7100273D66 /* SourceSansPro-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D128220746A3700D49B55 /* SourceSansPro-Italic.ttf */; };
		6A48456720F59C7100273D66 /* CinemaPriceHeaderView.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6A44992E20B626C8006B37A3 /* CinemaPriceHeaderView.xib */; };
		6A48456820F59C7100273D66 /* SeatCollectionHeaderView.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6A31AAFC20BB2AED00DC59B3 /* SeatCollectionHeaderView.xib */; };
		6A48456920F59C7100273D66 /* SourceSansPro-SemiBoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D127720746A3700D49B55 /* SourceSansPro-SemiBoldItalic.ttf */; };
		6A5B053520BE838F00E40BC0 /* Array+Ext.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A5B053420BE838F00E40BC0 /* Array+Ext.swift */; };
		6A661B4E208FF6E600CFC976 /* TimeListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A661B4D208FF6E600CFC976 /* TimeListView.swift */; };
		6A661B51208FF99F00CFC976 /* TimeListCollectionCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A661B50208FF99F00CFC976 /* TimeListCollectionCell.swift */; };
		6A661B54208FF9AA00CFC976 /* TimeListCollectionCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6A661B53208FF9AA00CFC976 /* TimeListCollectionCell.xib */; };
		6A661B5720913C5000CFC976 /* FilmChooseTimeViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A661B5620913C5000CFC976 /* FilmChooseTimeViewController.swift */; };
		6A6EE1C920BE242100B8FC6F /* CinemaFilmTimeTableCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A2B22C020924E9A00DA096B /* CinemaFilmTimeTableCell.swift */; };
		6A7C078820B3701A0092E553 /* CinemaPriceViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A7C078720B3701A0092E553 /* CinemaPriceViewController.swift */; };
		6A7C078B20B3730C0092E553 /* MemberCardCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A7C078A20B3730C0092E553 /* MemberCardCell.swift */; };
		6A812F0A2086BBEB0079ABF6 /* ConfirmPassViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A812F092086BBEB0079ABF6 /* ConfirmPassViewController.swift */; };
		6A812F0C2086BBF70079ABF6 /* AccountInfoViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A812F0B2086BBF70079ABF6 /* AccountInfoViewController.swift */; };
		6A812F1120870A480079ABF6 /* TransactionHistoryCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A812F1020870A480079ABF6 /* TransactionHistoryCell.swift */; };
		6A812F1420870F500079ABF6 /* TransactionDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A812F1320870F500079ABF6 /* TransactionDetailViewController.swift */; };
		6A812F172087C9280079ABF6 /* DashView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A812F162087C9280079ABF6 /* DashView.swift */; };
		6A812F1A20889E880079ABF6 /* AssetLibrary.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A812F1920889E880079ABF6 /* AssetLibrary.swift */; };
		6A812F1C20889EEE0079ABF6 /* ImagePickerController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A812F1B20889EEE0079ABF6 /* ImagePickerController.swift */; };
		6A812F202088A18C0079ABF6 /* Image+Ext.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A812F1F2088A18C0079ABF6 /* Image+Ext.swift */; };
		6A812F23208933160079ABF6 /* VoucherTableCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A812F22208933160079ABF6 /* VoucherTableCell.swift */; };
		6A812F26208939E60079ABF6 /* VoucherTableCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6A812F25208939E60079ABF6 /* VoucherTableCell.xib */; };
		6A912F162069806C003F98B3 /* Storyboard+Quick.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A912F152069806C003F98B3 /* Storyboard+Quick.swift */; };
		6A968EAC20B7EBC100A80BE2 /* YoutubeViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A968EAB20B7EBC100A80BE2 /* YoutubeViewController.swift */; };
		6A968EAE20B925A500A80BE2 /* SelectRegionViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A968EAD20B925A500A80BE2 /* SelectRegionViewController.swift */; };
		6A97191120A39962009FF4B7 /* VerticalLayoutButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A97191020A39962009FF4B7 /* VerticalLayoutButton.swift */; };
		6A9D123B2072D90C00D49B55 /* SSASideMenu.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A9D123A2072D90C00D49B55 /* SSASideMenu.swift */; };
		6A9D123F2072E3F700D49B55 /* ScrollPager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A9D123E2072E3F700D49B55 /* ScrollPager.swift */; };
		6A9D124520740CC800D49B55 /* AppDelegate+Appearance.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A9D124420740CC800D49B55 /* AppDelegate+Appearance.swift */; };
		6A9D125A207418B200D49B55 /* Oswald-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D1253207418B200D49B55 /* Oswald-Bold.ttf */; };
		6A9D125C207418B200D49B55 /* Oswald-SemiBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D1254207418B200D49B55 /* Oswald-SemiBold.ttf */; };
		6A9D125E207418B200D49B55 /* Oswald-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D1255207418B200D49B55 /* Oswald-Medium.ttf */; };
		6A9D1260207418B200D49B55 /* Oswald-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D1256207418B200D49B55 /* Oswald-Regular.ttf */; };
		6A9D1264207418B200D49B55 /* Oswald-ExtraLight.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D1258207418B200D49B55 /* Oswald-ExtraLight.ttf */; };
		6A9D1266207418B200D49B55 /* Oswald-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D1259207418B200D49B55 /* Oswald-Light.ttf */; };
		6A9D128320746A3700D49B55 /* SourceSansPro-BlackItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D127620746A3700D49B55 /* SourceSansPro-BlackItalic.ttf */; };
		6A9D128520746A3700D49B55 /* SourceSansPro-SemiBoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D127720746A3700D49B55 /* SourceSansPro-SemiBoldItalic.ttf */; };
		6A9D128720746A3700D49B55 /* SourceSansPro-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D127820746A3700D49B55 /* SourceSansPro-Regular.ttf */; };
		6A9D128920746A3700D49B55 /* SourceSansPro-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D127920746A3700D49B55 /* SourceSansPro-Bold.ttf */; };
		6A9D128B20746A3700D49B55 /* SourceSansPro-LightItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D127A20746A3700D49B55 /* SourceSansPro-LightItalic.ttf */; };
		6A9D128D20746A3700D49B55 /* SourceSansPro-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D127B20746A3700D49B55 /* SourceSansPro-Light.ttf */; };
		6A9D128F20746A3700D49B55 /* SourceSansPro-Black.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D127C20746A3700D49B55 /* SourceSansPro-Black.ttf */; };
		6A9D129120746A3700D49B55 /* OFL.txt in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D127D20746A3700D49B55 /* OFL.txt */; };
		6A9D129320746A3700D49B55 /* SourceSansPro-ExtraLight.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D127E20746A3700D49B55 /* SourceSansPro-ExtraLight.ttf */; };
		6A9D129520746A3700D49B55 /* SourceSansPro-BoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D127F20746A3700D49B55 /* SourceSansPro-BoldItalic.ttf */; };
		6A9D129720746A3700D49B55 /* SourceSansPro-SemiBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D128020746A3700D49B55 /* SourceSansPro-SemiBold.ttf */; };
		6A9D129920746A3700D49B55 /* SourceSansPro-ExtraLightItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D128120746A3700D49B55 /* SourceSansPro-ExtraLightItalic.ttf */; };
		6A9D129B20746A3700D49B55 /* SourceSansPro-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D128220746A3700D49B55 /* SourceSansPro-Italic.ttf */; };
		6AA13FE1208C901C007809AC /* PaymentPointViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AA13FE0208C901C007809AC /* PaymentPointViewController.swift */; };
		6AA13FE4208C97A3007809AC /* FilmTimeTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AA13FE2208C97A3007809AC /* FilmTimeTableViewCell.swift */; };
		6AA13FE6208C97A3007809AC /* FilmTimeTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6AA13FE3208C97A3007809AC /* FilmTimeTableViewCell.xib */; };
		6AA14021208F2FA1007809AC /* ListNewsViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AA14020208F2FA1007809AC /* ListNewsViewController.swift */; };
		6AA14024208F371B007809AC /* StickyHeaderViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AA14023208F371B007809AC /* StickyHeaderViewController.swift */; };
		6AA1AC852076CF220081188F /* TableViewHelper.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AA1AC842076CF220081188F /* TableViewHelper.swift */; };
		6AA299A9208BF2A20007E074 /* FilmItemTableCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AA299A7208BF2A20007E074 /* FilmItemTableCell.swift */; };
		6AA299AB208BF2A20007E074 /* FilmItemTableCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6AA299A8208BF2A20007E074 /* FilmItemTableCell.xib */; };
		6AA299AE208C04920007E074 /* FilmBookingViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AA299AD208C04920007E074 /* FilmBookingViewController.swift */; };
		6AA299B1208C42680007E074 /* CalendarHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AA299B0208C42680007E074 /* CalendarHeaderView.swift */; };
		6AA299B6208C43490007E074 /* CalendarHeaderViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AA299B4208C43490007E074 /* CalendarHeaderViewCell.swift */; };
		6AA299B8208C43490007E074 /* CalendarHeaderViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6AA299B5208C43490007E074 /* CalendarHeaderViewCell.xib */; };
		6AA959CF20B65DE000E6337A /* AvatarImageModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AA959CE20B65DE000E6337A /* AvatarImageModel.swift */; };
		6AC0703520FDA8430036CD1D /* InfoPlist.strings in Resources */ = {isa = PBXBuildFile; fileRef = 6AC0703820FDA8430036CD1D /* InfoPlist.strings */; };
		6AC0703620FDA8430036CD1D /* InfoPlist.strings in Resources */ = {isa = PBXBuildFile; fileRef = 6AC0703820FDA8430036CD1D /* InfoPlist.strings */; };
		6AC52E5320B54ED600D9BCAC /* ShowCinemaModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AC52E5220B54ED600D9BCAC /* ShowCinemaModel.swift */; };
		6AC52E5520B6064600D9BCAC /* RecruitmentViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AC52E5420B6064600D9BCAC /* RecruitmentViewController.swift */; };
		6AC8AC7520AF4C2800DE4F5B /* HCYoutubeParser.m in Sources */ = {isa = PBXBuildFile; fileRef = 6AC8AC7320AF4C2800DE4F5B /* HCYoutubeParser.m */; };
		6AC8AC7820AFDD2900DE4F5B /* LocationManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AC8AC7720AFDD2900DE4F5B /* LocationManager.swift */; };
		6AF06C6520A010A100A6459C /* ChooseSeatViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AF06C6420A010A100A6459C /* ChooseSeatViewController.swift */; };
		6AFD7003210FF59500E01D8A /* UpdatePasswordViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AFD7002210FF59500E01D8A /* UpdatePasswordViewController.swift */; };
		6FB8E11DF2B9AAA2A33D6AE6 /* Pods_Booking_test.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 24F82CE45BDF02E34BC848D2 /* Pods_Booking_test.framework */; };
		744255807C3E559178F7304F /* Pods_Booking_pro.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = E6223944CDAA907FCD2459C0 /* Pods_Booking_pro.framework */; };
		8439E1AD2CEB75F400D7A69D /* ConfirmVipZoomViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8439E1AC2CEB75F400D7A69D /* ConfirmVipZoomViewController.swift */; };
		8439E1AE2CEB75F400D7A69D /* ConfirmVipZoomViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8439E1AC2CEB75F400D7A69D /* ConfirmVipZoomViewController.swift */; };
		8439E1AF2CEB75F400D7A69D /* ConfirmVipZoomViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 8439E1AC2CEB75F400D7A69D /* ConfirmVipZoomViewController.swift */; };
		845BA7C62DFFDACA00AACEDC /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 845BA7C32DFFDACA00AACEDC /* <EMAIL> */; };
		845BA7C72DFFDACA00AACEDC /* Heart.png in Resources */ = {isa = PBXBuildFile; fileRef = 845BA7C22DFFDACA00AACEDC /* Heart.png */; };
		845BA7C82DFFDACA00AACEDC /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 845BA7C42DFFDACA00AACEDC /* <EMAIL> */; };
		845BA7C92DFFDACA00AACEDC /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 845BA7C32DFFDACA00AACEDC /* <EMAIL> */; };
		845BA7CA2DFFDACA00AACEDC /* Heart.png in Resources */ = {isa = PBXBuildFile; fileRef = 845BA7C22DFFDACA00AACEDC /* Heart.png */; };
		845BA7CB2DFFDACA00AACEDC /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 845BA7C42DFFDACA00AACEDC /* <EMAIL> */; };
		845BA7CC2DFFDACA00AACEDC /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 845BA7C32DFFDACA00AACEDC /* <EMAIL> */; };
		845BA7CD2DFFDACA00AACEDC /* Heart.png in Resources */ = {isa = PBXBuildFile; fileRef = 845BA7C22DFFDACA00AACEDC /* Heart.png */; };
		845BA7CE2DFFDACA00AACEDC /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 845BA7C42DFFDACA00AACEDC /* <EMAIL> */; };
		845BA7D02DFFDC1700AACEDC /* AppIconManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 845BA7CF2DFFDC1700AACEDC /* AppIconManager.swift */; };
		845BA7D12DFFDC1700AACEDC /* AppIconManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 845BA7CF2DFFDC1700AACEDC /* AppIconManager.swift */; };
		845BA7D22DFFDC1700AACEDC /* AppIconManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 845BA7CF2DFFDC1700AACEDC /* AppIconManager.swift */; };
		84DD28A82AE227AF000C5712 /* TransactionDetailCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 84DD28A72AE227AF000C5712 /* TransactionDetailCell.swift */; };
		84DD28A92AE227AF000C5712 /* TransactionDetailCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 84DD28A72AE227AF000C5712 /* TransactionDetailCell.swift */; };
		84DD28AA2AE227AF000C5712 /* TransactionDetailCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 84DD28A72AE227AF000C5712 /* TransactionDetailCell.swift */; };
		8CEE4F3C4F204DB134B3C545 /* Pods_Booking_dev.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 443BF9D058AE64C67C25B3BF /* Pods_Booking_dev.framework */; };
		CC1979AF28A3AA64006AD455 /* JSONMappable.swift in Sources */ = {isa = PBXBuildFile; fileRef = CC1979AE28A3AA64006AD455 /* JSONMappable.swift */; };
		CC1979B028A3AA64006AD455 /* JSONMappable.swift in Sources */ = {isa = PBXBuildFile; fileRef = CC1979AE28A3AA64006AD455 /* JSONMappable.swift */; };
		CC1979B128A3AA64006AD455 /* JSONMappable.swift in Sources */ = {isa = PBXBuildFile; fileRef = CC1979AE28A3AA64006AD455 /* JSONMappable.swift */; };
		CC1CF8E428B7661C00861409 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = CC1CF8E128B763A200861409 /* GoogleService-Info.plist */; };
		CC1CF8E528B7663200861409 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = CC1CF8E228B763A200861409 /* GoogleService-Info.plist */; };
		CC1CF8E628B7663700861409 /* GoogleService-Info.plist in Resources */ = {isa = PBXBuildFile; fileRef = CC1CF8E328B763A200861409 /* GoogleService-Info.plist */; };
		CC39C9A228AB75BE0015018A /* Tracking.swift in Sources */ = {isa = PBXBuildFile; fileRef = CC39C9A128AB75BE0015018A /* Tracking.swift */; };
		CC39C9A328AB75BE0015018A /* Tracking.swift in Sources */ = {isa = PBXBuildFile; fileRef = CC39C9A128AB75BE0015018A /* Tracking.swift */; };
		CC39C9A428AB75BE0015018A /* Tracking.swift in Sources */ = {isa = PBXBuildFile; fileRef = CC39C9A128AB75BE0015018A /* Tracking.swift */; };
		CC39C9A728ACED650015018A /* ComboListModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = CC39C9A628ACED650015018A /* ComboListModel.swift */; };
		CC39C9A828ACED650015018A /* ComboListModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = CC39C9A628ACED650015018A /* ComboListModel.swift */; };
		CC39C9A928ACED650015018A /* ComboListModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = CC39C9A628ACED650015018A /* ComboListModel.swift */; };
		CC39C9AB28ACEF220015018A /* DataBookingModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = CC39C9AA28ACEF220015018A /* DataBookingModel.swift */; };
		CC39C9AC28ACEF220015018A /* DataBookingModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = CC39C9AA28ACEF220015018A /* DataBookingModel.swift */; };
		CC39C9AD28ACEF220015018A /* DataBookingModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = CC39C9AA28ACEF220015018A /* DataBookingModel.swift */; };
		DE002F5A2074DC6A0001C63D /* Localizable.strings in Resources */ = {isa = PBXBuildFile; fileRef = DE002F5D2074DC6A0001C63D /* Localizable.strings */; };
		DE002F602074DD4C0001C63D /* String+Ext.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE002F5F2074DD4C0001C63D /* String+Ext.swift */; };
		DE019FB620B9585500A934B0 /* RegisterVoucherModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE019FB520B9585500A934B0 /* RegisterVoucherModel.swift */; };
		DE028936231A3FD200F24593 /* HomePage.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE028935231A3FD200F24593 /* HomePage.swift */; };
		DE028937231A3FD200F24593 /* HomePage.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE028935231A3FD200F24593 /* HomePage.swift */; };
		DE02893D231A419700F24593 /* CinemaFilmTimeTableCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A2B22C020924E9A00DA096B /* CinemaFilmTimeTableCell.swift */; };
		DE02893E231A419700F24593 /* TopicDetailModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE16D01720810693004D9722 /* TopicDetailModel.swift */; };
		DE02893F231A419700F24593 /* UpdatePasswordViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AFD7002210FF59500E01D8A /* UpdatePasswordViewController.swift */; };
		DE028940231A419700F24593 /* ConfirmPassViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A812F092086BBEB0079ABF6 /* ConfirmPassViewController.swift */; };
		DE028941231A419700F24593 /* YoutubeViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A968EAB20B7EBC100A80BE2 /* YoutubeViewController.swift */; };
		DE028942231A419700F24593 /* ShortUser.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFED17D2301D7B40006688A /* ShortUser.swift */; };
		DE028943231A419700F24593 /* GradientView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B853207C0D3800ED7F4C /* GradientView.swift */; };
		DE028944231A419700F24593 /* TicketBookingViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B74A207C0D1400ED7F4C /* TicketBookingViewController.swift */; };
		DE028945231A419700F24593 /* NewsAndDealsCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B87D207C2A6B00ED7F4C /* NewsAndDealsCell.swift */; };
		DE028946231A419700F24593 /* SeatCollectionHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A31AAFB20BB2AED00DC59B3 /* SeatCollectionHeaderView.swift */; };
		DE028947231A419700F24593 /* FreeVoucherViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFA4C8422FEC04C00725391 /* FreeVoucherViewController.swift */; };
		DE028948231A419700F24593 /* LanguageManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE16D0112080F1A5004D9722 /* LanguageManager.swift */; };
		DE028949231A419700F24593 /* MemberViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B76B207C0D1400ED7F4C /* MemberViewController.swift */; };
		DE02894A231A419700F24593 /* CinemaDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B751207C0D1400ED7F4C /* CinemaDetailViewController.swift */; };
		DE02894B231A419700F24593 /* LoginModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B781207C0D1500ED7F4C /* LoginModel.swift */; };
		DE02894C231A419700F24593 /* HCYoutubeParser.m in Sources */ = {isa = PBXBuildFile; fileRef = 6AC8AC7320AF4C2800DE4F5B /* HCYoutubeParser.m */; };
		DE02894D231A419700F24593 /* BaseNavigationViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B77D207C0D1500ED7F4C /* BaseNavigationViewController.swift */; };
		DE02894E231A419700F24593 /* Common+Ext.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* Common+Ext.swift */; };
		DE02894F231A419700F24593 /* AppDelegate+Initial.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE1C88E32053FD11003B5B9A /* AppDelegate+Initial.swift */; };
		DE028950231A419700F24593 /* FilmDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B74D207C0D1400ED7F4C /* FilmDetailViewController.swift */; };
		DE028951231A419700F24593 /* TableViewHelper.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AA1AC842076CF220081188F /* TableViewHelper.swift */; };
		DE028952231A419700F24593 /* Global.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B849207C0D3800ED7F4C /* Global.swift */; };
		DE028953231A419700F24593 /* ForgotPassViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B75F207C0D1400ED7F4C /* ForgotPassViewController.swift */; };
		DE028954231A419700F24593 /* NewsAndDealsViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B742207C0D1400ED7F4C /* NewsAndDealsViewController.swift */; };
		DE028955231A419700F24593 /* TopicModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE16D01420810652004D9722 /* TopicModel.swift */; };
		DE028956231A419700F24593 /* RegisterResultViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A31AAF920BA91E100DC59B3 /* RegisterResultViewController.swift */; };
		DE028957231A419700F24593 /* SeatBookingModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A34871A20C1499A0074F58F /* SeatBookingModel.swift */; };
		DE028958231A419700F24593 /* FreeVoucherTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFA4C8A22FEC10900725391 /* FreeVoucherTableViewCell.swift */; };
		DE028959231A419700F24593 /* LocalizableLabel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A4550BC2082520800AD3031 /* LocalizableLabel.swift */; };
		DE02895A231A419700F24593 /* FilmModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B785207C0D1500ED7F4C /* FilmModel.swift */; };
		DE02895B231A419700F24593 /* DDKCResult.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEBE7B1E207D09C1005A1F1D /* DDKCResult.swift */; };
		DE02895C231A419700F24593 /* NewsDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B73C207C0D1400ED7F4C /* NewsDetailViewController.swift */; };
		DE02895D231A419700F24593 /* PolicyModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DED214692083AA22003BBBA2 /* PolicyModel.swift */; };
		DE02895E231A419700F24593 /* ShowFilmModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B78C207C0D1500ED7F4C /* ShowFilmModel.swift */; };
		DE02895F231A419700F24593 /* FAQViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B89C207EC13300ED7F4C /* FAQViewController.swift */; };
		DE028960231A419700F24593 /* UseVoucherViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFA01F623040831003839C5 /* UseVoucherViewController.swift */; };
		DE028961231A419700F24593 /* TransactionDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A812F1320870F500079ABF6 /* TransactionDetailViewController.swift */; };
		DE028962231A419700F24593 /* Color.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B858207C0D3800ED7F4C /* Color.swift */; };
		DE028963231A419700F24593 /* NotificationModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEBE7B15207CC4F5005A1F1D /* NotificationModel.swift */; };
		DE028964231A419700F24593 /* PointHistory.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFA01F32303F197003839C5 /* PointHistory.swift */; };
		DE028965231A419700F24593 /* Fonts.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B84B207C0D3800ED7F4C /* Fonts.swift */; };
		DE028966231A419700F24593 /* ListPosterUrlModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B788207C0D1500ED7F4C /* ListPosterUrlModel.swift */; };
		DE028967231A419700F24593 /* CityAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* CityAPI.swift */; };
		DE028968231A419700F24593 /* FilmDescriptionTableCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B87A207C25B200ED7F4C /* FilmDescriptionTableCell.swift */; };
		DE028969231A419700F24593 /* PaymentPointViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AA13FE0208C901C007809AC /* PaymentPointViewController.swift */; };
		DE02896A231A419700F24593 /* AttributeString.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE0C143D22F7276600615CC3 /* AttributeString.swift */; };
		DE02896B231A419700F24593 /* FilmAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* FilmAPI.swift */; };
		DE02896C231A419700F24593 /* LocationManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AC8AC7720AFDD2900DE4F5B /* LocationManager.swift */; };
		DE02896D231A419700F24593 /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE1C887B20539BC3003B5B9A /* AppDelegate.swift */; };
		DE02896E231A419700F24593 /* NewHomeViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFF15DD22F86E9D0000D2C9 /* NewHomeViewController.swift */; };
		DE02896F231A419700F24593 /* MemberCardCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A7C078A20B3730C0092E553 /* MemberCardCell.swift */; };
		DE028970231A419700F24593 /* BaseRequestModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B780207C0D1500ED7F4C /* BaseRequestModel.swift */; };
		DE028971231A419700F24593 /* ListFilmWatchedViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B76C207C0D1400ED7F4C /* ListFilmWatchedViewController.swift */; };
		************************ /* NewsModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B791207C0D1500ED7F4C /* NewsModel.swift */; };
		************************ /* AreaCinemaHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B757207C0D1400ED7F4C /* AreaCinemaHeaderView.swift */; };
		************************ /* Repository.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B79B207C0D1500ED7F4C /* Repository.swift */; };
		************************ /* ShowCinemaModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AC52E5220B54ED600D9BCAC /* ShowCinemaModel.swift */; };
		************************ /* VoucherViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B768207C0D1400ED7F4C /* VoucherViewController.swift */; };
		************************ /* TransactionHistoryCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A812F1020870A480079ABF6 /* TransactionHistoryCell.swift */; };
		************************ /* AccountInfoViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A812F0B2086BBF70079ABF6 /* AccountInfoViewController.swift */; };
		************************ /* SlideMenuViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B770207C0D1400ED7F4C /* SlideMenuViewController.swift */; };
		************************ /* FilmChooseTimeViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A661B5620913C5000CFC976 /* FilmChooseTimeViewController.swift */; };
		************************ /* ListAllCinemasViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B750207C0D1400ED7F4C /* ListAllCinemasViewController.swift */; };
		************************ /* TransactionHistoryDetailModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE1B083A20C035A1000FFD1D /* TransactionHistoryDetailModel.swift */; };
		************************ /* OtherViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B779207C0D1400ED7F4C /* OtherViewController.swift */; };
		************************ /* ListFilmViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B74E207C0D1400ED7F4C /* ListFilmViewController.swift */; };
		************************ /* SettingTableCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B887207EA50F00ED7F4C /* SettingTableCell.swift */; };
		************************ /* CinemaTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B759207C0D1400ED7F4C /* CinemaTableViewCell.swift */; };
		************************ /* CheckboxTableCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B88D207EA54600ED7F4C /* CheckboxTableCell.swift */; };
		************************ /* GraphShitingViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B74B207C0D1400ED7F4C /* GraphShitingViewController.swift */; };
		************************ /* SelectRegionViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A968EAD20B925A500A80BE2 /* SelectRegionViewController.swift */; };
		************************ /* LoginViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B760207C0D1400ED7F4C /* LoginViewController.swift */; };
		************************ /* ListSeatModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A11D6CE20BA3F66009668B2 /* ListSeatModel.swift */; };
		DE028986231A419700F24593 /* Utils.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B857207C0D3800ED7F4C /* Utils.swift */; };
		DE028987231A419700F24593 /* ChooseCinemasViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B752207C0D1400ED7F4C /* ChooseCinemasViewController.swift */; };
		DE028988231A419700F24593 /* Storyboard+Quick.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A912F152069806C003F98B3 /* Storyboard+Quick.swift */; };
		DE028989231A419700F24593 /* Image+Ext.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A812F1F2088A18C0079ABF6 /* Image+Ext.swift */; };
		DE02898A231A419700F24593 /* CalendarHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AA299B0208C42680007E074 /* CalendarHeaderView.swift */; };
		DE02898B231A419700F24593 /* OthersCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFAB22A22F49C280019231D /* OthersCollectionViewCell.swift */; };
		DE02898C231A419700F24593 /* CinemaModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B786207C0D1500ED7F4C /* CinemaModel.swift */; };
		DE02898D231A419700F24593 /* ScanBarCodeViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFA6B042302C5CD00C0C156 /* ScanBarCodeViewController.swift */; };
		DE02898E231A419700F24593 /* CardGiftViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B76A207C0D1400ED7F4C /* CardGiftViewController.swift */; };
		DE02898F231A419700F24593 /* MenuItemCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B772207C0D1400ED7F4C /* MenuItemCell.swift */; };
		DE028990231A419700F24593 /* RecruitmentViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AC52E5420B6064600D9BCAC /* RecruitmentViewController.swift */; };
		DE028991231A419700F24593 /* String+Ext.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE002F5F2074DD4C0001C63D /* String+Ext.swift */; };
		DE028992231A419700F24593 /* ProfileViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B77A207C0D1400ED7F4C /* ProfileViewController.swift */; };
		DE028993231A419700F24593 /* FilmTimeTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AA13FE2208C97A3007809AC /* FilmTimeTableViewCell.swift */; };
		DE028994231A419700F24593 /* NewsTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE75FC17207F9A4C00604D7E /* NewsTableViewCell.swift */; };
		DE028995231A419700F24593 /* CinemaProvinceModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B79A207C0D1500ED7F4C /* CinemaProvinceModel.swift */; };
		DE028996231A419700F24593 /* Config.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B84A207C0D3800ED7F4C /* Config.swift */; };
		DE028997231A419700F24593 /* DonateVoucherTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEE1AACF22F5E1F40081FD3F /* DonateVoucherTableViewCell.swift */; };
		DE028998231A419700F24593 /* TimeListCollectionCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A661B50208FF99F00CFC976 /* TimeListCollectionCell.swift */; };
		DE028999231A419700F24593 /* PolicyContentModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DED2146C2083AA34003BBBA2 /* PolicyContentModel.swift */; };
		DE02899A231A419700F24593 /* AvatarImageModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AA959CE20B65DE000E6337A /* AvatarImageModel.swift */; };
		DE02899B231A419700F24593 /* IntroToFriendViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE0C143422F6757100615CC3 /* IntroToFriendViewController.swift */; };
		************************ /* RouteManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFEF51623055FBA00D107E8 /* RouteManager.swift */; };
		************************ /* DonateVoucherViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEE1AABD22F5DEEE0081FD3F /* DonateVoucherViewController.swift */; };
		************************ /* LocalizableButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A4550BF2082530200AD3031 /* LocalizableButton.swift */; };
		************************ /* Device.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B899207EADCB00ED7F4C /* Device.swift */; };
		************************ /* AddVoucherViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEE1AAB722F5DEDC0081FD3F /* AddVoucherViewController.swift */; };
		************************ /* SettingViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B776207C0D1400ED7F4C /* SettingViewController.swift */; };
		************************ /* AccountAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEB8F8A72075237A00715531 /* AccountAPI.swift */; };
		************************ /* MyVoucherViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEE1AAA722F5CFC80081FD3F /* MyVoucherViewController.swift */; };
		************************ /* ListFilmGenreModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B78A207C0D1500ED7F4C /* ListFilmGenreModel.swift */; };
		************************ /* BaseModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B79C207C0D1500ED7F4C /* BaseModel.swift */; };
		************************ /* GradientButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B852207C0D3800ED7F4C /* GradientButton.swift */; };
		************************ /* NearCinemaTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B754207C0D1400ED7F4C /* NearCinemaTableViewCell.swift */; };
		************************ /* MainTabContainer.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFAB21922F486680019231D /* MainTabContainer.swift */; };
		************************ /* Array+Ext.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A5B053420BE838F00E40BC0 /* Array+Ext.swift */; };
		************************ /* HistoryVoucherTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEE1AAC922F5E1E00081FD3F /* HistoryVoucherTableViewCell.swift */; };
		************************ /* StickyHeaderViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AA14023208F371B007809AC /* StickyHeaderViewController.swift */; };
		************************ /* SeatModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A11D6D220BA402A009668B2 /* SeatModel.swift */; };
		************************ /* TranferHistoryViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B76E207C0D1400ED7F4C /* TranferHistoryViewController.swift */; };
		************************ /* TicketType.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A11D6D420BA446E009668B2 /* TicketType.swift */; };
		************************ /* TabOtherViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFAB22322F48AB50019231D /* TabOtherViewController.swift */; };
		************************ /* Request.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEBDF17C22FC6CD30031A64D /* Request.swift */; };
		DE0289B1231A419700F24593 /* NotificationDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A41ED2820D1215F00BA16FA /* NotificationDetailViewController.swift */; };
		DE0289B2231A419700F24593 /* VoucherAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* VoucherAPI.swift */; };
		DE0289B3231A419700F24593 /* ChangePassViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B769207C0D1400ED7F4C /* ChangePassViewController.swift */; };
		DE0289B4231A419700F24593 /* CinemasViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFF6B67230A8C22009B04B2 /* CinemasViewController.swift */; };
		DE0289B5231A419700F24593 /* RoundTextField.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B850207C0D3800ED7F4C /* RoundTextField.swift */; };
		DE0289B6231A419700F24593 /* FAQDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE16D01D208109ED004D9722 /* FAQDetailViewController.swift */; };
		DE0289B7231A419700F24593 /* CinemaPriceViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A7C078720B3701A0092E553 /* CinemaPriceViewController.swift */; };
		DE0289B8231A419700F24593 /* BaseViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B77C207C0D1500ED7F4C /* BaseViewController.swift */; };
		DE0289B9231A419700F24593 /* VersionInfoViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B778207C0D1400ED7F4C /* VersionInfoViewController.swift */; };
		DE0289BA231A419700F24593 /* UIAlertController+Ext.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE26416F207A62DD00844F7F /* UIAlertController+Ext.swift */; };
		DE0289BB231A419700F24593 /* CardModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B787207C0D1500ED7F4C /* CardModel.swift */; };
		DE0289BC231A419700F24593 /* Date+Ext.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEBE7B18207CDF4D005A1F1D /* Date+Ext.swift */; };
		DE0289BD231A419700F24593 /* NewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B798207C0D1500ED7F4C /* NewModel.swift */; };
		DE0289BE231A419700F24593 /* RoundView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B84E207C0D3800ED7F4C /* RoundView.swift */; };
		DE0289BF231A419700F24593 /* AppDelegate+Appearance.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A9D124420740CC800D49B55 /* AppDelegate+Appearance.swift */; };
		DE0289C0231A419700F24593 /* UserModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B78B207C0D1500ED7F4C /* UserModel.swift */; };
		DE0289C1231A419700F24593 /* NotificationTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE16D02020810BCA004D9722 /* NotificationTableViewCell.swift */; };
		DE0289C2231A419700F24593 /* VourcherCouponViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B765207C0D1400ED7F4C /* VourcherCouponViewController.swift */; };
		DE0289C3231A419700F24593 /* SSASideMenu.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A9D123A2072D90C00D49B55 /* SSASideMenu.swift */; };
		DE0289C4231A419700F24593 /* DonatePointAlert.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE0C144722F72E6B00615CC3 /* DonatePointAlert.swift */; };
		DE0289C5231A419700F24593 /* EcmAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* EcmAPI.swift */; };
		DE0289C6231A419700F24593 /* FilmItemTableCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AA299A7208BF2A20007E074 /* FilmItemTableCell.swift */; };
		DE0289C7231A419700F24593 /* CustomizeAlert.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFA6AFE2302AF1700C0C156 /* CustomizeAlert.swift */; };
		DE0289C8231A419700F24593 /* DDKCResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE2164E72073C99B00938938 /* DDKCResponse.swift */; };
		DE0289C9231A419700F24593 /* PickerTextField.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B84D207C0D3800ED7F4C /* PickerTextField.swift */; };
		DE0289CA231A419700F24593 /* VoucherTableCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A812F22208933160079ABF6 /* VoucherTableCell.swift */; };
		DE0289CB231A419700F24593 /* SwitchTableCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B88A207EA51F00ED7F4C /* SwitchTableCell.swift */; };
		DE0289CC231A419700F24593 /* PaymentViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B75C207C0D1400ED7F4C /* PaymentViewController.swift */; };
		DE0289CD231A419700F24593 /* SeatCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A11D6CC20BA1567009668B2 /* SeatCollectionViewCell.swift */; };
		DE0289CE231A419700F24593 /* VoucherHistory.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFA01F02303F13D003839C5 /* VoucherHistory.swift */; };
		DE0289CF231A419700F24593 /* TimeListView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A661B4D208FF6E600CFC976 /* TimeListView.swift */; };
		DE0289D0231A419700F24593 /* TopViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A4550B020816C4A00AD3031 /* TopViewController.swift */; };
		DE0289D1231A419700F24593 /* ChooseSeatViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AF06C6420A010A100A6459C /* ChooseSeatViewController.swift */; };
		DE0289D2231A419700F24593 /* NetworkManager+Ecm.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEBE7B26207D13D8005A1F1D /* NetworkManager+Ecm.swift */; };
		DE0289D3231A419700F24593 /* CreateBookingModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A34871820C149390074F58F /* CreateBookingModel.swift */; };
		DE0289D4231A419700F24593 /* VoucherModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE154A7A2090EDC300A51889 /* VoucherModel.swift */; };
		DE0289D5231A419700F24593 /* NetworkManager+Film.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEBE7B23207D0C2C005A1F1D /* NetworkManager+Film.swift */; };
		DE0289D6231A419700F24593 /* DateTextField.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B855207C0D3800ED7F4C /* DateTextField.swift */; };
		DE0289D7231A419700F24593 /* FilmBookingViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AA299AD208C04920007E074 /* FilmBookingViewController.swift */; };
		DE0289D8231A419700F24593 /* VerticalLayoutButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A97191020A39962009FF4B7 /* VerticalLayoutButton.swift */; };
		DE0289D9231A419700F24593 /* RegisterModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B782207C0D1500ED7F4C /* RegisterModel.swift */; };
		DE0289DA231A419700F24593 /* View+Size.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A1F60142078EAB300212F7D /* View+Size.swift */; };
		DE0289DB231A419700F24593 /* TitleHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B758207C0D1400ED7F4C /* TitleHeaderView.swift */; };
		DE0289DC231A419700F24593 /* CinemaCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFF6B6E230A8E34009B04B2 /* CinemaCollectionViewCell.swift */; };
		DE0289DD231A419700F24593 /* Button.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE0C144D22F731C700615CC3 /* Button.swift */; };
		DE0289DE231A419700F24593 /* HomeCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFF15E322F8752D0000D2C9 /* HomeCollectionViewCell.swift */; };
		DE0289DF231A419700F24593 /* CinemaPriceHeaderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A44992C20B626BB006B37A3 /* CinemaPriceHeaderView.swift */; };
		DE0289E0231A419700F24593 /* TransparentAnimator.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A03F38B208106F0002FC2CD /* TransparentAnimator.swift */; };
		DE0289E1231A419700F24593 /* ScrollPager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A9D123E2072E3F700D49B55 /* ScrollPager.swift */; };
		DE0289E2231A419700F24593 /* RoundImageView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B854207C0D3800ED7F4C /* RoundImageView.swift */; };
		DE0289E3231A419700F24593 /* Images.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B856207C0D3800ED7F4C /* Images.swift */; };
		DE0289E4231A419700F24593 /* VoucherGot.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFA6B0A230313BB00C0C156 /* VoucherGot.swift */; };
		DE0289E5231A419700F24593 /* AppParams.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEBA9FDE230412BE00322EE4 /* AppParams.swift */; };
		DE0289E6231A419700F24593 /* App+Rx.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE1C88D42053E2D0003B5B9A /* App+Rx.swift */; };
		DE0289E7231A419700F24593 /* ScreenModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A11D6D020BA4021009668B2 /* ScreenModel.swift */; };
		DE0289E8231A419700F24593 /* AssetLibrary.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A812F1920889E880079ABF6 /* AssetLibrary.swift */; };
		DE0289E9231A419700F24593 /* NotificationViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B774207C0D1400ED7F4C /* NotificationViewController.swift */; };
		DE0289EA231A419700F24593 /* DashView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A812F162087C9280079ABF6 /* DashView.swift */; };
		DE0289EB231A419700F24593 /* Constants.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B848207C0D3800ED7F4C /* Constants.swift */; };
		DE0289EC231A419700F24593 /* TableView.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEE1AAB422F5D11B0081FD3F /* TableView.swift */; };
		DE0289ED231A419700F24593 /* HistoryVoucherViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEE1AAC322F5DF000081FD3F /* HistoryVoucherViewController.swift */; };
		DE0289EE231A419700F24593 /* Owner.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B79D207C0D1500ED7F4C /* Owner.swift */; };
		DE0289EF231A419700F24593 /* CinemaAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEA5BEA12073F636006D8DE1 /* CinemaAPI.swift */; };
		DE0289F0231A419700F24593 /* RewardPointsViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B764207C0D1400ED7F4C /* RewardPointsViewController.swift */; };
		DE0289F1231A419700F24593 /* ConfirmBookAgeViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A3FB3B620BF93390034FC3D /* ConfirmBookAgeViewController.swift */; };
		DE0289F2231A419700F24593 /* CinemaByProvinceTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFF6B74230A8E6A009B04B2 /* CinemaByProvinceTableViewCell.swift */; };
		DE0289F3231A419700F24593 /* NetworkManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEBE7B1B207D01EC005A1F1D /* NetworkManager.swift */; };
		DE0289F4231A419700F24593 /* RegisterVoucherModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE019FB520B9585500A934B0 /* RegisterVoucherModel.swift */; };
		DE0289F5231A419700F24593 /* ShowModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B799207C0D1500ED7F4C /* ShowModel.swift */; };
		DE0289F6231A419700F24593 /* ColumnLayout.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A11D6CA20B9D69A009668B2 /* ColumnLayout.swift */; };
		DE0289F7231A419700F24593 /* RoundButton.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B84F207C0D3800ED7F4C /* RoundButton.swift */; };
		DE0289F8231A419700F24593 /* FreeVoucher.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFED1792301B6AD0006688A /* FreeVoucher.swift */; };
		DE0289F9231A419700F24593 /* PointModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B784207C0D1500ED7F4C /* PointModel.swift */; };
		DE0289FA231A419700F24593 /* DashBorderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE0C143A22F6E81600615CC3 /* DashBorderView.swift */; };
		DE0289FB231A419700F24593 /* NotificationTableCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B773207C0D1400ED7F4C /* NotificationTableCell.swift */; };
		DE0289FC231A419700F24593 /* HistoryPointTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE0C144022F72B4E00615CC3 /* HistoryPointTableViewCell.swift */; };
		DE0289FD231A419700F24593 /* RegisterViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B762207C0D1400ED7F4C /* RegisterViewController.swift */; };
		DE0289FE231A419700F24593 /* Notification.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE4453B520F3B13B00835007 /* Notification.swift */; };
		DE0289FF231A419700F24593 /* ImagePickerController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A812F1B20889EEE0079ABF6 /* ImagePickerController.swift */; };
		DE028A00231A419700F24593 /* MemberCardViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B767207C0D1400ED7F4C /* MemberCardViewController.swift */; };
		DE028A01231A419700F24593 /* GradientImageView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A0A8B36208571BD00679340 /* GradientImageView.swift */; };
		DE028A02231A419700F24593 /* BannerCardView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B73F207C0D1400ED7F4C /* BannerCardView.swift */; };
		DE028A03231A419700F24593 /* Location+Ext.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE75FC1E207F9FC200604D7E /* Location+Ext.swift */; };
		DE028A04231A419700F24593 /* HomePage.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE028935231A3FD200F24593 /* HomePage.swift */; };
		DE028A05231A419700F24593 /* ListNewsViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AA14020208F2FA1007809AC /* ListNewsViewController.swift */; };
		DE028A06231A419700F24593 /* CouponViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B76D207C0D1400ED7F4C /* CouponViewController.swift */; };
		DE028A07231A419700F24593 /* TransactionHistoryModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE1CF99120BD9885001D89F2 /* TransactionHistoryModel.swift */; };
		DE028A08231A419700F24593 /* MyVoucherTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEE1AAAE22F5D09A0081FD3F /* MyVoucherTableViewCell.swift */; };
		DE028A09231A419700F24593 /* HomeViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B741207C0D1400ED7F4C /* HomeViewController.swift */; };
		DE028A0A231A419700F24593 /* UserDefault+Quick.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE1C88E02053FBA3003B5B9A /* UserDefault+Quick.swift */; };
		DE028A0B231A419700F24593 /* CardClassModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A41ECBB20CB83D100BA16FA /* CardClassModel.swift */; };
		DE028A0C231A419700F24593 /* UILabel+Localization.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE26416C207A56C700844F7F /* UILabel+Localization.swift */; };
		DE028A0D231A419700F24593 /* LocalizableProtocol.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A4550B92082517300AD3031 /* LocalizableProtocol.swift */; };
		DE028A0E231A419700F24593 /* InputTextField.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B851207C0D3800ED7F4C /* InputTextField.swift */; };
		DE028A0F231A419700F24593 /* CityModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B789207C0D1500ED7F4C /* CityModel.swift */; };
		DE028A10231A419700F24593 /* NewAndDealsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B79F207C0D1500ED7F4C /* NewAndDealsView.swift */; };
		DE028A11231A419700F24593 /* DateFormat.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A29B883207CC07900ED7F4C /* DateFormat.swift */; };
		DE028A12231A419700F24593 /* Banner.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFA4C7822FE6E6D00725391 /* Banner.swift */; };
		DE028A13231A419700F24593 /* CalendarHeaderViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6AA299B4208C43490007E074 /* CalendarHeaderViewCell.swift */; };
		DE028A14231A419700F24593 /* View+Layer.swift in Sources */ = {isa = PBXBuildFile; fileRef = 6A1F60122078E71A00212F7D /* View+Layer.swift */; };
		DE028A19231A419700F24593 /* OFL.txt in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D127D20746A3700D49B55 /* OFL.txt */; };
		DE028A1A231A419700F24593 /* Film.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B74C207C0D1400ED7F4C /* Film.storyboard */; };
		DE028A1B231A419700F24593 /* DonatePointAlert.xib in Resources */ = {isa = PBXBuildFile; fileRef = DE0C144822F72E6B00615CC3 /* DonatePointAlert.xib */; };
		DE028A1C231A419700F24593 /* TimeListCollectionCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6A661B53208FF9AA00CFC976 /* TimeListCollectionCell.xib */; };
		DE028A1D231A419700F24593 /* SourceSansPro-BoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D127F20746A3700D49B55 /* SourceSansPro-BoldItalic.ttf */; };
		DE028A1E231A419700F24593 /* DonateVoucherTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEE1AAD022F5E1F40081FD3F /* DonateVoucherTableViewCell.xib */; };
		DE028A1F231A419700F24593 /* SourceSansPro-BlackItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D127620746A3700D49B55 /* SourceSansPro-BlackItalic.ttf */; };
		DE028A20231A419700F24593 /* SourceSansPro-ExtraLightItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D128120746A3700D49B55 /* SourceSansPro-ExtraLightItalic.ttf */; };
		DE028A21231A419700F24593 /* UseVoucherViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEFA01F723040831003839C5 /* UseVoucherViewController.xib */; };
		DE028A22231A419700F24593 /* SourceSansPro-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D127820746A3700D49B55 /* SourceSansPro-Regular.ttf */; };
		DE028A23231A419700F24593 /* Oswald-ExtraLight.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D1258207418B200D49B55 /* Oswald-ExtraLight.ttf */; };
		DE028A24231A419700F24593 /* Oswald-SemiBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D1254207418B200D49B55 /* Oswald-SemiBold.ttf */; };
		DE028A25231A419700F24593 /* HistoryVoucherTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEE1AACA22F5E1E00081FD3F /* HistoryVoucherTableViewCell.xib */; };
		DE028A26231A419700F24593 /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = DE1C888B20539BC3003B5B9A /* LaunchScreen.storyboard */; };
		DE028A27231A419700F24593 /* HistoryVoucherViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEE1AAC422F5DF000081FD3F /* HistoryVoucherViewController.xib */; };
		DE028A28231A419700F24593 /* ScanBarCodeViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEFA6B052302C5CD00C0C156 /* ScanBarCodeViewController.xib */; };
		DE028A29231A419700F24593 /* SourceSansPro-ExtraLight.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D127E20746A3700D49B55 /* SourceSansPro-ExtraLight.ttf */; };
		DE028A2A231A419700F24593 /* VoucherTableCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6A812F25208939E60079ABF6 /* VoucherTableCell.xib */; };
		DE028A2B231A419700F24593 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = DE1C88F920540F9E003B5B9A /* <EMAIL> */; };
		DE028A2C231A419700F24593 /* SourceSansPro-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D127920746A3700D49B55 /* SourceSansPro-Bold.ttf */; };
		DE028A2D231A419700F24593 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = DE1C88EF20540F9E003B5B9A /* <EMAIL> */; };
		DE028A2E231A419700F24593 /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = DE1C888A20539BC3003B5B9A /* Assets.xcassets */; };
		DE028A2F231A419700F24593 /* CalendarHeaderViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6AA299B5208C43490007E074 /* CalendarHeaderViewCell.xib */; };
		DE028A30231A419700F24593 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = DE1C88FA20540F9E003B5B9A /* <EMAIL> */; };
		DE028A31231A419700F24593 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = DE1C88F320540F9E003B5B9A /* <EMAIL> */; };
		DE028A32231A419700F24593 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = DE1C88F820540F9E003B5B9A /* <EMAIL> */; };
		DE028A33231A419700F24593 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = DE1C88F520540F9E003B5B9A /* <EMAIL> */; };
		DE028A34231A419700F24593 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = DE1C88F420540F9E003B5B9A /* <EMAIL> */; };
		DE028A35231A419700F24593 /* Member.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B766207C0D1400ED7F4C /* Member.storyboard */; };
		DE028A36231A419700F24593 /* IntroToFriendViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = DE0C143522F6757100615CC3 /* IntroToFriendViewController.xib */; };
		DE028A37231A419700F24593 /* Oswald-Medium.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D1255207418B200D49B55 /* Oswald-Medium.ttf */; };
		DE028A38231A419700F24593 /* CheckboxTableCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B890207EA57E00ED7F4C /* CheckboxTableCell.xib */; };
		DE028A39231A419700F24593 /* CustomizeAlert.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEFA6AFF2302AF1700C0C156 /* CustomizeAlert.xib */; };
		DE028A3A231A419700F24593 /* OthersCollectionViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEFAB22B22F49C280019231D /* OthersCollectionViewCell.xib */; };
		DE028A3B231A419700F24593 /* HistoryPointTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = DE0C144122F72B4E00615CC3 /* HistoryPointTableViewCell.xib */; };
		DE028A3C231A419700F24593 /* CinemaCollectionViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEFF6B6F230A8E34009B04B2 /* CinemaCollectionViewCell.xib */; };
		DE028A3D231A419700F24593 /* NewsAndDealsCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B880207C2AB100ED7F4C /* NewsAndDealsCell.xib */; };
		DE028A3E231A419700F24593 /* CinemaFilmTimeTableCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = DE04A2BB2309A1250096ED85 /* CinemaFilmTimeTableCell.xib */; };
		DE028A3F231A419700F24593 /* SourceSansPro-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D127B20746A3700D49B55 /* SourceSansPro-Light.ttf */; };
		DE028A40231A419700F24593 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = DE1C88EE20540F9E003B5B9A /* <EMAIL> */; };
		DE028A41231A419700F24593 /* SourceSansPro-SemiBold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D128020746A3700D49B55 /* SourceSansPro-SemiBold.ttf */; };
		DE028A42231A419700F24593 /* Localizable.strings in Resources */ = {isa = PBXBuildFile; fileRef = DE002F5D2074DC6A0001C63D /* Localizable.strings */; };
		DE028A43231A419700F24593 /* CinemaByProvinceTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEFF6B75230A8E6A009B04B2 /* CinemaByProvinceTableViewCell.xib */; };
		DE028A44231A419700F24593 /* HomeCollectionViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEFF15E422F8752D0000D2C9 /* HomeCollectionViewCell.xib */; };
		DE028A45231A419700F24593 /* pageDot.png in Resources */ = {isa = PBXBuildFile; fileRef = DE1C88EC20540F9E003B5B9A /* pageDot.png */; };
		DE028A46231A419700F24593 /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = DE1C888D20539BC3003B5B9A /* Main.storyboard */; };
		DE028A47231A419700F24593 /* MyVoucherViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEE1AAA822F5CFC80081FD3F /* MyVoucherViewController.xib */; };
		DE028A48231A419700F24593 /* FilmTimeTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6AA13FE3208C97A3007809AC /* FilmTimeTableViewCell.xib */; };
		DE028A49231A419700F24593 /* InfoPlist.strings in Resources */ = {isa = PBXBuildFile; fileRef = 6AC0703820FDA8430036CD1D /* InfoPlist.strings */; };
		DE028A4A231A419700F24593 /* Oswald-Regular.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D1256207418B200D49B55 /* Oswald-Regular.ttf */; };
		DE028A4B231A419700F24593 /* NewAndDealsView.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B7A0207C0D1500ED7F4C /* NewAndDealsView.xib */; };
		DE028A4C231A419700F24593 /* SwitchTableCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B896207EA59D00ED7F4C /* SwitchTableCell.xib */; };
		DE028A4D231A419700F24593 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = DE1C88ED20540F9E003B5B9A /* <EMAIL> */; };
		DE028A4E231A419700F24593 /* DonateVoucherViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEE1AABE22F5DEEE0081FD3F /* DonateVoucherViewController.xib */; };
		DE028A4F231A419700F24593 /* Authen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B761207C0D1400ED7F4C /* Authen.storyboard */; };
		DE028A50231A419700F24593 /* Home.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B740207C0D1400ED7F4C /* Home.storyboard */; };
		DE028A51231A419700F24593 /* NewHomeViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEFF15DE22F86E9D0000D2C9 /* NewHomeViewController.xib */; };
		DE028A52231A419700F24593 /* SettingTableCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B893207EA58C00ED7F4C /* SettingTableCell.xib */; };
		DE028A53231A419700F24593 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = DE1C88F220540F9E003B5B9A /* <EMAIL> */; };
		DE028A54231A419700F24593 /* FreeVoucherViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEFA4C8522FEC04C00725391 /* FreeVoucherViewController.xib */; };
		DE028A55231A419700F24593 /* Cinema.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B75A207C0D1400ED7F4C /* Cinema.storyboard */; };
		DE028A56231A419700F24593 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = DE1C88F120540F9E003B5B9A /* <EMAIL> */; };
		DE028A57231A419700F24593 /* NotificationTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = DE16D02120810BCA004D9722 /* NotificationTableViewCell.xib */; };
		DE028A59231A419700F24593 /* Payment.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B75D207C0D1400ED7F4C /* Payment.storyboard */; };
		DE028A5A231A419700F24593 /* NewsTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B745207C0D1400ED7F4C /* NewsTableViewCell.xib */; };
		DE028A5B231A419700F24593 /* CinemasViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEFF6B68230A8C22009B04B2 /* CinemasViewController.xib */; };
		DE028A5C231A419700F24593 /* Setting.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B777207C0D1400ED7F4C /* Setting.storyboard */; };
		DE028A5D231A419700F24593 /* TitleHeaderView.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B756207C0D1400ED7F4C /* TitleHeaderView.xib */; };
		DE028A5E231A419700F24593 /* BannerCardView.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B73E207C0D1400ED7F4C /* BannerCardView.xib */; };
		DE028A5F231A419700F24593 /* SourceSansPro-Black.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D127C20746A3700D49B55 /* SourceSansPro-Black.ttf */; };
		DE028A60231A419700F24593 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = DE1C88F720540F9E003B5B9A /* <EMAIL> */; };
		DE028A61231A419700F24593 /* FilmItemTableCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6AA299A8208BF2A20007E074 /* FilmItemTableCell.xib */; };
		DE028A62231A419700F24593 /* Oswald-Light.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D1259207418B200D49B55 /* Oswald-Light.ttf */; };
		DE028A63231A419700F24593 /* TabOtherViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEFAB22422F48AB50019231D /* TabOtherViewController.xib */; };
		DE028A64231A419700F24593 /* SourceSansPro-LightItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D127A20746A3700D49B55 /* SourceSansPro-LightItalic.ttf */; };
		DE028A65231A419700F24593 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = DE1C88F620540F9E003B5B9A /* <EMAIL> */; };
		DE028A66231A419700F24593 /* AreaCinemaHeaderView.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6A29B755207C0D1400ED7F4C /* AreaCinemaHeaderView.xib */; };
		DE028A67231A419700F24593 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = DE1C88F020540F9E003B5B9A /* <EMAIL> */; };
		DE028A68231A419700F24593 /* Oswald-Bold.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D1253207418B200D49B55 /* Oswald-Bold.ttf */; };
		DE028A69231A419700F24593 /* SourceSansPro-Italic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D128220746A3700D49B55 /* SourceSansPro-Italic.ttf */; };
		DE028A6A231A419700F24593 /* MyVoucherTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEE1AAAF22F5D09A0081FD3F /* MyVoucherTableViewCell.xib */; };
		DE028A6B231A419700F24593 /* AddVoucherViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEE1AAB822F5DEDC0081FD3F /* AddVoucherViewController.xib */; };
		DE028A6C231A419700F24593 /* CinemaPriceHeaderView.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6A44992E20B626C8006B37A3 /* CinemaPriceHeaderView.xib */; };
		DE028A6D231A419700F24593 /* SeatCollectionHeaderView.xib in Resources */ = {isa = PBXBuildFile; fileRef = 6A31AAFC20BB2AED00DC59B3 /* SeatCollectionHeaderView.xib */; };
		DE028A6E231A419700F24593 /* FreeVoucherTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEFA4C8B22FEC10900725391 /* FreeVoucherTableViewCell.xib */; };
		DE028A6F231A419700F24593 /* SourceSansPro-SemiBoldItalic.ttf in Resources */ = {isa = PBXBuildFile; fileRef = 6A9D127720746A3700D49B55 /* SourceSansPro-SemiBoldItalic.ttf */; };
		DE04A2BA230998DA0096ED85 /* ShortUser.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFED17D2301D7B40006688A /* ShortUser.swift */; };
		DE04A2BC2309A1250096ED85 /* CinemaFilmTimeTableCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = DE04A2BB2309A1250096ED85 /* CinemaFilmTimeTableCell.xib */; };
		DE04A2BD2309A1250096ED85 /* CinemaFilmTimeTableCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = DE04A2BB2309A1250096ED85 /* CinemaFilmTimeTableCell.xib */; };
		DE084C49233910F700705A0F /* NotificationCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE084C47233910F700705A0F /* NotificationCell.swift */; };
		DE084C4A233910F700705A0F /* NotificationCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE084C47233910F700705A0F /* NotificationCell.swift */; };
		DE084C4B233910F700705A0F /* NotificationCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE084C47233910F700705A0F /* NotificationCell.swift */; };
		DE084C4C233910F700705A0F /* NotificationCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = DE084C48233910F700705A0F /* NotificationCell.xib */; };
		DE084C4D233910F700705A0F /* NotificationCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = DE084C48233910F700705A0F /* NotificationCell.xib */; };
		DE084C4E233910F700705A0F /* NotificationCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = DE084C48233910F700705A0F /* NotificationCell.xib */; };
		DE0B85512334BDFC009D6E32 /* MyTabbarViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE0B85502334BDFC009D6E32 /* MyTabbarViewController.swift */; };
		DE0B85522334BDFC009D6E32 /* MyTabbarViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE0B85502334BDFC009D6E32 /* MyTabbarViewController.swift */; };
		DE0B85532334BDFC009D6E32 /* MyTabbarViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE0B85502334BDFC009D6E32 /* MyTabbarViewController.swift */; };
		DE0B85552334BF34009D6E32 /* UIMaker.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE0B85542334BF34009D6E32 /* UIMaker.swift */; };
		DE0B85562334BF34009D6E32 /* UIMaker.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE0B85542334BF34009D6E32 /* UIMaker.swift */; };
		DE0B85572334BF34009D6E32 /* UIMaker.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE0B85542334BF34009D6E32 /* UIMaker.swift */; };
		DE0C143622F6757100615CC3 /* IntroToFriendViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE0C143422F6757100615CC3 /* IntroToFriendViewController.swift */; };
		DE0C143722F6757100615CC3 /* IntroToFriendViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE0C143422F6757100615CC3 /* IntroToFriendViewController.swift */; };
		DE0C143822F6757100615CC3 /* IntroToFriendViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = DE0C143522F6757100615CC3 /* IntroToFriendViewController.xib */; };
		DE0C143922F6757100615CC3 /* IntroToFriendViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = DE0C143522F6757100615CC3 /* IntroToFriendViewController.xib */; };
		DE0C143B22F6E81600615CC3 /* DashBorderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE0C143A22F6E81600615CC3 /* DashBorderView.swift */; };
		DE0C143C22F6E81600615CC3 /* DashBorderView.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE0C143A22F6E81600615CC3 /* DashBorderView.swift */; };
		DE0C143E22F7276600615CC3 /* AttributeString.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE0C143D22F7276600615CC3 /* AttributeString.swift */; };
		DE0C143F22F7276600615CC3 /* AttributeString.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE0C143D22F7276600615CC3 /* AttributeString.swift */; };
		DE0C144222F72B4E00615CC3 /* HistoryPointTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE0C144022F72B4E00615CC3 /* HistoryPointTableViewCell.swift */; };
		DE0C144322F72B4E00615CC3 /* HistoryPointTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE0C144022F72B4E00615CC3 /* HistoryPointTableViewCell.swift */; };
		DE0C144422F72B4E00615CC3 /* HistoryPointTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = DE0C144122F72B4E00615CC3 /* HistoryPointTableViewCell.xib */; };
		DE0C144522F72B4E00615CC3 /* HistoryPointTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = DE0C144122F72B4E00615CC3 /* HistoryPointTableViewCell.xib */; };
		DE0C144922F72E6B00615CC3 /* DonatePointAlert.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE0C144722F72E6B00615CC3 /* DonatePointAlert.swift */; };
		DE0C144A22F72E6B00615CC3 /* DonatePointAlert.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE0C144722F72E6B00615CC3 /* DonatePointAlert.swift */; };
		DE0C144B22F72E6B00615CC3 /* DonatePointAlert.xib in Resources */ = {isa = PBXBuildFile; fileRef = DE0C144822F72E6B00615CC3 /* DonatePointAlert.xib */; };
		DE0C144C22F72E6B00615CC3 /* DonatePointAlert.xib in Resources */ = {isa = PBXBuildFile; fileRef = DE0C144822F72E6B00615CC3 /* DonatePointAlert.xib */; };
		DE0C144E22F731C700615CC3 /* Button.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE0C144D22F731C700615CC3 /* Button.swift */; };
		DE0C144F22F731C700615CC3 /* Button.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE0C144D22F731C700615CC3 /* Button.swift */; };
		DE154A782090E9C000A51889 /* VoucherAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* VoucherAPI.swift */; };
		DE154A7B2090EDC300A51889 /* VoucherModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE154A7A2090EDC300A51889 /* VoucherModel.swift */; };
		DE16D0122080F1A5004D9722 /* LanguageManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE16D0112080F1A5004D9722 /* LanguageManager.swift */; };
		DE16D01520810652004D9722 /* TopicModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE16D01420810652004D9722 /* TopicModel.swift */; };
		DE16D01820810693004D9722 /* TopicDetailModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE16D01720810693004D9722 /* TopicDetailModel.swift */; };
		DE16D01E208109ED004D9722 /* FAQDetailViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE16D01D208109ED004D9722 /* FAQDetailViewController.swift */; };
		DE16D02220810BCA004D9722 /* NotificationTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE16D02020810BCA004D9722 /* NotificationTableViewCell.swift */; };
		DE16D02420810BCA004D9722 /* NotificationTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = DE16D02120810BCA004D9722 /* NotificationTableViewCell.xib */; };
		DE180CAF20767B180026A00D /* EcmAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* EcmAPI.swift */; };
		DE1B083B20C035A1000FFD1D /* TransactionHistoryDetailModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE1B083A20C035A1000FFD1D /* TransactionHistoryDetailModel.swift */; };
		DE1C888F20539BC3003B5B9A /* AppDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE1C887B20539BC3003B5B9A /* AppDelegate.swift */; };
		DE1C889620539BC3003B5B9A /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = DE1C888A20539BC3003B5B9A /* Assets.xcassets */; };
		DE1C889720539BC3003B5B9A /* LaunchScreen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = DE1C888B20539BC3003B5B9A /* LaunchScreen.storyboard */; };
		DE1C889820539BC3003B5B9A /* Main.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = DE1C888D20539BC3003B5B9A /* Main.storyboard */; };
		DE1C88D52053E2D0003B5B9A /* App+Rx.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE1C88D42053E2D0003B5B9A /* App+Rx.swift */; };
		DE1C88E12053FBA3003B5B9A /* UserDefault+Quick.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE1C88E02053FBA3003B5B9A /* UserDefault+Quick.swift */; };
		DE1C88E42053FD11003B5B9A /* AppDelegate+Initial.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE1C88E32053FD11003B5B9A /* AppDelegate+Initial.swift */; };
		DE1C88FB20540F9E003B5B9A /* pageDot.png in Resources */ = {isa = PBXBuildFile; fileRef = DE1C88EC20540F9E003B5B9A /* pageDot.png */; };
		DE1C88FD20540F9E003B5B9A /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = DE1C88ED20540F9E003B5B9A /* <EMAIL> */; };
		DE1C88FF20540F9E003B5B9A /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = DE1C88EE20540F9E003B5B9A /* <EMAIL> */; };
		DE1C890120540F9E003B5B9A /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = DE1C88EF20540F9E003B5B9A /* <EMAIL> */; };
		DE1C890320540F9E003B5B9A /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = DE1C88F020540F9E003B5B9A /* <EMAIL> */; };
		DE1C890520540F9E003B5B9A /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = DE1C88F120540F9E003B5B9A /* <EMAIL> */; };
		DE1C890720540F9E003B5B9A /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = DE1C88F220540F9E003B5B9A /* <EMAIL> */; };
		DE1C890920540F9E003B5B9A /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = DE1C88F320540F9E003B5B9A /* <EMAIL> */; };
		DE1C890B20540F9E003B5B9A /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = DE1C88F420540F9E003B5B9A /* <EMAIL> */; };
		DE1C890D20540F9E003B5B9A /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = DE1C88F520540F9E003B5B9A /* <EMAIL> */; };
		DE1C890F20540F9E003B5B9A /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = DE1C88F620540F9E003B5B9A /* <EMAIL> */; };
		DE1C891120540F9E003B5B9A /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = DE1C88F720540F9E003B5B9A /* <EMAIL> */; };
		DE1C891320540F9E003B5B9A /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = DE1C88F820540F9E003B5B9A /* <EMAIL> */; };
		DE1C891520540F9E003B5B9A /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = DE1C88F920540F9E003B5B9A /* <EMAIL> */; };
		DE1C891720540F9E003B5B9A /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = DE1C88FA20540F9E003B5B9A /* <EMAIL> */; };
		DE1CF99220BD9885001D89F2 /* TransactionHistoryModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE1CF99120BD9885001D89F2 /* TransactionHistoryModel.swift */; };
		DE2164E52073BBB600938938 /* FilmAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* FilmAPI.swift */; };
		DE2164E82073C99B00938938 /* DDKCResponse.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE2164E72073C99B00938938 /* DDKCResponse.swift */; };
		DE26416D207A56C700844F7F /* UILabel+Localization.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE26416C207A56C700844F7F /* UILabel+Localization.swift */; };
		DE264170207A62DD00844F7F /* UIAlertController+Ext.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE26416F207A62DD00844F7F /* UIAlertController+Ext.swift */; };
		DE4453B620F3B13B00835007 /* Notification.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE4453B520F3B13B00835007 /* Notification.swift */; };
		DE75FC18207F9A4D00604D7E /* NewsTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE75FC17207F9A4C00604D7E /* NewsTableViewCell.swift */; };
		DE75FC1F207F9FC200604D7E /* Location+Ext.swift in Sources */ = {isa = PBXBuildFile; fileRef = DE75FC1E207F9FC200604D7E /* Location+Ext.swift */; };
		************************ /* CityAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* CityAPI.swift */; };
		************************ /* Common+Ext.swift in Sources */ = {isa = PBXBuildFile; fileRef = ************************ /* Common+Ext.swift */; };
		DEA5BEA22073F636006D8DE1 /* CinemaAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEA5BEA12073F636006D8DE1 /* CinemaAPI.swift */; };
		DEB8F8A82075237A00715531 /* AccountAPI.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEB8F8A72075237A00715531 /* AccountAPI.swift */; };
		DEBA9FDF230412BE00322EE4 /* AppParams.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEBA9FDE230412BE00322EE4 /* AppParams.swift */; };
		DEBA9FE0230412BE00322EE4 /* AppParams.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEBA9FDE230412BE00322EE4 /* AppParams.swift */; };
		DEBDF17D22FC6CD40031A64D /* Request.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEBDF17C22FC6CD30031A64D /* Request.swift */; };
		DEBDF17E22FC6CD40031A64D /* Request.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEBDF17C22FC6CD30031A64D /* Request.swift */; };
		DEBE7B16207CC4F5005A1F1D /* NotificationModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEBE7B15207CC4F5005A1F1D /* NotificationModel.swift */; };
		DEBE7B19207CDF4D005A1F1D /* Date+Ext.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEBE7B18207CDF4D005A1F1D /* Date+Ext.swift */; };
		DEBE7B1C207D01EC005A1F1D /* NetworkManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEBE7B1B207D01EC005A1F1D /* NetworkManager.swift */; };
		DEBE7B1F207D09C1005A1F1D /* DDKCResult.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEBE7B1E207D09C1005A1F1D /* DDKCResult.swift */; };
		DEBE7B24207D0C2C005A1F1D /* NetworkManager+Film.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEBE7B23207D0C2C005A1F1D /* NetworkManager+Film.swift */; };
		DEBE7B27207D13D8005A1F1D /* NetworkManager+Ecm.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEBE7B26207D13D8005A1F1D /* NetworkManager+Ecm.swift */; };
		DED2146A2083AA22003BBBA2 /* PolicyModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DED214692083AA22003BBBA2 /* PolicyModel.swift */; };
		DED2146D2083AA34003BBBA2 /* PolicyContentModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = DED2146C2083AA34003BBBA2 /* PolicyContentModel.swift */; };
		DEE1AAA922F5CFC80081FD3F /* MyVoucherViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEE1AAA722F5CFC80081FD3F /* MyVoucherViewController.swift */; };
		DEE1AAAA22F5CFC80081FD3F /* MyVoucherViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEE1AAA722F5CFC80081FD3F /* MyVoucherViewController.swift */; };
		DEE1AAAB22F5CFC80081FD3F /* MyVoucherViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEE1AAA822F5CFC80081FD3F /* MyVoucherViewController.xib */; };
		DEE1AAAC22F5CFC80081FD3F /* MyVoucherViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEE1AAA822F5CFC80081FD3F /* MyVoucherViewController.xib */; };
		DEE1AAB022F5D09A0081FD3F /* MyVoucherTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEE1AAAE22F5D09A0081FD3F /* MyVoucherTableViewCell.swift */; };
		DEE1AAB122F5D09A0081FD3F /* MyVoucherTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEE1AAAE22F5D09A0081FD3F /* MyVoucherTableViewCell.swift */; };
		DEE1AAB222F5D09A0081FD3F /* MyVoucherTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEE1AAAF22F5D09A0081FD3F /* MyVoucherTableViewCell.xib */; };
		DEE1AAB322F5D09A0081FD3F /* MyVoucherTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEE1AAAF22F5D09A0081FD3F /* MyVoucherTableViewCell.xib */; };
		DEE1AAB522F5D11B0081FD3F /* TableView.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEE1AAB422F5D11B0081FD3F /* TableView.swift */; };
		DEE1AAB622F5D11B0081FD3F /* TableView.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEE1AAB422F5D11B0081FD3F /* TableView.swift */; };
		DEE1AAB922F5DEDC0081FD3F /* AddVoucherViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEE1AAB722F5DEDC0081FD3F /* AddVoucherViewController.swift */; };
		DEE1AABA22F5DEDC0081FD3F /* AddVoucherViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEE1AAB722F5DEDC0081FD3F /* AddVoucherViewController.swift */; };
		DEE1AABB22F5DEDC0081FD3F /* AddVoucherViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEE1AAB822F5DEDC0081FD3F /* AddVoucherViewController.xib */; };
		DEE1AABC22F5DEDC0081FD3F /* AddVoucherViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEE1AAB822F5DEDC0081FD3F /* AddVoucherViewController.xib */; };
		DEE1AABF22F5DEEE0081FD3F /* DonateVoucherViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEE1AABD22F5DEEE0081FD3F /* DonateVoucherViewController.swift */; };
		DEE1AAC022F5DEEE0081FD3F /* DonateVoucherViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEE1AABD22F5DEEE0081FD3F /* DonateVoucherViewController.swift */; };
		DEE1AAC122F5DEEE0081FD3F /* DonateVoucherViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEE1AABE22F5DEEE0081FD3F /* DonateVoucherViewController.xib */; };
		DEE1AAC222F5DEEE0081FD3F /* DonateVoucherViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEE1AABE22F5DEEE0081FD3F /* DonateVoucherViewController.xib */; };
		DEE1AAC522F5DF000081FD3F /* HistoryVoucherViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEE1AAC322F5DF000081FD3F /* HistoryVoucherViewController.swift */; };
		DEE1AAC622F5DF000081FD3F /* HistoryVoucherViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEE1AAC322F5DF000081FD3F /* HistoryVoucherViewController.swift */; };
		DEE1AAC722F5DF000081FD3F /* HistoryVoucherViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEE1AAC422F5DF000081FD3F /* HistoryVoucherViewController.xib */; };
		DEE1AAC822F5DF000081FD3F /* HistoryVoucherViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEE1AAC422F5DF000081FD3F /* HistoryVoucherViewController.xib */; };
		DEE1AACB22F5E1E00081FD3F /* HistoryVoucherTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEE1AAC922F5E1E00081FD3F /* HistoryVoucherTableViewCell.swift */; };
		DEE1AACC22F5E1E00081FD3F /* HistoryVoucherTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEE1AAC922F5E1E00081FD3F /* HistoryVoucherTableViewCell.swift */; };
		DEE1AACD22F5E1E00081FD3F /* HistoryVoucherTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEE1AACA22F5E1E00081FD3F /* HistoryVoucherTableViewCell.xib */; };
		DEE1AACE22F5E1E00081FD3F /* HistoryVoucherTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEE1AACA22F5E1E00081FD3F /* HistoryVoucherTableViewCell.xib */; };
		DEE1AAD122F5E1F40081FD3F /* DonateVoucherTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEE1AACF22F5E1F40081FD3F /* DonateVoucherTableViewCell.swift */; };
		DEE1AAD222F5E1F40081FD3F /* DonateVoucherTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEE1AACF22F5E1F40081FD3F /* DonateVoucherTableViewCell.swift */; };
		DEE1AAD322F5E1F40081FD3F /* DonateVoucherTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEE1AAD022F5E1F40081FD3F /* DonateVoucherTableViewCell.xib */; };
		DEE1AAD422F5E1F40081FD3F /* DonateVoucherTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEE1AAD022F5E1F40081FD3F /* DonateVoucherTableViewCell.xib */; };
		DEFA01F12303F13D003839C5 /* VoucherHistory.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFA01F02303F13D003839C5 /* VoucherHistory.swift */; };
		DEFA01F22303F13D003839C5 /* VoucherHistory.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFA01F02303F13D003839C5 /* VoucherHistory.swift */; };
		DEFA01F42303F197003839C5 /* PointHistory.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFA01F32303F197003839C5 /* PointHistory.swift */; };
		DEFA01F52303F197003839C5 /* PointHistory.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFA01F32303F197003839C5 /* PointHistory.swift */; };
		DEFA01F823040831003839C5 /* UseVoucherViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFA01F623040831003839C5 /* UseVoucherViewController.swift */; };
		DEFA01F923040831003839C5 /* UseVoucherViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFA01F623040831003839C5 /* UseVoucherViewController.swift */; };
		DEFA01FA23040831003839C5 /* UseVoucherViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEFA01F723040831003839C5 /* UseVoucherViewController.xib */; };
		DEFA01FB23040831003839C5 /* UseVoucherViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEFA01F723040831003839C5 /* UseVoucherViewController.xib */; };
		DEFA4C7922FE6E6D00725391 /* Banner.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFA4C7822FE6E6D00725391 /* Banner.swift */; };
		DEFA4C7A22FE6E6D00725391 /* Banner.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFA4C7822FE6E6D00725391 /* Banner.swift */; };
		DEFA4C8622FEC04C00725391 /* FreeVoucherViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFA4C8422FEC04C00725391 /* FreeVoucherViewController.swift */; };
		DEFA4C8722FEC04C00725391 /* FreeVoucherViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFA4C8422FEC04C00725391 /* FreeVoucherViewController.swift */; };
		DEFA4C8822FEC04C00725391 /* FreeVoucherViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEFA4C8522FEC04C00725391 /* FreeVoucherViewController.xib */; };
		DEFA4C8922FEC04C00725391 /* FreeVoucherViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEFA4C8522FEC04C00725391 /* FreeVoucherViewController.xib */; };
		DEFA4C8C22FEC10900725391 /* FreeVoucherTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFA4C8A22FEC10900725391 /* FreeVoucherTableViewCell.swift */; };
		DEFA4C8D22FEC10900725391 /* FreeVoucherTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFA4C8A22FEC10900725391 /* FreeVoucherTableViewCell.swift */; };
		DEFA4C8E22FEC10900725391 /* FreeVoucherTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEFA4C8B22FEC10900725391 /* FreeVoucherTableViewCell.xib */; };
		DEFA4C8F22FEC10900725391 /* FreeVoucherTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEFA4C8B22FEC10900725391 /* FreeVoucherTableViewCell.xib */; };
		DEFA6B002302AF1700C0C156 /* CustomizeAlert.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFA6AFE2302AF1700C0C156 /* CustomizeAlert.swift */; };
		DEFA6B012302AF1700C0C156 /* CustomizeAlert.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFA6AFE2302AF1700C0C156 /* CustomizeAlert.swift */; };
		DEFA6B022302AF1700C0C156 /* CustomizeAlert.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEFA6AFF2302AF1700C0C156 /* CustomizeAlert.xib */; };
		DEFA6B032302AF1700C0C156 /* CustomizeAlert.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEFA6AFF2302AF1700C0C156 /* CustomizeAlert.xib */; };
		DEFA6B062302C5CD00C0C156 /* ScanBarCodeViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFA6B042302C5CD00C0C156 /* ScanBarCodeViewController.swift */; };
		DEFA6B072302C5CD00C0C156 /* ScanBarCodeViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFA6B042302C5CD00C0C156 /* ScanBarCodeViewController.swift */; };
		DEFA6B082302C5CD00C0C156 /* ScanBarCodeViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEFA6B052302C5CD00C0C156 /* ScanBarCodeViewController.xib */; };
		DEFA6B092302C5CD00C0C156 /* ScanBarCodeViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEFA6B052302C5CD00C0C156 /* ScanBarCodeViewController.xib */; };
		DEFA6B0B230313BB00C0C156 /* VoucherGot.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFA6B0A230313BB00C0C156 /* VoucherGot.swift */; };
		DEFA6B0C230313BB00C0C156 /* VoucherGot.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFA6B0A230313BB00C0C156 /* VoucherGot.swift */; };
		DEFAB21A22F486680019231D /* MainTabContainer.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFAB21922F486680019231D /* MainTabContainer.swift */; };
		DEFAB21B22F4866B0019231D /* MainTabContainer.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFAB21922F486680019231D /* MainTabContainer.swift */; };
		DEFAB22522F48AB50019231D /* TabOtherViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFAB22322F48AB50019231D /* TabOtherViewController.swift */; };
		DEFAB22622F48AB50019231D /* TabOtherViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFAB22322F48AB50019231D /* TabOtherViewController.swift */; };
		DEFAB22722F48AB50019231D /* TabOtherViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEFAB22422F48AB50019231D /* TabOtherViewController.xib */; };
		DEFAB22822F48AB50019231D /* TabOtherViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEFAB22422F48AB50019231D /* TabOtherViewController.xib */; };
		DEFAB22C22F49C280019231D /* OthersCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFAB22A22F49C280019231D /* OthersCollectionViewCell.swift */; };
		DEFAB22D22F49C280019231D /* OthersCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFAB22A22F49C280019231D /* OthersCollectionViewCell.swift */; };
		DEFAB22E22F49C280019231D /* OthersCollectionViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEFAB22B22F49C280019231D /* OthersCollectionViewCell.xib */; };
		DEFAB22F22F49C280019231D /* OthersCollectionViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEFAB22B22F49C280019231D /* OthersCollectionViewCell.xib */; };
		DEFED17A2301B6AD0006688A /* FreeVoucher.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFED1792301B6AD0006688A /* FreeVoucher.swift */; };
		DEFED17B2301B6AD0006688A /* FreeVoucher.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFED1792301B6AD0006688A /* FreeVoucher.swift */; };
		DEFED17E2301D7B40006688A /* ShortUser.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFED17D2301D7B40006688A /* ShortUser.swift */; };
		DEFEF51723055FBA00D107E8 /* RouteManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFEF51623055FBA00D107E8 /* RouteManager.swift */; };
		DEFEF51823055FBA00D107E8 /* RouteManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFEF51623055FBA00D107E8 /* RouteManager.swift */; };
		DEFF15DF22F86E9D0000D2C9 /* NewHomeViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFF15DD22F86E9D0000D2C9 /* NewHomeViewController.swift */; };
		DEFF15E022F86E9D0000D2C9 /* NewHomeViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFF15DD22F86E9D0000D2C9 /* NewHomeViewController.swift */; };
		DEFF15E122F86E9D0000D2C9 /* NewHomeViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEFF15DE22F86E9D0000D2C9 /* NewHomeViewController.xib */; };
		DEFF15E222F86E9D0000D2C9 /* NewHomeViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEFF15DE22F86E9D0000D2C9 /* NewHomeViewController.xib */; };
		DEFF15E522F8752D0000D2C9 /* HomeCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFF15E322F8752D0000D2C9 /* HomeCollectionViewCell.swift */; };
		DEFF15E622F8752D0000D2C9 /* HomeCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFF15E322F8752D0000D2C9 /* HomeCollectionViewCell.swift */; };
		DEFF15E722F8752D0000D2C9 /* HomeCollectionViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEFF15E422F8752D0000D2C9 /* HomeCollectionViewCell.xib */; };
		DEFF15E822F8752D0000D2C9 /* HomeCollectionViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEFF15E422F8752D0000D2C9 /* HomeCollectionViewCell.xib */; };
		DEFF6B69230A8C22009B04B2 /* CinemasViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFF6B67230A8C22009B04B2 /* CinemasViewController.swift */; };
		DEFF6B6A230A8C22009B04B2 /* CinemasViewController.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFF6B67230A8C22009B04B2 /* CinemasViewController.swift */; };
		DEFF6B6B230A8C22009B04B2 /* CinemasViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEFF6B68230A8C22009B04B2 /* CinemasViewController.xib */; };
		DEFF6B6C230A8C22009B04B2 /* CinemasViewController.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEFF6B68230A8C22009B04B2 /* CinemasViewController.xib */; };
		DEFF6B70230A8E34009B04B2 /* CinemaCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFF6B6E230A8E34009B04B2 /* CinemaCollectionViewCell.swift */; };
		DEFF6B71230A8E34009B04B2 /* CinemaCollectionViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFF6B6E230A8E34009B04B2 /* CinemaCollectionViewCell.swift */; };
		DEFF6B72230A8E34009B04B2 /* CinemaCollectionViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEFF6B6F230A8E34009B04B2 /* CinemaCollectionViewCell.xib */; };
		DEFF6B73230A8E34009B04B2 /* CinemaCollectionViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEFF6B6F230A8E34009B04B2 /* CinemaCollectionViewCell.xib */; };
		DEFF6B76230A8E6A009B04B2 /* CinemaByProvinceTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFF6B74230A8E6A009B04B2 /* CinemaByProvinceTableViewCell.swift */; };
		DEFF6B77230A8E6A009B04B2 /* CinemaByProvinceTableViewCell.swift in Sources */ = {isa = PBXBuildFile; fileRef = DEFF6B74230A8E6A009B04B2 /* CinemaByProvinceTableViewCell.swift */; };
		DEFF6B78230A8E6A009B04B2 /* CinemaByProvinceTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEFF6B75230A8E6A009B04B2 /* CinemaByProvinceTableViewCell.xib */; };
		DEFF6B79230A8E6A009B04B2 /* CinemaByProvinceTableViewCell.xib in Resources */ = {isa = PBXBuildFile; fileRef = DEFF6B75230A8E6A009B04B2 /* CinemaByProvinceTableViewCell.xib */; };
/* End PBXBuildFile section */

/* Begin PBXCopyFilesBuildPhase section */
		6A48456D20F59C7100273D66 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		6AC52E4F20B4CBCC00D9BCAC /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
		DE028A73231A419700F24593 /* Embed Frameworks */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 10;
			files = (
			);
			name = "Embed Frameworks";
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		24F82CE45BDF02E34BC848D2 /* Pods_Booking_test.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_Booking_test.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		443BF9D058AE64C67C25B3BF /* Pods_Booking_dev.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_Booking_dev.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		6A03F38B208106F0002FC2CD /* TransparentAnimator.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TransparentAnimator.swift; sourceTree = "<group>"; };
		6A0A8B36208571BD00679340 /* GradientImageView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = GradientImageView.swift; sourceTree = "<group>"; };
		6A11D6CA20B9D69A009668B2 /* ColumnLayout.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ColumnLayout.swift; sourceTree = "<group>"; };
		6A11D6CC20BA1567009668B2 /* SeatCollectionViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SeatCollectionViewCell.swift; sourceTree = "<group>"; };
		6A11D6CE20BA3F66009668B2 /* ListSeatModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ListSeatModel.swift; sourceTree = "<group>"; };
		6A11D6D020BA4021009668B2 /* ScreenModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ScreenModel.swift; sourceTree = "<group>"; };
		6A11D6D220BA402A009668B2 /* SeatModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SeatModel.swift; sourceTree = "<group>"; };
		6A11D6D420BA446E009668B2 /* TicketType.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TicketType.swift; sourceTree = "<group>"; };
		6A1F60122078E71A00212F7D /* View+Layer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "View+Layer.swift"; sourceTree = "<group>"; };
		6A1F60142078EAB300212F7D /* View+Size.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "View+Size.swift"; sourceTree = "<group>"; };
		6A29B73C207C0D1400ED7F4C /* NewsDetailViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NewsDetailViewController.swift; sourceTree = "<group>"; };
		6A29B73E207C0D1400ED7F4C /* BannerCardView.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = BannerCardView.xib; sourceTree = "<group>"; };
		6A29B73F207C0D1400ED7F4C /* BannerCardView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BannerCardView.swift; sourceTree = "<group>"; };
		6A29B740207C0D1400ED7F4C /* Home.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; path = Home.storyboard; sourceTree = "<group>"; };
		6A29B741207C0D1400ED7F4C /* HomeViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = HomeViewController.swift; sourceTree = "<group>"; };
		6A29B742207C0D1400ED7F4C /* NewsAndDealsViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NewsAndDealsViewController.swift; sourceTree = "<group>"; };
		6A29B745207C0D1400ED7F4C /* NewsTableViewCell.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = NewsTableViewCell.xib; sourceTree = "<group>"; };
		6A29B74A207C0D1400ED7F4C /* TicketBookingViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = TicketBookingViewController.swift; sourceTree = "<group>"; };
		6A29B74B207C0D1400ED7F4C /* GraphShitingViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GraphShitingViewController.swift; sourceTree = "<group>"; };
		6A29B74C207C0D1400ED7F4C /* Film.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; path = Film.storyboard; sourceTree = "<group>"; };
		6A29B74D207C0D1400ED7F4C /* FilmDetailViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = FilmDetailViewController.swift; sourceTree = "<group>"; };
		6A29B74E207C0D1400ED7F4C /* ListFilmViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ListFilmViewController.swift; sourceTree = "<group>"; };
		6A29B750207C0D1400ED7F4C /* ListAllCinemasViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ListAllCinemasViewController.swift; sourceTree = "<group>"; };
		6A29B751207C0D1400ED7F4C /* CinemaDetailViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CinemaDetailViewController.swift; sourceTree = "<group>"; };
		6A29B752207C0D1400ED7F4C /* ChooseCinemasViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ChooseCinemasViewController.swift; sourceTree = "<group>"; };
		6A29B754207C0D1400ED7F4C /* NearCinemaTableViewCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NearCinemaTableViewCell.swift; sourceTree = "<group>"; };
		6A29B755207C0D1400ED7F4C /* AreaCinemaHeaderView.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = AreaCinemaHeaderView.xib; sourceTree = "<group>"; };
		6A29B756207C0D1400ED7F4C /* TitleHeaderView.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = TitleHeaderView.xib; sourceTree = "<group>"; };
		6A29B757207C0D1400ED7F4C /* AreaCinemaHeaderView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AreaCinemaHeaderView.swift; sourceTree = "<group>"; };
		6A29B758207C0D1400ED7F4C /* TitleHeaderView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = TitleHeaderView.swift; sourceTree = "<group>"; };
		6A29B759207C0D1400ED7F4C /* CinemaTableViewCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CinemaTableViewCell.swift; sourceTree = "<group>"; };
		6A29B75A207C0D1400ED7F4C /* Cinema.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; path = Cinema.storyboard; sourceTree = "<group>"; };
		6A29B75C207C0D1400ED7F4C /* PaymentViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PaymentViewController.swift; sourceTree = "<group>"; };
		6A29B75D207C0D1400ED7F4C /* Payment.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; path = Payment.storyboard; sourceTree = "<group>"; };
		6A29B75F207C0D1400ED7F4C /* ForgotPassViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ForgotPassViewController.swift; sourceTree = "<group>"; };
		6A29B760207C0D1400ED7F4C /* LoginViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LoginViewController.swift; sourceTree = "<group>"; };
		6A29B761207C0D1400ED7F4C /* Authen.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; path = Authen.storyboard; sourceTree = "<group>"; };
		6A29B762207C0D1400ED7F4C /* RegisterViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = RegisterViewController.swift; sourceTree = "<group>"; };
		6A29B764207C0D1400ED7F4C /* RewardPointsViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = RewardPointsViewController.swift; sourceTree = "<group>"; };
		6A29B765207C0D1400ED7F4C /* VourcherCouponViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = VourcherCouponViewController.swift; sourceTree = "<group>"; };
		6A29B766207C0D1400ED7F4C /* Member.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; path = Member.storyboard; sourceTree = "<group>"; };
		6A29B767207C0D1400ED7F4C /* MemberCardViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MemberCardViewController.swift; sourceTree = "<group>"; };
		6A29B768207C0D1400ED7F4C /* VoucherViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = VoucherViewController.swift; sourceTree = "<group>"; };
		6A29B769207C0D1400ED7F4C /* ChangePassViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ChangePassViewController.swift; sourceTree = "<group>"; };
		6A29B76A207C0D1400ED7F4C /* CardGiftViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CardGiftViewController.swift; sourceTree = "<group>"; };
		6A29B76B207C0D1400ED7F4C /* MemberViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MemberViewController.swift; sourceTree = "<group>"; };
		6A29B76C207C0D1400ED7F4C /* ListFilmWatchedViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ListFilmWatchedViewController.swift; sourceTree = "<group>"; };
		6A29B76D207C0D1400ED7F4C /* CouponViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CouponViewController.swift; sourceTree = "<group>"; };
		6A29B76E207C0D1400ED7F4C /* TranferHistoryViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = TranferHistoryViewController.swift; sourceTree = "<group>"; };
		6A29B770207C0D1400ED7F4C /* SlideMenuViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SlideMenuViewController.swift; sourceTree = "<group>"; };
		6A29B772207C0D1400ED7F4C /* MenuItemCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = MenuItemCell.swift; sourceTree = "<group>"; };
		6A29B773207C0D1400ED7F4C /* NotificationTableCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NotificationTableCell.swift; sourceTree = "<group>"; };
		6A29B774207C0D1400ED7F4C /* NotificationViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NotificationViewController.swift; sourceTree = "<group>"; };
		6A29B776207C0D1400ED7F4C /* SettingViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SettingViewController.swift; sourceTree = "<group>"; };
		6A29B777207C0D1400ED7F4C /* Setting.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; path = Setting.storyboard; sourceTree = "<group>"; };
		6A29B778207C0D1400ED7F4C /* VersionInfoViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = VersionInfoViewController.swift; sourceTree = "<group>"; };
		6A29B779207C0D1400ED7F4C /* OtherViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = OtherViewController.swift; sourceTree = "<group>"; };
		6A29B77A207C0D1400ED7F4C /* ProfileViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ProfileViewController.swift; sourceTree = "<group>"; };
		6A29B77C207C0D1500ED7F4C /* BaseViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BaseViewController.swift; sourceTree = "<group>"; };
		6A29B77D207C0D1500ED7F4C /* BaseNavigationViewController.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BaseNavigationViewController.swift; sourceTree = "<group>"; };
		6A29B780207C0D1500ED7F4C /* BaseRequestModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BaseRequestModel.swift; sourceTree = "<group>"; };
		6A29B781207C0D1500ED7F4C /* LoginModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = LoginModel.swift; sourceTree = "<group>"; };
		6A29B782207C0D1500ED7F4C /* RegisterModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = RegisterModel.swift; sourceTree = "<group>"; };
		6A29B784207C0D1500ED7F4C /* PointModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PointModel.swift; sourceTree = "<group>"; };
		6A29B785207C0D1500ED7F4C /* FilmModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = FilmModel.swift; sourceTree = "<group>"; };
		6A29B786207C0D1500ED7F4C /* CinemaModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CinemaModel.swift; sourceTree = "<group>"; };
		6A29B787207C0D1500ED7F4C /* CardModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CardModel.swift; sourceTree = "<group>"; };
		6A29B788207C0D1500ED7F4C /* ListPosterUrlModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ListPosterUrlModel.swift; sourceTree = "<group>"; };
		6A29B789207C0D1500ED7F4C /* CityModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CityModel.swift; sourceTree = "<group>"; };
		6A29B78A207C0D1500ED7F4C /* ListFilmGenreModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ListFilmGenreModel.swift; sourceTree = "<group>"; };
		6A29B78B207C0D1500ED7F4C /* UserModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = UserModel.swift; sourceTree = "<group>"; };
		6A29B78C207C0D1500ED7F4C /* ShowFilmModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ShowFilmModel.swift; sourceTree = "<group>"; };
		6A29B791207C0D1500ED7F4C /* NewsModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NewsModel.swift; sourceTree = "<group>"; };
		6A29B798207C0D1500ED7F4C /* NewModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NewModel.swift; sourceTree = "<group>"; };
		6A29B799207C0D1500ED7F4C /* ShowModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ShowModel.swift; sourceTree = "<group>"; };
		6A29B79A207C0D1500ED7F4C /* CinemaProvinceModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = CinemaProvinceModel.swift; sourceTree = "<group>"; };
		6A29B79B207C0D1500ED7F4C /* Repository.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Repository.swift; sourceTree = "<group>"; };
		6A29B79C207C0D1500ED7F4C /* BaseModel.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = BaseModel.swift; sourceTree = "<group>"; };
		6A29B79D207C0D1500ED7F4C /* Owner.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Owner.swift; sourceTree = "<group>"; };
		6A29B79F207C0D1500ED7F4C /* NewAndDealsView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NewAndDealsView.swift; sourceTree = "<group>"; };
		6A29B7A0207C0D1500ED7F4C /* NewAndDealsView.xib */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.xib; path = NewAndDealsView.xib; sourceTree = "<group>"; };
		6A29B848207C0D3800ED7F4C /* Constants.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Constants.swift; sourceTree = "<group>"; };
		6A29B849207C0D3800ED7F4C /* Global.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Global.swift; sourceTree = "<group>"; };
		6A29B84A207C0D3800ED7F4C /* Config.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Config.swift; sourceTree = "<group>"; };
		6A29B84B207C0D3800ED7F4C /* Fonts.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Fonts.swift; sourceTree = "<group>"; };
		6A29B84D207C0D3800ED7F4C /* PickerTextField.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = PickerTextField.swift; sourceTree = "<group>"; };
		6A29B84E207C0D3800ED7F4C /* RoundView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = RoundView.swift; sourceTree = "<group>"; };
		6A29B84F207C0D3800ED7F4C /* RoundButton.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = RoundButton.swift; sourceTree = "<group>"; };
		6A29B850207C0D3800ED7F4C /* RoundTextField.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = RoundTextField.swift; sourceTree = "<group>"; };
		6A29B851207C0D3800ED7F4C /* InputTextField.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = InputTextField.swift; sourceTree = "<group>"; };
		6A29B852207C0D3800ED7F4C /* GradientButton.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GradientButton.swift; sourceTree = "<group>"; };
		6A29B853207C0D3800ED7F4C /* GradientView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = GradientView.swift; sourceTree = "<group>"; };
		6A29B854207C0D3800ED7F4C /* RoundImageView.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = RoundImageView.swift; sourceTree = "<group>"; };
		6A29B855207C0D3800ED7F4C /* DateTextField.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = DateTextField.swift; sourceTree = "<group>"; };
		6A29B856207C0D3800ED7F4C /* Images.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Images.swift; sourceTree = "<group>"; };
		6A29B857207C0D3800ED7F4C /* Utils.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Utils.swift; sourceTree = "<group>"; };
		6A29B858207C0D3800ED7F4C /* Color.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Color.swift; sourceTree = "<group>"; };
		6A29B87A207C25B200ED7F4C /* FilmDescriptionTableCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FilmDescriptionTableCell.swift; sourceTree = "<group>"; };
		6A29B87D207C2A6B00ED7F4C /* NewsAndDealsCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NewsAndDealsCell.swift; sourceTree = "<group>"; };
		6A29B880207C2AB100ED7F4C /* NewsAndDealsCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = NewsAndDealsCell.xib; sourceTree = "<group>"; };
		6A29B883207CC07900ED7F4C /* DateFormat.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DateFormat.swift; sourceTree = "<group>"; };
		6A29B887207EA50F00ED7F4C /* SettingTableCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SettingTableCell.swift; sourceTree = "<group>"; };
		6A29B88A207EA51F00ED7F4C /* SwitchTableCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SwitchTableCell.swift; sourceTree = "<group>"; };
		6A29B88D207EA54600ED7F4C /* CheckboxTableCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CheckboxTableCell.swift; sourceTree = "<group>"; };
		6A29B890207EA57E00ED7F4C /* CheckboxTableCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = CheckboxTableCell.xib; sourceTree = "<group>"; };
		6A29B893207EA58C00ED7F4C /* SettingTableCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = SettingTableCell.xib; sourceTree = "<group>"; };
		6A29B896207EA59D00ED7F4C /* SwitchTableCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = SwitchTableCell.xib; sourceTree = "<group>"; };
		6A29B899207EADCB00ED7F4C /* Device.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Device.swift; sourceTree = "<group>"; };
		6A29B89C207EC13300ED7F4C /* FAQViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FAQViewController.swift; sourceTree = "<group>"; };
		6A2B22C020924E9A00DA096B /* CinemaFilmTimeTableCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CinemaFilmTimeTableCell.swift; sourceTree = "<group>"; };
		6A31AAF920BA91E100DC59B3 /* RegisterResultViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RegisterResultViewController.swift; sourceTree = "<group>"; };
		6A31AAFB20BB2AED00DC59B3 /* SeatCollectionHeaderView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SeatCollectionHeaderView.swift; sourceTree = "<group>"; };
		6A31AAFC20BB2AED00DC59B3 /* SeatCollectionHeaderView.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = SeatCollectionHeaderView.xib; sourceTree = "<group>"; };
		6A34871820C149390074F58F /* CreateBookingModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CreateBookingModel.swift; sourceTree = "<group>"; };
		6A34871A20C1499A0074F58F /* SeatBookingModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SeatBookingModel.swift; sourceTree = "<group>"; };
		6A3FB3B620BF93390034FC3D /* ConfirmBookAgeViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ConfirmBookAgeViewController.swift; sourceTree = "<group>"; };
		6A41ECBB20CB83D100BA16FA /* CardClassModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CardClassModel.swift; sourceTree = "<group>"; };
		6A41ED2820D1215F00BA16FA /* NotificationDetailViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NotificationDetailViewController.swift; sourceTree = "<group>"; };
		6A44992C20B626BB006B37A3 /* CinemaPriceHeaderView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CinemaPriceHeaderView.swift; sourceTree = "<group>"; };
		6A44992E20B626C8006B37A3 /* CinemaPriceHeaderView.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = CinemaPriceHeaderView.xib; sourceTree = "<group>"; };
		6A4550B020816C4A00AD3031 /* TopViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TopViewController.swift; sourceTree = "<group>"; };
		6A4550B92082517300AD3031 /* LocalizableProtocol.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LocalizableProtocol.swift; sourceTree = "<group>"; };
		6A4550BC2082520800AD3031 /* LocalizableLabel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LocalizableLabel.swift; sourceTree = "<group>"; };
		6A4550BF2082530200AD3031 /* LocalizableButton.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LocalizableButton.swift; sourceTree = "<group>"; };
		6A48457220F59C7100273D66 /* Beta Cinemas.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "Beta Cinemas.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		6A5B053420BE838F00E40BC0 /* Array+Ext.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Array+Ext.swift"; sourceTree = "<group>"; };
		6A661B4D208FF6E600CFC976 /* TimeListView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TimeListView.swift; sourceTree = "<group>"; };
		6A661B50208FF99F00CFC976 /* TimeListCollectionCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TimeListCollectionCell.swift; sourceTree = "<group>"; };
		6A661B53208FF9AA00CFC976 /* TimeListCollectionCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = TimeListCollectionCell.xib; sourceTree = "<group>"; };
		6A661B5620913C5000CFC976 /* FilmChooseTimeViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FilmChooseTimeViewController.swift; sourceTree = "<group>"; };
		6A7C078720B3701A0092E553 /* CinemaPriceViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CinemaPriceViewController.swift; sourceTree = "<group>"; };
		6A7C078A20B3730C0092E553 /* MemberCardCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MemberCardCell.swift; sourceTree = "<group>"; };
		6A812F092086BBEB0079ABF6 /* ConfirmPassViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ConfirmPassViewController.swift; sourceTree = "<group>"; };
		6A812F0B2086BBF70079ABF6 /* AccountInfoViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AccountInfoViewController.swift; sourceTree = "<group>"; };
		6A812F1020870A480079ABF6 /* TransactionHistoryCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TransactionHistoryCell.swift; sourceTree = "<group>"; };
		6A812F1320870F500079ABF6 /* TransactionDetailViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TransactionDetailViewController.swift; sourceTree = "<group>"; };
		6A812F162087C9280079ABF6 /* DashView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DashView.swift; sourceTree = "<group>"; };
		6A812F1920889E880079ABF6 /* AssetLibrary.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AssetLibrary.swift; sourceTree = "<group>"; };
		6A812F1B20889EEE0079ABF6 /* ImagePickerController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ImagePickerController.swift; sourceTree = "<group>"; };
		6A812F1F2088A18C0079ABF6 /* Image+Ext.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Image+Ext.swift"; sourceTree = "<group>"; };
		6A812F22208933160079ABF6 /* VoucherTableCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VoucherTableCell.swift; sourceTree = "<group>"; };
		6A812F25208939E60079ABF6 /* VoucherTableCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = VoucherTableCell.xib; sourceTree = "<group>"; };
		6A8945C120AA235C0021C8EB /* Booking-dev.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; path = "Booking-dev.entitlements"; sourceTree = "<group>"; };
		6A912F152069806C003F98B3 /* Storyboard+Quick.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Storyboard+Quick.swift"; sourceTree = "<group>"; };
		6A968EAB20B7EBC100A80BE2 /* YoutubeViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = YoutubeViewController.swift; sourceTree = "<group>"; };
		6A968EAD20B925A500A80BE2 /* SelectRegionViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SelectRegionViewController.swift; sourceTree = "<group>"; };
		6A97191020A39962009FF4B7 /* VerticalLayoutButton.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VerticalLayoutButton.swift; sourceTree = "<group>"; };
		6A9D123A2072D90C00D49B55 /* SSASideMenu.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = SSASideMenu.swift; sourceTree = "<group>"; };
		6A9D123E2072E3F700D49B55 /* ScrollPager.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = ScrollPager.swift; sourceTree = "<group>"; };
		6A9D124420740CC800D49B55 /* AppDelegate+Appearance.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "AppDelegate+Appearance.swift"; sourceTree = "<group>"; };
		6A9D1253207418B200D49B55 /* Oswald-Bold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Oswald-Bold.ttf"; sourceTree = "<group>"; };
		6A9D1254207418B200D49B55 /* Oswald-SemiBold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Oswald-SemiBold.ttf"; sourceTree = "<group>"; };
		6A9D1255207418B200D49B55 /* Oswald-Medium.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Oswald-Medium.ttf"; sourceTree = "<group>"; };
		6A9D1256207418B200D49B55 /* Oswald-Regular.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Oswald-Regular.ttf"; sourceTree = "<group>"; };
		6A9D1257207418B200D49B55 /* OFL.txt */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = OFL.txt; sourceTree = "<group>"; };
		6A9D1258207418B200D49B55 /* Oswald-ExtraLight.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Oswald-ExtraLight.ttf"; sourceTree = "<group>"; };
		6A9D1259207418B200D49B55 /* Oswald-Light.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "Oswald-Light.ttf"; sourceTree = "<group>"; };
		6A9D127620746A3700D49B55 /* SourceSansPro-BlackItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "SourceSansPro-BlackItalic.ttf"; sourceTree = "<group>"; };
		6A9D127720746A3700D49B55 /* SourceSansPro-SemiBoldItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "SourceSansPro-SemiBoldItalic.ttf"; sourceTree = "<group>"; };
		6A9D127820746A3700D49B55 /* SourceSansPro-Regular.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "SourceSansPro-Regular.ttf"; sourceTree = "<group>"; };
		6A9D127920746A3700D49B55 /* SourceSansPro-Bold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "SourceSansPro-Bold.ttf"; sourceTree = "<group>"; };
		6A9D127A20746A3700D49B55 /* SourceSansPro-LightItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "SourceSansPro-LightItalic.ttf"; sourceTree = "<group>"; };
		6A9D127B20746A3700D49B55 /* SourceSansPro-Light.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "SourceSansPro-Light.ttf"; sourceTree = "<group>"; };
		6A9D127C20746A3700D49B55 /* SourceSansPro-Black.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "SourceSansPro-Black.ttf"; sourceTree = "<group>"; };
		6A9D127D20746A3700D49B55 /* OFL.txt */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text; path = OFL.txt; sourceTree = "<group>"; };
		6A9D127E20746A3700D49B55 /* SourceSansPro-ExtraLight.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "SourceSansPro-ExtraLight.ttf"; sourceTree = "<group>"; };
		6A9D127F20746A3700D49B55 /* SourceSansPro-BoldItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "SourceSansPro-BoldItalic.ttf"; sourceTree = "<group>"; };
		6A9D128020746A3700D49B55 /* SourceSansPro-SemiBold.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "SourceSansPro-SemiBold.ttf"; sourceTree = "<group>"; };
		6A9D128120746A3700D49B55 /* SourceSansPro-ExtraLightItalic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "SourceSansPro-ExtraLightItalic.ttf"; sourceTree = "<group>"; };
		6A9D128220746A3700D49B55 /* SourceSansPro-Italic.ttf */ = {isa = PBXFileReference; lastKnownFileType = file; path = "SourceSansPro-Italic.ttf"; sourceTree = "<group>"; };
		6AA13FE0208C901C007809AC /* PaymentPointViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PaymentPointViewController.swift; sourceTree = "<group>"; };
		6AA13FE2208C97A3007809AC /* FilmTimeTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FilmTimeTableViewCell.swift; sourceTree = "<group>"; };
		6AA13FE3208C97A3007809AC /* FilmTimeTableViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = FilmTimeTableViewCell.xib; sourceTree = "<group>"; };
		6AA14020208F2FA1007809AC /* ListNewsViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ListNewsViewController.swift; sourceTree = "<group>"; };
		6AA14023208F371B007809AC /* StickyHeaderViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = StickyHeaderViewController.swift; sourceTree = "<group>"; };
		6AA1AC842076CF220081188F /* TableViewHelper.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TableViewHelper.swift; sourceTree = "<group>"; };
		6AA299A7208BF2A20007E074 /* FilmItemTableCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FilmItemTableCell.swift; sourceTree = "<group>"; };
		6AA299A8208BF2A20007E074 /* FilmItemTableCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = FilmItemTableCell.xib; sourceTree = "<group>"; };
		6AA299AD208C04920007E074 /* FilmBookingViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FilmBookingViewController.swift; sourceTree = "<group>"; };
		6AA299B0208C42680007E074 /* CalendarHeaderView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CalendarHeaderView.swift; sourceTree = "<group>"; };
		6AA299B4208C43490007E074 /* CalendarHeaderViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CalendarHeaderViewCell.swift; sourceTree = "<group>"; };
		6AA299B5208C43490007E074 /* CalendarHeaderViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = CalendarHeaderViewCell.xib; sourceTree = "<group>"; };
		6AA959CE20B65DE000E6337A /* AvatarImageModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AvatarImageModel.swift; sourceTree = "<group>"; };
		6AC0703020FDA7660036CD1D /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = Booking/App/Prod/Info.plist; sourceTree = SOURCE_ROOT; };
		6AC0703720FDA8430036CD1D /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		6AC0703920FDA8440036CD1D /* vi */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = vi; path = vi.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		6AC52E5220B54ED600D9BCAC /* ShowCinemaModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ShowCinemaModel.swift; sourceTree = "<group>"; };
		6AC52E5420B6064600D9BCAC /* RecruitmentViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RecruitmentViewController.swift; sourceTree = "<group>"; };
		6AC8AC7320AF4C2800DE4F5B /* HCYoutubeParser.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; path = HCYoutubeParser.m; sourceTree = "<group>"; };
		6AC8AC7420AF4C2800DE4F5B /* HCYoutubeParser.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; path = HCYoutubeParser.h; sourceTree = "<group>"; };
		6AC8AC7720AFDD2900DE4F5B /* LocationManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LocationManager.swift; sourceTree = "<group>"; };
		6AF06C6420A010A100A6459C /* ChooseSeatViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ChooseSeatViewController.swift; sourceTree = "<group>"; };
		6AFD7002210FF59500E01D8A /* UpdatePasswordViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UpdatePasswordViewController.swift; sourceTree = "<group>"; };
		6C143B96DAFE2D401D53AAB0 /* Pods-Booking-pro.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Booking-pro.release.xcconfig"; path = "Target Support Files/Pods-Booking-pro/Pods-Booking-pro.release.xcconfig"; sourceTree = "<group>"; };
		756FAA11648D14113A3CE05E /* Pods-Booking-test.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Booking-test.debug.xcconfig"; path = "Target Support Files/Pods-Booking-test/Pods-Booking-test.debug.xcconfig"; sourceTree = "<group>"; };
		8439E1AC2CEB75F400D7A69D /* ConfirmVipZoomViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ConfirmVipZoomViewController.swift; sourceTree = "<group>"; };
		845BA7C22DFFDACA00AACEDC /* Heart.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = Heart.png; sourceTree = "<group>"; };
		845BA7C32DFFDACA00AACEDC /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		845BA7C42DFFDACA00AACEDC /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		845BA7CF2DFFDC1700AACEDC /* AppIconManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppIconManager.swift; sourceTree = "<group>"; };
		84DD28A72AE227AF000C5712 /* TransactionDetailCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TransactionDetailCell.swift; sourceTree = "<group>"; };
		93FCEAD34C3CB63A1649D2C9 /* Pods-Booking-test.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Booking-test.release.xcconfig"; path = "Target Support Files/Pods-Booking-test/Pods-Booking-test.release.xcconfig"; sourceTree = "<group>"; };
		96A763A7A558D3C44FCB0660 /* Pods-Booking-dev.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Booking-dev.release.xcconfig"; path = "Target Support Files/Pods-Booking-dev/Pods-Booking-dev.release.xcconfig"; sourceTree = "<group>"; };
		ACC8EC1E73AAF5AE7B3DF1FE /* Pods-Booking-dev.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Booking-dev.debug.xcconfig"; path = "Target Support Files/Pods-Booking-dev/Pods-Booking-dev.debug.xcconfig"; sourceTree = "<group>"; };
		CC1979AE28A3AA64006AD455 /* JSONMappable.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = JSONMappable.swift; sourceTree = "<group>"; };
		CC1CF8E028B7601100861409 /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		CC1CF8E128B763A200861409 /* GoogleService-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		CC1CF8E228B763A200861409 /* GoogleService-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		CC1CF8E328B763A200861409 /* GoogleService-Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = "GoogleService-Info.plist"; sourceTree = "<group>"; };
		CC39C9A128AB75BE0015018A /* Tracking.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Tracking.swift; sourceTree = "<group>"; };
		CC39C9A628ACED650015018A /* ComboListModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ComboListModel.swift; sourceTree = "<group>"; };
		CC39C9AA28ACEF220015018A /* DataBookingModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DataBookingModel.swift; sourceTree = "<group>"; };
		DE002F552074DB960001C63D /* vi */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = vi; path = vi.lproj/LaunchScreen.strings; sourceTree = "<group>"; };
		DE002F562074DB960001C63D /* vi */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = vi; path = vi.lproj/Main.strings; sourceTree = "<group>"; };
		DE002F5C2074DC6A0001C63D /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/Localizable.strings; sourceTree = "<group>"; };
		DE002F5E2074DC6D0001C63D /* vi */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = vi; path = vi.lproj/Localizable.strings; sourceTree = "<group>"; };
		DE002F5F2074DD4C0001C63D /* String+Ext.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "String+Ext.swift"; sourceTree = "<group>"; };
		DE019FB520B9585500A934B0 /* RegisterVoucherModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RegisterVoucherModel.swift; sourceTree = "<group>"; };
		DE028935231A3FD200F24593 /* HomePage.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HomePage.swift; sourceTree = "<group>"; };
		DE028A78231A419700F24593 /* Booking-test.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "Booking-test.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		DE04A2BB2309A1250096ED85 /* CinemaFilmTimeTableCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = CinemaFilmTimeTableCell.xib; sourceTree = "<group>"; };
		DE084C47233910F700705A0F /* NotificationCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NotificationCell.swift; sourceTree = "<group>"; };
		DE084C48233910F700705A0F /* NotificationCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = NotificationCell.xib; sourceTree = "<group>"; };
		DE0B85502334BDFC009D6E32 /* MyTabbarViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MyTabbarViewController.swift; sourceTree = "<group>"; };
		DE0B85542334BF34009D6E32 /* UIMaker.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UIMaker.swift; sourceTree = "<group>"; };
		DE0C143422F6757100615CC3 /* IntroToFriendViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = IntroToFriendViewController.swift; sourceTree = "<group>"; };
		DE0C143522F6757100615CC3 /* IntroToFriendViewController.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = IntroToFriendViewController.xib; sourceTree = "<group>"; };
		DE0C143A22F6E81600615CC3 /* DashBorderView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DashBorderView.swift; sourceTree = "<group>"; };
		DE0C143D22F7276600615CC3 /* AttributeString.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AttributeString.swift; sourceTree = "<group>"; };
		DE0C144022F72B4E00615CC3 /* HistoryPointTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HistoryPointTableViewCell.swift; sourceTree = "<group>"; };
		DE0C144122F72B4E00615CC3 /* HistoryPointTableViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = HistoryPointTableViewCell.xib; sourceTree = "<group>"; };
		DE0C144722F72E6B00615CC3 /* DonatePointAlert.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DonatePointAlert.swift; sourceTree = "<group>"; };
		DE0C144822F72E6B00615CC3 /* DonatePointAlert.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = DonatePointAlert.xib; sourceTree = "<group>"; };
		DE0C144D22F731C700615CC3 /* Button.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Button.swift; sourceTree = "<group>"; };
		************************ /* VoucherAPI.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VoucherAPI.swift; sourceTree = "<group>"; };
		DE154A7A2090EDC300A51889 /* VoucherModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VoucherModel.swift; sourceTree = "<group>"; };
		DE16D0112080F1A5004D9722 /* LanguageManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = LanguageManager.swift; sourceTree = "<group>"; };
		DE16D01420810652004D9722 /* TopicModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TopicModel.swift; sourceTree = "<group>"; };
		DE16D01720810693004D9722 /* TopicDetailModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TopicDetailModel.swift; sourceTree = "<group>"; };
		DE16D01D208109ED004D9722 /* FAQDetailViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FAQDetailViewController.swift; sourceTree = "<group>"; };
		DE16D02020810BCA004D9722 /* NotificationTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NotificationTableViewCell.swift; sourceTree = "<group>"; };
		DE16D02120810BCA004D9722 /* NotificationTableViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = NotificationTableViewCell.xib; sourceTree = "<group>"; };
		************************ /* EcmAPI.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = EcmAPI.swift; sourceTree = "<group>"; };
		DE1B083A20C035A1000FFD1D /* TransactionHistoryDetailModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TransactionHistoryDetailModel.swift; sourceTree = "<group>"; };
		DE1C887B20539BC3003B5B9A /* AppDelegate.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = AppDelegate.swift; sourceTree = "<group>"; };
		DE1C887C20539BC3003B5B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		DE1C888A20539BC3003B5B9A /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		DE1C888C20539BC3003B5B9A /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/LaunchScreen.storyboard; sourceTree = "<group>"; };
		DE1C888E20539BC3003B5B9A /* Base */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; name = Base; path = Base.lproj/Main.storyboard; sourceTree = "<group>"; };
		DE1C88D42053E2D0003B5B9A /* App+Rx.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "App+Rx.swift"; sourceTree = "<group>"; };
		DE1C88DA2053FA2F003B5B9A /* Booking-dev-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "Booking-dev-Bridging-Header.h"; sourceTree = "<group>"; };
		DE1C88DB2053FA2F003B5B9A /* Booking-release-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "Booking-release-Bridging-Header.h"; sourceTree = "<group>"; };
		DE1C88E02053FBA3003B5B9A /* UserDefault+Quick.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "UserDefault+Quick.swift"; sourceTree = "<group>"; };
		DE1C88E32053FD11003B5B9A /* AppDelegate+Initial.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "AppDelegate+Initial.swift"; sourceTree = "<group>"; };
		DE1C88EC20540F9E003B5B9A /* pageDot.png */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = pageDot.png; sourceTree = "<group>"; };
		DE1C88ED20540F9E003B5B9A /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		DE1C88EE20540F9E003B5B9A /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		DE1C88EF20540F9E003B5B9A /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		DE1C88F020540F9E003B5B9A /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		DE1C88F120540F9E003B5B9A /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		DE1C88F220540F9E003B5B9A /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		DE1C88F320540F9E003B5B9A /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		DE1C88F420540F9E003B5B9A /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		DE1C88F520540F9E003B5B9A /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		DE1C88F620540F9E003B5B9A /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		DE1C88F720540F9E003B5B9A /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		DE1C88F820540F9E003B5B9A /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		DE1C88F920540F9E003B5B9A /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		DE1C88FA20540F9E003B5B9A /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		DE1CF99120BD9885001D89F2 /* TransactionHistoryModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TransactionHistoryModel.swift; sourceTree = "<group>"; };
		************************ /* FilmAPI.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FilmAPI.swift; sourceTree = "<group>"; };
		DE2164E72073C99B00938938 /* DDKCResponse.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DDKCResponse.swift; sourceTree = "<group>"; };
		DE26416C207A56C700844F7F /* UILabel+Localization.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "UILabel+Localization.swift"; sourceTree = "<group>"; };
		DE26416F207A62DD00844F7F /* UIAlertController+Ext.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "UIAlertController+Ext.swift"; sourceTree = "<group>"; };
		DE42738220503C080047666D /* Booking-dev.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = "Booking-dev.app"; sourceTree = BUILT_PRODUCTS_DIR; };
		DE4453B520F3B13B00835007 /* Notification.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Notification.swift; sourceTree = "<group>"; };
		DE743B3FAEABD98F86D019D8 /* Pods-Booking-pro.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-Booking-pro.debug.xcconfig"; path = "Target Support Files/Pods-Booking-pro/Pods-Booking-pro.debug.xcconfig"; sourceTree = "<group>"; };
		DE75FC17207F9A4C00604D7E /* NewsTableViewCell.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = NewsTableViewCell.swift; sourceTree = "<group>"; };
		DE75FC1E207F9FC200604D7E /* Location+Ext.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Location+Ext.swift"; sourceTree = "<group>"; };
		************************ /* CityAPI.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CityAPI.swift; sourceTree = "<group>"; };
		************************ /* Common+Ext.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Common+Ext.swift"; sourceTree = "<group>"; };
		DEA5BEA12073F636006D8DE1 /* CinemaAPI.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CinemaAPI.swift; sourceTree = "<group>"; };
		DEB8F8A72075237A00715531 /* AccountAPI.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AccountAPI.swift; sourceTree = "<group>"; };
		DEBA9FDE230412BE00322EE4 /* AppParams.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppParams.swift; sourceTree = "<group>"; };
		DEBDF17C22FC6CD30031A64D /* Request.swift */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.swift; path = Request.swift; sourceTree = "<group>"; };
		DEBE7B15207CC4F5005A1F1D /* NotificationModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NotificationModel.swift; sourceTree = "<group>"; };
		DEBE7B18207CDF4D005A1F1D /* Date+Ext.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Date+Ext.swift"; sourceTree = "<group>"; };
		DEBE7B1B207D01EC005A1F1D /* NetworkManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NetworkManager.swift; sourceTree = "<group>"; };
		DEBE7B1E207D09C1005A1F1D /* DDKCResult.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DDKCResult.swift; sourceTree = "<group>"; };
		DEBE7B23207D0C2C005A1F1D /* NetworkManager+Film.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "NetworkManager+Film.swift"; sourceTree = "<group>"; };
		DEBE7B26207D13D8005A1F1D /* NetworkManager+Ecm.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "NetworkManager+Ecm.swift"; sourceTree = "<group>"; };
		DED214692083AA22003BBBA2 /* PolicyModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PolicyModel.swift; sourceTree = "<group>"; };
		DED2146C2083AA34003BBBA2 /* PolicyContentModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PolicyContentModel.swift; sourceTree = "<group>"; };
		DEE1AAA722F5CFC80081FD3F /* MyVoucherViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MyVoucherViewController.swift; sourceTree = "<group>"; };
		DEE1AAA822F5CFC80081FD3F /* MyVoucherViewController.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = MyVoucherViewController.xib; sourceTree = "<group>"; };
		DEE1AAAE22F5D09A0081FD3F /* MyVoucherTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MyVoucherTableViewCell.swift; sourceTree = "<group>"; };
		DEE1AAAF22F5D09A0081FD3F /* MyVoucherTableViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = MyVoucherTableViewCell.xib; sourceTree = "<group>"; };
		DEE1AAB422F5D11B0081FD3F /* TableView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TableView.swift; sourceTree = "<group>"; };
		DEE1AAB722F5DEDC0081FD3F /* AddVoucherViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AddVoucherViewController.swift; sourceTree = "<group>"; };
		DEE1AAB822F5DEDC0081FD3F /* AddVoucherViewController.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = AddVoucherViewController.xib; sourceTree = "<group>"; };
		DEE1AABD22F5DEEE0081FD3F /* DonateVoucherViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DonateVoucherViewController.swift; sourceTree = "<group>"; };
		DEE1AABE22F5DEEE0081FD3F /* DonateVoucherViewController.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = DonateVoucherViewController.xib; sourceTree = "<group>"; };
		DEE1AAC322F5DF000081FD3F /* HistoryVoucherViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HistoryVoucherViewController.swift; sourceTree = "<group>"; };
		DEE1AAC422F5DF000081FD3F /* HistoryVoucherViewController.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = HistoryVoucherViewController.xib; sourceTree = "<group>"; };
		DEE1AAC922F5E1E00081FD3F /* HistoryVoucherTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HistoryVoucherTableViewCell.swift; sourceTree = "<group>"; };
		DEE1AACA22F5E1E00081FD3F /* HistoryVoucherTableViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = HistoryVoucherTableViewCell.xib; sourceTree = "<group>"; };
		DEE1AACF22F5E1F40081FD3F /* DonateVoucherTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = DonateVoucherTableViewCell.swift; sourceTree = "<group>"; };
		DEE1AAD022F5E1F40081FD3F /* DonateVoucherTableViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = DonateVoucherTableViewCell.xib; sourceTree = "<group>"; };
		DEFA01F02303F13D003839C5 /* VoucherHistory.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VoucherHistory.swift; sourceTree = "<group>"; };
		DEFA01F32303F197003839C5 /* PointHistory.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PointHistory.swift; sourceTree = "<group>"; };
		DEFA01F623040831003839C5 /* UseVoucherViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UseVoucherViewController.swift; sourceTree = "<group>"; };
		DEFA01F723040831003839C5 /* UseVoucherViewController.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = UseVoucherViewController.xib; sourceTree = "<group>"; };
		DEFA4C7822FE6E6D00725391 /* Banner.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Banner.swift; sourceTree = "<group>"; };
		DEFA4C8422FEC04C00725391 /* FreeVoucherViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FreeVoucherViewController.swift; sourceTree = "<group>"; };
		DEFA4C8522FEC04C00725391 /* FreeVoucherViewController.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = FreeVoucherViewController.xib; sourceTree = "<group>"; };
		DEFA4C8A22FEC10900725391 /* FreeVoucherTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FreeVoucherTableViewCell.swift; sourceTree = "<group>"; };
		DEFA4C8B22FEC10900725391 /* FreeVoucherTableViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = FreeVoucherTableViewCell.xib; sourceTree = "<group>"; };
		DEFA6AFE2302AF1700C0C156 /* CustomizeAlert.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CustomizeAlert.swift; sourceTree = "<group>"; };
		DEFA6AFF2302AF1700C0C156 /* CustomizeAlert.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = CustomizeAlert.xib; sourceTree = "<group>"; };
		DEFA6B042302C5CD00C0C156 /* ScanBarCodeViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ScanBarCodeViewController.swift; sourceTree = "<group>"; };
		DEFA6B052302C5CD00C0C156 /* ScanBarCodeViewController.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = ScanBarCodeViewController.xib; sourceTree = "<group>"; };
		DEFA6B0A230313BB00C0C156 /* VoucherGot.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = VoucherGot.swift; sourceTree = "<group>"; };
		DEFAB21922F486680019231D /* MainTabContainer.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MainTabContainer.swift; sourceTree = "<group>"; };
		DEFAB22322F48AB50019231D /* TabOtherViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TabOtherViewController.swift; sourceTree = "<group>"; };
		DEFAB22422F48AB50019231D /* TabOtherViewController.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = TabOtherViewController.xib; sourceTree = "<group>"; };
		DEFAB22A22F49C280019231D /* OthersCollectionViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = OthersCollectionViewCell.swift; sourceTree = "<group>"; };
		DEFAB22B22F49C280019231D /* OthersCollectionViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = OthersCollectionViewCell.xib; sourceTree = "<group>"; };
		DEFED1792301B6AD0006688A /* FreeVoucher.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = FreeVoucher.swift; sourceTree = "<group>"; };
		DEFED17D2301D7B40006688A /* ShortUser.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ShortUser.swift; sourceTree = "<group>"; };
		DEFEF51623055FBA00D107E8 /* RouteManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = RouteManager.swift; sourceTree = "<group>"; };
		DEFF15DD22F86E9D0000D2C9 /* NewHomeViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = NewHomeViewController.swift; sourceTree = "<group>"; };
		DEFF15DE22F86E9D0000D2C9 /* NewHomeViewController.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = NewHomeViewController.xib; sourceTree = "<group>"; };
		DEFF15E322F8752D0000D2C9 /* HomeCollectionViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = HomeCollectionViewCell.swift; sourceTree = "<group>"; };
		DEFF15E422F8752D0000D2C9 /* HomeCollectionViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = HomeCollectionViewCell.xib; sourceTree = "<group>"; };
		DEFF6B67230A8C22009B04B2 /* CinemasViewController.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CinemasViewController.swift; sourceTree = "<group>"; };
		DEFF6B68230A8C22009B04B2 /* CinemasViewController.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = CinemasViewController.xib; sourceTree = "<group>"; };
		DEFF6B6E230A8E34009B04B2 /* CinemaCollectionViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CinemaCollectionViewCell.swift; sourceTree = "<group>"; };
		DEFF6B6F230A8E34009B04B2 /* CinemaCollectionViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = CinemaCollectionViewCell.xib; sourceTree = "<group>"; };
		DEFF6B74230A8E6A009B04B2 /* CinemaByProvinceTableViewCell.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CinemaByProvinceTableViewCell.swift; sourceTree = "<group>"; };
		DEFF6B75230A8E6A009B04B2 /* CinemaByProvinceTableViewCell.xib */ = {isa = PBXFileReference; lastKnownFileType = file.xib; path = CinemaByProvinceTableViewCell.xib; sourceTree = "<group>"; };
		E6223944CDAA907FCD2459C0 /* Pods_Booking_pro.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_Booking_pro.framework; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		6A48452820F59C7100273D66 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				744255807C3E559178F7304F /* Pods_Booking_pro.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DE028A15231A419700F24593 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6FB8E11DF2B9AAA2A33D6AE6 /* Pods_Booking_test.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DE42737F20503C080047666D /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8CEE4F3C4F204DB134B3C545 /* Pods_Booking_dev.framework in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		4430EC0BFBC372EBADE4B77F /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				443BF9D058AE64C67C25B3BF /* Pods_Booking_dev.framework */,
				E6223944CDAA907FCD2459C0 /* Pods_Booking_pro.framework */,
				24F82CE45BDF02E34BC848D2 /* Pods_Booking_test.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		49DB472566AD4CE3F7604927 /* Pods */ = {
			isa = PBXGroup;
			children = (
				ACC8EC1E73AAF5AE7B3DF1FE /* Pods-Booking-dev.debug.xcconfig */,
				96A763A7A558D3C44FCB0660 /* Pods-Booking-dev.release.xcconfig */,
				DE743B3FAEABD98F86D019D8 /* Pods-Booking-pro.debug.xcconfig */,
				6C143B96DAFE2D401D53AAB0 /* Pods-Booking-pro.release.xcconfig */,
				756FAA11648D14113A3CE05E /* Pods-Booking-test.debug.xcconfig */,
				93FCEAD34C3CB63A1649D2C9 /* Pods-Booking-test.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		6A03F38A208106B6002FC2CD /* UIViewTransition */ = {
			isa = PBXGroup;
			children = (
				6A03F38B208106F0002FC2CD /* TransparentAnimator.swift */,
			);
			path = UIViewTransition;
			sourceTree = "<group>";
		};
		6A11D6C920B9D686009668B2 /* Layout */ = {
			isa = PBXGroup;
			children = (
				6A11D6CA20B9D69A009668B2 /* ColumnLayout.swift */,
			);
			path = Layout;
			sourceTree = "<group>";
		};
		6A29B739207C0D1400ED7F4C /* Class */ = {
			isa = PBXGroup;
			children = (
				6A29B73A207C0D1400ED7F4C /* Controller */,
				6A29B77E207C0D1500ED7F4C /* Model */,
				6A29B79E207C0D1500ED7F4C /* View */,
			);
			path = Class;
			sourceTree = "<group>";
		};
		6A29B73A207C0D1400ED7F4C /* Controller */ = {
			isa = PBXGroup;
			children = (
				DEE1AAA622F5CFAF0081FD3F /* Voucher */,
				DEFAB21C22F489B80019231D /* Other */,
				DEFAB21822F4861F0019231D /* MainTab */,
				6A29B75E207C0D1400ED7F4C /* Authen */,
				6A29B77B207C0D1400ED7F4C /* Base */,
				6A29B74F207C0D1400ED7F4C /* Cinema */,
				6A29B749207C0D1400ED7F4C /* Film */,
				6A29B73B207C0D1400ED7F4C /* Home */,
				6A29B763207C0D1400ED7F4C /* Member */,
				6A29B76F207C0D1400ED7F4C /* Menu */,
				6A29B75B207C0D1400ED7F4C /* Payment */,
				6A29B775207C0D1400ED7F4C /* Setting */,
			);
			path = Controller;
			sourceTree = "<group>";
		};
		6A29B73B207C0D1400ED7F4C /* Home */ = {
			isa = PBXGroup;
			children = (
				6A29B73C207C0D1400ED7F4C /* NewsDetailViewController.swift */,
				6A29B73D207C0D1400ED7F4C /* BannerCard */,
				6A29B740207C0D1400ED7F4C /* Home.storyboard */,
				6A29B741207C0D1400ED7F4C /* HomeViewController.swift */,
				6A29B742207C0D1400ED7F4C /* NewsAndDealsViewController.swift */,
				6A29B743207C0D1400ED7F4C /* Cell */,
				6A4550B020816C4A00AD3031 /* TopViewController.swift */,
				6AA14020208F2FA1007809AC /* ListNewsViewController.swift */,
				6A968EAB20B7EBC100A80BE2 /* YoutubeViewController.swift */,
				DEFF15DD22F86E9D0000D2C9 /* NewHomeViewController.swift */,
				DEFF15DE22F86E9D0000D2C9 /* NewHomeViewController.xib */,
			);
			path = Home;
			sourceTree = "<group>";
		};
		6A29B73D207C0D1400ED7F4C /* BannerCard */ = {
			isa = PBXGroup;
			children = (
				6A29B73E207C0D1400ED7F4C /* BannerCardView.xib */,
				6A29B73F207C0D1400ED7F4C /* BannerCardView.swift */,
			);
			path = BannerCard;
			sourceTree = "<group>";
		};
		6A29B743207C0D1400ED7F4C /* Cell */ = {
			isa = PBXGroup;
			children = (
				DE75FC17207F9A4C00604D7E /* NewsTableViewCell.swift */,
				6A29B745207C0D1400ED7F4C /* NewsTableViewCell.xib */,
				DE16D02020810BCA004D9722 /* NotificationTableViewCell.swift */,
				DE16D02120810BCA004D9722 /* NotificationTableViewCell.xib */,
				DEFF15E322F8752D0000D2C9 /* HomeCollectionViewCell.swift */,
				DEFF15E422F8752D0000D2C9 /* HomeCollectionViewCell.xib */,
				DE084C47233910F700705A0F /* NotificationCell.swift */,
				DE084C48233910F700705A0F /* NotificationCell.xib */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		6A29B749207C0D1400ED7F4C /* Film */ = {
			isa = PBXGroup;
			children = (
				6A29B879207C259A00ED7F4C /* Cell */,
				6A29B74A207C0D1400ED7F4C /* TicketBookingViewController.swift */,
				6A29B74B207C0D1400ED7F4C /* GraphShitingViewController.swift */,
				6A29B74C207C0D1400ED7F4C /* Film.storyboard */,
				6A29B74D207C0D1400ED7F4C /* FilmDetailViewController.swift */,
				6A29B74E207C0D1400ED7F4C /* ListFilmViewController.swift */,
				6AA299AD208C04920007E074 /* FilmBookingViewController.swift */,
				6A661B5620913C5000CFC976 /* FilmChooseTimeViewController.swift */,
			);
			path = Film;
			sourceTree = "<group>";
		};
		6A29B74F207C0D1400ED7F4C /* Cinema */ = {
			isa = PBXGroup;
			children = (
				6A29B753207C0D1400ED7F4C /* Cell */,
				6A661B4C208FF6CD00CFC976 /* TimeListView */,
				6A29B750207C0D1400ED7F4C /* ListAllCinemasViewController.swift */,
				6A29B751207C0D1400ED7F4C /* CinemaDetailViewController.swift */,
				6A29B752207C0D1400ED7F4C /* ChooseCinemasViewController.swift */,
				6A29B75A207C0D1400ED7F4C /* Cinema.storyboard */,
				6AF06C6420A010A100A6459C /* ChooseSeatViewController.swift */,
				6A7C078720B3701A0092E553 /* CinemaPriceViewController.swift */,
				6A968EAD20B925A500A80BE2 /* SelectRegionViewController.swift */,
				6A3FB3B620BF93390034FC3D /* ConfirmBookAgeViewController.swift */,
				8439E1AC2CEB75F400D7A69D /* ConfirmVipZoomViewController.swift */,
				DEFF6B67230A8C22009B04B2 /* CinemasViewController.swift */,
				DEFF6B68230A8C22009B04B2 /* CinemasViewController.xib */,
			);
			path = Cinema;
			sourceTree = "<group>";
		};
		6A29B753207C0D1400ED7F4C /* Cell */ = {
			isa = PBXGroup;
			children = (
				6A29B757207C0D1400ED7F4C /* AreaCinemaHeaderView.swift */,
				6A29B755207C0D1400ED7F4C /* AreaCinemaHeaderView.xib */,
				6A29B759207C0D1400ED7F4C /* CinemaTableViewCell.swift */,
				6A29B754207C0D1400ED7F4C /* NearCinemaTableViewCell.swift */,
				6A29B758207C0D1400ED7F4C /* TitleHeaderView.swift */,
				6A29B756207C0D1400ED7F4C /* TitleHeaderView.xib */,
				6AA13FE2208C97A3007809AC /* FilmTimeTableViewCell.swift */,
				6AA13FE3208C97A3007809AC /* FilmTimeTableViewCell.xib */,
				6A44992C20B626BB006B37A3 /* CinemaPriceHeaderView.swift */,
				6A44992E20B626C8006B37A3 /* CinemaPriceHeaderView.xib */,
				6A11D6CC20BA1567009668B2 /* SeatCollectionViewCell.swift */,
				6A31AAFB20BB2AED00DC59B3 /* SeatCollectionHeaderView.swift */,
				6A31AAFC20BB2AED00DC59B3 /* SeatCollectionHeaderView.xib */,
				DEFF6B6E230A8E34009B04B2 /* CinemaCollectionViewCell.swift */,
				DEFF6B6F230A8E34009B04B2 /* CinemaCollectionViewCell.xib */,
				DEFF6B74230A8E6A009B04B2 /* CinemaByProvinceTableViewCell.swift */,
				DEFF6B75230A8E6A009B04B2 /* CinemaByProvinceTableViewCell.xib */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		6A29B75B207C0D1400ED7F4C /* Payment */ = {
			isa = PBXGroup;
			children = (
				6A29B75C207C0D1400ED7F4C /* PaymentViewController.swift */,
				6A29B75D207C0D1400ED7F4C /* Payment.storyboard */,
				6AA13FE0208C901C007809AC /* PaymentPointViewController.swift */,
			);
			path = Payment;
			sourceTree = "<group>";
		};
		6A29B75E207C0D1400ED7F4C /* Authen */ = {
			isa = PBXGroup;
			children = (
				6A29B75F207C0D1400ED7F4C /* ForgotPassViewController.swift */,
				6A29B760207C0D1400ED7F4C /* LoginViewController.swift */,
				6A29B761207C0D1400ED7F4C /* Authen.storyboard */,
				6A29B762207C0D1400ED7F4C /* RegisterViewController.swift */,
				6A31AAF920BA91E100DC59B3 /* RegisterResultViewController.swift */,
				6AFD7002210FF59500E01D8A /* UpdatePasswordViewController.swift */,
			);
			path = Authen;
			sourceTree = "<group>";
		};
		6A29B763207C0D1400ED7F4C /* Member */ = {
			isa = PBXGroup;
			children = (
				6A812F0F20870A330079ABF6 /* Cell */,
				6A29B764207C0D1400ED7F4C /* RewardPointsViewController.swift */,
				6A29B765207C0D1400ED7F4C /* VourcherCouponViewController.swift */,
				6A29B766207C0D1400ED7F4C /* Member.storyboard */,
				6A29B767207C0D1400ED7F4C /* MemberCardViewController.swift */,
				6A29B768207C0D1400ED7F4C /* VoucherViewController.swift */,
				6A29B769207C0D1400ED7F4C /* ChangePassViewController.swift */,
				6A29B76A207C0D1400ED7F4C /* CardGiftViewController.swift */,
				6A29B76B207C0D1400ED7F4C /* MemberViewController.swift */,
				6A29B76C207C0D1400ED7F4C /* ListFilmWatchedViewController.swift */,
				6A29B76D207C0D1400ED7F4C /* CouponViewController.swift */,
				6A29B76E207C0D1400ED7F4C /* TranferHistoryViewController.swift */,
				6A812F092086BBEB0079ABF6 /* ConfirmPassViewController.swift */,
				6A812F0B2086BBF70079ABF6 /* AccountInfoViewController.swift */,
				6A812F1320870F500079ABF6 /* TransactionDetailViewController.swift */,
				DE0C143422F6757100615CC3 /* IntroToFriendViewController.swift */,
				DE0C143522F6757100615CC3 /* IntroToFriendViewController.xib */,
			);
			path = Member;
			sourceTree = "<group>";
		};
		6A29B76F207C0D1400ED7F4C /* Menu */ = {
			isa = PBXGroup;
			children = (
				6A29B770207C0D1400ED7F4C /* SlideMenuViewController.swift */,
				6A29B774207C0D1400ED7F4C /* NotificationViewController.swift */,
				6A29B771207C0D1400ED7F4C /* Cell */,
				6AC52E5420B6064600D9BCAC /* RecruitmentViewController.swift */,
				6A41ED2820D1215F00BA16FA /* NotificationDetailViewController.swift */,
			);
			path = Menu;
			sourceTree = "<group>";
		};
		6A29B771207C0D1400ED7F4C /* Cell */ = {
			isa = PBXGroup;
			children = (
				6A29B772207C0D1400ED7F4C /* MenuItemCell.swift */,
				6A29B773207C0D1400ED7F4C /* NotificationTableCell.swift */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		6A29B775207C0D1400ED7F4C /* Setting */ = {
			isa = PBXGroup;
			children = (
				6A29B886207D683D00ED7F4C /* Cell */,
				6A29B776207C0D1400ED7F4C /* SettingViewController.swift */,
				6A29B777207C0D1400ED7F4C /* Setting.storyboard */,
				6A29B778207C0D1400ED7F4C /* VersionInfoViewController.swift */,
				6A29B779207C0D1400ED7F4C /* OtherViewController.swift */,
				6A29B77A207C0D1400ED7F4C /* ProfileViewController.swift */,
				6A29B89C207EC13300ED7F4C /* FAQViewController.swift */,
				DE16D01D208109ED004D9722 /* FAQDetailViewController.swift */,
			);
			path = Setting;
			sourceTree = "<group>";
		};
		6A29B77B207C0D1400ED7F4C /* Base */ = {
			isa = PBXGroup;
			children = (
				6A29B77C207C0D1500ED7F4C /* BaseViewController.swift */,
				6A29B77D207C0D1500ED7F4C /* BaseNavigationViewController.swift */,
				6AA14023208F371B007809AC /* StickyHeaderViewController.swift */,
			);
			path = Base;
			sourceTree = "<group>";
		};
		6A29B77E207C0D1500ED7F4C /* Model */ = {
			isa = PBXGroup;
			children = (
				6A29B77F207C0D1500ED7F4C /* RequestModel */,
				6A29B783207C0D1500ED7F4C /* ResponseModel */,
				6A29B79B207C0D1500ED7F4C /* Repository.swift */,
				6A29B79C207C0D1500ED7F4C /* BaseModel.swift */,
				6A29B79D207C0D1500ED7F4C /* Owner.swift */,
				CC39C9A628ACED650015018A /* ComboListModel.swift */,
				CC39C9AA28ACEF220015018A /* DataBookingModel.swift */,
			);
			path = Model;
			sourceTree = "<group>";
		};
		6A29B77F207C0D1500ED7F4C /* RequestModel */ = {
			isa = PBXGroup;
			children = (
				6A29B780207C0D1500ED7F4C /* BaseRequestModel.swift */,
				6A29B781207C0D1500ED7F4C /* LoginModel.swift */,
				6A29B782207C0D1500ED7F4C /* RegisterModel.swift */,
				DE019FB520B9585500A934B0 /* RegisterVoucherModel.swift */,
				6A34871820C149390074F58F /* CreateBookingModel.swift */,
				6A34871A20C1499A0074F58F /* SeatBookingModel.swift */,
			);
			path = RequestModel;
			sourceTree = "<group>";
		};
		6A29B783207C0D1500ED7F4C /* ResponseModel */ = {
			isa = PBXGroup;
			children = (
				6A29B787207C0D1500ED7F4C /* CardModel.swift */,
				6A29B786207C0D1500ED7F4C /* CinemaModel.swift */,
				6A29B79A207C0D1500ED7F4C /* CinemaProvinceModel.swift */,
				6A29B789207C0D1500ED7F4C /* CityModel.swift */,
				6A29B785207C0D1500ED7F4C /* FilmModel.swift */,
				6A29B78A207C0D1500ED7F4C /* ListFilmGenreModel.swift */,
				6A29B788207C0D1500ED7F4C /* ListPosterUrlModel.swift */,
				6A29B798207C0D1500ED7F4C /* NewModel.swift */,
				6A29B791207C0D1500ED7F4C /* NewsModel.swift */,
				DEBE7B15207CC4F5005A1F1D /* NotificationModel.swift */,
				6A29B784207C0D1500ED7F4C /* PointModel.swift */,
				DED2146C2083AA34003BBBA2 /* PolicyContentModel.swift */,
				DED214692083AA22003BBBA2 /* PolicyModel.swift */,
				6AC52E5220B54ED600D9BCAC /* ShowCinemaModel.swift */,
				6A29B78C207C0D1500ED7F4C /* ShowFilmModel.swift */,
				6A29B799207C0D1500ED7F4C /* ShowModel.swift */,
				6A11D6CE20BA3F66009668B2 /* ListSeatModel.swift */,
				DE16D01720810693004D9722 /* TopicDetailModel.swift */,
				DE16D01420810652004D9722 /* TopicModel.swift */,
				6A29B78B207C0D1500ED7F4C /* UserModel.swift */,
				DE154A7A2090EDC300A51889 /* VoucherModel.swift */,
				6A11D6D020BA4021009668B2 /* ScreenModel.swift */,
				6A11D6D220BA402A009668B2 /* SeatModel.swift */,
				6A11D6D420BA446E009668B2 /* TicketType.swift */,
				6AA959CE20B65DE000E6337A /* AvatarImageModel.swift */,
				DE1CF99120BD9885001D89F2 /* TransactionHistoryModel.swift */,
				DE1B083A20C035A1000FFD1D /* TransactionHistoryDetailModel.swift */,
				6A41ECBB20CB83D100BA16FA /* CardClassModel.swift */,
				DE4453B520F3B13B00835007 /* Notification.swift */,
				DEFA4C7822FE6E6D00725391 /* Banner.swift */,
				DEFED1792301B6AD0006688A /* FreeVoucher.swift */,
				DEFED17D2301D7B40006688A /* ShortUser.swift */,
				DEFA6B0A230313BB00C0C156 /* VoucherGot.swift */,
				DEFA01F02303F13D003839C5 /* VoucherHistory.swift */,
				DEFA01F32303F197003839C5 /* PointHistory.swift */,
				DEBA9FDE230412BE00322EE4 /* AppParams.swift */,
				DE028935231A3FD200F24593 /* HomePage.swift */,
			);
			path = ResponseModel;
			sourceTree = "<group>";
		};
		6A29B79E207C0D1500ED7F4C /* View */ = {
			isa = PBXGroup;
			children = (
				6A29B79F207C0D1500ED7F4C /* NewAndDealsView.swift */,
				6A29B7A0207C0D1500ED7F4C /* NewAndDealsView.xib */,
			);
			path = View;
			sourceTree = "<group>";
		};
		6A29B847207C0D3800ED7F4C /* Common */ = {
			isa = PBXGroup;
			children = (
				6A11D6C920B9D686009668B2 /* Layout */,
				6A4550B82082515900AD3031 /* Localizable */,
				6A29B848207C0D3800ED7F4C /* Constants.swift */,
				6A29B849207C0D3800ED7F4C /* Global.swift */,
				6A29B84A207C0D3800ED7F4C /* Config.swift */,
				6A29B84B207C0D3800ED7F4C /* Fonts.swift */,
				6A29B84C207C0D3800ED7F4C /* View */,
				6A29B856207C0D3800ED7F4C /* Images.swift */,
				6A29B857207C0D3800ED7F4C /* Utils.swift */,
				6A29B858207C0D3800ED7F4C /* Color.swift */,
				6A29B883207CC07900ED7F4C /* DateFormat.swift */,
				CC39C9A128AB75BE0015018A /* Tracking.swift */,
			);
			path = Common;
			sourceTree = "<group>";
		};
		6A29B84C207C0D3800ED7F4C /* View */ = {
			isa = PBXGroup;
			children = (
				DEFA6AFD2302AEF700C0C156 /* Alert */,
				DE0C144622F72E4300615CC3 /* DonatePoint */,
				6AA299B3208C432B0007E074 /* Cell */,
				6A29B84D207C0D3800ED7F4C /* PickerTextField.swift */,
				6A29B84E207C0D3800ED7F4C /* RoundView.swift */,
				6A29B84F207C0D3800ED7F4C /* RoundButton.swift */,
				6A29B850207C0D3800ED7F4C /* RoundTextField.swift */,
				6A29B851207C0D3800ED7F4C /* InputTextField.swift */,
				6A29B852207C0D3800ED7F4C /* GradientButton.swift */,
				6A29B853207C0D3800ED7F4C /* GradientView.swift */,
				6A29B854207C0D3800ED7F4C /* RoundImageView.swift */,
				6A29B855207C0D3800ED7F4C /* DateTextField.swift */,
				6A0A8B36208571BD00679340 /* GradientImageView.swift */,
				6A812F162087C9280079ABF6 /* DashView.swift */,
				6AA299B0208C42680007E074 /* CalendarHeaderView.swift */,
				6A97191020A39962009FF4B7 /* VerticalLayoutButton.swift */,
				DE0C143A22F6E81600615CC3 /* DashBorderView.swift */,
				DE0C143D22F7276600615CC3 /* AttributeString.swift */,
			);
			path = View;
			sourceTree = "<group>";
		};
		6A29B879207C259A00ED7F4C /* Cell */ = {
			isa = PBXGroup;
			children = (
				6A29B87A207C25B200ED7F4C /* FilmDescriptionTableCell.swift */,
				6A29B87D207C2A6B00ED7F4C /* NewsAndDealsCell.swift */,
				6A29B880207C2AB100ED7F4C /* NewsAndDealsCell.xib */,
				6AA299A7208BF2A20007E074 /* FilmItemTableCell.swift */,
				6AA299A8208BF2A20007E074 /* FilmItemTableCell.xib */,
				6A2B22C020924E9A00DA096B /* CinemaFilmTimeTableCell.swift */,
				DE04A2BB2309A1250096ED85 /* CinemaFilmTimeTableCell.xib */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		6A29B886207D683D00ED7F4C /* Cell */ = {
			isa = PBXGroup;
			children = (
				6A29B88D207EA54600ED7F4C /* CheckboxTableCell.swift */,
				6A29B890207EA57E00ED7F4C /* CheckboxTableCell.xib */,
				6A29B887207EA50F00ED7F4C /* SettingTableCell.swift */,
				6A29B893207EA58C00ED7F4C /* SettingTableCell.xib */,
				6A29B88A207EA51F00ED7F4C /* SwitchTableCell.swift */,
				6A29B896207EA59D00ED7F4C /* SwitchTableCell.xib */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		6A4550B82082515900AD3031 /* Localizable */ = {
			isa = PBXGroup;
			children = (
				6A4550B92082517300AD3031 /* LocalizableProtocol.swift */,
				6A4550BC2082520800AD3031 /* LocalizableLabel.swift */,
				6A4550BF2082530200AD3031 /* LocalizableButton.swift */,
			);
			path = Localizable;
			sourceTree = "<group>";
		};
		6A661B4C208FF6CD00CFC976 /* TimeListView */ = {
			isa = PBXGroup;
			children = (
				6A661B4D208FF6E600CFC976 /* TimeListView.swift */,
				6A661B50208FF99F00CFC976 /* TimeListCollectionCell.swift */,
				6A661B53208FF9AA00CFC976 /* TimeListCollectionCell.xib */,
			);
			path = TimeListView;
			sourceTree = "<group>";
		};
		6A812F0F20870A330079ABF6 /* Cell */ = {
			isa = PBXGroup;
			children = (
				6A812F1020870A480079ABF6 /* TransactionHistoryCell.swift */,
				6A812F22208933160079ABF6 /* VoucherTableCell.swift */,
				6A812F25208939E60079ABF6 /* VoucherTableCell.xib */,
				6A7C078A20B3730C0092E553 /* MemberCardCell.swift */,
				84DD28A72AE227AF000C5712 /* TransactionDetailCell.swift */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		6A812F1820889E760079ABF6 /* Images */ = {
			isa = PBXGroup;
			children = (
				6A812F1920889E880079ABF6 /* AssetLibrary.swift */,
				6A812F1B20889EEE0079ABF6 /* ImagePickerController.swift */,
			);
			path = Images;
			sourceTree = "<group>";
		};
		6A9D12382072D90C00D49B55 /* Lib */ = {
			isa = PBXGroup;
			children = (
				6AC8AC7220AF4C2800DE4F5B /* YoutubeParser */,
				6A9D123D2072E3F700D49B55 /* ScrollPager */,
				6A9D12392072D90C00D49B55 /* SSASideMenu */,
			);
			path = Lib;
			sourceTree = "<group>";
		};
		6A9D12392072D90C00D49B55 /* SSASideMenu */ = {
			isa = PBXGroup;
			children = (
				6A9D123A2072D90C00D49B55 /* SSASideMenu.swift */,
			);
			path = SSASideMenu;
			sourceTree = "<group>";
		};
		6A9D123D2072E3F700D49B55 /* ScrollPager */ = {
			isa = PBXGroup;
			children = (
				6A9D123E2072E3F700D49B55 /* ScrollPager.swift */,
			);
			path = ScrollPager;
			sourceTree = "<group>";
		};
		6A9D1251207418B200D49B55 /* Font */ = {
			isa = PBXGroup;
			children = (
				6A9D127520746A3700D49B55 /* Source_Sans_Pro */,
				6A9D1252207418B200D49B55 /* Oswald */,
			);
			path = Font;
			sourceTree = "<group>";
		};
		6A9D1252207418B200D49B55 /* Oswald */ = {
			isa = PBXGroup;
			children = (
				6A9D1253207418B200D49B55 /* Oswald-Bold.ttf */,
				6A9D1254207418B200D49B55 /* Oswald-SemiBold.ttf */,
				6A9D1255207418B200D49B55 /* Oswald-Medium.ttf */,
				6A9D1256207418B200D49B55 /* Oswald-Regular.ttf */,
				6A9D1257207418B200D49B55 /* OFL.txt */,
				6A9D1258207418B200D49B55 /* Oswald-ExtraLight.ttf */,
				6A9D1259207418B200D49B55 /* Oswald-Light.ttf */,
			);
			path = Oswald;
			sourceTree = "<group>";
		};
		6A9D127520746A3700D49B55 /* Source_Sans_Pro */ = {
			isa = PBXGroup;
			children = (
				6A9D127620746A3700D49B55 /* SourceSansPro-BlackItalic.ttf */,
				6A9D127720746A3700D49B55 /* SourceSansPro-SemiBoldItalic.ttf */,
				6A9D127820746A3700D49B55 /* SourceSansPro-Regular.ttf */,
				6A9D127920746A3700D49B55 /* SourceSansPro-Bold.ttf */,
				6A9D127A20746A3700D49B55 /* SourceSansPro-LightItalic.ttf */,
				6A9D127B20746A3700D49B55 /* SourceSansPro-Light.ttf */,
				6A9D127C20746A3700D49B55 /* SourceSansPro-Black.ttf */,
				6A9D127D20746A3700D49B55 /* OFL.txt */,
				6A9D127E20746A3700D49B55 /* SourceSansPro-ExtraLight.ttf */,
				6A9D127F20746A3700D49B55 /* SourceSansPro-BoldItalic.ttf */,
				6A9D128020746A3700D49B55 /* SourceSansPro-SemiBold.ttf */,
				6A9D128120746A3700D49B55 /* SourceSansPro-ExtraLightItalic.ttf */,
				6A9D128220746A3700D49B55 /* SourceSansPro-Italic.ttf */,
			);
			path = Source_Sans_Pro;
			sourceTree = "<group>";
		};
		6AA1AC832076CED90081188F /* TableView */ = {
			isa = PBXGroup;
			children = (
				6AA1AC842076CF220081188F /* TableViewHelper.swift */,
			);
			path = TableView;
			sourceTree = "<group>";
		};
		6AA299B3208C432B0007E074 /* Cell */ = {
			isa = PBXGroup;
			children = (
				6AA299B4208C43490007E074 /* CalendarHeaderViewCell.swift */,
				6AA299B5208C43490007E074 /* CalendarHeaderViewCell.xib */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		6AC8AC7220AF4C2800DE4F5B /* YoutubeParser */ = {
			isa = PBXGroup;
			children = (
				6AC8AC7320AF4C2800DE4F5B /* HCYoutubeParser.m */,
				6AC8AC7420AF4C2800DE4F5B /* HCYoutubeParser.h */,
			);
			path = YoutubeParser;
			sourceTree = "<group>";
		};
		6AC8AC7620AFDD1400DE4F5B /* Location */ = {
			isa = PBXGroup;
			children = (
				6AC8AC7720AFDD2900DE4F5B /* LocationManager.swift */,
			);
			path = Location;
			sourceTree = "<group>";
		};
		845BA7C12DFFDA8300AACEDC /* Recovered References */ = {
			isa = PBXGroup;
			children = (
			);
			name = "Recovered References";
			sourceTree = "<group>";
		};
		845BA7C52DFFDACA00AACEDC /* AppIcons */ = {
			isa = PBXGroup;
			children = (
				845BA7C22DFFDACA00AACEDC /* Heart.png */,
				845BA7C32DFFDACA00AACEDC /* <EMAIL> */,
				845BA7C42DFFDACA00AACEDC /* <EMAIL> */,
			);
			path = AppIcons;
			sourceTree = "<group>";
		};
		CCB93E4128DD8F6100AAA81B /* Dev */ = {
			isa = PBXGroup;
			children = (
				CC1CF8E128B763A200861409 /* GoogleService-Info.plist */,
				DE1C887C20539BC3003B5B9A /* Info.plist */,
			);
			path = Dev;
			sourceTree = "<group>";
		};
		CCB93E4228DD8F6700AAA81B /* Test */ = {
			isa = PBXGroup;
			children = (
				CC1CF8E328B763A200861409 /* GoogleService-Info.plist */,
				CC1CF8E028B7601100861409 /* Info.plist */,
			);
			path = Test;
			sourceTree = "<group>";
		};
		CCB93E4328DD8F7600AAA81B /* Prod */ = {
			isa = PBXGroup;
			children = (
				CC1CF8E228B763A200861409 /* GoogleService-Info.plist */,
				6AC0703020FDA7660036CD1D /* Info.plist */,
			);
			path = Prod;
			sourceTree = "<group>";
		};
		DE0C144622F72E4300615CC3 /* DonatePoint */ = {
			isa = PBXGroup;
			children = (
				DE0C144722F72E6B00615CC3 /* DonatePointAlert.swift */,
				DE0C144822F72E6B00615CC3 /* DonatePointAlert.xib */,
			);
			path = DonatePoint;
			sourceTree = "<group>";
		};
		DE1C887920539BC3003B5B9A /* Booking */ = {
			isa = PBXGroup;
			children = (
				DE1C887A20539BC3003B5B9A /* App */,
				6A29B739207C0D1400ED7F4C /* Class */,
				6A29B847207C0D3800ED7F4C /* Common */,
				DE1C88D22053E2B8003B5B9A /* Helper */,
				6A9D12382072D90C00D49B55 /* Lib */,
				DE1C887D20539BC3003B5B9A /* Manager */,
				DE1C888920539BC3003B5B9A /* Resource */,
				845BA7C52DFFDACA00AACEDC /* AppIcons */,
			);
			path = Booking;
			sourceTree = "<group>";
		};
		DE1C887A20539BC3003B5B9A /* App */ = {
			isa = PBXGroup;
			children = (
				CCB93E4328DD8F7600AAA81B /* Prod */,
				CCB93E4228DD8F6700AAA81B /* Test */,
				CCB93E4128DD8F6100AAA81B /* Dev */,
				DE1C887B20539BC3003B5B9A /* AppDelegate.swift */,
				DE1C88E32053FD11003B5B9A /* AppDelegate+Initial.swift */,
				6A9D124420740CC800D49B55 /* AppDelegate+Appearance.swift */,
				DE1C88DA2053FA2F003B5B9A /* Booking-dev-Bridging-Header.h */,
				DE1C88DB2053FA2F003B5B9A /* Booking-release-Bridging-Header.h */,
				DE002F5D2074DC6A0001C63D /* Localizable.strings */,
				6AC0703820FDA8430036CD1D /* InfoPlist.strings */,
			);
			path = App;
			sourceTree = "<group>";
		};
		DE1C887D20539BC3003B5B9A /* Manager */ = {
			isa = PBXGroup;
			children = (
				DEFEF51523055F9C00D107E8 /* Router */,
				6AC8AC7620AFDD1400DE4F5B /* Location */,
				DE1C887E20539BC3003B5B9A /* Network */,
				DE16D0112080F1A5004D9722 /* LanguageManager.swift */,
				845BA7CF2DFFDC1700AACEDC /* AppIconManager.swift */,
			);
			path = Manager;
			sourceTree = "<group>";
		};
		DE1C887E20539BC3003B5B9A /* Network */ = {
			isa = PBXGroup;
			children = (
				DEBE7B22207D0C05005A1F1D /* Moya */,
				DEBE7B21207D0BFC005A1F1D /* Alamofire */,
				DE2164E62073C96800938938 /* Response */,
			);
			path = Network;
			sourceTree = "<group>";
		};
		DE1C888920539BC3003B5B9A /* Resource */ = {
			isa = PBXGroup;
			children = (
				6A9D1251207418B200D49B55 /* Font */,
				DE1C88EA20540F96003B5B9A /* Image */,
				DE1C888A20539BC3003B5B9A /* Assets.xcassets */,
				DE1C888B20539BC3003B5B9A /* LaunchScreen.storyboard */,
				DE1C888D20539BC3003B5B9A /* Main.storyboard */,
			);
			path = Resource;
			sourceTree = "<group>";
		};
		DE1C88D22053E2B8003B5B9A /* Helper */ = {
			isa = PBXGroup;
			children = (
				6A812F1820889E760079ABF6 /* Images */,
				6A03F38A208106B6002FC2CD /* UIViewTransition */,
				6AA1AC832076CED90081188F /* TableView */,
				DE1C88D32053E2C1003B5B9A /* Extensions */,
			);
			path = Helper;
			sourceTree = "<group>";
		};
		DE1C88D32053E2C1003B5B9A /* Extensions */ = {
			isa = PBXGroup;
			children = (
				DE1C88D42053E2D0003B5B9A /* App+Rx.swift */,
				DE1C88E02053FBA3003B5B9A /* UserDefault+Quick.swift */,
				************************ /* Common+Ext.swift */,
				6A912F152069806C003F98B3 /* Storyboard+Quick.swift */,
				DE002F5F2074DD4C0001C63D /* String+Ext.swift */,
				6A1F60122078E71A00212F7D /* View+Layer.swift */,
				6A1F60142078EAB300212F7D /* View+Size.swift */,
				DE26416C207A56C700844F7F /* UILabel+Localization.swift */,
				DE26416F207A62DD00844F7F /* UIAlertController+Ext.swift */,
				DEBE7B18207CDF4D005A1F1D /* Date+Ext.swift */,
				6A29B899207EADCB00ED7F4C /* Device.swift */,
				DE75FC1E207F9FC200604D7E /* Location+Ext.swift */,
				6A812F1F2088A18C0079ABF6 /* Image+Ext.swift */,
				6A5B053420BE838F00E40BC0 /* Array+Ext.swift */,
				DEE1AAB422F5D11B0081FD3F /* TableView.swift */,
				DE0C144D22F731C700615CC3 /* Button.swift */,
				CC1979AE28A3AA64006AD455 /* JSONMappable.swift */,
			);
			path = Extensions;
			sourceTree = "<group>";
		};
		DE1C88EA20540F96003B5B9A /* Image */ = {
			isa = PBXGroup;
			children = (
				DE1C88EB20540F9E003B5B9A /* Intro */,
			);
			path = Image;
			sourceTree = "<group>";
		};
		DE1C88EB20540F9E003B5B9A /* Intro */ = {
			isa = PBXGroup;
			children = (
				DE1C88EC20540F9E003B5B9A /* pageDot.png */,
				DE1C88ED20540F9E003B5B9A /* <EMAIL> */,
				DE1C88EE20540F9E003B5B9A /* <EMAIL> */,
				DE1C88EF20540F9E003B5B9A /* <EMAIL> */,
				DE1C88F020540F9E003B5B9A /* <EMAIL> */,
				DE1C88F120540F9E003B5B9A /* <EMAIL> */,
				DE1C88F220540F9E003B5B9A /* <EMAIL> */,
				DE1C88F320540F9E003B5B9A /* <EMAIL> */,
				DE1C88F420540F9E003B5B9A /* <EMAIL> */,
				DE1C88F520540F9E003B5B9A /* <EMAIL> */,
				DE1C88F620540F9E003B5B9A /* <EMAIL> */,
				DE1C88F720540F9E003B5B9A /* <EMAIL> */,
				DE1C88F820540F9E003B5B9A /* <EMAIL> */,
				DE1C88F920540F9E003B5B9A /* <EMAIL> */,
				DE1C88FA20540F9E003B5B9A /* <EMAIL> */,
			);
			path = Intro;
			sourceTree = "<group>";
		};
		DE2164E62073C96800938938 /* Response */ = {
			isa = PBXGroup;
			children = (
				DE2164E72073C99B00938938 /* DDKCResponse.swift */,
				DEBE7B1E207D09C1005A1F1D /* DDKCResult.swift */,
			);
			path = Response;
			sourceTree = "<group>";
		};
		DE42737920503C080047666D = {
			isa = PBXGroup;
			children = (
				6A8945C120AA235C0021C8EB /* Booking-dev.entitlements */,
				DE1C887920539BC3003B5B9A /* Booking */,
				DE42738320503C080047666D /* Products */,
				49DB472566AD4CE3F7604927 /* Pods */,
				4430EC0BFBC372EBADE4B77F /* Frameworks */,
				845BA7C12DFFDA8300AACEDC /* Recovered References */,
			);
			sourceTree = "<group>";
		};
		DE42738320503C080047666D /* Products */ = {
			isa = PBXGroup;
			children = (
				DE42738220503C080047666D /* Booking-dev.app */,
				6A48457220F59C7100273D66 /* Beta Cinemas.app */,
				DE028A78231A419700F24593 /* Booking-test.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		DEBE7B21207D0BFC005A1F1D /* Alamofire */ = {
			isa = PBXGroup;
			children = (
				DEBE7B1B207D01EC005A1F1D /* NetworkManager.swift */,
				DEBE7B23207D0C2C005A1F1D /* NetworkManager+Film.swift */,
				DEBE7B26207D13D8005A1F1D /* NetworkManager+Ecm.swift */,
			);
			path = Alamofire;
			sourceTree = "<group>";
		};
		DEBE7B22207D0C05005A1F1D /* Moya */ = {
			isa = PBXGroup;
			children = (
				DEBDF17C22FC6CD30031A64D /* Request.swift */,
				************************ /* FilmAPI.swift */,
				DEA5BEA12073F636006D8DE1 /* CinemaAPI.swift */,
				DEB8F8A72075237A00715531 /* AccountAPI.swift */,
				************************ /* EcmAPI.swift */,
				************************ /* CityAPI.swift */,
				************************ /* VoucherAPI.swift */,
			);
			path = Moya;
			sourceTree = "<group>";
		};
		DEE1AAA622F5CFAF0081FD3F /* Voucher */ = {
			isa = PBXGroup;
			children = (
				DEE1AAAD22F5D0760081FD3F /* Cell */,
				DEE1AAA722F5CFC80081FD3F /* MyVoucherViewController.swift */,
				DEE1AAA822F5CFC80081FD3F /* MyVoucherViewController.xib */,
				DEE1AAB722F5DEDC0081FD3F /* AddVoucherViewController.swift */,
				DEE1AAB822F5DEDC0081FD3F /* AddVoucherViewController.xib */,
				DEE1AABD22F5DEEE0081FD3F /* DonateVoucherViewController.swift */,
				DEE1AABE22F5DEEE0081FD3F /* DonateVoucherViewController.xib */,
				DEE1AAC322F5DF000081FD3F /* HistoryVoucherViewController.swift */,
				DEE1AAC422F5DF000081FD3F /* HistoryVoucherViewController.xib */,
				DEFA4C8422FEC04C00725391 /* FreeVoucherViewController.swift */,
				DEFA4C8522FEC04C00725391 /* FreeVoucherViewController.xib */,
				DEFA6B042302C5CD00C0C156 /* ScanBarCodeViewController.swift */,
				DEFA6B052302C5CD00C0C156 /* ScanBarCodeViewController.xib */,
				DEFA01F623040831003839C5 /* UseVoucherViewController.swift */,
				DEFA01F723040831003839C5 /* UseVoucherViewController.xib */,
			);
			path = Voucher;
			sourceTree = "<group>";
		};
		DEE1AAAD22F5D0760081FD3F /* Cell */ = {
			isa = PBXGroup;
			children = (
				DEE1AAAE22F5D09A0081FD3F /* MyVoucherTableViewCell.swift */,
				DEE1AAAF22F5D09A0081FD3F /* MyVoucherTableViewCell.xib */,
				DEE1AAC922F5E1E00081FD3F /* HistoryVoucherTableViewCell.swift */,
				DEE1AACA22F5E1E00081FD3F /* HistoryVoucherTableViewCell.xib */,
				DEE1AACF22F5E1F40081FD3F /* DonateVoucherTableViewCell.swift */,
				DEE1AAD022F5E1F40081FD3F /* DonateVoucherTableViewCell.xib */,
				DE0C144022F72B4E00615CC3 /* HistoryPointTableViewCell.swift */,
				DE0C144122F72B4E00615CC3 /* HistoryPointTableViewCell.xib */,
				DEFA4C8A22FEC10900725391 /* FreeVoucherTableViewCell.swift */,
				DEFA4C8B22FEC10900725391 /* FreeVoucherTableViewCell.xib */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		DEFA6AFD2302AEF700C0C156 /* Alert */ = {
			isa = PBXGroup;
			children = (
				DEFA6AFE2302AF1700C0C156 /* CustomizeAlert.swift */,
				DEFA6AFF2302AF1700C0C156 /* CustomizeAlert.xib */,
			);
			path = Alert;
			sourceTree = "<group>";
		};
		DEFAB21822F4861F0019231D /* MainTab */ = {
			isa = PBXGroup;
			children = (
				DEFAB21922F486680019231D /* MainTabContainer.swift */,
				DE0B85502334BDFC009D6E32 /* MyTabbarViewController.swift */,
				DE0B85542334BF34009D6E32 /* UIMaker.swift */,
			);
			path = MainTab;
			sourceTree = "<group>";
		};
		DEFAB21C22F489B80019231D /* Other */ = {
			isa = PBXGroup;
			children = (
				DEFAB22922F49C120019231D /* Cell */,
				DEFAB22322F48AB50019231D /* TabOtherViewController.swift */,
				DEFAB22422F48AB50019231D /* TabOtherViewController.xib */,
			);
			path = Other;
			sourceTree = "<group>";
		};
		DEFAB22922F49C120019231D /* Cell */ = {
			isa = PBXGroup;
			children = (
				DEFAB22A22F49C280019231D /* OthersCollectionViewCell.swift */,
				DEFAB22B22F49C280019231D /* OthersCollectionViewCell.xib */,
			);
			path = Cell;
			sourceTree = "<group>";
		};
		DEFEF51523055F9C00D107E8 /* Router */ = {
			isa = PBXGroup;
			children = (
				DEFEF51623055FBA00D107E8 /* RouteManager.swift */,
			);
			path = Router;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		6A48447020F59C7100273D66 /* Booking-pro */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 6A48456F20F59C7100273D66 /* Build configuration list for PBXNativeTarget "Booking-pro" */;
			buildPhases = (
				8FBC132A09FE49738F4689DF /* [CP] Check Pods Manifest.lock */,
				6A48447420F59C7100273D66 /* Sources */,
				6A48452820F59C7100273D66 /* Frameworks */,
				6A48452B20F59C7100273D66 /* Resources */,
				6A48456C20F59C7100273D66 /* ShellScript */,
				6A48456D20F59C7100273D66 /* Embed Frameworks */,
				4DA5C1DE8FD63ED1877CEB5E /* [CP] Embed Pods Frameworks */,
				76B714AC3EDF05CE86F681C6 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "Booking-pro";
			productName = ProjectBase;
			productReference = 6A48457220F59C7100273D66 /* Beta Cinemas.app */;
			productType = "com.apple.product-type.application";
		};
		DE028938231A419700F24593 /* Booking-test */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DE028A75231A419700F24593 /* Build configuration list for PBXNativeTarget "Booking-test" */;
			buildPhases = (
				6F13E9CB584E641C12064FE8 /* [CP] Check Pods Manifest.lock */,
				DE02893C231A419700F24593 /* Sources */,
				DE028A15231A419700F24593 /* Frameworks */,
				DE028A18231A419700F24593 /* Resources */,
				DE028A72231A419700F24593 /* ShellScript */,
				DE028A73231A419700F24593 /* Embed Frameworks */,
				4F9C62A6802640DBD1E50B81 /* [CP] Embed Pods Frameworks */,
				75C513F640568ADC0C95E884 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "Booking-test";
			productName = ProjectBase;
			productReference = DE028A78231A419700F24593 /* Booking-test.app */;
			productType = "com.apple.product-type.application";
		};
		DE42738120503C080047666D /* Booking-dev */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = DE42739420503C080047666D /* Build configuration list for PBXNativeTarget "Booking-dev" */;
			buildPhases = (
				9E854E8D26E1F20EC5F658E3 /* [CP] Check Pods Manifest.lock */,
				DE42737E20503C080047666D /* Sources */,
				DE42737F20503C080047666D /* Frameworks */,
				DE42738020503C080047666D /* Resources */,
				6ADFE41B20B3EAB800E06C44 /* ShellScript */,
				6AC52E4F20B4CBCC00D9BCAC /* Embed Frameworks */,
				3E48583C5A6A79107F1515A0 /* [CP] Embed Pods Frameworks */,
				5B15EF3CA480ADC4822EA7D3 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = "Booking-dev";
			productName = ProjectBase;
			productReference = DE42738220503C080047666D /* Booking-dev.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		DE42737A20503C080047666D /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastSwiftUpdateCheck = 0920;
				LastUpgradeCheck = 0940;
				ORGANIZATIONNAME = ddkc;
				TargetAttributes = {
					6A48447020F59C7100273D66 = {
						ProvisioningStyle = Automatic;
					};
					DE028938231A419700F24593 = {
						ProvisioningStyle = Automatic;
					};
					DE42738120503C080047666D = {
						CreatedOnToolsVersion = 9.2;
						LastSwiftMigration = 0920;
						ProvisioningStyle = Automatic;
						SystemCapabilities = {
							com.apple.BackgroundModes = {
								enabled = 1;
							};
							com.apple.Push = {
								enabled = 1;
							};
							com.apple.SafariKeychain = {
								enabled = 1;
							};
						};
					};
				};
			};
			buildConfigurationList = DE42737D20503C080047666D /* Build configuration list for PBXProject "Booking" */;
			compatibilityVersion = "Xcode 8.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				vi,
			);
			mainGroup = DE42737920503C080047666D;
			productRefGroup = DE42738320503C080047666D /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				DE42738120503C080047666D /* Booking-dev */,
				6A48447020F59C7100273D66 /* Booking-pro */,
				DE028938231A419700F24593 /* Booking-test */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		6A48452B20F59C7100273D66 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6A48452C20F59C7100273D66 /* OFL.txt in Resources */,
				DEFA01FB23040831003839C5 /* UseVoucherViewController.xib in Resources */,
				DEFA4C8F22FEC10900725391 /* FreeVoucherTableViewCell.xib in Resources */,
				6A48452D20F59C7100273D66 /* Film.storyboard in Resources */,
				6A48452E20F59C7100273D66 /* TimeListCollectionCell.xib in Resources */,
				CC1CF8E528B7663200861409 /* GoogleService-Info.plist in Resources */,
				6A48452F20F59C7100273D66 /* SourceSansPro-BoldItalic.ttf in Resources */,
				6A48453020F59C7100273D66 /* SourceSansPro-BlackItalic.ttf in Resources */,
				6A48453120F59C7100273D66 /* SourceSansPro-ExtraLightItalic.ttf in Resources */,
				DEE1AAC222F5DEEE0081FD3F /* DonateVoucherViewController.xib in Resources */,
				DEFF6B6C230A8C22009B04B2 /* CinemasViewController.xib in Resources */,
				6A48453220F59C7100273D66 /* SourceSansPro-Regular.ttf in Resources */,
				845BA7C92DFFDACA00AACEDC /* <EMAIL> in Resources */,
				845BA7CA2DFFDACA00AACEDC /* Heart.png in Resources */,
				845BA7CB2DFFDACA00AACEDC /* <EMAIL> in Resources */,
				6A48453320F59C7100273D66 /* Oswald-ExtraLight.ttf in Resources */,
				DE04A2BD2309A1250096ED85 /* CinemaFilmTimeTableCell.xib in Resources */,
				6A48453420F59C7100273D66 /* Oswald-SemiBold.ttf in Resources */,
				6A48453520F59C7100273D66 /* LaunchScreen.storyboard in Resources */,
				DE0C144C22F72E6B00615CC3 /* DonatePointAlert.xib in Resources */,
				DE0C143922F6757100615CC3 /* IntroToFriendViewController.xib in Resources */,
				6A48453620F59C7100273D66 /* SourceSansPro-ExtraLight.ttf in Resources */,
				6A48453720F59C7100273D66 /* VoucherTableCell.xib in Resources */,
				6A48453820F59C7100273D66 /* <EMAIL> in Resources */,
				6A48453920F59C7100273D66 /* SourceSansPro-Bold.ttf in Resources */,
				6A48453A20F59C7100273D66 /* <EMAIL> in Resources */,
				6A48453B20F59C7100273D66 /* Assets.xcassets in Resources */,
				DEE1AAD422F5E1F40081FD3F /* DonateVoucherTableViewCell.xib in Resources */,
				6A48453C20F59C7100273D66 /* CalendarHeaderViewCell.xib in Resources */,
				6A48453D20F59C7100273D66 /* <EMAIL> in Resources */,
				DE0C144522F72B4E00615CC3 /* HistoryPointTableViewCell.xib in Resources */,
				6A48453E20F59C7100273D66 /* <EMAIL> in Resources */,
				6A48453F20F59C7100273D66 /* <EMAIL> in Resources */,
				DEFA4C8922FEC04C00725391 /* FreeVoucherViewController.xib in Resources */,
				6A48454020F59C7100273D66 /* <EMAIL> in Resources */,
				6A48454120F59C7100273D66 /* <EMAIL> in Resources */,
				DEFF6B73230A8E34009B04B2 /* CinemaCollectionViewCell.xib in Resources */,
				6A48454220F59C7100273D66 /* Member.storyboard in Resources */,
				DEFA6B032302AF1700C0C156 /* CustomizeAlert.xib in Resources */,
				6A48454320F59C7100273D66 /* Oswald-Medium.ttf in Resources */,
				6A48454420F59C7100273D66 /* CheckboxTableCell.xib in Resources */,
				6A48454520F59C7100273D66 /* NewsAndDealsCell.xib in Resources */,
				6A48454620F59C7100273D66 /* SourceSansPro-Light.ttf in Resources */,
				6A48454720F59C7100273D66 /* <EMAIL> in Resources */,
				6A48454820F59C7100273D66 /* SourceSansPro-SemiBold.ttf in Resources */,
				DEE1AAC822F5DF000081FD3F /* HistoryVoucherViewController.xib in Resources */,
				6A48454920F59C7100273D66 /* Localizable.strings in Resources */,
				6A48454A20F59C7100273D66 /* pageDot.png in Resources */,
				6A48454B20F59C7100273D66 /* Main.storyboard in Resources */,
				6A48454C20F59C7100273D66 /* FilmTimeTableViewCell.xib in Resources */,
				6A48454D20F59C7100273D66 /* Oswald-Regular.ttf in Resources */,
				6A48454E20F59C7100273D66 /* NewAndDealsView.xib in Resources */,
				DEFAB22822F48AB50019231D /* TabOtherViewController.xib in Resources */,
				6A48454F20F59C7100273D66 /* SwitchTableCell.xib in Resources */,
				6A48455020F59C7100273D66 /* <EMAIL> in Resources */,
				DEFF15E222F86E9D0000D2C9 /* NewHomeViewController.xib in Resources */,
				6A48455120F59C7100273D66 /* Authen.storyboard in Resources */,
				6A48455220F59C7100273D66 /* Home.storyboard in Resources */,
				DEFA6B092302C5CD00C0C156 /* ScanBarCodeViewController.xib in Resources */,
				6A48455320F59C7100273D66 /* SettingTableCell.xib in Resources */,
				6A48455420F59C7100273D66 /* <EMAIL> in Resources */,
				DEFAB22F22F49C280019231D /* OthersCollectionViewCell.xib in Resources */,
				DEE1AAAC22F5CFC80081FD3F /* MyVoucherViewController.xib in Resources */,
				6A48455520F59C7100273D66 /* Cinema.storyboard in Resources */,
				6A48455620F59C7100273D66 /* <EMAIL> in Resources */,
				6AC0703620FDA8430036CD1D /* InfoPlist.strings in Resources */,
				DEE1AAB322F5D09A0081FD3F /* MyVoucherTableViewCell.xib in Resources */,
				6A48455720F59C7100273D66 /* NotificationTableViewCell.xib in Resources */,
				6A48455820F59C7100273D66 /* Payment.storyboard in Resources */,
				6A48455920F59C7100273D66 /* NewsTableViewCell.xib in Resources */,
				6A48455A20F59C7100273D66 /* Setting.storyboard in Resources */,
				6A48455B20F59C7100273D66 /* TitleHeaderView.xib in Resources */,
				6A48455C20F59C7100273D66 /* BannerCardView.xib in Resources */,
				6A48455D20F59C7100273D66 /* SourceSansPro-Black.ttf in Resources */,
				DEE1AACE22F5E1E00081FD3F /* HistoryVoucherTableViewCell.xib in Resources */,
				6A48455E20F59C7100273D66 /* <EMAIL> in Resources */,
				6A48455F20F59C7100273D66 /* FilmItemTableCell.xib in Resources */,
				DEFF6B79230A8E6A009B04B2 /* CinemaByProvinceTableViewCell.xib in Resources */,
				6A48456020F59C7100273D66 /* Oswald-Light.ttf in Resources */,
				6A48456120F59C7100273D66 /* SourceSansPro-LightItalic.ttf in Resources */,
				6A48456220F59C7100273D66 /* <EMAIL> in Resources */,
				6A48456320F59C7100273D66 /* AreaCinemaHeaderView.xib in Resources */,
				DEE1AABC22F5DEDC0081FD3F /* AddVoucherViewController.xib in Resources */,
				6A48456420F59C7100273D66 /* <EMAIL> in Resources */,
				6A48456520F59C7100273D66 /* Oswald-Bold.ttf in Resources */,
				6A48456620F59C7100273D66 /* SourceSansPro-Italic.ttf in Resources */,
				6A48456720F59C7100273D66 /* CinemaPriceHeaderView.xib in Resources */,
				DEFF15E822F8752D0000D2C9 /* HomeCollectionViewCell.xib in Resources */,
				6A48456820F59C7100273D66 /* SeatCollectionHeaderView.xib in Resources */,
				6A48456920F59C7100273D66 /* SourceSansPro-SemiBoldItalic.ttf in Resources */,
				DE084C4D233910F700705A0F /* NotificationCell.xib in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DE028A18231A419700F24593 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DE028A19231A419700F24593 /* OFL.txt in Resources */,
				CC1CF8E628B7663700861409 /* GoogleService-Info.plist in Resources */,
				DE028A1A231A419700F24593 /* Film.storyboard in Resources */,
				DE028A1B231A419700F24593 /* DonatePointAlert.xib in Resources */,
				DE028A1C231A419700F24593 /* TimeListCollectionCell.xib in Resources */,
				DE028A1D231A419700F24593 /* SourceSansPro-BoldItalic.ttf in Resources */,
				DE028A1E231A419700F24593 /* DonateVoucherTableViewCell.xib in Resources */,
				DE028A1F231A419700F24593 /* SourceSansPro-BlackItalic.ttf in Resources */,
				DE028A20231A419700F24593 /* SourceSansPro-ExtraLightItalic.ttf in Resources */,
				DE028A21231A419700F24593 /* UseVoucherViewController.xib in Resources */,
				DE028A22231A419700F24593 /* SourceSansPro-Regular.ttf in Resources */,
				DE028A23231A419700F24593 /* Oswald-ExtraLight.ttf in Resources */,
				845BA7CC2DFFDACA00AACEDC /* <EMAIL> in Resources */,
				845BA7CD2DFFDACA00AACEDC /* Heart.png in Resources */,
				845BA7CE2DFFDACA00AACEDC /* <EMAIL> in Resources */,
				DE028A24231A419700F24593 /* Oswald-SemiBold.ttf in Resources */,
				DE028A25231A419700F24593 /* HistoryVoucherTableViewCell.xib in Resources */,
				DE028A26231A419700F24593 /* LaunchScreen.storyboard in Resources */,
				DE028A27231A419700F24593 /* HistoryVoucherViewController.xib in Resources */,
				DE028A28231A419700F24593 /* ScanBarCodeViewController.xib in Resources */,
				DE028A29231A419700F24593 /* SourceSansPro-ExtraLight.ttf in Resources */,
				DE028A2A231A419700F24593 /* VoucherTableCell.xib in Resources */,
				DE028A2B231A419700F24593 /* <EMAIL> in Resources */,
				DE028A2C231A419700F24593 /* SourceSansPro-Bold.ttf in Resources */,
				DE028A2D231A419700F24593 /* <EMAIL> in Resources */,
				DE028A2E231A419700F24593 /* Assets.xcassets in Resources */,
				DE028A2F231A419700F24593 /* CalendarHeaderViewCell.xib in Resources */,
				DE028A30231A419700F24593 /* <EMAIL> in Resources */,
				DE028A31231A419700F24593 /* <EMAIL> in Resources */,
				DE028A32231A419700F24593 /* <EMAIL> in Resources */,
				DE028A33231A419700F24593 /* <EMAIL> in Resources */,
				DE028A34231A419700F24593 /* <EMAIL> in Resources */,
				DE028A35231A419700F24593 /* Member.storyboard in Resources */,
				DE028A36231A419700F24593 /* IntroToFriendViewController.xib in Resources */,
				DE028A37231A419700F24593 /* Oswald-Medium.ttf in Resources */,
				DE028A38231A419700F24593 /* CheckboxTableCell.xib in Resources */,
				DE028A39231A419700F24593 /* CustomizeAlert.xib in Resources */,
				DE028A3A231A419700F24593 /* OthersCollectionViewCell.xib in Resources */,
				DE028A3B231A419700F24593 /* HistoryPointTableViewCell.xib in Resources */,
				DE028A3C231A419700F24593 /* CinemaCollectionViewCell.xib in Resources */,
				DE028A3D231A419700F24593 /* NewsAndDealsCell.xib in Resources */,
				DE028A3E231A419700F24593 /* CinemaFilmTimeTableCell.xib in Resources */,
				DE028A3F231A419700F24593 /* SourceSansPro-Light.ttf in Resources */,
				DE028A40231A419700F24593 /* <EMAIL> in Resources */,
				DE028A41231A419700F24593 /* SourceSansPro-SemiBold.ttf in Resources */,
				DE028A42231A419700F24593 /* Localizable.strings in Resources */,
				DE028A43231A419700F24593 /* CinemaByProvinceTableViewCell.xib in Resources */,
				DE028A44231A419700F24593 /* HomeCollectionViewCell.xib in Resources */,
				DE084C4E233910F700705A0F /* NotificationCell.xib in Resources */,
				DE028A45231A419700F24593 /* pageDot.png in Resources */,
				DE028A46231A419700F24593 /* Main.storyboard in Resources */,
				DE028A47231A419700F24593 /* MyVoucherViewController.xib in Resources */,
				DE028A48231A419700F24593 /* FilmTimeTableViewCell.xib in Resources */,
				DE028A49231A419700F24593 /* InfoPlist.strings in Resources */,
				DE028A4A231A419700F24593 /* Oswald-Regular.ttf in Resources */,
				DE028A4B231A419700F24593 /* NewAndDealsView.xib in Resources */,
				DE028A4C231A419700F24593 /* SwitchTableCell.xib in Resources */,
				DE028A4D231A419700F24593 /* <EMAIL> in Resources */,
				DE028A4E231A419700F24593 /* DonateVoucherViewController.xib in Resources */,
				DE028A4F231A419700F24593 /* Authen.storyboard in Resources */,
				DE028A50231A419700F24593 /* Home.storyboard in Resources */,
				DE028A51231A419700F24593 /* NewHomeViewController.xib in Resources */,
				DE028A52231A419700F24593 /* SettingTableCell.xib in Resources */,
				DE028A53231A419700F24593 /* <EMAIL> in Resources */,
				DE028A54231A419700F24593 /* FreeVoucherViewController.xib in Resources */,
				DE028A55231A419700F24593 /* Cinema.storyboard in Resources */,
				DE028A56231A419700F24593 /* <EMAIL> in Resources */,
				DE028A57231A419700F24593 /* NotificationTableViewCell.xib in Resources */,
				DE028A59231A419700F24593 /* Payment.storyboard in Resources */,
				DE028A5A231A419700F24593 /* NewsTableViewCell.xib in Resources */,
				DE028A5B231A419700F24593 /* CinemasViewController.xib in Resources */,
				DE028A5C231A419700F24593 /* Setting.storyboard in Resources */,
				DE028A5D231A419700F24593 /* TitleHeaderView.xib in Resources */,
				DE028A5E231A419700F24593 /* BannerCardView.xib in Resources */,
				DE028A5F231A419700F24593 /* SourceSansPro-Black.ttf in Resources */,
				DE028A60231A419700F24593 /* <EMAIL> in Resources */,
				DE028A61231A419700F24593 /* FilmItemTableCell.xib in Resources */,
				DE028A62231A419700F24593 /* Oswald-Light.ttf in Resources */,
				DE028A63231A419700F24593 /* TabOtherViewController.xib in Resources */,
				DE028A64231A419700F24593 /* SourceSansPro-LightItalic.ttf in Resources */,
				DE028A65231A419700F24593 /* <EMAIL> in Resources */,
				DE028A66231A419700F24593 /* AreaCinemaHeaderView.xib in Resources */,
				DE028A67231A419700F24593 /* <EMAIL> in Resources */,
				DE028A68231A419700F24593 /* Oswald-Bold.ttf in Resources */,
				DE028A69231A419700F24593 /* SourceSansPro-Italic.ttf in Resources */,
				DE028A6A231A419700F24593 /* MyVoucherTableViewCell.xib in Resources */,
				DE028A6B231A419700F24593 /* AddVoucherViewController.xib in Resources */,
				DE028A6C231A419700F24593 /* CinemaPriceHeaderView.xib in Resources */,
				DE028A6D231A419700F24593 /* SeatCollectionHeaderView.xib in Resources */,
				DE028A6E231A419700F24593 /* FreeVoucherTableViewCell.xib in Resources */,
				DE028A6F231A419700F24593 /* SourceSansPro-SemiBoldItalic.ttf in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DE42738020503C080047666D /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6A9D129120746A3700D49B55 /* OFL.txt in Resources */,
				6A29B7B9207C0D1500ED7F4C /* Film.storyboard in Resources */,
				DE0C144B22F72E6B00615CC3 /* DonatePointAlert.xib in Resources */,
				6A661B54208FF9AA00CFC976 /* TimeListCollectionCell.xib in Resources */,
				6A9D129520746A3700D49B55 /* SourceSansPro-BoldItalic.ttf in Resources */,
				CC1CF8E428B7661C00861409 /* GoogleService-Info.plist in Resources */,
				DEE1AAD322F5E1F40081FD3F /* DonateVoucherTableViewCell.xib in Resources */,
				6A9D128320746A3700D49B55 /* SourceSansPro-BlackItalic.ttf in Resources */,
				6A9D129920746A3700D49B55 /* SourceSansPro-ExtraLightItalic.ttf in Resources */,
				DEFA01FA23040831003839C5 /* UseVoucherViewController.xib in Resources */,
				6A9D128720746A3700D49B55 /* SourceSansPro-Regular.ttf in Resources */,
				6A9D1264207418B200D49B55 /* Oswald-ExtraLight.ttf in Resources */,
				845BA7C62DFFDACA00AACEDC /* <EMAIL> in Resources */,
				845BA7C72DFFDACA00AACEDC /* Heart.png in Resources */,
				845BA7C82DFFDACA00AACEDC /* <EMAIL> in Resources */,
				6A9D125C207418B200D49B55 /* Oswald-SemiBold.ttf in Resources */,
				DEE1AACD22F5E1E00081FD3F /* HistoryVoucherTableViewCell.xib in Resources */,
				DE1C889720539BC3003B5B9A /* LaunchScreen.storyboard in Resources */,
				DEE1AAC722F5DF000081FD3F /* HistoryVoucherViewController.xib in Resources */,
				DEFA6B082302C5CD00C0C156 /* ScanBarCodeViewController.xib in Resources */,
				6A9D129320746A3700D49B55 /* SourceSansPro-ExtraLight.ttf in Resources */,
				6A812F26208939E60079ABF6 /* VoucherTableCell.xib in Resources */,
				DE1C891520540F9E003B5B9A /* <EMAIL> in Resources */,
				6A9D128920746A3700D49B55 /* SourceSansPro-Bold.ttf in Resources */,
				DE1C890120540F9E003B5B9A /* <EMAIL> in Resources */,
				DE1C889620539BC3003B5B9A /* Assets.xcassets in Resources */,
				6AA299B8208C43490007E074 /* CalendarHeaderViewCell.xib in Resources */,
				DE1C891720540F9E003B5B9A /* <EMAIL> in Resources */,
				DE1C890920540F9E003B5B9A /* <EMAIL> in Resources */,
				DE1C891320540F9E003B5B9A /* <EMAIL> in Resources */,
				DE1C890D20540F9E003B5B9A /* <EMAIL> in Resources */,
				DE1C890B20540F9E003B5B9A /* <EMAIL> in Resources */,
				6A29B7E3207C0D1500ED7F4C /* Member.storyboard in Resources */,
				DE0C143822F6757100615CC3 /* IntroToFriendViewController.xib in Resources */,
				6A9D125E207418B200D49B55 /* Oswald-Medium.ttf in Resources */,
				6A29B891207EA57E00ED7F4C /* CheckboxTableCell.xib in Resources */,
				DEFA6B022302AF1700C0C156 /* CustomizeAlert.xib in Resources */,
				DEFAB22E22F49C280019231D /* OthersCollectionViewCell.xib in Resources */,
				DE0C144422F72B4E00615CC3 /* HistoryPointTableViewCell.xib in Resources */,
				DEFF6B72230A8E34009B04B2 /* CinemaCollectionViewCell.xib in Resources */,
				6A29B881207C2AB100ED7F4C /* NewsAndDealsCell.xib in Resources */,
				DE04A2BC2309A1250096ED85 /* CinemaFilmTimeTableCell.xib in Resources */,
				6A9D128D20746A3700D49B55 /* SourceSansPro-Light.ttf in Resources */,
				DE1C88FF20540F9E003B5B9A /* <EMAIL> in Resources */,
				6A9D129720746A3700D49B55 /* SourceSansPro-SemiBold.ttf in Resources */,
				DE002F5A2074DC6A0001C63D /* Localizable.strings in Resources */,
				DEFF6B78230A8E6A009B04B2 /* CinemaByProvinceTableViewCell.xib in Resources */,
				DEFF15E722F8752D0000D2C9 /* HomeCollectionViewCell.xib in Resources */,
				DE084C4C233910F700705A0F /* NotificationCell.xib in Resources */,
				DE1C88FB20540F9E003B5B9A /* pageDot.png in Resources */,
				DE1C889820539BC3003B5B9A /* Main.storyboard in Resources */,
				DEE1AAAB22F5CFC80081FD3F /* MyVoucherViewController.xib in Resources */,
				6AA13FE6208C97A3007809AC /* FilmTimeTableViewCell.xib in Resources */,
				6AC0703520FDA8430036CD1D /* InfoPlist.strings in Resources */,
				6A9D1260207418B200D49B55 /* Oswald-Regular.ttf in Resources */,
				6A29B845207C0D1500ED7F4C /* NewAndDealsView.xib in Resources */,
				6A29B897207EA59D00ED7F4C /* SwitchTableCell.xib in Resources */,
				DE1C88FD20540F9E003B5B9A /* <EMAIL> in Resources */,
				DEE1AAC122F5DEEE0081FD3F /* DonateVoucherViewController.xib in Resources */,
				6A29B7DB207C0D1500ED7F4C /* Authen.storyboard in Resources */,
				6A29B7A7207C0D1500ED7F4C /* Home.storyboard in Resources */,
				DEFF15E122F86E9D0000D2C9 /* NewHomeViewController.xib in Resources */,
				6A29B894207EA58C00ED7F4C /* SettingTableCell.xib in Resources */,
				DE1C890720540F9E003B5B9A /* <EMAIL> in Resources */,
				DEFA4C8822FEC04C00725391 /* FreeVoucherViewController.xib in Resources */,
				6A29B7D1207C0D1500ED7F4C /* Cinema.storyboard in Resources */,
				DE1C890520540F9E003B5B9A /* <EMAIL> in Resources */,
				DE16D02420810BCA004D9722 /* NotificationTableViewCell.xib in Resources */,
				6A29B7D5207C0D1500ED7F4C /* Payment.storyboard in Resources */,
				6A29B7AF207C0D1500ED7F4C /* NewsTableViewCell.xib in Resources */,
				DEFF6B6B230A8C22009B04B2 /* CinemasViewController.xib in Resources */,
				6A29B7FF207C0D1500ED7F4C /* Setting.storyboard in Resources */,
				6A29B7C9207C0D1500ED7F4C /* TitleHeaderView.xib in Resources */,
				6A29B7A3207C0D1500ED7F4C /* BannerCardView.xib in Resources */,
				6A9D128F20746A3700D49B55 /* SourceSansPro-Black.ttf in Resources */,
				DE1C891120540F9E003B5B9A /* <EMAIL> in Resources */,
				6AA299AB208BF2A20007E074 /* FilmItemTableCell.xib in Resources */,
				6A9D1266207418B200D49B55 /* Oswald-Light.ttf in Resources */,
				DEFAB22722F48AB50019231D /* TabOtherViewController.xib in Resources */,
				6A9D128B20746A3700D49B55 /* SourceSansPro-LightItalic.ttf in Resources */,
				DE1C890F20540F9E003B5B9A /* <EMAIL> in Resources */,
				6A29B7C7207C0D1500ED7F4C /* AreaCinemaHeaderView.xib in Resources */,
				DE1C890320540F9E003B5B9A /* <EMAIL> in Resources */,
				6A9D125A207418B200D49B55 /* Oswald-Bold.ttf in Resources */,
				6A9D129B20746A3700D49B55 /* SourceSansPro-Italic.ttf in Resources */,
				DEE1AAB222F5D09A0081FD3F /* MyVoucherTableViewCell.xib in Resources */,
				DEE1AABB22F5DEDC0081FD3F /* AddVoucherViewController.xib in Resources */,
				6A44992F20B626C8006B37A3 /* CinemaPriceHeaderView.xib in Resources */,
				6A31AAFE20BB2AED00DC59B3 /* SeatCollectionHeaderView.xib in Resources */,
				DEFA4C8E22FEC10900725391 /* FreeVoucherTableViewCell.xib in Resources */,
				6A9D128520746A3700D49B55 /* SourceSansPro-SemiBoldItalic.ttf in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		3E48583C5A6A79107F1515A0 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Booking-dev/Pods-Booking-dev-frameworks.sh",
				"${BUILT_PRODUCTS_DIR}/ActiveLabel/ActiveLabel.framework",
				"${BUILT_PRODUCTS_DIR}/Alamofire/Alamofire.framework",
				"${BUILT_PRODUCTS_DIR}/AlamofireImage/AlamofireImage.framework",
				"${BUILT_PRODUCTS_DIR}/DRPLoadingSpinner/DRPLoadingSpinner.framework",
				"${BUILT_PRODUCTS_DIR}/DynamicBlurView/DynamicBlurView.framework",
				"${BUILT_PRODUCTS_DIR}/FirebaseABTesting/FirebaseABTesting.framework",
				"${BUILT_PRODUCTS_DIR}/FirebaseCore/FirebaseCore.framework",
				"${BUILT_PRODUCTS_DIR}/FirebaseCoreInternal/FirebaseCoreInternal.framework",
				"${BUILT_PRODUCTS_DIR}/FirebaseInstallations/FirebaseInstallations.framework",
				"${BUILT_PRODUCTS_DIR}/FirebaseMessaging/FirebaseMessaging.framework",
				"${BUILT_PRODUCTS_DIR}/FirebaseRemoteConfig/FirebaseRemoteConfig.framework",
				"${BUILT_PRODUCTS_DIR}/FirebaseRemoteConfigInterop/FirebaseRemoteConfigInterop.framework",
				"${BUILT_PRODUCTS_DIR}/FirebaseSharedSwift/FirebaseSharedSwift.framework",
				"${BUILT_PRODUCTS_DIR}/GoogleDataTransport/GoogleDataTransport.framework",
				"${BUILT_PRODUCTS_DIR}/GoogleUtilities/GoogleUtilities.framework",
				"${BUILT_PRODUCTS_DIR}/IQKeyboardManagerSwift/IQKeyboardManagerSwift.framework",
				"${BUILT_PRODUCTS_DIR}/ImageSlideshow/ImageSlideshow.framework",
				"${BUILT_PRODUCTS_DIR}/KMNavigationBarTransition/KMNavigationBarTransition.framework",
				"${BUILT_PRODUCTS_DIR}/Mixpanel-swift/Mixpanel.framework",
				"${BUILT_PRODUCTS_DIR}/Moya/Moya.framework",
				"${BUILT_PRODUCTS_DIR}/ObjectMapper/ObjectMapper.framework",
				"${BUILT_PRODUCTS_DIR}/PKHUD/PKHUD.framework",
				"${BUILT_PRODUCTS_DIR}/PopupDialog/PopupDialog.framework",
				"${BUILT_PRODUCTS_DIR}/PromisesObjC/FBLPromises.framework",
				"${BUILT_PRODUCTS_DIR}/PullToRefreshKit/PullToRefreshKit.framework",
				"${BUILT_PRODUCTS_DIR}/RSBarcodes_Swift/RSBarcodes_Swift.framework",
				"${BUILT_PRODUCTS_DIR}/ReCaptcha/ReCaptcha.framework",
				"${BUILT_PRODUCTS_DIR}/Result/Result.framework",
				"${BUILT_PRODUCTS_DIR}/RxCocoa/RxCocoa.framework",
				"${BUILT_PRODUCTS_DIR}/RxRelay/RxRelay.framework",
				"${BUILT_PRODUCTS_DIR}/RxSwift/RxSwift.framework",
				"${BUILT_PRODUCTS_DIR}/SDWebImage/SDWebImage.framework",
				"${BUILT_PRODUCTS_DIR}/SignalRSwift/SignalRSwift.framework",
				"${BUILT_PRODUCTS_DIR}/Starscream/Starscream.framework",
				"${BUILT_PRODUCTS_DIR}/SwiftDate/SwiftDate.framework",
				"${BUILT_PRODUCTS_DIR}/SwiftSignalRClient/SwiftSignalRClient.framework",
				"${BUILT_PRODUCTS_DIR}/UITableView+FDTemplateLayoutCell/UITableView_FDTemplateLayoutCell.framework",
				"${BUILT_PRODUCTS_DIR}/VisualEffectView/VisualEffectView.framework",
				"${BUILT_PRODUCTS_DIR}/iCarousel/iCarousel.framework",
				"${BUILT_PRODUCTS_DIR}/nanopb/nanopb.framework",
				"${BUILT_PRODUCTS_DIR}/youtube-ios-player-helper/youtube_ios_player_helper.framework",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/FBAEMKit/FBAEMKit.framework/FBAEMKit",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/FBSDKCoreKit/FBSDKCoreKit.framework/FBSDKCoreKit",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/FBSDKCoreKit_Basics/FBSDKCoreKit_Basics.framework/FBSDKCoreKit_Basics",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/FBSDKLoginKit/FBSDKLoginKit.framework/FBSDKLoginKit",
			);
			name = "[CP] Embed Pods Frameworks";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/ActiveLabel.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Alamofire.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/AlamofireImage.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/DRPLoadingSpinner.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/DynamicBlurView.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebaseABTesting.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebaseCore.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebaseCoreInternal.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebaseInstallations.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebaseMessaging.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebaseRemoteConfig.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebaseRemoteConfigInterop.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebaseSharedSwift.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/GoogleDataTransport.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/GoogleUtilities.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/IQKeyboardManagerSwift.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/ImageSlideshow.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/KMNavigationBarTransition.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Mixpanel.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Moya.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/ObjectMapper.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/PKHUD.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/PopupDialog.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FBLPromises.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/PullToRefreshKit.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/RSBarcodes_Swift.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/ReCaptcha.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Result.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/RxCocoa.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/RxRelay.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/RxSwift.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/SDWebImage.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/SignalRSwift.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Starscream.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/SwiftDate.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/SwiftSignalRClient.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/UITableView_FDTemplateLayoutCell.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/VisualEffectView.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/iCarousel.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/nanopb.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/youtube_ios_player_helper.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FBAEMKit.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FBSDKCoreKit.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FBSDKCoreKit_Basics.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FBSDKLoginKit.framework",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Booking-dev/Pods-Booking-dev-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		4DA5C1DE8FD63ED1877CEB5E /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Booking-pro/Pods-Booking-pro-frameworks.sh",
				"${BUILT_PRODUCTS_DIR}/ActiveLabel/ActiveLabel.framework",
				"${BUILT_PRODUCTS_DIR}/Alamofire/Alamofire.framework",
				"${BUILT_PRODUCTS_DIR}/AlamofireImage/AlamofireImage.framework",
				"${BUILT_PRODUCTS_DIR}/DRPLoadingSpinner/DRPLoadingSpinner.framework",
				"${BUILT_PRODUCTS_DIR}/DynamicBlurView/DynamicBlurView.framework",
				"${BUILT_PRODUCTS_DIR}/FirebaseABTesting/FirebaseABTesting.framework",
				"${BUILT_PRODUCTS_DIR}/FirebaseCore/FirebaseCore.framework",
				"${BUILT_PRODUCTS_DIR}/FirebaseCoreInternal/FirebaseCoreInternal.framework",
				"${BUILT_PRODUCTS_DIR}/FirebaseInstallations/FirebaseInstallations.framework",
				"${BUILT_PRODUCTS_DIR}/FirebaseMessaging/FirebaseMessaging.framework",
				"${BUILT_PRODUCTS_DIR}/FirebaseRemoteConfig/FirebaseRemoteConfig.framework",
				"${BUILT_PRODUCTS_DIR}/FirebaseRemoteConfigInterop/FirebaseRemoteConfigInterop.framework",
				"${BUILT_PRODUCTS_DIR}/FirebaseSharedSwift/FirebaseSharedSwift.framework",
				"${BUILT_PRODUCTS_DIR}/GoogleDataTransport/GoogleDataTransport.framework",
				"${BUILT_PRODUCTS_DIR}/GoogleUtilities/GoogleUtilities.framework",
				"${BUILT_PRODUCTS_DIR}/IQKeyboardManagerSwift/IQKeyboardManagerSwift.framework",
				"${BUILT_PRODUCTS_DIR}/ImageSlideshow/ImageSlideshow.framework",
				"${BUILT_PRODUCTS_DIR}/KMNavigationBarTransition/KMNavigationBarTransition.framework",
				"${BUILT_PRODUCTS_DIR}/Mixpanel-swift/Mixpanel.framework",
				"${BUILT_PRODUCTS_DIR}/Moya/Moya.framework",
				"${BUILT_PRODUCTS_DIR}/ObjectMapper/ObjectMapper.framework",
				"${BUILT_PRODUCTS_DIR}/PKHUD/PKHUD.framework",
				"${BUILT_PRODUCTS_DIR}/PopupDialog/PopupDialog.framework",
				"${BUILT_PRODUCTS_DIR}/PromisesObjC/FBLPromises.framework",
				"${BUILT_PRODUCTS_DIR}/PullToRefreshKit/PullToRefreshKit.framework",
				"${BUILT_PRODUCTS_DIR}/RSBarcodes_Swift/RSBarcodes_Swift.framework",
				"${BUILT_PRODUCTS_DIR}/ReCaptcha/ReCaptcha.framework",
				"${BUILT_PRODUCTS_DIR}/Result/Result.framework",
				"${BUILT_PRODUCTS_DIR}/RxCocoa/RxCocoa.framework",
				"${BUILT_PRODUCTS_DIR}/RxRelay/RxRelay.framework",
				"${BUILT_PRODUCTS_DIR}/RxSwift/RxSwift.framework",
				"${BUILT_PRODUCTS_DIR}/SDWebImage/SDWebImage.framework",
				"${BUILT_PRODUCTS_DIR}/SignalRSwift/SignalRSwift.framework",
				"${BUILT_PRODUCTS_DIR}/Starscream/Starscream.framework",
				"${BUILT_PRODUCTS_DIR}/SwiftDate/SwiftDate.framework",
				"${BUILT_PRODUCTS_DIR}/SwiftSignalRClient/SwiftSignalRClient.framework",
				"${BUILT_PRODUCTS_DIR}/UITableView+FDTemplateLayoutCell/UITableView_FDTemplateLayoutCell.framework",
				"${BUILT_PRODUCTS_DIR}/VisualEffectView/VisualEffectView.framework",
				"${BUILT_PRODUCTS_DIR}/iCarousel/iCarousel.framework",
				"${BUILT_PRODUCTS_DIR}/nanopb/nanopb.framework",
				"${BUILT_PRODUCTS_DIR}/youtube-ios-player-helper/youtube_ios_player_helper.framework",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/FBAEMKit/FBAEMKit.framework/FBAEMKit",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/FBSDKCoreKit/FBSDKCoreKit.framework/FBSDKCoreKit",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/FBSDKCoreKit_Basics/FBSDKCoreKit_Basics.framework/FBSDKCoreKit_Basics",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/FBSDKLoginKit/FBSDKLoginKit.framework/FBSDKLoginKit",
			);
			name = "[CP] Embed Pods Frameworks";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/ActiveLabel.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Alamofire.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/AlamofireImage.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/DRPLoadingSpinner.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/DynamicBlurView.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebaseABTesting.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebaseCore.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebaseCoreInternal.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebaseInstallations.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebaseMessaging.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebaseRemoteConfig.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebaseRemoteConfigInterop.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebaseSharedSwift.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/GoogleDataTransport.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/GoogleUtilities.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/IQKeyboardManagerSwift.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/ImageSlideshow.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/KMNavigationBarTransition.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Mixpanel.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Moya.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/ObjectMapper.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/PKHUD.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/PopupDialog.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FBLPromises.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/PullToRefreshKit.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/RSBarcodes_Swift.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/ReCaptcha.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Result.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/RxCocoa.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/RxRelay.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/RxSwift.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/SDWebImage.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/SignalRSwift.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Starscream.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/SwiftDate.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/SwiftSignalRClient.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/UITableView_FDTemplateLayoutCell.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/VisualEffectView.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/iCarousel.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/nanopb.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/youtube_ios_player_helper.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FBAEMKit.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FBSDKCoreKit.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FBSDKCoreKit_Basics.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FBSDKLoginKit.framework",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Booking-pro/Pods-Booking-pro-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		4F9C62A6802640DBD1E50B81 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Booking-test/Pods-Booking-test-frameworks.sh",
				"${BUILT_PRODUCTS_DIR}/ActiveLabel/ActiveLabel.framework",
				"${BUILT_PRODUCTS_DIR}/Alamofire/Alamofire.framework",
				"${BUILT_PRODUCTS_DIR}/AlamofireImage/AlamofireImage.framework",
				"${BUILT_PRODUCTS_DIR}/DRPLoadingSpinner/DRPLoadingSpinner.framework",
				"${BUILT_PRODUCTS_DIR}/DynamicBlurView/DynamicBlurView.framework",
				"${BUILT_PRODUCTS_DIR}/FirebaseABTesting/FirebaseABTesting.framework",
				"${BUILT_PRODUCTS_DIR}/FirebaseCore/FirebaseCore.framework",
				"${BUILT_PRODUCTS_DIR}/FirebaseCoreInternal/FirebaseCoreInternal.framework",
				"${BUILT_PRODUCTS_DIR}/FirebaseInstallations/FirebaseInstallations.framework",
				"${BUILT_PRODUCTS_DIR}/FirebaseMessaging/FirebaseMessaging.framework",
				"${BUILT_PRODUCTS_DIR}/FirebaseRemoteConfig/FirebaseRemoteConfig.framework",
				"${BUILT_PRODUCTS_DIR}/FirebaseRemoteConfigInterop/FirebaseRemoteConfigInterop.framework",
				"${BUILT_PRODUCTS_DIR}/FirebaseSharedSwift/FirebaseSharedSwift.framework",
				"${BUILT_PRODUCTS_DIR}/GoogleDataTransport/GoogleDataTransport.framework",
				"${BUILT_PRODUCTS_DIR}/GoogleUtilities/GoogleUtilities.framework",
				"${BUILT_PRODUCTS_DIR}/IQKeyboardManagerSwift/IQKeyboardManagerSwift.framework",
				"${BUILT_PRODUCTS_DIR}/ImageSlideshow/ImageSlideshow.framework",
				"${BUILT_PRODUCTS_DIR}/KMNavigationBarTransition/KMNavigationBarTransition.framework",
				"${BUILT_PRODUCTS_DIR}/Mixpanel-swift/Mixpanel.framework",
				"${BUILT_PRODUCTS_DIR}/Moya/Moya.framework",
				"${BUILT_PRODUCTS_DIR}/ObjectMapper/ObjectMapper.framework",
				"${BUILT_PRODUCTS_DIR}/PKHUD/PKHUD.framework",
				"${BUILT_PRODUCTS_DIR}/PopupDialog/PopupDialog.framework",
				"${BUILT_PRODUCTS_DIR}/PromisesObjC/FBLPromises.framework",
				"${BUILT_PRODUCTS_DIR}/PullToRefreshKit/PullToRefreshKit.framework",
				"${BUILT_PRODUCTS_DIR}/RSBarcodes_Swift/RSBarcodes_Swift.framework",
				"${BUILT_PRODUCTS_DIR}/ReCaptcha/ReCaptcha.framework",
				"${BUILT_PRODUCTS_DIR}/Result/Result.framework",
				"${BUILT_PRODUCTS_DIR}/RxCocoa/RxCocoa.framework",
				"${BUILT_PRODUCTS_DIR}/RxRelay/RxRelay.framework",
				"${BUILT_PRODUCTS_DIR}/RxSwift/RxSwift.framework",
				"${BUILT_PRODUCTS_DIR}/SDWebImage/SDWebImage.framework",
				"${BUILT_PRODUCTS_DIR}/SignalRSwift/SignalRSwift.framework",
				"${BUILT_PRODUCTS_DIR}/Starscream/Starscream.framework",
				"${BUILT_PRODUCTS_DIR}/SwiftDate/SwiftDate.framework",
				"${BUILT_PRODUCTS_DIR}/SwiftSignalRClient/SwiftSignalRClient.framework",
				"${BUILT_PRODUCTS_DIR}/UITableView+FDTemplateLayoutCell/UITableView_FDTemplateLayoutCell.framework",
				"${BUILT_PRODUCTS_DIR}/VisualEffectView/VisualEffectView.framework",
				"${BUILT_PRODUCTS_DIR}/iCarousel/iCarousel.framework",
				"${BUILT_PRODUCTS_DIR}/nanopb/nanopb.framework",
				"${BUILT_PRODUCTS_DIR}/youtube-ios-player-helper/youtube_ios_player_helper.framework",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/FBAEMKit/FBAEMKit.framework/FBAEMKit",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/FBSDKCoreKit/FBSDKCoreKit.framework/FBSDKCoreKit",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/FBSDKCoreKit_Basics/FBSDKCoreKit_Basics.framework/FBSDKCoreKit_Basics",
				"${PODS_XCFRAMEWORKS_BUILD_DIR}/FBSDKLoginKit/FBSDKLoginKit.framework/FBSDKLoginKit",
			);
			name = "[CP] Embed Pods Frameworks";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/ActiveLabel.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Alamofire.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/AlamofireImage.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/DRPLoadingSpinner.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/DynamicBlurView.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebaseABTesting.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebaseCore.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebaseCoreInternal.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebaseInstallations.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebaseMessaging.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebaseRemoteConfig.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebaseRemoteConfigInterop.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FirebaseSharedSwift.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/GoogleDataTransport.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/GoogleUtilities.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/IQKeyboardManagerSwift.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/ImageSlideshow.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/KMNavigationBarTransition.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Mixpanel.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Moya.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/ObjectMapper.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/PKHUD.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/PopupDialog.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FBLPromises.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/PullToRefreshKit.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/RSBarcodes_Swift.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/ReCaptcha.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Result.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/RxCocoa.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/RxRelay.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/RxSwift.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/SDWebImage.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/SignalRSwift.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/Starscream.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/SwiftDate.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/SwiftSignalRClient.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/UITableView_FDTemplateLayoutCell.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/VisualEffectView.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/iCarousel.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/nanopb.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/youtube_ios_player_helper.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FBAEMKit.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FBSDKCoreKit.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FBSDKCoreKit_Basics.framework",
				"${TARGET_BUILD_DIR}/${FRAMEWORKS_FOLDER_PATH}/FBSDKLoginKit.framework",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Booking-test/Pods-Booking-test-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		5B15EF3CA480ADC4822EA7D3 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Booking-dev/Pods-Booking-dev-resources.sh",
				"${PODS_ROOT}/GoogleMaps/Maps/Frameworks/GoogleMaps.framework/Resources/GoogleMaps.bundle",
				"${PODS_ROOT}/GooglePlaces/Frameworks/GooglePlaces.framework/Resources/GooglePlaces.bundle",
			);
			name = "[CP] Copy Pods Resources";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/GoogleMaps.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/GooglePlaces.bundle",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Booking-dev/Pods-Booking-dev-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		6A48456C20F59C7100273D66 /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Fabric/run\" da44f5feea4b0f520db5482d4c4c4243137ab224 1d0e2d4a17bd8be3a46bc33024627875cef8b450235b3ee5d29cf11ec91d251d";
		};
		6ADFE41B20B3EAB800E06C44 /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Fabric/run\" da44f5feea4b0f520db5482d4c4c4243137ab224 1d0e2d4a17bd8be3a46bc33024627875cef8b450235b3ee5d29cf11ec91d251d\n";
		};
		6F13E9CB584E641C12064FE8 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Booking-test-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		75C513F640568ADC0C95E884 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Booking-test/Pods-Booking-test-resources.sh",
				"${PODS_ROOT}/GoogleMaps/Maps/Frameworks/GoogleMaps.framework/Resources/GoogleMaps.bundle",
				"${PODS_ROOT}/GooglePlaces/Frameworks/GooglePlaces.framework/Resources/GooglePlaces.bundle",
			);
			name = "[CP] Copy Pods Resources";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/GoogleMaps.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/GooglePlaces.bundle",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Booking-test/Pods-Booking-test-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		76B714AC3EDF05CE86F681C6 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-Booking-pro/Pods-Booking-pro-resources.sh",
				"${PODS_ROOT}/GoogleMaps/Maps/Frameworks/GoogleMaps.framework/Resources/GoogleMaps.bundle",
				"${PODS_ROOT}/GooglePlaces/Frameworks/GooglePlaces.framework/Resources/GooglePlaces.bundle",
			);
			name = "[CP] Copy Pods Resources";
			outputPaths = (
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/GoogleMaps.bundle",
				"${TARGET_BUILD_DIR}/${UNLOCALIZED_RESOURCES_FOLDER_PATH}/GooglePlaces.bundle",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-Booking-pro/Pods-Booking-pro-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		8FBC132A09FE49738F4689DF /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Booking-pro-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		9E854E8D26E1F20EC5F658E3 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-Booking-dev-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		DE028A72231A419700F24593 /* ShellScript */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Fabric/run\" da44f5feea4b0f520db5482d4c4c4243137ab224 1d0e2d4a17bd8be3a46bc33024627875cef8b450235b3ee5d29cf11ec91d251d";
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		6A48447420F59C7100273D66 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6A48447520F59C7100273D66 /* CinemaFilmTimeTableCell.swift in Sources */,
				DE04A2BA230998DA0096ED85 /* ShortUser.swift in Sources */,
				6A48447620F59C7100273D66 /* TopicDetailModel.swift in Sources */,
				6A22620021120E3F0029B300 /* UpdatePasswordViewController.swift in Sources */,
				8439E1AE2CEB75F400D7A69D /* ConfirmVipZoomViewController.swift in Sources */,
				6A48447720F59C7100273D66 /* ConfirmPassViewController.swift in Sources */,
				6A48447820F59C7100273D66 /* YoutubeViewController.swift in Sources */,
				6A48447920F59C7100273D66 /* GradientView.swift in Sources */,
				6A48447A20F59C7100273D66 /* TicketBookingViewController.swift in Sources */,
				6A48447B20F59C7100273D66 /* NewsAndDealsCell.swift in Sources */,
				6A48447C20F59C7100273D66 /* SeatCollectionHeaderView.swift in Sources */,
				DEFA4C8722FEC04C00725391 /* FreeVoucherViewController.swift in Sources */,
				6A48447D20F59C7100273D66 /* LanguageManager.swift in Sources */,
				6A48447E20F59C7100273D66 /* MemberViewController.swift in Sources */,
				6A48447F20F59C7100273D66 /* CinemaDetailViewController.swift in Sources */,
				6A48448020F59C7100273D66 /* LoginModel.swift in Sources */,
				84DD28A92AE227AF000C5712 /* TransactionDetailCell.swift in Sources */,
				6A48448120F59C7100273D66 /* HCYoutubeParser.m in Sources */,
				6A48448220F59C7100273D66 /* BaseNavigationViewController.swift in Sources */,
				6A48448320F59C7100273D66 /* Common+Ext.swift in Sources */,
				CC39C9A328AB75BE0015018A /* Tracking.swift in Sources */,
				6A48448420F59C7100273D66 /* AppDelegate+Initial.swift in Sources */,
				6A48448520F59C7100273D66 /* FilmDetailViewController.swift in Sources */,
				DEFA6B0C230313BB00C0C156 /* VoucherGot.swift in Sources */,
				6A48448620F59C7100273D66 /* TableViewHelper.swift in Sources */,
				6A48448720F59C7100273D66 /* Global.swift in Sources */,
				6A48448820F59C7100273D66 /* ForgotPassViewController.swift in Sources */,
				6A48448920F59C7100273D66 /* NewsAndDealsViewController.swift in Sources */,
				6A48448A20F59C7100273D66 /* TopicModel.swift in Sources */,
				DEFA01F52303F197003839C5 /* PointHistory.swift in Sources */,
				6A48448B20F59C7100273D66 /* RegisterResultViewController.swift in Sources */,
				6A48448C20F59C7100273D66 /* SeatBookingModel.swift in Sources */,
				DEFA4C8D22FEC10900725391 /* FreeVoucherTableViewCell.swift in Sources */,
				6A48448D20F59C7100273D66 /* LocalizableLabel.swift in Sources */,
				6A48448E20F59C7100273D66 /* FilmModel.swift in Sources */,
				6A48448F20F59C7100273D66 /* DDKCResult.swift in Sources */,
				6A48449020F59C7100273D66 /* NewsDetailViewController.swift in Sources */,
				6A48449120F59C7100273D66 /* PolicyModel.swift in Sources */,
				6A48449220F59C7100273D66 /* ShowFilmModel.swift in Sources */,
				6A48449320F59C7100273D66 /* FAQViewController.swift in Sources */,
				6A48449420F59C7100273D66 /* TransactionDetailViewController.swift in Sources */,
				6A48449520F59C7100273D66 /* Color.swift in Sources */,
				6A48449620F59C7100273D66 /* NotificationModel.swift in Sources */,
				6A48449720F59C7100273D66 /* Fonts.swift in Sources */,
				6A48449820F59C7100273D66 /* ListPosterUrlModel.swift in Sources */,
				6A48449920F59C7100273D66 /* CityAPI.swift in Sources */,
				6A48449A20F59C7100273D66 /* FilmDescriptionTableCell.swift in Sources */,
				6A48449B20F59C7100273D66 /* PaymentPointViewController.swift in Sources */,
				DE0C143F22F7276600615CC3 /* AttributeString.swift in Sources */,
				6A48449C20F59C7100273D66 /* FilmAPI.swift in Sources */,
				6A48449D20F59C7100273D66 /* LocationManager.swift in Sources */,
				6A48449E20F59C7100273D66 /* AppDelegate.swift in Sources */,
				DEFF15E022F86E9D0000D2C9 /* NewHomeViewController.swift in Sources */,
				6A48449F20F59C7100273D66 /* MemberCardCell.swift in Sources */,
				6A4844A020F59C7100273D66 /* BaseRequestModel.swift in Sources */,
				6A4844A120F59C7100273D66 /* ListFilmWatchedViewController.swift in Sources */,
				DEFA6B072302C5CD00C0C156 /* ScanBarCodeViewController.swift in Sources */,
				6A4844A220F59C7100273D66 /* NewsModel.swift in Sources */,
				6A4844A320F59C7100273D66 /* AreaCinemaHeaderView.swift in Sources */,
				6A4844A420F59C7100273D66 /* Repository.swift in Sources */,
				6A4844A520F59C7100273D66 /* ShowCinemaModel.swift in Sources */,
				6A4844A620F59C7100273D66 /* VoucherViewController.swift in Sources */,
				6A4844A720F59C7100273D66 /* TransactionHistoryCell.swift in Sources */,
				6A4844A820F59C7100273D66 /* AccountInfoViewController.swift in Sources */,
				6A4844A920F59C7100273D66 /* SlideMenuViewController.swift in Sources */,
				6A4844AA20F59C7100273D66 /* FilmChooseTimeViewController.swift in Sources */,
				6A4844AB20F59C7100273D66 /* ListAllCinemasViewController.swift in Sources */,
				DEFEF51823055FBA00D107E8 /* RouteManager.swift in Sources */,
				6A4844AC20F59C7100273D66 /* TransactionHistoryDetailModel.swift in Sources */,
				6A4844AD20F59C7100273D66 /* OtherViewController.swift in Sources */,
				6A4844AE20F59C7100273D66 /* ListFilmViewController.swift in Sources */,
				6A4844AF20F59C7100273D66 /* SettingTableCell.swift in Sources */,
				6A4844B020F59C7100273D66 /* CinemaTableViewCell.swift in Sources */,
				6A4844B120F59C7100273D66 /* CheckboxTableCell.swift in Sources */,
				6A4844B220F59C7100273D66 /* GraphShitingViewController.swift in Sources */,
				6A4844B320F59C7100273D66 /* SelectRegionViewController.swift in Sources */,
				6A4844B420F59C7100273D66 /* LoginViewController.swift in Sources */,
				6A4844B520F59C7100273D66 /* ListSeatModel.swift in Sources */,
				6A4844B620F59C7100273D66 /* Utils.swift in Sources */,
				6A4844B720F59C7100273D66 /* ChooseCinemasViewController.swift in Sources */,
				6A4844B820F59C7100273D66 /* Storyboard+Quick.swift in Sources */,
				6A4844B920F59C7100273D66 /* Image+Ext.swift in Sources */,
				6A4844BA20F59C7100273D66 /* CalendarHeaderView.swift in Sources */,
				DEFAB22D22F49C280019231D /* OthersCollectionViewCell.swift in Sources */,
				6A4844BB20F59C7100273D66 /* CinemaModel.swift in Sources */,
				6A4844BC20F59C7100273D66 /* CardGiftViewController.swift in Sources */,
				6A4844BD20F59C7100273D66 /* MenuItemCell.swift in Sources */,
				6A4844BE20F59C7100273D66 /* RecruitmentViewController.swift in Sources */,
				6A4844BF20F59C7100273D66 /* String+Ext.swift in Sources */,
				6A4844C020F59C7100273D66 /* ProfileViewController.swift in Sources */,
				6A4844C120F59C7100273D66 /* FilmTimeTableViewCell.swift in Sources */,
				6A4844C220F59C7100273D66 /* NewsTableViewCell.swift in Sources */,
				CC39C9A828ACED650015018A /* ComboListModel.swift in Sources */,
				6A4844C320F59C7100273D66 /* CinemaProvinceModel.swift in Sources */,
				6A4844C420F59C7100273D66 /* Config.swift in Sources */,
				DEE1AAD222F5E1F40081FD3F /* DonateVoucherTableViewCell.swift in Sources */,
				6A4844C520F59C7100273D66 /* TimeListCollectionCell.swift in Sources */,
				6A4844C620F59C7100273D66 /* PolicyContentModel.swift in Sources */,
				6A4844C720F59C7100273D66 /* AvatarImageModel.swift in Sources */,
				DE0C143722F6757100615CC3 /* IntroToFriendViewController.swift in Sources */,
				DEE1AAC022F5DEEE0081FD3F /* DonateVoucherViewController.swift in Sources */,
				6A4844C820F59C7100273D66 /* LocalizableButton.swift in Sources */,
				6A4844C920F59C7100273D66 /* Device.swift in Sources */,
				DEE1AABA22F5DEDC0081FD3F /* AddVoucherViewController.swift in Sources */,
				6A4844CA20F59C7100273D66 /* SettingViewController.swift in Sources */,
				6A4844CB20F59C7100273D66 /* AccountAPI.swift in Sources */,
				DEE1AAAA22F5CFC80081FD3F /* MyVoucherViewController.swift in Sources */,
				DEBA9FE0230412BE00322EE4 /* AppParams.swift in Sources */,
				6A4844CC20F59C7100273D66 /* ListFilmGenreModel.swift in Sources */,
				6A4844CD20F59C7100273D66 /* BaseModel.swift in Sources */,
				6A4844CE20F59C7100273D66 /* GradientButton.swift in Sources */,
				6A4844CF20F59C7100273D66 /* NearCinemaTableViewCell.swift in Sources */,
				DEFAB21B22F4866B0019231D /* MainTabContainer.swift in Sources */,
				6A4844D020F59C7100273D66 /* Array+Ext.swift in Sources */,
				DEE1AACC22F5E1E00081FD3F /* HistoryVoucherTableViewCell.swift in Sources */,
				6A4844D120F59C7100273D66 /* StickyHeaderViewController.swift in Sources */,
				6A4844D220F59C7100273D66 /* SeatModel.swift in Sources */,
				6A4844D320F59C7100273D66 /* TranferHistoryViewController.swift in Sources */,
				6A4844D420F59C7100273D66 /* TicketType.swift in Sources */,
				DEFAB22622F48AB50019231D /* TabOtherViewController.swift in Sources */,
				DEBDF17E22FC6CD40031A64D /* Request.swift in Sources */,
				6A4844D520F59C7100273D66 /* NotificationDetailViewController.swift in Sources */,
				CC39C9AC28ACEF220015018A /* DataBookingModel.swift in Sources */,
				6A4844D620F59C7100273D66 /* VoucherAPI.swift in Sources */,
				DEFF6B6A230A8C22009B04B2 /* CinemasViewController.swift in Sources */,
				6A4844D720F59C7100273D66 /* ChangePassViewController.swift in Sources */,
				6A4844D820F59C7100273D66 /* RoundTextField.swift in Sources */,
				6A4844D920F59C7100273D66 /* FAQDetailViewController.swift in Sources */,
				6A4844DA20F59C7100273D66 /* CinemaPriceViewController.swift in Sources */,
				6A4844DB20F59C7100273D66 /* BaseViewController.swift in Sources */,
				6A4844DC20F59C7100273D66 /* VersionInfoViewController.swift in Sources */,
				6A4844DD20F59C7100273D66 /* UIAlertController+Ext.swift in Sources */,
				6A4844DE20F59C7100273D66 /* CardModel.swift in Sources */,
				6A4844DF20F59C7100273D66 /* Date+Ext.swift in Sources */,
				6A4844E020F59C7100273D66 /* NewModel.swift in Sources */,
				6A4844E120F59C7100273D66 /* RoundView.swift in Sources */,
				6A4844E220F59C7100273D66 /* AppDelegate+Appearance.swift in Sources */,
				6A4844E320F59C7100273D66 /* UserModel.swift in Sources */,
				6A4844E420F59C7100273D66 /* NotificationTableViewCell.swift in Sources */,
				6A4844E520F59C7100273D66 /* VourcherCouponViewController.swift in Sources */,
				6A4844E620F59C7100273D66 /* SSASideMenu.swift in Sources */,
				DE0C144A22F72E6B00615CC3 /* DonatePointAlert.swift in Sources */,
				6A4844E720F59C7100273D66 /* EcmAPI.swift in Sources */,
				6A4844E820F59C7100273D66 /* FilmItemTableCell.swift in Sources */,
				6A4844E920F59C7100273D66 /* DDKCResponse.swift in Sources */,
				6A4844EA20F59C7100273D66 /* PickerTextField.swift in Sources */,
				6A4844EB20F59C7100273D66 /* VoucherTableCell.swift in Sources */,
				6A4844EC20F59C7100273D66 /* SwitchTableCell.swift in Sources */,
				6A4844ED20F59C7100273D66 /* PaymentViewController.swift in Sources */,
				6A4844EE20F59C7100273D66 /* SeatCollectionViewCell.swift in Sources */,
				6A4844EF20F59C7100273D66 /* TimeListView.swift in Sources */,
				6A4844F020F59C7100273D66 /* TopViewController.swift in Sources */,
				6A4844F120F59C7100273D66 /* ChooseSeatViewController.swift in Sources */,
				6A4844F220F59C7100273D66 /* NetworkManager+Ecm.swift in Sources */,
				CC1979B028A3AA64006AD455 /* JSONMappable.swift in Sources */,
				6A4844F320F59C7100273D66 /* CreateBookingModel.swift in Sources */,
				6A4844F420F59C7100273D66 /* VoucherModel.swift in Sources */,
				6A4844F520F59C7100273D66 /* NetworkManager+Film.swift in Sources */,
				6A4844F620F59C7100273D66 /* DateTextField.swift in Sources */,
				DEFA6B012302AF1700C0C156 /* CustomizeAlert.swift in Sources */,
				6A4844F720F59C7100273D66 /* FilmBookingViewController.swift in Sources */,
				6A4844F820F59C7100273D66 /* VerticalLayoutButton.swift in Sources */,
				6A4844F920F59C7100273D66 /* RegisterModel.swift in Sources */,
				6A4844FA20F59C7100273D66 /* View+Size.swift in Sources */,
				6A4844FB20F59C7100273D66 /* TitleHeaderView.swift in Sources */,
				DEFF6B71230A8E34009B04B2 /* CinemaCollectionViewCell.swift in Sources */,
				DE0C144F22F731C700615CC3 /* Button.swift in Sources */,
				DEFF15E622F8752D0000D2C9 /* HomeCollectionViewCell.swift in Sources */,
				6A4844FC20F59C7100273D66 /* CinemaPriceHeaderView.swift in Sources */,
				6A4844FD20F59C7100273D66 /* TransparentAnimator.swift in Sources */,
				6A4844FE20F59C7100273D66 /* ScrollPager.swift in Sources */,
				6A4844FF20F59C7100273D66 /* RoundImageView.swift in Sources */,
				6A48450020F59C7100273D66 /* Images.swift in Sources */,
				6A48450120F59C7100273D66 /* App+Rx.swift in Sources */,
				6A48450220F59C7100273D66 /* ScreenModel.swift in Sources */,
				6A48450320F59C7100273D66 /* AssetLibrary.swift in Sources */,
				6A48450420F59C7100273D66 /* NotificationViewController.swift in Sources */,
				6A48450520F59C7100273D66 /* DashView.swift in Sources */,
				6A48450620F59C7100273D66 /* Constants.swift in Sources */,
				DEE1AAB622F5D11B0081FD3F /* TableView.swift in Sources */,
				DEE1AAC622F5DF000081FD3F /* HistoryVoucherViewController.swift in Sources */,
				6A48450720F59C7100273D66 /* Owner.swift in Sources */,
				6A48450820F59C7100273D66 /* CinemaAPI.swift in Sources */,
				6A48450920F59C7100273D66 /* RewardPointsViewController.swift in Sources */,
				6A48450A20F59C7100273D66 /* ConfirmBookAgeViewController.swift in Sources */,
				6A48450B20F59C7100273D66 /* NetworkManager.swift in Sources */,
				6A48450C20F59C7100273D66 /* RegisterVoucherModel.swift in Sources */,
				DEFF6B77230A8E6A009B04B2 /* CinemaByProvinceTableViewCell.swift in Sources */,
				6A48450D20F59C7100273D66 /* ShowModel.swift in Sources */,
				DEFA01F923040831003839C5 /* UseVoucherViewController.swift in Sources */,
				6A48450E20F59C7100273D66 /* ColumnLayout.swift in Sources */,
				6A48450F20F59C7100273D66 /* RoundButton.swift in Sources */,
				DEFED17B2301B6AD0006688A /* FreeVoucher.swift in Sources */,
				6A48451020F59C7100273D66 /* PointModel.swift in Sources */,
				DE0C143C22F6E81600615CC3 /* DashBorderView.swift in Sources */,
				6A48451120F59C7100273D66 /* NotificationTableCell.swift in Sources */,
				DE0C144322F72B4E00615CC3 /* HistoryPointTableViewCell.swift in Sources */,
				6A48451220F59C7100273D66 /* RegisterViewController.swift in Sources */,
				6A48451320F59C7100273D66 /* Notification.swift in Sources */,
				6A48451420F59C7100273D66 /* ImagePickerController.swift in Sources */,
				6A48451520F59C7100273D66 /* MemberCardViewController.swift in Sources */,
				6A48451620F59C7100273D66 /* GradientImageView.swift in Sources */,
				DE084C4A233910F700705A0F /* NotificationCell.swift in Sources */,
				6A48451720F59C7100273D66 /* BannerCardView.swift in Sources */,
				6A48451820F59C7100273D66 /* Location+Ext.swift in Sources */,
				6A48451920F59C7100273D66 /* ListNewsViewController.swift in Sources */,
				DE028937231A3FD200F24593 /* HomePage.swift in Sources */,
				6A48451A20F59C7100273D66 /* CouponViewController.swift in Sources */,
				6A48451C20F59C7100273D66 /* TransactionHistoryModel.swift in Sources */,
				DE0B85562334BF34009D6E32 /* UIMaker.swift in Sources */,
				DEE1AAB122F5D09A0081FD3F /* MyVoucherTableViewCell.swift in Sources */,
				6A48451D20F59C7100273D66 /* HomeViewController.swift in Sources */,
				DE0B85522334BDFC009D6E32 /* MyTabbarViewController.swift in Sources */,
				6A48451E20F59C7100273D66 /* UserDefault+Quick.swift in Sources */,
				6A48451F20F59C7100273D66 /* CardClassModel.swift in Sources */,
				6A48452020F59C7100273D66 /* UILabel+Localization.swift in Sources */,
				6A48452120F59C7100273D66 /* LocalizableProtocol.swift in Sources */,
				6A48452220F59C7100273D66 /* InputTextField.swift in Sources */,
				DEFA01F22303F13D003839C5 /* VoucherHistory.swift in Sources */,
				6A48452320F59C7100273D66 /* CityModel.swift in Sources */,
				845BA7D22DFFDC1700AACEDC /* AppIconManager.swift in Sources */,
				6A48452420F59C7100273D66 /* NewAndDealsView.swift in Sources */,
				6A48452520F59C7100273D66 /* DateFormat.swift in Sources */,
				DEFA4C7A22FE6E6D00725391 /* Banner.swift in Sources */,
				6A48452620F59C7100273D66 /* CalendarHeaderViewCell.swift in Sources */,
				6A48452720F59C7100273D66 /* View+Layer.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DE02893C231A419700F24593 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				DE02893D231A419700F24593 /* CinemaFilmTimeTableCell.swift in Sources */,
				DE02893E231A419700F24593 /* TopicDetailModel.swift in Sources */,
				DE02893F231A419700F24593 /* UpdatePasswordViewController.swift in Sources */,
				DE028940231A419700F24593 /* ConfirmPassViewController.swift in Sources */,
				8439E1AF2CEB75F400D7A69D /* ConfirmVipZoomViewController.swift in Sources */,
				DE028941231A419700F24593 /* YoutubeViewController.swift in Sources */,
				DE028942231A419700F24593 /* ShortUser.swift in Sources */,
				DE028943231A419700F24593 /* GradientView.swift in Sources */,
				DE028944231A419700F24593 /* TicketBookingViewController.swift in Sources */,
				DE028945231A419700F24593 /* NewsAndDealsCell.swift in Sources */,
				DE028946231A419700F24593 /* SeatCollectionHeaderView.swift in Sources */,
				DE028947231A419700F24593 /* FreeVoucherViewController.swift in Sources */,
				DE028948231A419700F24593 /* LanguageManager.swift in Sources */,
				DE028949231A419700F24593 /* MemberViewController.swift in Sources */,
				DE02894A231A419700F24593 /* CinemaDetailViewController.swift in Sources */,
				DE02894B231A419700F24593 /* LoginModel.swift in Sources */,
				84DD28AA2AE227AF000C5712 /* TransactionDetailCell.swift in Sources */,
				DE02894C231A419700F24593 /* HCYoutubeParser.m in Sources */,
				DE02894D231A419700F24593 /* BaseNavigationViewController.swift in Sources */,
				DE02894E231A419700F24593 /* Common+Ext.swift in Sources */,
				CC39C9A428AB75BE0015018A /* Tracking.swift in Sources */,
				DE02894F231A419700F24593 /* AppDelegate+Initial.swift in Sources */,
				DE028950231A419700F24593 /* FilmDetailViewController.swift in Sources */,
				DE028951231A419700F24593 /* TableViewHelper.swift in Sources */,
				DE028952231A419700F24593 /* Global.swift in Sources */,
				DE028953231A419700F24593 /* ForgotPassViewController.swift in Sources */,
				DE028954231A419700F24593 /* NewsAndDealsViewController.swift in Sources */,
				DE028955231A419700F24593 /* TopicModel.swift in Sources */,
				DE028956231A419700F24593 /* RegisterResultViewController.swift in Sources */,
				DE028957231A419700F24593 /* SeatBookingModel.swift in Sources */,
				DE028958231A419700F24593 /* FreeVoucherTableViewCell.swift in Sources */,
				DE028959231A419700F24593 /* LocalizableLabel.swift in Sources */,
				DE02895A231A419700F24593 /* FilmModel.swift in Sources */,
				DE02895B231A419700F24593 /* DDKCResult.swift in Sources */,
				DE02895C231A419700F24593 /* NewsDetailViewController.swift in Sources */,
				DE02895D231A419700F24593 /* PolicyModel.swift in Sources */,
				DE02895E231A419700F24593 /* ShowFilmModel.swift in Sources */,
				DE02895F231A419700F24593 /* FAQViewController.swift in Sources */,
				DE028960231A419700F24593 /* UseVoucherViewController.swift in Sources */,
				DE028961231A419700F24593 /* TransactionDetailViewController.swift in Sources */,
				DE028962231A419700F24593 /* Color.swift in Sources */,
				DE028963231A419700F24593 /* NotificationModel.swift in Sources */,
				DE028964231A419700F24593 /* PointHistory.swift in Sources */,
				DE028965231A419700F24593 /* Fonts.swift in Sources */,
				DE028966231A419700F24593 /* ListPosterUrlModel.swift in Sources */,
				DE028967231A419700F24593 /* CityAPI.swift in Sources */,
				DE028968231A419700F24593 /* FilmDescriptionTableCell.swift in Sources */,
				DE028969231A419700F24593 /* PaymentPointViewController.swift in Sources */,
				DE02896A231A419700F24593 /* AttributeString.swift in Sources */,
				DE02896B231A419700F24593 /* FilmAPI.swift in Sources */,
				DE02896C231A419700F24593 /* LocationManager.swift in Sources */,
				DE02896D231A419700F24593 /* AppDelegate.swift in Sources */,
				DE02896E231A419700F24593 /* NewHomeViewController.swift in Sources */,
				DE02896F231A419700F24593 /* MemberCardCell.swift in Sources */,
				DE028970231A419700F24593 /* BaseRequestModel.swift in Sources */,
				DE028971231A419700F24593 /* ListFilmWatchedViewController.swift in Sources */,
				************************ /* NewsModel.swift in Sources */,
				************************ /* AreaCinemaHeaderView.swift in Sources */,
				************************ /* Repository.swift in Sources */,
				************************ /* ShowCinemaModel.swift in Sources */,
				************************ /* VoucherViewController.swift in Sources */,
				************************ /* TransactionHistoryCell.swift in Sources */,
				************************ /* AccountInfoViewController.swift in Sources */,
				************************ /* SlideMenuViewController.swift in Sources */,
				************************ /* FilmChooseTimeViewController.swift in Sources */,
				************************ /* ListAllCinemasViewController.swift in Sources */,
				************************ /* TransactionHistoryDetailModel.swift in Sources */,
				************************ /* OtherViewController.swift in Sources */,
				************************ /* ListFilmViewController.swift in Sources */,
				************************ /* SettingTableCell.swift in Sources */,
				************************ /* CinemaTableViewCell.swift in Sources */,
				************************ /* CheckboxTableCell.swift in Sources */,
				************************ /* GraphShitingViewController.swift in Sources */,
				************************ /* SelectRegionViewController.swift in Sources */,
				************************ /* LoginViewController.swift in Sources */,
				************************ /* ListSeatModel.swift in Sources */,
				DE028986231A419700F24593 /* Utils.swift in Sources */,
				DE028987231A419700F24593 /* ChooseCinemasViewController.swift in Sources */,
				DE028988231A419700F24593 /* Storyboard+Quick.swift in Sources */,
				DE028989231A419700F24593 /* Image+Ext.swift in Sources */,
				DE02898A231A419700F24593 /* CalendarHeaderView.swift in Sources */,
				DE02898B231A419700F24593 /* OthersCollectionViewCell.swift in Sources */,
				DE02898C231A419700F24593 /* CinemaModel.swift in Sources */,
				DE02898D231A419700F24593 /* ScanBarCodeViewController.swift in Sources */,
				DE02898E231A419700F24593 /* CardGiftViewController.swift in Sources */,
				DE02898F231A419700F24593 /* MenuItemCell.swift in Sources */,
				DE028990231A419700F24593 /* RecruitmentViewController.swift in Sources */,
				DE028991231A419700F24593 /* String+Ext.swift in Sources */,
				DE028992231A419700F24593 /* ProfileViewController.swift in Sources */,
				DE028993231A419700F24593 /* FilmTimeTableViewCell.swift in Sources */,
				DE028994231A419700F24593 /* NewsTableViewCell.swift in Sources */,
				DE028995231A419700F24593 /* CinemaProvinceModel.swift in Sources */,
				CC39C9A928ACED650015018A /* ComboListModel.swift in Sources */,
				DE028996231A419700F24593 /* Config.swift in Sources */,
				DE028997231A419700F24593 /* DonateVoucherTableViewCell.swift in Sources */,
				DE028998231A419700F24593 /* TimeListCollectionCell.swift in Sources */,
				DE028999231A419700F24593 /* PolicyContentModel.swift in Sources */,
				DE02899A231A419700F24593 /* AvatarImageModel.swift in Sources */,
				DE02899B231A419700F24593 /* IntroToFriendViewController.swift in Sources */,
				************************ /* RouteManager.swift in Sources */,
				************************ /* DonateVoucherViewController.swift in Sources */,
				************************ /* LocalizableButton.swift in Sources */,
				************************ /* Device.swift in Sources */,
				************************ /* AddVoucherViewController.swift in Sources */,
				************************ /* SettingViewController.swift in Sources */,
				************************ /* AccountAPI.swift in Sources */,
				************************ /* MyVoucherViewController.swift in Sources */,
				************************ /* ListFilmGenreModel.swift in Sources */,
				************************ /* BaseModel.swift in Sources */,
				************************ /* GradientButton.swift in Sources */,
				************************ /* NearCinemaTableViewCell.swift in Sources */,
				************************ /* MainTabContainer.swift in Sources */,
				************************ /* Array+Ext.swift in Sources */,
				************************ /* HistoryVoucherTableViewCell.swift in Sources */,
				************************ /* StickyHeaderViewController.swift in Sources */,
				************************ /* SeatModel.swift in Sources */,
				************************ /* TranferHistoryViewController.swift in Sources */,
				************************ /* TicketType.swift in Sources */,
				************************ /* TabOtherViewController.swift in Sources */,
				************************ /* Request.swift in Sources */,
				DE0289B1231A419700F24593 /* NotificationDetailViewController.swift in Sources */,
				DE0289B2231A419700F24593 /* VoucherAPI.swift in Sources */,
				CC39C9AD28ACEF220015018A /* DataBookingModel.swift in Sources */,
				DE0289B3231A419700F24593 /* ChangePassViewController.swift in Sources */,
				DE0289B4231A419700F24593 /* CinemasViewController.swift in Sources */,
				DE0289B5231A419700F24593 /* RoundTextField.swift in Sources */,
				DE0289B6231A419700F24593 /* FAQDetailViewController.swift in Sources */,
				DE0289B7231A419700F24593 /* CinemaPriceViewController.swift in Sources */,
				DE0289B8231A419700F24593 /* BaseViewController.swift in Sources */,
				DE0289B9231A419700F24593 /* VersionInfoViewController.swift in Sources */,
				DE0289BA231A419700F24593 /* UIAlertController+Ext.swift in Sources */,
				DE0289BB231A419700F24593 /* CardModel.swift in Sources */,
				DE0289BC231A419700F24593 /* Date+Ext.swift in Sources */,
				DE0289BD231A419700F24593 /* NewModel.swift in Sources */,
				DE0289BE231A419700F24593 /* RoundView.swift in Sources */,
				DE0289BF231A419700F24593 /* AppDelegate+Appearance.swift in Sources */,
				DE0289C0231A419700F24593 /* UserModel.swift in Sources */,
				DE0289C1231A419700F24593 /* NotificationTableViewCell.swift in Sources */,
				DE0289C2231A419700F24593 /* VourcherCouponViewController.swift in Sources */,
				DE0289C3231A419700F24593 /* SSASideMenu.swift in Sources */,
				DE0289C4231A419700F24593 /* DonatePointAlert.swift in Sources */,
				DE0289C5231A419700F24593 /* EcmAPI.swift in Sources */,
				DE0289C6231A419700F24593 /* FilmItemTableCell.swift in Sources */,
				DE0289C7231A419700F24593 /* CustomizeAlert.swift in Sources */,
				DE0289C8231A419700F24593 /* DDKCResponse.swift in Sources */,
				DE0289C9231A419700F24593 /* PickerTextField.swift in Sources */,
				DE0289CA231A419700F24593 /* VoucherTableCell.swift in Sources */,
				DE0289CB231A419700F24593 /* SwitchTableCell.swift in Sources */,
				DE0289CC231A419700F24593 /* PaymentViewController.swift in Sources */,
				DE0289CD231A419700F24593 /* SeatCollectionViewCell.swift in Sources */,
				DE0289CE231A419700F24593 /* VoucherHistory.swift in Sources */,
				DE0289CF231A419700F24593 /* TimeListView.swift in Sources */,
				DE0289D0231A419700F24593 /* TopViewController.swift in Sources */,
				DE0289D1231A419700F24593 /* ChooseSeatViewController.swift in Sources */,
				CC1979B128A3AA64006AD455 /* JSONMappable.swift in Sources */,
				DE0289D2231A419700F24593 /* NetworkManager+Ecm.swift in Sources */,
				DE0289D3231A419700F24593 /* CreateBookingModel.swift in Sources */,
				DE0289D4231A419700F24593 /* VoucherModel.swift in Sources */,
				DE0289D5231A419700F24593 /* NetworkManager+Film.swift in Sources */,
				DE0289D6231A419700F24593 /* DateTextField.swift in Sources */,
				DE0289D7231A419700F24593 /* FilmBookingViewController.swift in Sources */,
				DE0289D8231A419700F24593 /* VerticalLayoutButton.swift in Sources */,
				DE0289D9231A419700F24593 /* RegisterModel.swift in Sources */,
				DE0289DA231A419700F24593 /* View+Size.swift in Sources */,
				DE0289DB231A419700F24593 /* TitleHeaderView.swift in Sources */,
				DE0289DC231A419700F24593 /* CinemaCollectionViewCell.swift in Sources */,
				DE0289DD231A419700F24593 /* Button.swift in Sources */,
				DE0289DE231A419700F24593 /* HomeCollectionViewCell.swift in Sources */,
				DE0289DF231A419700F24593 /* CinemaPriceHeaderView.swift in Sources */,
				DE0289E0231A419700F24593 /* TransparentAnimator.swift in Sources */,
				DE0289E1231A419700F24593 /* ScrollPager.swift in Sources */,
				DE0289E2231A419700F24593 /* RoundImageView.swift in Sources */,
				DE0289E3231A419700F24593 /* Images.swift in Sources */,
				DE0289E4231A419700F24593 /* VoucherGot.swift in Sources */,
				DE0289E5231A419700F24593 /* AppParams.swift in Sources */,
				DE0289E6231A419700F24593 /* App+Rx.swift in Sources */,
				DE0289E7231A419700F24593 /* ScreenModel.swift in Sources */,
				DE0289E8231A419700F24593 /* AssetLibrary.swift in Sources */,
				DE0289E9231A419700F24593 /* NotificationViewController.swift in Sources */,
				DE0289EA231A419700F24593 /* DashView.swift in Sources */,
				DE0289EB231A419700F24593 /* Constants.swift in Sources */,
				DE0289EC231A419700F24593 /* TableView.swift in Sources */,
				DE0289ED231A419700F24593 /* HistoryVoucherViewController.swift in Sources */,
				DE0289EE231A419700F24593 /* Owner.swift in Sources */,
				DE0289EF231A419700F24593 /* CinemaAPI.swift in Sources */,
				DE0289F0231A419700F24593 /* RewardPointsViewController.swift in Sources */,
				DE0289F1231A419700F24593 /* ConfirmBookAgeViewController.swift in Sources */,
				DE0289F2231A419700F24593 /* CinemaByProvinceTableViewCell.swift in Sources */,
				DE0289F3231A419700F24593 /* NetworkManager.swift in Sources */,
				DE0289F4231A419700F24593 /* RegisterVoucherModel.swift in Sources */,
				DE0289F5231A419700F24593 /* ShowModel.swift in Sources */,
				DE0289F6231A419700F24593 /* ColumnLayout.swift in Sources */,
				DE0289F7231A419700F24593 /* RoundButton.swift in Sources */,
				DE0289F8231A419700F24593 /* FreeVoucher.swift in Sources */,
				DE0289F9231A419700F24593 /* PointModel.swift in Sources */,
				DE0289FA231A419700F24593 /* DashBorderView.swift in Sources */,
				DE0289FB231A419700F24593 /* NotificationTableCell.swift in Sources */,
				DE0289FC231A419700F24593 /* HistoryPointTableViewCell.swift in Sources */,
				DE0289FD231A419700F24593 /* RegisterViewController.swift in Sources */,
				DE0289FE231A419700F24593 /* Notification.swift in Sources */,
				DE0289FF231A419700F24593 /* ImagePickerController.swift in Sources */,
				DE028A00231A419700F24593 /* MemberCardViewController.swift in Sources */,
				DE084C4B233910F700705A0F /* NotificationCell.swift in Sources */,
				DE028A01231A419700F24593 /* GradientImageView.swift in Sources */,
				DE028A02231A419700F24593 /* BannerCardView.swift in Sources */,
				DE028A03231A419700F24593 /* Location+Ext.swift in Sources */,
				DE028A04231A419700F24593 /* HomePage.swift in Sources */,
				DE028A05231A419700F24593 /* ListNewsViewController.swift in Sources */,
				DE028A06231A419700F24593 /* CouponViewController.swift in Sources */,
				DE0B85572334BF34009D6E32 /* UIMaker.swift in Sources */,
				DE028A07231A419700F24593 /* TransactionHistoryModel.swift in Sources */,
				DE028A08231A419700F24593 /* MyVoucherTableViewCell.swift in Sources */,
				DE0B85532334BDFC009D6E32 /* MyTabbarViewController.swift in Sources */,
				DE028A09231A419700F24593 /* HomeViewController.swift in Sources */,
				DE028A0A231A419700F24593 /* UserDefault+Quick.swift in Sources */,
				DE028A0B231A419700F24593 /* CardClassModel.swift in Sources */,
				DE028A0C231A419700F24593 /* UILabel+Localization.swift in Sources */,
				DE028A0D231A419700F24593 /* LocalizableProtocol.swift in Sources */,
				DE028A0E231A419700F24593 /* InputTextField.swift in Sources */,
				DE028A0F231A419700F24593 /* CityModel.swift in Sources */,
				845BA7D12DFFDC1700AACEDC /* AppIconManager.swift in Sources */,
				DE028A10231A419700F24593 /* NewAndDealsView.swift in Sources */,
				DE028A11231A419700F24593 /* DateFormat.swift in Sources */,
				DE028A12231A419700F24593 /* Banner.swift in Sources */,
				DE028A13231A419700F24593 /* CalendarHeaderViewCell.swift in Sources */,
				DE028A14231A419700F24593 /* View+Layer.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		DE42737E20503C080047666D /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				6A6EE1C920BE242100B8FC6F /* CinemaFilmTimeTableCell.swift in Sources */,
				DE16D01820810693004D9722 /* TopicDetailModel.swift in Sources */,
				6AFD7003210FF59500E01D8A /* UpdatePasswordViewController.swift in Sources */,
				6A812F0A2086BBEB0079ABF6 /* ConfirmPassViewController.swift in Sources */,
				8439E1AD2CEB75F400D7A69D /* ConfirmVipZoomViewController.swift in Sources */,
				6A968EAC20B7EBC100A80BE2 /* YoutubeViewController.swift in Sources */,
				DEFED17E2301D7B40006688A /* ShortUser.swift in Sources */,
				6A29B86D207C0D3800ED7F4C /* GradientView.swift in Sources */,
				6A29B7B5207C0D1500ED7F4C /* TicketBookingViewController.swift in Sources */,
				6A29B87E207C2A6B00ED7F4C /* NewsAndDealsCell.swift in Sources */,
				6A31AAFD20BB2AED00DC59B3 /* SeatCollectionHeaderView.swift in Sources */,
				DEFA4C8622FEC04C00725391 /* FreeVoucherViewController.swift in Sources */,
				DE16D0122080F1A5004D9722 /* LanguageManager.swift in Sources */,
				6A29B7ED207C0D1500ED7F4C /* MemberViewController.swift in Sources */,
				6A29B7C1207C0D1500ED7F4C /* CinemaDetailViewController.swift in Sources */,
				6A29B80D207C0D1500ED7F4C /* LoginModel.swift in Sources */,
				84DD28A82AE227AF000C5712 /* TransactionDetailCell.swift in Sources */,
				6AC8AC7520AF4C2800DE4F5B /* HCYoutubeParser.m in Sources */,
				6A29B809207C0D1500ED7F4C /* BaseNavigationViewController.swift in Sources */,
				************************ /* Common+Ext.swift in Sources */,
				CC39C9A228AB75BE0015018A /* Tracking.swift in Sources */,
				DE1C88E42053FD11003B5B9A /* AppDelegate+Initial.swift in Sources */,
				6A29B7BB207C0D1500ED7F4C /* FilmDetailViewController.swift in Sources */,
				6AA1AC852076CF220081188F /* TableViewHelper.swift in Sources */,
				6A29B85B207C0D3800ED7F4C /* Global.swift in Sources */,
				6A29B7D7207C0D1500ED7F4C /* ForgotPassViewController.swift in Sources */,
				6A29B7AB207C0D1500ED7F4C /* NewsAndDealsViewController.swift in Sources */,
				DE16D01520810652004D9722 /* TopicModel.swift in Sources */,
				6A31AAFA20BA91E100DC59B3 /* RegisterResultViewController.swift in Sources */,
				6A34871B20C1499A0074F58F /* SeatBookingModel.swift in Sources */,
				DEFA4C8C22FEC10900725391 /* FreeVoucherTableViewCell.swift in Sources */,
				6A4550BD2082520800AD3031 /* LocalizableLabel.swift in Sources */,
				6A29B813207C0D1500ED7F4C /* FilmModel.swift in Sources */,
				DEBE7B1F207D09C1005A1F1D /* DDKCResult.swift in Sources */,
				6A29B7A1207C0D1500ED7F4C /* NewsDetailViewController.swift in Sources */,
				DED2146A2083AA22003BBBA2 /* PolicyModel.swift in Sources */,
				6A29B821207C0D1500ED7F4C /* ShowFilmModel.swift in Sources */,
				6A29B89D207EC13400ED7F4C /* FAQViewController.swift in Sources */,
				DEFA01F823040831003839C5 /* UseVoucherViewController.swift in Sources */,
				6A812F1420870F500079ABF6 /* TransactionDetailViewController.swift in Sources */,
				6A29B877207C0D3800ED7F4C /* Color.swift in Sources */,
				DEBE7B16207CC4F5005A1F1D /* NotificationModel.swift in Sources */,
				DEFA01F42303F197003839C5 /* PointHistory.swift in Sources */,
				6A29B85F207C0D3800ED7F4C /* Fonts.swift in Sources */,
				6A29B819207C0D1500ED7F4C /* ListPosterUrlModel.swift in Sources */,
				************************ /* CityAPI.swift in Sources */,
				6A29B87B207C25B200ED7F4C /* FilmDescriptionTableCell.swift in Sources */,
				6AA13FE1208C901C007809AC /* PaymentPointViewController.swift in Sources */,
				DE0C143E22F7276600615CC3 /* AttributeString.swift in Sources */,
				DE2164E52073BBB600938938 /* FilmAPI.swift in Sources */,
				6AC8AC7820AFDD2900DE4F5B /* LocationManager.swift in Sources */,
				DE1C888F20539BC3003B5B9A /* AppDelegate.swift in Sources */,
				DEFF15DF22F86E9D0000D2C9 /* NewHomeViewController.swift in Sources */,
				6A7C078B20B3730C0092E553 /* MemberCardCell.swift in Sources */,
				6A29B80B207C0D1500ED7F4C /* BaseRequestModel.swift in Sources */,
				6A29B7EF207C0D1500ED7F4C /* ListFilmWatchedViewController.swift in Sources */,
				6A29B829207C0D1500ED7F4C /* NewsModel.swift in Sources */,
				6A29B7CB207C0D1500ED7F4C /* AreaCinemaHeaderView.swift in Sources */,
				6A29B83D207C0D1500ED7F4C /* Repository.swift in Sources */,
				6AC52E5320B54ED600D9BCAC /* ShowCinemaModel.swift in Sources */,
				6A29B7E7207C0D1500ED7F4C /* VoucherViewController.swift in Sources */,
				6A812F1120870A480079ABF6 /* TransactionHistoryCell.swift in Sources */,
				6A812F0C2086BBF70079ABF6 /* AccountInfoViewController.swift in Sources */,
				6A29B7F5207C0D1500ED7F4C /* SlideMenuViewController.swift in Sources */,
				6A661B5720913C5000CFC976 /* FilmChooseTimeViewController.swift in Sources */,
				6A29B7BF207C0D1500ED7F4C /* ListAllCinemasViewController.swift in Sources */,
				DE1B083B20C035A1000FFD1D /* TransactionHistoryDetailModel.swift in Sources */,
				6A29B803207C0D1500ED7F4C /* OtherViewController.swift in Sources */,
				6A29B7BD207C0D1500ED7F4C /* ListFilmViewController.swift in Sources */,
				6A29B888207EA50F00ED7F4C /* SettingTableCell.swift in Sources */,
				6A29B7CF207C0D1500ED7F4C /* CinemaTableViewCell.swift in Sources */,
				6A29B88E207EA54600ED7F4C /* CheckboxTableCell.swift in Sources */,
				6A29B7B7207C0D1500ED7F4C /* GraphShitingViewController.swift in Sources */,
				6A968EAE20B925A500A80BE2 /* SelectRegionViewController.swift in Sources */,
				6A29B7D9207C0D1500ED7F4C /* LoginViewController.swift in Sources */,
				6A11D6CF20BA3F66009668B2 /* ListSeatModel.swift in Sources */,
				6A29B875207C0D3800ED7F4C /* Utils.swift in Sources */,
				6A29B7C3207C0D1500ED7F4C /* ChooseCinemasViewController.swift in Sources */,
				6A912F162069806C003F98B3 /* Storyboard+Quick.swift in Sources */,
				6A812F202088A18C0079ABF6 /* Image+Ext.swift in Sources */,
				6AA299B1208C42680007E074 /* CalendarHeaderView.swift in Sources */,
				DEFAB22C22F49C280019231D /* OthersCollectionViewCell.swift in Sources */,
				6A29B815207C0D1500ED7F4C /* CinemaModel.swift in Sources */,
				DEFA6B062302C5CD00C0C156 /* ScanBarCodeViewController.swift in Sources */,
				6A29B7EB207C0D1500ED7F4C /* CardGiftViewController.swift in Sources */,
				6A29B7F7207C0D1500ED7F4C /* MenuItemCell.swift in Sources */,
				6AC52E5520B6064600D9BCAC /* RecruitmentViewController.swift in Sources */,
				DE002F602074DD4C0001C63D /* String+Ext.swift in Sources */,
				6A29B805207C0D1500ED7F4C /* ProfileViewController.swift in Sources */,
				6AA13FE4208C97A3007809AC /* FilmTimeTableViewCell.swift in Sources */,
				DE75FC18207F9A4D00604D7E /* NewsTableViewCell.swift in Sources */,
				6A29B83B207C0D1500ED7F4C /* CinemaProvinceModel.swift in Sources */,
				CC39C9A728ACED650015018A /* ComboListModel.swift in Sources */,
				6A29B85D207C0D3800ED7F4C /* Config.swift in Sources */,
				DEE1AAD122F5E1F40081FD3F /* DonateVoucherTableViewCell.swift in Sources */,
				6A661B51208FF99F00CFC976 /* TimeListCollectionCell.swift in Sources */,
				DED2146D2083AA34003BBBA2 /* PolicyContentModel.swift in Sources */,
				6AA959CF20B65DE000E6337A /* AvatarImageModel.swift in Sources */,
				DE0C143622F6757100615CC3 /* IntroToFriendViewController.swift in Sources */,
				DEFEF51723055FBA00D107E8 /* RouteManager.swift in Sources */,
				DEE1AABF22F5DEEE0081FD3F /* DonateVoucherViewController.swift in Sources */,
				6A4550C02082530200AD3031 /* LocalizableButton.swift in Sources */,
				6A29B89A207EADCB00ED7F4C /* Device.swift in Sources */,
				DEE1AAB922F5DEDC0081FD3F /* AddVoucherViewController.swift in Sources */,
				6A29B7FD207C0D1500ED7F4C /* SettingViewController.swift in Sources */,
				DEB8F8A82075237A00715531 /* AccountAPI.swift in Sources */,
				DEE1AAA922F5CFC80081FD3F /* MyVoucherViewController.swift in Sources */,
				6A29B81D207C0D1500ED7F4C /* ListFilmGenreModel.swift in Sources */,
				6A29B83F207C0D1500ED7F4C /* BaseModel.swift in Sources */,
				6A29B86B207C0D3800ED7F4C /* GradientButton.swift in Sources */,
				6A29B7C5207C0D1500ED7F4C /* NearCinemaTableViewCell.swift in Sources */,
				DEFAB21A22F486680019231D /* MainTabContainer.swift in Sources */,
				6A5B053520BE838F00E40BC0 /* Array+Ext.swift in Sources */,
				DEE1AACB22F5E1E00081FD3F /* HistoryVoucherTableViewCell.swift in Sources */,
				6AA14024208F371B007809AC /* StickyHeaderViewController.swift in Sources */,
				6A11D6D320BA402A009668B2 /* SeatModel.swift in Sources */,
				6A29B7F3207C0D1500ED7F4C /* TranferHistoryViewController.swift in Sources */,
				6A11D6D520BA446E009668B2 /* TicketType.swift in Sources */,
				DEFAB22522F48AB50019231D /* TabOtherViewController.swift in Sources */,
				DEBDF17D22FC6CD40031A64D /* Request.swift in Sources */,
				6A41ED2920D1215F00BA16FA /* NotificationDetailViewController.swift in Sources */,
				DE154A782090E9C000A51889 /* VoucherAPI.swift in Sources */,
				CC39C9AB28ACEF220015018A /* DataBookingModel.swift in Sources */,
				6A29B7E9207C0D1500ED7F4C /* ChangePassViewController.swift in Sources */,
				DEFF6B69230A8C22009B04B2 /* CinemasViewController.swift in Sources */,
				6A29B867207C0D3800ED7F4C /* RoundTextField.swift in Sources */,
				DE16D01E208109ED004D9722 /* FAQDetailViewController.swift in Sources */,
				6A7C078820B3701A0092E553 /* CinemaPriceViewController.swift in Sources */,
				6A29B807207C0D1500ED7F4C /* BaseViewController.swift in Sources */,
				6A29B801207C0D1500ED7F4C /* VersionInfoViewController.swift in Sources */,
				DE264170207A62DD00844F7F /* UIAlertController+Ext.swift in Sources */,
				6A29B817207C0D1500ED7F4C /* CardModel.swift in Sources */,
				DEBE7B19207CDF4D005A1F1D /* Date+Ext.swift in Sources */,
				6A29B837207C0D1500ED7F4C /* NewModel.swift in Sources */,
				6A29B863207C0D3800ED7F4C /* RoundView.swift in Sources */,
				6A9D124520740CC800D49B55 /* AppDelegate+Appearance.swift in Sources */,
				6A29B81F207C0D1500ED7F4C /* UserModel.swift in Sources */,
				DE16D02220810BCA004D9722 /* NotificationTableViewCell.swift in Sources */,
				6A29B7E1207C0D1500ED7F4C /* VourcherCouponViewController.swift in Sources */,
				6A9D123B2072D90C00D49B55 /* SSASideMenu.swift in Sources */,
				DE0C144922F72E6B00615CC3 /* DonatePointAlert.swift in Sources */,
				DE180CAF20767B180026A00D /* EcmAPI.swift in Sources */,
				6AA299A9208BF2A20007E074 /* FilmItemTableCell.swift in Sources */,
				DEFA6B002302AF1700C0C156 /* CustomizeAlert.swift in Sources */,
				DE2164E82073C99B00938938 /* DDKCResponse.swift in Sources */,
				6A29B861207C0D3800ED7F4C /* PickerTextField.swift in Sources */,
				6A812F23208933160079ABF6 /* VoucherTableCell.swift in Sources */,
				6A29B88B207EA51F00ED7F4C /* SwitchTableCell.swift in Sources */,
				6A29B7D3207C0D1500ED7F4C /* PaymentViewController.swift in Sources */,
				6A11D6CD20BA1567009668B2 /* SeatCollectionViewCell.swift in Sources */,
				DEFA01F12303F13D003839C5 /* VoucherHistory.swift in Sources */,
				6A661B4E208FF6E600CFC976 /* TimeListView.swift in Sources */,
				6A4550B120816C4A00AD3031 /* TopViewController.swift in Sources */,
				6AF06C6520A010A100A6459C /* ChooseSeatViewController.swift in Sources */,
				CC1979AF28A3AA64006AD455 /* JSONMappable.swift in Sources */,
				DEBE7B27207D13D8005A1F1D /* NetworkManager+Ecm.swift in Sources */,
				6A34871920C149390074F58F /* CreateBookingModel.swift in Sources */,
				DE154A7B2090EDC300A51889 /* VoucherModel.swift in Sources */,
				DEBE7B24207D0C2C005A1F1D /* NetworkManager+Film.swift in Sources */,
				6A29B871207C0D3800ED7F4C /* DateTextField.swift in Sources */,
				6AA299AE208C04920007E074 /* FilmBookingViewController.swift in Sources */,
				6A97191120A39962009FF4B7 /* VerticalLayoutButton.swift in Sources */,
				6A29B80F207C0D1500ED7F4C /* RegisterModel.swift in Sources */,
				6A1F60152078EAB300212F7D /* View+Size.swift in Sources */,
				6A29B7CD207C0D1500ED7F4C /* TitleHeaderView.swift in Sources */,
				DEFF6B70230A8E34009B04B2 /* CinemaCollectionViewCell.swift in Sources */,
				DE0C144E22F731C700615CC3 /* Button.swift in Sources */,
				DEFF15E522F8752D0000D2C9 /* HomeCollectionViewCell.swift in Sources */,
				6A44992D20B626BB006B37A3 /* CinemaPriceHeaderView.swift in Sources */,
				6A03F38C208106F0002FC2CD /* TransparentAnimator.swift in Sources */,
				6A9D123F2072E3F700D49B55 /* ScrollPager.swift in Sources */,
				6A29B86F207C0D3800ED7F4C /* RoundImageView.swift in Sources */,
				6A29B873207C0D3800ED7F4C /* Images.swift in Sources */,
				DEFA6B0B230313BB00C0C156 /* VoucherGot.swift in Sources */,
				DEBA9FDF230412BE00322EE4 /* AppParams.swift in Sources */,
				DE1C88D52053E2D0003B5B9A /* App+Rx.swift in Sources */,
				6A11D6D120BA4021009668B2 /* ScreenModel.swift in Sources */,
				6A812F1A20889E880079ABF6 /* AssetLibrary.swift in Sources */,
				6A29B7FB207C0D1500ED7F4C /* NotificationViewController.swift in Sources */,
				6A812F172087C9280079ABF6 /* DashView.swift in Sources */,
				6A29B859207C0D3800ED7F4C /* Constants.swift in Sources */,
				DEE1AAB522F5D11B0081FD3F /* TableView.swift in Sources */,
				DEE1AAC522F5DF000081FD3F /* HistoryVoucherViewController.swift in Sources */,
				6A29B841207C0D1500ED7F4C /* Owner.swift in Sources */,
				DEA5BEA22073F636006D8DE1 /* CinemaAPI.swift in Sources */,
				6A29B7DF207C0D1500ED7F4C /* RewardPointsViewController.swift in Sources */,
				6A3FB3B720BF93390034FC3D /* ConfirmBookAgeViewController.swift in Sources */,
				DEFF6B76230A8E6A009B04B2 /* CinemaByProvinceTableViewCell.swift in Sources */,
				DEBE7B1C207D01EC005A1F1D /* NetworkManager.swift in Sources */,
				DE019FB620B9585500A934B0 /* RegisterVoucherModel.swift in Sources */,
				6A29B839207C0D1500ED7F4C /* ShowModel.swift in Sources */,
				6A11D6CB20B9D69A009668B2 /* ColumnLayout.swift in Sources */,
				6A29B865207C0D3800ED7F4C /* RoundButton.swift in Sources */,
				DEFED17A2301B6AD0006688A /* FreeVoucher.swift in Sources */,
				6A29B811207C0D1500ED7F4C /* PointModel.swift in Sources */,
				DE0C143B22F6E81600615CC3 /* DashBorderView.swift in Sources */,
				6A29B7F9207C0D1500ED7F4C /* NotificationTableCell.swift in Sources */,
				DE0C144222F72B4E00615CC3 /* HistoryPointTableViewCell.swift in Sources */,
				6A29B7DD207C0D1500ED7F4C /* RegisterViewController.swift in Sources */,
				DE4453B620F3B13B00835007 /* Notification.swift in Sources */,
				6A812F1C20889EEE0079ABF6 /* ImagePickerController.swift in Sources */,
				6A29B7E5207C0D1500ED7F4C /* MemberCardViewController.swift in Sources */,
				DE084C49233910F700705A0F /* NotificationCell.swift in Sources */,
				6A0A8B37208571BD00679340 /* GradientImageView.swift in Sources */,
				6A29B7A5207C0D1500ED7F4C /* BannerCardView.swift in Sources */,
				DE75FC1F207F9FC200604D7E /* Location+Ext.swift in Sources */,
				DE028936231A3FD200F24593 /* HomePage.swift in Sources */,
				6AA14021208F2FA1007809AC /* ListNewsViewController.swift in Sources */,
				6A29B7F1207C0D1500ED7F4C /* CouponViewController.swift in Sources */,
				DE0B85552334BF34009D6E32 /* UIMaker.swift in Sources */,
				DE1CF99220BD9885001D89F2 /* TransactionHistoryModel.swift in Sources */,
				DEE1AAB022F5D09A0081FD3F /* MyVoucherTableViewCell.swift in Sources */,
				DE0B85512334BDFC009D6E32 /* MyTabbarViewController.swift in Sources */,
				6A29B7A9207C0D1500ED7F4C /* HomeViewController.swift in Sources */,
				DE1C88E12053FBA3003B5B9A /* UserDefault+Quick.swift in Sources */,
				6A41ECBC20CB83D100BA16FA /* CardClassModel.swift in Sources */,
				DE26416D207A56C700844F7F /* UILabel+Localization.swift in Sources */,
				6A4550BA2082517300AD3031 /* LocalizableProtocol.swift in Sources */,
				6A29B869207C0D3800ED7F4C /* InputTextField.swift in Sources */,
				6A29B81B207C0D1500ED7F4C /* CityModel.swift in Sources */,
				845BA7D02DFFDC1700AACEDC /* AppIconManager.swift in Sources */,
				6A29B843207C0D1500ED7F4C /* NewAndDealsView.swift in Sources */,
				6A29B884207CC07900ED7F4C /* DateFormat.swift in Sources */,
				DEFA4C7922FE6E6D00725391 /* Banner.swift in Sources */,
				6AA299B6208C43490007E074 /* CalendarHeaderViewCell.swift in Sources */,
				6A1F60132078E71A00212F7D /* View+Layer.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXVariantGroup section */
		6AC0703820FDA8430036CD1D /* InfoPlist.strings */ = {
			isa = PBXVariantGroup;
			children = (
				6AC0703720FDA8430036CD1D /* en */,
				6AC0703920FDA8440036CD1D /* vi */,
			);
			name = InfoPlist.strings;
			sourceTree = "<group>";
		};
		DE002F5D2074DC6A0001C63D /* Localizable.strings */ = {
			isa = PBXVariantGroup;
			children = (
				DE002F5C2074DC6A0001C63D /* en */,
				DE002F5E2074DC6D0001C63D /* vi */,
			);
			name = Localizable.strings;
			sourceTree = "<group>";
		};
		DE1C888B20539BC3003B5B9A /* LaunchScreen.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				DE1C888C20539BC3003B5B9A /* Base */,
				DE002F552074DB960001C63D /* vi */,
			);
			name = LaunchScreen.storyboard;
			sourceTree = "<group>";
		};
		DE1C888D20539BC3003B5B9A /* Main.storyboard */ = {
			isa = PBXVariantGroup;
			children = (
				DE1C888E20539BC3003B5B9A /* Base */,
				DE002F562074DB960001C63D /* vi */,
			);
			name = Main.storyboard;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		6A48457020F59C7100273D66 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = DE743B3FAEABD98F86D019D8 /* Pods-Booking-pro.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "Booking-dev.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = K6YBR32CYT;
				EXCLUDED_ARCHS = "";
				GCC_WARN_INHIBIT_ALL_WARNINGS = NO;
				INFOPLIST_FILE = "$(SRCROOT)/Booking/App/Prod/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				MARKETING_VERSION = 2.7.8;
				OTHER_SWIFT_FLAGS = "$(inherited) \"-D\" \"COCOAPODS\" -DBETA_PRO";
				PRODUCT_BUNDLE_IDENTIFIER = com.beta.betacineplex;
				PRODUCT_NAME = "Beta Cinemas";
				PROVISIONING_PROFILE = "";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Booking/App/Booking-dev-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 4.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		6A48457120F59C7100273D66 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 6C143B96DAFE2D401D53AAB0 /* Pods-Booking-pro.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "Booking-dev.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = K6YBR32CYT;
				EXCLUDED_ARCHS = "";
				GCC_WARN_INHIBIT_ALL_WARNINGS = NO;
				INFOPLIST_FILE = "$(SRCROOT)/Booking/App/Prod/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				MARKETING_VERSION = 2.7.8;
				OTHER_SWIFT_FLAGS = "$(inherited) \"-D\" \"COCOAPODS\" -DBETA_PRO";
				PRODUCT_BUNDLE_IDENTIFIER = com.beta.betacineplex;
				PRODUCT_NAME = "Beta Cinemas";
				PROVISIONING_PROFILE = "";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Booking/App/Booking-dev-Bridging-Header.h";
				SWIFT_VERSION = 4.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		DE028A76231A419700F24593 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 756FAA11648D14113A3CE05E /* Pods-Booking-test.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "Booking-dev.entitlements";
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 14;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = K6YBR32CYT;
				EXCLUDED_ARCHS = arm64;
				GCC_WARN_INHIBIT_ALL_WARNINGS = NO;
				INFOPLIST_FILE = "$(SRCROOT)/Booking/App/Test/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				MARKETING_VERSION = 1.0.0;
				OTHER_SWIFT_FLAGS = "$(inherited) \"-D\" \"COCOAPODS\" -DDEV -DTEST";
				PRODUCT_BUNDLE_IDENTIFIER = com.beta.betacineplex.test;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE = "";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Booking/App/Booking-dev-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 4.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		DE028A77231A419700F24593 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 93FCEAD34C3CB63A1649D2C9 /* Pods-Booking-test.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "Booking-dev.entitlements";
				CODE_SIGN_IDENTITY = "iPhone Developer";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 14;
				DEVELOPMENT_TEAM = K6YBR32CYT;
				EXCLUDED_ARCHS = arm64;
				GCC_WARN_INHIBIT_ALL_WARNINGS = NO;
				INFOPLIST_FILE = "$(SRCROOT)/Booking/App/Test/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				MARKETING_VERSION = 1.0.0;
				OTHER_SWIFT_FLAGS = "$(inherited) \"-D\" \"COCOAPODS\" -DDEV -DTEST";
				PRODUCT_BUNDLE_IDENTIFIER = com.beta.betacineplex.test;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE = "";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Booking/App/Booking-dev-Bridging-Header.h";
				SWIFT_VERSION = 4.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
		DE42739220503C080047666D /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				EXCLUDED_ARCHS = "";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 11.3;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = DEBUG;
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		DE42739320503C080047666D /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++14";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				CODE_SIGN_IDENTITY = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				EXCLUDED_ARCHS = "";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 11.3;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				SWIFT_OPTIMIZATION_LEVEL = "-Owholemodule";
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		DE42739520503C080047666D /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = ACC8EC1E73AAF5AE7B3DF1FE /* Pods-Booking-dev.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "Booking-dev.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = K6YBR32CYT;
				ENABLE_BITCODE = NO;
				EXCLUDED_ARCHS = "";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_WARN_INHIBIT_ALL_WARNINGS = NO;
				INFOPLIST_FILE = "$(SRCROOT)/Booking/App/Dev/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				MARKETING_VERSION = 2.7.8;
				OTHER_SWIFT_FLAGS = "$(inherited) \"-D\" \"COCOAPODS\" -DDEV -DBETA_DEV";
				PRODUCT_BUNDLE_IDENTIFIER = com.beta.betacineplex.dev;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE = "";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Booking/App/Booking-dev-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 4.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Debug;
		};
		DE42739620503C080047666D /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 96A763A7A558D3C44FCB0660 /* Pods-Booking-dev.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = "Booking-dev.entitlements";
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = K6YBR32CYT;
				ENABLE_BITCODE = NO;
				EXCLUDED_ARCHS = "";
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				GCC_WARN_INHIBIT_ALL_WARNINGS = NO;
				INFOPLIST_FILE = "$(SRCROOT)/Booking/App/Dev/Info.plist";
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = "$(inherited) @executable_path/Frameworks";
				MARKETING_VERSION = 2.7.8;
				OTHER_SWIFT_FLAGS = "$(inherited) \"-D\" \"COCOAPODS\" -DDEV -DBETA_DEV";
				PRODUCT_BUNDLE_IDENTIFIER = com.beta.betacineplex.dev;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE = "";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OBJC_BRIDGING_HEADER = "Booking/App/Booking-dev-Bridging-Header.h";
				SWIFT_VERSION = 4.0;
				TARGETED_DEVICE_FAMILY = 1;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		6A48456F20F59C7100273D66 /* Build configuration list for PBXNativeTarget "Booking-pro" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				6A48457020F59C7100273D66 /* Debug */,
				6A48457120F59C7100273D66 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		DE028A75231A419700F24593 /* Build configuration list for PBXNativeTarget "Booking-test" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DE028A76231A419700F24593 /* Debug */,
				DE028A77231A419700F24593 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		DE42737D20503C080047666D /* Build configuration list for PBXProject "Booking" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DE42739220503C080047666D /* Debug */,
				DE42739320503C080047666D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		DE42739420503C080047666D /* Build configuration list for PBXNativeTarget "Booking-dev" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				DE42739520503C080047666D /* Debug */,
				DE42739620503C080047666D /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = DE42737A20503C080047666D /* Project object */;
}

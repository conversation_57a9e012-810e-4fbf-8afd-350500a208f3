# So <PERSON><PERSON>h UI "Lịch Sử Giao Dịch" - Flutter vs iOS

## Tóm Tắt Khác B<PERSON>t

### 🎨 **Layout & Design**
| Aspect | Flutter | iOS |
|--------|---------|-----|
| **Card Style** | Card với border radius 5px | UITableViewCell với XIB layout |
| **Layout** | Row-based với 2 columns | Vertical stack layout |
| **Elevation** | Card elevation 2dp | Flat cell design |
| **Spacing** | Padding 12px | Standard UITableView spacing |

### 📱 **UI Components**

#### Flutter Implementation:
```dart
// Card-based design với Row layout
Card(
  margin: EdgeInsets.only(bottom: 16),
  elevation: 2,
  shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(5)),
  child: Row(
    children: [
      Expanded(flex: 2, child: leftColumn),  // Film info
      Container(width: 2, height: 80, color: Colors.grey), // Divider
      Expanded(flex: 1, child: rightColumn), // Price & Points
    ]
  )
)
```

#### iOS Implementation:
```swift
// XIB-based UITableViewCell
class TransactionHistoryCell: UITableViewCell {
    @IBOutlet weak var lbFilmName: UILabel!
    @IBOutlet weak var lbFilmDate: UILabel!
    @IBOutlet weak var lbFilmCinema: UILabel!
    @IBOutlet weak var lbMoney: UILabel!
    @IBOutlet weak var lbPoint: UILabel!
    @IBOutlet weak var lbDatePoint: UILabel!
    @IBOutlet weak var waitingView: UIView!
}
```

## Chi Tiết Khác Biệt

### 1. **Layout Structure**

#### Flutter:
- **2-column layout** với `Row` widget
- **Left column**: Film name, show time, cinema name
- **Right column**: Price, points, expiry date
- **Vertical divider** giữa 2 columns
- **Card container** với shadow

#### iOS:
- **Vertical stack layout** trong UITableViewCell
- **All elements** arranged vertically
- **No explicit columns** - relies on XIB constraints
- **Flat design** without card container

### 2. **Information Display**

#### Flutter:
```dart
// Left Column
Text(filmName, style: TextStyle(fontSize: CFontSize.xl, fontFamily: 'Oswald'))
Text(formattedShowTime, style: TextStyle(fontSize: 15))
Text(cinemaName, style: TextStyle(fontSize: 15))

// Right Column  
Text(price, style: TextStyle(fontFamily: 'Oswald', fontSize: CFontSize.xl))
Text('Điểm tích luỹ')
Text(points, style: TextStyle(color: Colors.red))
Text('Thời hạn của điểm')
Text(expiryDate, style: TextStyle(color: Colors.red))
```

#### iOS:
```swift
// Vertical arrangement
lbFilmName.text = item.FilmName
lbFilmDate.text = formattedDate  // "dd/MM/yyyy | HH:mm"
lbFilmCinema.text = item.CinemaName
lbMoney.text = currency.toCurrency()
lbPoint.text = "\(item.QuantityPoint ?? 0)"
lbDatePoint.text = formattedExpiryDate
```

### 3. **Waiting Status Indicator**

#### Flutter:
```dart
if (transaction.isWaiting) 
  Container(
    padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
    decoration: BoxDecoration(
      color: Colors.orange.withValues(alpha: 0.1),
      borderRadius: BorderRadius.circular(8),
    ),
    child: Text('ĐANG XỬ LÝ', 
      style: TextStyle(color: Colors.orange, fontWeight: FontWeight.bold)
    ),
  )
```

#### iOS:
```swift
if item.Invoice_Id == "00000000-0000-0000-0000-000000000000" {
    waitingView.isHidden = false
    waitingLabel.isHidden = false
    waitingLabel.text = "waiting_process".localized.uppercased()
    // Hide point-related labels
}
```

### 4. **Typography & Styling**

#### Flutter:
- **Oswald font** cho film name và price
- **CFontSize.xl** (large text)
- **Colors.indigo** cho price
- **Colors.red** cho points
- **Consistent spacing** với SizedBox

#### iOS:
- **System fonts** với XIB configuration
- **Localized labels** với LocalizableLabel
- **Color coding** based on status
- **Auto Layout** constraints

### 5. **Interaction**

#### Flutter:
```dart
InkWell(
  onTap: () => _showTransactionDetails(transaction),
  borderRadius: BorderRadius.circular(12),
  child: // Card content
)
```

#### iOS:
```swift
func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
    let item = items[indexPath.row]
    if item.Invoice_Id == "00000000-0000-0000-0000-000000000000" {
        // Open payment URL
        UIApplication.shared.open(url)
    } else {
        // Navigate to detail
        let vc = TransactionDetailViewController()
        show(vc, sender: nil)
    }
}
```

## Khuyến Nghị Cải Thiện

### 1. **Consistency với iOS**
- **Thay đổi layout Flutter** từ 2-column sang vertical layout
- **Sử dụng typography** tương tự iOS
- **Implement waiting status** giống iOS

### 2. **UI Enhancement**
```dart
// Suggested Flutter improvement
Widget _buildTransactionItem(TransactionHistoryModel transaction) {
  return Container(
    margin: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
    padding: EdgeInsets.all(16),
    decoration: BoxDecoration(
      color: Colors.white,
      borderRadius: BorderRadius.circular(8),
      boxShadow: [BoxShadow(color: Colors.grey.withOpacity(0.1), blurRadius: 4)]
    ),
    child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Film name - bold, larger
        Text(transaction.filmName, 
          style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)
        ),
        SizedBox(height: 8),
        
        // Date and time
        Text(transaction.formattedShowTime,
          style: TextStyle(fontSize: 14, color: Colors.grey[600])
        ),
        
        // Cinema name
        Text(transaction.cinemaName,
          style: TextStyle(fontSize: 14, color: Colors.grey[600])
        ),
        
        SizedBox(height: 12),
        
        // Price and points row
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(transaction.formattedPrice,
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Colors.blue)
            ),
            if (transaction.quantityPoint > 0)
              Text('+${transaction.quantityPoint} điểm',
                style: TextStyle(fontSize: 14, color: Colors.green)
              ),
          ],
        ),
        
        // Waiting status if applicable
        if (transaction.isWaiting)
          Container(
            margin: EdgeInsets.only(top: 8),
            padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.orange[100],
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text('ĐANG XỬ LÝ',
              style: TextStyle(fontSize: 12, color: Colors.orange[800])
            ),
          ),
      ],
    ),
  );
}
```

### 3. **Feature Parity**
- **Implement refresh indicator** như iOS
- **Add loading states** tương tự iOS
- **Handle empty states** consistently
- **Implement navigation** to detail screen

## Kết Luận

Flutter và iOS có **approach khác nhau** về UI design:
- **Flutter**: Modern card-based design với 2-column layout
- **iOS**: Traditional table view với vertical layout

**Khuyến nghị**: Chọn design pattern **iOS làm chuẩn** để đảm bảo consistency across platforms, hoặc **update iOS** để match Flutter's modern design.

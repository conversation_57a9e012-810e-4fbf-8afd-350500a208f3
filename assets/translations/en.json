{"HelloWorld": "Hello World", "Member": {"Title": "Beta Membership", "AccountInfo": "Account information", "ChangePass": "Change password", "MemberCard": "Member card", "BetaPoint": "BETA Points", "PreferentialCard": "Preferential Cards", "TransactionHistory": "Transaction history", "TransactionHistoryNote": "Showing transactions from the last 3 months. Please visit the website to view complete transaction history", "Intro": "Introduce your friends", "DeleteAccount": "Delete Account", "ChooseImage": "<PERSON><PERSON>", "TotalSpent": "Total spending", "TotalPoint": "Accumulating Point", "Logout": "Log out"}, "Bt": {"OK": "OK", "Yes": "Yes", "SEND": "SEND", "Confirm": "CONFIRM", "Update": "UPDATE", "Cancel": "Cancel", "Retry": "Retry", "CaptureImage": "Capture new photo", "ChooseFromLibrary": "From photo library", "REGISTER": "REGISTER", "LocationSetting": "Open Settings", "Close": "Close", "Continue": "Continue", "TimeLeft": "Time left"}, "Home": {"Minute": "min"}, "add_new": "ADD", "Menu": {"Home": "Home", "Member": "BETA Membership", "Cenima": "BETA Theaters", "NewsAndHotDeals": "News and Offers", "Barcode": "Barcode", "Recruitment": "Recruitment", "Notification": "Notifications", "Setting": "Setting", "Price": "Ticket Prices", "BookByMovie": "BOOKING BY MOVIE", "BookByTheater": "BOOKING BY THEATER"}, "Morning": "AM", "Afternoon": "PM", "UPDATE_SUCCESSFULLY": "Update profile successfully", "CinemaList": {"ChooseByArea": "Choose by area", "NearCinema": "Theater near you", "NoCinemaInArea": "No cinemas in this area."}, "Film": {"SelectRegion": "Select region", "AgeRestrict": {"C13": "For persons aged 13 and above only.", "C16": "For persons aged 16 and above only.", "C18": "For persons aged 18 and above only."}, "FilmDetail": {"Director": "Director", "Actor": "Actors", "Type": "Type", "Duration": "Duration", "Language": "Language", "DateShow": "Start Date", "Title": "Booking by film", "Share": "SHARE", "BuyTicket": "BUY TICKET"}}, "FilmBooking": {"BookByFilm": "Book by film", "BookByCinema": "Book by cinema", "NoShowDate": "No show dates", "NoShowTime": "No showtimes for this date", "Region": "Region", "All": "All", "LocationEnabled": "Location enabled", "ShowingNearby": "Showing nearby cinemas", "Refresh": "Refresh", "EnableLocation": "Enable location", "FilmDetail": "Film details", "LoadMore": "More", "ShowingCinemas": "Showing {0}/{1} cinemas", "Empty": "empty"}, "Location": {"ServiceDisabled": "Location services disabled", "ServiceDisabledMessage": "Please enable location services to see distance to cinemas.", "PermissionDenied": "Location permission denied", "PermissionDeniedMessage": "Please grant location permission to see distance to cinemas.", "PermissionPermanentlyDenied": "Location permission permanently denied", "PermissionPermanentlyDeniedMessage": "Please grant location permission in app settings to see distance to cinemas.", "OpenSettings": "Open settings", "PermissionTitle": "Location Permission", "PermissionMessage": "Please grant location permission in app settings to enable location services."}, "Common": {"Cancel": "Cancel", "Settings": "Settings"}, "Auth": {"Login": "<PERSON><PERSON>", "LoginRequired": "You need to login to book tickets."}, "Setting": {"Notify": "Notifications", "Location": "Location", "FAQ": "F.A.Q", "Version": "Version", "Policy": "Term of use", "PaymentPolicy": "Payment Policy", "SecurePolicy": "Secure Policy", "CompanyInfo": "Company Profile", "VietNam": "Vietnamese", "English": "English", "Title": "Setting", "Language": "Language"}, "ListCinema": {"Title": "BETA Theater"}, "Cinema": {"Screen": "SCREEN", "SeatTypes": "Seat Types", "Standard": "Standard", "VIP": "VIP", "Couple": "<PERSON><PERSON><PERSON>", "Premium": "Premium", "Available": "Available", "Selected": "Selected", "Sold": "Sold", "Reserved": "Reserved", "SelectSeats": "Select seats", "SelectedSeats": "Selected seats", "TotalPrice": "Total price", "ProceedToPayment": "Proceed to payment", "TimeLeft": "Time left", "SeatSelection": "Seat Selection"}, "Notification": {"Title": "Notifications", "MarkAllRead": "Mark all as read", "DeleteAll": "Delete all", "DeleteAllTitle": "Delete all notifications", "DeleteAllMessage": "All notifications will be permanently deleted. Are you sure you want to delete all notifications?", "DeleteTitle": "Delete notification", "DeleteMessage": "This notification will be permanently deleted. Are you sure you want to delete this notification?", "Yes": "Yes", "No": "No"}, "FAQ": {"Title": "F.A.Q"}, "AppVersion": {"Title": "App Information"}, "App": {"NewVersionAvailable": "New version available", "UsingLatestVersion": "You are using the latest version", "VersionSupport": "Supports iOS XXX and above", "CurrentVersion": "Current version"}, "Update": {"RequiredTitle": "Update Required", "AvailableTitle": "Update Available", "CurrentVersion": "Current Version", "LatestVersion": "Latest Version", "Later": "Later", "Update": "Update", "UpdateNow": "Update Now"}, "Version": {"Title": "Version Information", "AppVersion": "App Version", "Current": "Current", "Status": "Status", "UpToDate": "Up to date", "UpdateAvailable": "Update available", "UpdateRequired": "Update required", "CheckFailed": "Check failed", "CheckForUpdates": "Check for Updates", "Checking": "Checking for updates...", "CheckError": "Error checking for updates: {0}", "SupportInfo": "Support Information", "MinimumOS": "Minimum OS: {0} / {1}", "LastUpdated": "Last updated: {0}", "UpToDateMessage": "You are using the latest version", "CheckFailedMessage": "Failed to check for updates", "CheckErrorMessage": "Error: {0}"}, "Alert": {"CheckNewVersionFailed": "Failed to check for new version"}, "NewsAndDeals": {"Title": "News and Offers", "Promotion": "PROMOTIONS", "News": "NEWS"}, "NewsDetail": {"Title": "News and Offers", "Share": "SHARE"}, "BookingByFilm": {"Title": "Booking by Movie"}, "BookingByTheater": {"Title": "Booking by Theater"}, "AlertWarningLogin": "You must login to see notifications", "DontEmptySeat": "Don't leave the seat {seat_name} empty", "DontEmptyLeftRightSeat": "Please do not leave 1 seat empty at left or right of selected seats", "DontEmptyBetweenSeat": "Please do not leave 1 seat empty between selected seats", "SeatsTimeOut": "Time for selecting seats is out!", "UpdatePassword": {"Title": "Update Password"}, "Tab1": "Booking by\nMovies", "Tab2": "Booking by\nCinema", "Tab3": "Voucher\n", "Tab4": "Deals\n", "Tab5": "Other\n", "free_voucher": "Free Voucher", "your_voucher": "Your Voucher", "add_new_voucher": "Add new voucher", "voucher_history": "Voucher history used", "point_history": "Points History", "donate_voucher": "Don<PERSON>", "Message_401": "This session expired, please login again!", "out_of_voucher": "Out of Voucher", "hsd": "ED: ", "add_success": "Add new success", "add_voucher_success": "You have added this voucher to your storehouse", "donate_voucher_success": "donate voucher success", "donate_voucher_success_alert": "You have donated voucher for account \n #%s# \n (%s) \n success!", "donate_point_success": "donate point success", "donate_point_success_alert": "You have donated point for account \n #%s# \n (%s) \n success!", "CPref": {"ALL": "All", "ADMIN": "Administrator", "REJECTED": "Rejected", "WFA": "Wait Confirm", "WAIT_CONFIRM": "Wait Confirm", "APPROVED": "Approved", "COMPLETED": "Completed", "WAIT_TRANSFER": "Wait Transfer", "TRANSFER_CONFIRMED": "Transfer Confirmed", "UN_CONFIRM": "Un Confirm", "CANCELED": "Canceled", "DRAFT": "Draft", "MALE": "Male", "FEMALE": "<PERSON><PERSON><PERSON>", "KT": "Accountant", "ORDERER": "Order Side", "FARMER": "Farmer Side"}, "pages": {"login": {"introduction": {"Skip": "<PERSON><PERSON>", "Next": "Next", "Get started": "Get started"}, "login": {"Email address": "Email address", "Password": "Password", "Log in": "Log in", "Remember me": "Remember me", "Forgot password": "Forgot password", "Password reset": "Password reset", "terms of service": "By logging in, you agree to the application's terms of service and privacy policy", "You don't have an account yet?": "Don't have an account yet?", "Register": "Register", "log in Facebook": "Login with Facebook", "log in apple": "Sign in with Apple"}, "forgot_password": {"otp_title": "Forgot Password", "otp_body": "Please check your email to continue password recovery.", "otp_btn": "OK", "success": "Please check your email to continue password recovery.", "error": "Password recovery failed.", "email_not_found": "Email does not exist in the system.", "email_invalid": "Invalid email format.", "server_error": "An error occurred, please try again later."}, "otp_verification": {"Sign Up Success": "Sign Up Success", "Change password successfully": "Password changed successfully", "Confirm OTP": "Confirm OTP", "Check your registered email": "Check your registered email. We’ve sent you a PIN code", "Confirm": "Confirm"}, "register": {"Fullname": "Fullname", "Gender": "Gender", "Male": "Male", "Female": "Female", "Other": "Other", "Address": "Address", "birthDate": "Date of birth", "Phone number": "Phone number", "Re-enter password": "Re-enter password", "Account Type": "Account Type", "Professional Degree": "Professional Degree", "Do you already have an account?": "Register for a Beta Cinemas account"}}}, "utils": {"app_console": {"minutes ago": "minutes ago", "hours ago": "hours ago", "days ago": "days ago", "months ago": "months ago", "years ago": "years ago", "Agreement": "Agreement", "million": "million", "billion": "billion", "System management": "System management", "Undefined": "Undefined"}}, "widgets": {"drawer": {"Log out": "Log out"}, "form": {"input": {"rulesRequired": "Enter {}", "Please choose file": "Please choose file", "Enter": "Enter {}", "Choose": "Choose {}"}, "select": {"Search": "Search", "rulesRequired": "Choose {}"}, "date": {"Save": "Save", "Time from": "Start time", "Time to": "End time", "Error message": "You have not selected the full date", "rulesRequired": "Choose {}"}, "upload": {"Upload": "Upload", "Cancel": "Cancel", "Select image from gallery": "Select image from gallery", "Select multiple images from gallery": "Select multiple images from gallery", "Take a photo": "Take a photo", "Select Video from gallery": "Select video from gallery", "Shoot a movie": "Record a video"}}, "list": {"details": {"Empty": "Empty", "Phone number copied": "Phone number copied", "Successfully copied": "<PERSON><PERSON>d successfully"}}, "description_text_widget": {"See more": "See more"}}, "introduction": {"Title": {"0": "Support for organizations and businesses", "1": "Support for individuals and organizations", "2": "Open data provision"}, "Content": {"0": "Utilize open data sets", "1": "Share data with the community", "2": "Through web platforms, SMS, Zalo, and API"}}, "Voucher": {"MyVoucher": "My Vouchers", "Available": "Available", "Used": "Used", "Expired": "Expired", "LoginError": "Login check error: {0}", "LoadVoucherError": "Failed to load vouchers: {0}", "AddVoucher": "Add voucher", "VoucherHistory": "Voucher history", "NotLoggedIn": "You are not logged in", "LoginToViewVouchers": "Login to view and use your vouchers", "Login": "<PERSON><PERSON>", "NoVouchers": "No vouchers yet", "GetFreeVouchersOrAdd": "Get free vouchers or add new vouchers", "InvalidVoucherCode": "Invalid voucher code", "Error": "Error: {0}", "CannotGiftVoucher": "This voucher cannot be gifted to others", "DonateVoucher": "Gift voucher", "VoucherCode": "Voucher code", "EnterVoucherCode": "Enter the voucher code you want to gift", "RecipientEmail": "Recipient email", "EnterRecipientEmail": "Enter the email of the person you want to gift", "GiftVoucher": "Gift voucher", "DonateSuccess": "Voucher gifted successfully", "DonateError": "Cannot gift voucher", "DonateErrorMessage": "Gift voucher error: {0}", "DonateVoucherTitle": "Don<PERSON>", "VoucherCodeLabel": "Voucher Code: {0}", "RecipientEmailLabel": "Recipient Email", "Donate": "Donate", "EnterRecipientEmailError": "Please enter recipient email", "InvalidVoucherCodeError": "Invalid voucher code", "DonateSuccessMessage": "Voucher donated successfully", "DonateFailedMessage": "Failed to donate voucher", "AddVoucherTitle": "Add voucher", "AddNewVoucher": "Add new voucher", "EnterVoucherAndPin": "Enter voucher code and PIN to add to account", "VoucherCodeField": "Voucher code", "EnterVoucherCodeHint": "Enter your voucher code", "PinCode": "PIN code", "EnterPinHint": "Enter voucher PIN code", "ScanQR": "Scan QR code", "AddVoucherButton": "Add voucher", "Processing": "Processing...", "EnterVoucherCodeError": "Please enter voucher code", "EnterPinError": "Please enter PIN code", "RegisterSuccess": "Voucher registered successfully", "RegisterError": "Cannot register voucher. Code is invalid or already used.", "ScanSuccess": "Code scanned successfully", "ScanError": "Scan error: {0}", "ImportantNotes": "Important notes:", "Note1": "Voucher code and PIN can be found on gift cards or promotional emails", "Note2": "Voucher codes are case sensitive", "Note3": "Each voucher code can only be used once", "Note4": "Vouchers may have expiration dates, please check carefully", "Note5": "Contact support if you encounter issues registering vouchers", "FreeVoucher": "Free vouchers", "Retry": "Retry", "NoFreeVouchers": "No free vouchers available", "FailedToLoadFreeVouchers": "Failed to load free vouchers", "GetVoucher": "Get voucher", "OutOfStock": "Out of stock", "GetVoucherSuccess": "Voucher received successfully", "GetVoucherError": "Get voucher error: {0}", "AlreadyReceived": "You have already received this voucher", "LoginRequired": "Please login to receive vouchers"}, "Promotion": {"Title": "PROMOTIONS", "ViewAll": "View All", "NewsAndDeals": "News and Promotions", "PromotionSection": "Promotions", "Share": "Share", "NewPromotions": "NEW PROMOTIONS", "SideNews": "SIDE NEWS", "NewsAndOffers": "News and Offers"}, "Booking": {"ByMovie": "Book by movie", "ByTheater": "Book by theater", "PolicyCommitment": "I agree to comply with Beta Cinemas' terms and policies", "Payment": "Payment", "PaymentTitle": "Payment page", "CancelPayment": "Cancel payment", "CancelPaymentConfirm": "Are you sure you want to cancel payment?", "CancelPaymentMessage": "Canceling payment will lose your selected seats and you need to start over."}, "Account": {"PersonalInfo": "Personal information", "ChangeAvatar": "Change avatar", "ChoosePhoto": "Choose photo", "TakePhoto": "Take photo", "PhotoLibrary": "Photo library", "Camera": "Camera", "UpdateSuccess": "Update successful", "UpdateFailed": "Update failed"}, "Other": {"Title": "Other", "FreeVoucher": "Free vouchers", "CinemaList": "BETA Theaters", "Member": "BETA Membership", "Notification": "Notifications", "Recruitment": "Recruitment", "Setting": "Settings"}, "Recruitment": {"Title": "Recruitment", "JobOpportunities": "Job opportunities", "ApplyNow": "Apply now", "JobDescription": "Job description", "Requirements": "Requirements", "Benefits": "Benefits"}, "Register": {"RequiredInfo": "Required information", "AdditionalInfo": "Additional information", "PersonalId": "ID/Passport", "TermsAgreement": "You must agree to the terms of use", "FullNameRequired": "Full name is required", "InvalidEmailFormat": "Invalid email format", "PasswordMinLength": "Password must be at least 6 characters", "PasswordMismatch": "Password confirmation does not match", "PhoneRequired": "Phone number is required", "RegistrationFailed": "Registration failed", "RegistrationError": "Registration error", "TermsText": "I agree to comply with ", "PrivacyPolicy": "privacy policy", "And": " and ", "TermsOfUse": "terms of use", "OfBetacinemas": " of Betacinemas."}, "AccountInfo": {"BasicInfo": "Basic information", "ContactInfo": "Contact information", "FullName": "Full name", "Gender": "Gender", "BirthDate": "Birth date", "PhoneNumber": "Phone number", "Province": "Province/City", "District": "District", "Address": "Address"}, "Transaction": {"Detail": "Transaction details", "TransactionCode": "Transaction code", "TransactionSuccess": "Transaction successful", "MovieInfo": "Movie information", "TicketInfo": "Ticket information", "PaymentInfo": "Payment information", "Movie": "Movie", "Cinema": "Cinema", "ShowDate": "Show date", "ShowTime": "Show time", "TotalAmount": "Total amount", "AccumulatedPoints": "Accumulated points", "PointExpiryDate": "Point expiry date", "TransactionDate": "Transaction date", "ShareSuccess": "Transaction information copied", "LoadError": "Unable to load transaction information", "NoTransactionInfo": "No transaction information", "UnknownError": "An unknown error occurred", "Retry": "Retry", "Combo": "Combo", "ElectronicTicketCode": "Electronic ticket code", "PresentCodeAtCounter": "Please present this code at the counter to receive tickets", "NoTransactions": "No transactions yet", "Processing": "PROCESSING", "Room": "Room", "Seats": "Seats", "RefundPolicy": "Note: Beta does not accept refunds or exchanges for tickets that have been successfully paid on the Website and Beta Application"}}
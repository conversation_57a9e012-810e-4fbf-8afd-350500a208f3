# Register & Promotion Localization Fix

## Tổng Quan

Đã chuyển đổi các text constants còn thiếu trong `register.dart` (RichText trong _buildTermsCheckbox) và `promotion_screen.dart` (Tab texts) thành dạng đa ngôn ngữ.

## Files Đã Cập Nhật

### 1. **Register Screen - RichText Terms** (`lib/pages/access/register.dart`)

#### **Issue Fixed:**
RichText trong `_buildTermsCheckbox` method chứa hardcoded Vietnamese text

#### **Before:**
```dart
child: RichText(
  text: TextSpan(
    children: [
      const TextSpan(text: 'Tôi cam kết tuân theo '),
      TextSpan(
        text: 'ch<PERSON>h sách bảo mật',
        style: const TextStyle(color: Colors.blue, fontWeight: FontWeight.w500),
        recognizer: TapGestureRecognizer()..onTap = () => _openPrivacyPolicy(),
      ),
      const TextSpan(text: ' và '),
      TextSpan(
        text: 'đi<PERSON><PERSON> khoản sử dụng',
        style: const TextStyle(color: Colors.blue, fontWeight: FontWeight.w500),
        recognizer: TapGestureRecognizer()..onTap = () => _openTermsOfUse(),
      ),
      const TextSpan(text: ' của Betacinemas.'),
    ],
  ),
),
```

#### **After:**
```dart
child: RichText(
  text: TextSpan(
    children: [
      TextSpan(text: 'Register.TermsText'.tr()),
      TextSpan(
        text: 'Register.PrivacyPolicy'.tr(),
        style: const TextStyle(color: Colors.blue, fontWeight: FontWeight.w500),
        recognizer: TapGestureRecognizer()..onTap = () => _openPrivacyPolicy(),
      ),
      TextSpan(text: 'Register.And'.tr()),
      TextSpan(
        text: 'Register.TermsOfUse'.tr(),
        style: const TextStyle(color: Colors.blue, fontWeight: FontWeight.w500),
        recognizer: TapGestureRecognizer()..onTap = () => _openTermsOfUse(),
      ),
      TextSpan(text: 'Register.OfBetacinemas'.tr()),
    ],
  ),
),
```

### 2. **Promotion Screen - Tab Texts** (`lib/pages/promotion/promotion_screen.dart`)

#### **Issue Fixed:**
Tab texts trong TabBar chứa hardcoded Vietnamese text

#### **Before:**
```dart
tabs: const [
  Tab(text: 'KHUYẾN MÃI MỚI'),
  Tab(text: 'TIN BÊN LỀ'),
],
```

#### **After:**
```dart
tabs: [
  Tab(text: 'Promotion.NewPromotions'.tr()),
  Tab(text: 'Promotion.SideNews'.tr()),
],
```

#### **AppBar Title Also Fixed:**
```dart
// Before
appBar: appBar(title: 'Tin mới và Ưu đãi')

// After  
appBar: appBar(title: 'Promotion.NewsAndOffers'.tr())
```

## New Translation Keys Added

### **Vietnamese (vi.json):**
```json
"Register": {
  // ... existing keys ...
  "TermsText": "Tôi cam kết tuân theo ",
  "PrivacyPolicy": "chính sách bảo mật",
  "And": " và ",
  "TermsOfUse": "điều khoản sử dụng",
  "OfBetacinemas": " của Betacinemas."
},
"Promotion": {
  // ... existing keys ...
  "NewPromotions": "KHUYẾN MÃI MỚI",
  "SideNews": "TIN BÊN LỀ",
  "NewsAndOffers": "Tin mới và Ưu đãi"
}
```

### **English (en.json):**
```json
"Register": {
  // ... existing keys ...
  "TermsText": "I agree to comply with ",
  "PrivacyPolicy": "privacy policy",
  "And": " and ",
  "TermsOfUse": "terms of use",
  "OfBetacinemas": " of Betacinemas."
},
"Promotion": {
  // ... existing keys ...
  "NewPromotions": "NEW PROMOTIONS",
  "SideNews": "SIDE NEWS", 
  "NewsAndOffers": "News and Offers"
}
```

## Technical Details

### **RichText Localization Strategy:**
- **Segmented Approach**: Chia RichText thành các TextSpan nhỏ để dễ localize
- **Clickable Links Preserved**: Giữ nguyên functionality của clickable links
- **Styling Maintained**: Giữ nguyên styling cho các link texts

### **Tab Localization:**
- **Removed const**: Thay đổi từ `const []` thành `[]` để support dynamic text
- **Added Import**: Thêm `easy_localization` import
- **Consistent Naming**: Sử dụng naming convention nhất quán

## Summary of Changes

### **Files Modified:**
1. `lib/pages/access/register.dart` - **RichText với 5 text segments** converted
2. `lib/pages/promotion/promotion_screen.dart` - **3 text constants** converted
3. `assets/translations/vi.json` - **8 new Vietnamese keys** added
4. `assets/translations/en.json` - **8 new English keys** added

### **Text Constants Converted:**

#### **Register Screen:**
- Terms agreement text (5 segments)
- Privacy policy link text
- Terms of use link text
- Connecting words ("và" / "and")
- Company reference text

#### **Promotion Screen:**
- Tab 1: "KHUYẾN MÃI MỚI" / "NEW PROMOTIONS"
- Tab 2: "TIN BÊN LỀ" / "SIDE NEWS"
- AppBar title: "Tin mới và Ưu đãi" / "News and Offers"

## Language Examples

### **Terms Agreement Text:**

#### **Vietnamese:**
"Tôi cam kết tuân theo **chính sách bảo mật** và **điều khoản sử dụng** của Betacinemas."

#### **English:**
"I agree to comply with **privacy policy** and **terms of use** of Betacinemas."

### **Promotion Tabs:**

#### **Vietnamese:**
- Tab 1: "KHUYẾN MÃI MỚI"
- Tab 2: "TIN BÊN LỀ"

#### **English:**
- Tab 1: "NEW PROMOTIONS"  
- Tab 2: "SIDE NEWS"

## Benefits Achieved

### **✅ Complete Registration Flow:**
- Terms agreement text hoàn toàn localized
- Clickable links vẫn hoạt động bình thường
- Professional presentation trong cả 2 ngôn ngữ

### **✅ Promotion Screen Consistency:**
- Tab titles nhất quán với app language
- AppBar title cũng được localized
- User experience seamless

### **✅ Technical Quality:**
- RichText segments properly separated
- Clickable functionality preserved
- Clean code structure maintained

## Ready for Use

Bây giờ **TẤT CẢ** text constants trong register và promotion screens đều hỗ trợ đầy đủ đa ngôn ngữ:

### **Register Screen:**
- ✅ Form fields và validation messages
- ✅ Section titles
- ✅ **Terms agreement RichText** (FIXED)
- ✅ Error messages

### **Promotion Screen:**
- ✅ **Tab titles** (FIXED)
- ✅ **AppBar title** (FIXED)
- ✅ Content sections

Users có thể switch giữa tiếng Việt và tiếng Anh một cách seamless trong toàn bộ registration và promotion flows! 🎉

## Testing Recommendations

### **Register Screen:**
1. Test terms agreement text display trong cả 2 ngôn ngữ
2. Verify clickable links vẫn hoạt động
3. Check text wrapping và layout

### **Promotion Screen:**
1. Test tab switching với localized titles
2. Verify AppBar title changes với language
3. Check tab indicator alignment

Tất cả text constants đã được chuyển đổi thành công và sẵn sàng cho production! ✅

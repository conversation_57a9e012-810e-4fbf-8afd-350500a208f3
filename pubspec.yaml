name: flutter_app
description: "A new Flutter project 3.22.3"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ">=3.3.0 <4.0.0"

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.6
  introduction_screen: ^3.1.14
  flutter_svg: ^2.0.10+1
  pinput: ^5.0.0
  lottie: ^3.1.2
  go_router: ^14.2.1
  http: ^1.2.2
  extended_image: ^8.2.1
  flutter_bloc: ^8.1.6
  shared_preferences: ^2.3.0
  syncfusion_flutter_datepicker: ^26.2.7
  image_picker: ^1.1.2
  permission_handler: ^11.3.1
  path_provider: ^2.1.4
  url_launcher: ^6.3.0
  flutter_dotenv: ^5.1.0
  easy_localization: ^3.0.7
  image_gallery_saver: ^2.0.3
  flutter_hooks: ^0.20.5
  syncfusion_flutter_charts: ^26.2.7
  qr_code_scanner: ^1.0.1
  device_info_plus: ^10.1.1
  package_info_plus: ^8.0.2
  intl: ^0.19.0
  firebase_core: 3.3.0
  firebase_messaging: ^15.0.4
  flutter_local_notifications: ^17.2.2
  google_maps_flutter: ^2.7.0
  geocoding: ^2.2.2
  geolocator: ^12.0.0
  fl_location: ^4.0.0
  qr_flutter: ^4.1.0
  barcode: ^2.2.8
  barcode_widget: ^2.0.4
  mask_text_input_formatter: ^2.9.0
#  simple_barcode_scanner: ^0.1.1
  flutter_contacts: ^1.1.9
#  flutter_rating_bar: ^4.0.1
  dropdown_button2: ^2.3.9
  flutter_widget_from_html: ^0.15.2
  flutter_html: ^3.0.0-beta.2
  excel: ^2.0.1
  open_filex: ^4.5.0
  pdf: ^3.11.1
  flutter_slidable: ^3.1.1
  cart_stepper: ^4.3.0
  timeline_tile: ^2.0.0
  dotted_border: ^2.1.0
  syncfusion_flutter_signaturepad: ^26.2.14
  syncfusion_flutter_pdfviewer: ^26.2.14
  chewie: ^1.8.7
  signalr_netcore: ^1.3.0
  smooth_page_indicator:
  youtube_player_flutter: ^9.0.2
#  geolocator:
#  signalr_core: ^1.1.2
  webview_flutter: ^4.9.0
  webview_flutter_platform_interface: ^2.11.0
  webview_flutter_wkwebview: ^3.19.0
  flutter_facebook_auth: ^6.2.0
  sign_in_with_apple: ^6.1.2
  flutter_inappwebview: ^6.0.0
  app_links: ^6.0.0
  share_plus: ^10.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  integration_test:
    sdk: flutter
  faker: ^2.1.0
  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^4.0.0

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - .env.production
    - .env.development
    - assets/images/
    - assets/svgs/
    - assets/svgs/upload/
    - assets/svgs/splash/
    - assets/svgs/share/
    - assets/svgs/mime-type/
    - assets/form/
    - assets/
    - assets/icon/
    - assets/icon/cinema/
    - assets/icon/setting/
    - assets/icon/login/
    - assets/icon/menu/
    - assets/icon/tabbar/
    - assets/icon/tab/
    - assets/icon/tab/sneak/
    - assets/icon/tab/now showing/
    - assets/icon/tab/coming soon/
    - assets/json/
    - assets/translations/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
     - family: PlusJakartaSans
       fonts:
         - asset: assets/fonts/PlusJakartaSans-VariableFont_wght.ttf
     - family: SourceSansPro
       fonts:
         - asset: assets/fonts/Source_Sans_Pro/SourceSansPro-Regular.ttf
     - family: Oswald
       fonts:
         - asset: assets/fonts/Oswald/Oswald-Light.ttf

  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages



# Price Debug Analysis - <PERSON><PERSON><PERSON> Vé Gấp Đôi Cho Tất Cả Loạ<PERSON>hế

## Vấn Đề Mới Phát Hiện

User báo cáo: **"khi chọn ghế đơn hay ghế VIP giá tiền vẫn gấp đôi mặc dù không có ghế đôi"**

Điều này cho thấy vấn đề **KHÔNG CHỈ Ở GHẾ ĐÔI** mà ở **TẤT CẢ LOẠI GHẾ**.

## Possible Root Causes

### 1. **Ticket Types Mismatch**
```dart
// Có thể server trả về ticket types không đúng
final normalTicket = _listSeat?.ticketTypes?.firstWhere(
    (ticket) => ticket.isNormal, 
    orElse: () => TicketType(price: 0)  // ❌ Fallback to 0, but maybe wrong logic
);
```

### 2. **Seat Type Detection Wrong**
```dart
// Có thể seat.seatTypeEnum không được set đúng
if (seat.seatTypeEnum == SeatType.NORMAL) {
    // ❌ Có thể seat type không match
}
```

### 3. **Server-Side Price Recalculation**
```dart
// Server có thể ignore client price và tính lại từ booking model
final response = await api.film.booking(body: bookingModel.toJson());
// ❌ Server tính lại giá từ seats, không dùng TotalMoney
```

### 4. **Multiple Price Sources**
```dart
// Có thể có nhiều nơi tính giá và bị conflict
// 1. seat.dart updateTotalPrice() 
// 2. ios_style_webview_payment.dart booking model
// 3. Server-side calculation
// 4. WebView JavaScript calculation
```

## Debug Steps Added

### 1. **Seat Selection Debug** (`seat.dart`)
```dart
void updateTotalPrice() {
    print('💰 === PRICE CALCULATION START ===');
    print('💰 Selected seats count: ${_selectedSeats.length}');
    
    // Debug ticket types
    print('💰 Available ticket types:');
    _listSeat?.ticketTypes?.forEach((ticket) {
        print('💰   - ${ticket.seatTypeId}: ${ticket.price}đ (Normal: ${ticket.isNormal}, VIP: ${ticket.isVip}, Couple: ${ticket.isCouple})');
    });

    for (var seat in _selectedSeats) {
        print('💰 Processing seat: ${seat.seatNumber} - Type: ${seat.seatTypeEnum}');
        // ... price calculation with detailed logging
        print('💰 Added ${seatType} seat: ${seat.seatNumber} - Price: ${price} - Running total: $total');
    }
    
    print('💰 FINAL Total price calculated: $total');
}
```

### 2. **Booking Model Debug** (`ios_style_webview_payment.dart`)
```dart
CreateBookingModel? _createBookingModel() {
    // ... seat processing
    
    // Calculate total from booking model seats for verification
    final bookingModelTotal = seats.fold<int>(0, (sum, seat) => sum + seat.price);
    print('💰 Booking model total: $bookingModelTotal');
    print('💰 Widget totalPrice: ${widget.totalPrice}');
    print('💰 Selected seats count: ${widget.selectedSeats?.length}');
    print('💰 Booking seats count: ${seats.length}');
}
```

### 3. **JavaScript Debug** (`ios_style_webview_payment.dart`)
```dart
void _updateBookingInfo() {
    print('💰 DEBUG: widget.totalPrice = ${widget.totalPrice}');
    print('💰 DEBUG: formatted currency = ${_formatCurrency(widget.totalPrice ?? 0)}');
    
    final jsGetBookingInfo = '...' +
        ' bookingIf.TotalMoney = "${_formatCurrency(widget.totalPrice ?? 0)}";' +
        '...';
    
    print('💰 DEBUG: JavaScript TotalMoney = "${_formatCurrency(widget.totalPrice ?? 0)}"');
}
```

### 4. **Server Response Debug** (`ios_style_webview_payment.dart`)
```dart
Future<void> _loadBookingPayment() async {
    print('📤 Booking model JSON: ${bookingModel!.toJson()}');
    
    final response = await api.film.booking(body: bookingModel.toJson());
    final data = response?.data;
    
    print('📥 Booking response data type: ${data.runtimeType}');
    if (data is String) {
        final priceMatches = RegExp(r'(\d{1,3}(?:,\d{3})*)\s*đ').allMatches(data);
        print('💰 Found prices in HTML: ${priceMatches.map((m) => m.group(0)).toList()}');
    }
}
```

## Expected Debug Output

### **Normal Case (1 Normal Seat)**:
```
💰 === PRICE CALCULATION START ===
💰 Selected seats count: 1
💰 Available ticket types:
💰   - normal-id: 80000đ (Normal: true, VIP: false, Couple: false)
💰   - vip-id: 100000đ (Normal: false, VIP: true, Couple: false)
💰   - couple-id: 160000đ (Normal: false, VIP: false, Couple: true)
💰 Processing seat: A1 - Type: SeatType.NORMAL
💰 Added Normal seat: A1 - Price: 80000 - Running total: 80000
💰 FINAL Total price calculated: 80000

💰 Booking model total: 80000
💰 Widget totalPrice: 80000
💰 Selected seats count: 1
💰 Booking seats count: 1

💰 DEBUG: widget.totalPrice = 80000
💰 DEBUG: formatted currency = 80,000
💰 DEBUG: JavaScript TotalMoney = "80,000"

📤 Booking model JSON: {"ShowId":"...", "Seats":[{"SeatIndex":1,"SeatName":"A1","SeatType":"STARDAR","TicketTypeId":"normal-id","Price":80000}], "CountDown":"..."}
📥 Booking response data type: String
💰 Found prices in HTML: [80,000đ]
```

### **Problem Case (1 Normal Seat but Double Price)**:
```
💰 === PRICE CALCULATION START ===
💰 Selected seats count: 1
💰 Available ticket types:
💰   - normal-id: 80000đ (Normal: true, VIP: false, Couple: false)
💰 Processing seat: A1 - Type: SeatType.NORMAL
💰 Added Normal seat: A1 - Price: 80000 - Running total: 80000
💰 FINAL Total price calculated: 80000  // ✅ Correct in Flutter

💰 Booking model total: 80000  // ✅ Correct in booking model
💰 Widget totalPrice: 80000    // ✅ Correct passed to payment

💰 DEBUG: JavaScript TotalMoney = "80,000"  // ✅ Correct in JavaScript

📤 Booking model JSON: {"Seats":[{"Price":80000}]}  // ✅ Correct sent to server
📥 Found prices in HTML: [160,000đ]  // ❌ WRONG! Server returns double!
```

## Possible Issues to Check

### 1. **Server-Side Issue**
- Server có thể có bug tính giá gấp đôi
- Server ignore client price và tính lại sai
- Server có logic khác với client

### 2. **Ticket Type Mapping Issue**
```dart
// Check if ticket type detection is wrong
final normalTicket = _listSeat?.ticketTypes?.firstWhere(
    (ticket) => ticket.isNormal,  // ❌ Có thể isNormal logic sai
    orElse: () => TicketType(price: 0)
);
```

### 3. **Seat Type Enum Issue**
```dart
// Check if seat type enum is set correctly
if (seat.seatTypeEnum == SeatType.NORMAL) {  // ❌ Có thể enum không đúng
```

### 4. **WebView JavaScript Issue**
```javascript
// Check if JavaScript in webview modifies price
getBookingInfo(bookingIf);  // ❌ Có thể JS tính lại giá
```

## Next Steps

### 1. **Run Debug Test**
- Chọn 1 ghế thường
- Xem debug output
- Identify exact failure point

### 2. **Compare with iOS/Android**
- Check if iOS/Android có cùng vấn đề
- Compare API request/response
- Check server behavior

### 3. **Isolate Issue**
- Test với different seat types
- Test với different cinemas
- Test với different showtimes

### 4. **Fix Based on Root Cause**
- If server issue: Fix server logic
- If client issue: Fix Flutter logic
- If mapping issue: Fix ticket type detection

## Hypothesis

**Most Likely**: Server-side đang tính lại giá và có bug gấp đôi, ignore client price completely.

**Test**: So sánh request/response với iOS/Android để confirm.

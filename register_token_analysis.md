# Register Token API Analysis - iOS vs Android vs Flutter

## API Endpoint

**Tất cả 3 platforms sử dụng cùng endpoint:**
- **URL**: `/api/v1/erp/notifications/register-device-token`
- **Method**: `POST`
- **Purpose**: Register FCM/APNs token for push notifications

## iOS Implementation

### **1. <PERSON><PERSON>:**

#### **A. <PERSON>u <PERSON>hi <PERSON>gin Thành Công**
```swift
// Booking/Class/Controller/Authen/LoginViewController.swift
private func getProfile(id: String?, email: String? = nil) {
    // ... get profile logic
    self.registerDeviceToken(userId)  // ✅ Called after login
}

private func registerDeviceToken(_ accountId: String) {
    guard let token: String = UserDefaults.standard.object(forKey: DefaultKey.deviceToken.rawValue) as? String,
    let deviceId = UIDevice.current.identifierForVendor?.uuidString else {
        return
    }
    AccountProvider.rx.request(.registerDeviceToken(deviceId, accountId, token))
        .mapObject(DDKCResponse<RegisterDeviceToken>.self)
        .subscribe().disposed(by: disposeBag)
}
```

#### **B. Khi Nhận Device Token Từ APNs**
```swift
// Booking/App/AppDelegate+Initial.swift
func application(_ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
    let token = tokenParts.joined()
    UserDefaults.standard.set(token, forKey: DefaultKey.deviceToken.rawValue)
    
    if let accountId = Global.shared.user?.UserId,
        let deviceId = UIDevice.current.identifierForVendor?.uuidString {
        let _ = AccountProvider.rx.request(.registerDeviceToken(deviceId, accountId, token))
            .mapObject(DDKCResponse<RegisterDeviceToken>.self).asObservable().subscribe()
    }
}
```

### **2. Tại Đâu Được Gọi:**
- **LoginViewController.swift** - sau khi login thành công
- **AppDelegate+Initial.swift** - khi nhận device token từ system

## Android Implementation

### **1. Khi Nào Được Gọi:**

#### **A. Khi App Khởi Động (Nếu Đã Login)**
```kotlin
// app/src/main/java/vn/zenity/betacineplex/app/App.kt
override fun onCreate() {
    super.onCreate()
    Global.share().user = BetaDB.getInstance().userDao().getCurrentUser()
    if (Global.share().isLogin && !PreferencesHelper.shared.getBooleanValue(Constant.Key.isRegisterToken, false)) {
        registerFCMDevice()  // ✅ Called on app start if logged in
    }
}
```

#### **B. Sau Khi Login Thành Công**
```kotlin
// app/src/main/java/vn/zenity/betacineplex/view/auth/LoginPresenter.kt
private fun processResponse(response: DDKCReponse<UserModel>) {
    if (response.isSuccess) {
        // ... save user data
        disposable = APIClient.shared.accountAPI.getProfile(response.Data?.AccountId!!).applyOn().subscribe(
            { responseProfile ->
                if (responseProfile.isSuccess) {
                    responseProfile.Data?.Token = Global.share().token
                    Global.share().user = responseProfile.Data
                    App.shared().registerFCMDevice()  // ✅ Called after login
                }
            }
        )
    }
}
```

#### **C. Khi Nhận FCM Token Mới**
```kotlin
// app/src/main/java/vn/zenity/betacineplex/fcm/BetaFirebaseMessagingService.kt
override fun onNewToken(token: String) {
    super.onNewToken(token)
    val deviceId = DeviceHelper.shared.deviceId()
    if (token.isNotEmpty()) {
        logD("GCM TOKEN $token")
        val dis = APIClient.shared.accountAPI.registerFCMToken(mapOf(
            "DeviceId" to deviceId,
            "AccountId" to (Global.share().user?.AccountId ?: ""),
            "DeviceToken" to token,
            "DeviceType" to "android"
        )).applyOn().subscribe({
            if (it.isSuccess) {
                PreferencesHelper.shared.putValue(Constant.Key.isRegisterToken, true)
            }
        })
    }
}
```

#### **D. Khi Update User Info**
```kotlin
// app/src/main/java/vn/zenity/betacineplex/app/App.kt
fun updateUserInfo(accountId: String) {
    val disposable = APIClient.shared.accountAPI.getProfile(accountId).applyOn().subscribe(
        { responseProfile ->
            if (responseProfile.isSuccess) {
                responseProfile.Data?.Token = Global.share().token
                Global.share().user = responseProfile.Data
                App.shared().registerFCMDevice()  // ✅ Called when updating user info
            }
        }
    )
}

fun updateTopInfo() {
    if (!Global.share().isLogin) return
    val disposable = APIClient.shared.accountAPI.getTopInfo().applyOn().subscribe(
        { responseProfile ->
            if (responseProfile.isSuccess) {
                // ... update user data
                App.shared().registerFCMDevice()  // ✅ Called when updating top info
            }
        }
    )
}
```

### **2. Tại Đâu Được Gọi:**
- **App.kt** - app startup, update user info, update top info
- **LoginPresenter.kt** - sau khi login thành công
- **BetaFirebaseMessagingService.kt** - khi nhận FCM token mới

## Flutter Implementation

### **1. Khi Nào Được Gọi:**

#### **A. Khi Nhận FCM Token**
```dart
// lib/service/notification_service.dart
Future<void> _registerToken(String token) async {
    try {
        final deviceId = await _getDeviceId();
        final prefs = await SharedPreferences.getInstance();
        final accountId = prefs.getString(CPref.accountId) ?? '';

        debugPrint('🔔 Registering FCM token (UNIFIED)...');
        
        final api = Api();
        final response = await api.auth.registerFCMToken(
            deviceId: deviceId,
            accountId: accountId,
            deviceToken: token,
            deviceType: Platform.isIOS ? 'ios' : 'android',
        );
    } catch (e) {
        debugPrint('🔔 Error registering FCM token: $e');
    }
}
```

### **2. Tại Đâu Được Gọi:**
- **NotificationService.dart** - khi nhận FCM token từ Firebase

## So Sánh Timing

| Platform | App Start | After Login | Token Refresh | User Update |
|----------|-----------|-------------|---------------|-------------|
| **iOS** | ❌ | ✅ | ✅ | ❌ |
| **Android** | ✅ | ✅ | ✅ | ✅ |
| **Flutter** | ❌ | ❌ | ✅ | ❌ |

## API Request Format

### **iOS**:
```swift
AccountProvider.rx.request(.registerDeviceToken(deviceId, accountId, token))
// Parameters: deviceId, accountId, token
```

### **Android**:
```kotlin
APIClient.shared.accountAPI.registerFCMToken(mapOf(
    "DeviceId" to deviceId,
    "AccountId" to accountId,
    "DeviceToken" to token,
    "DeviceType" to "android"
))
```

### **Flutter**:
```dart
api.auth.registerFCMToken(
    deviceId: deviceId,
    accountId: accountId,
    deviceToken: token,
    deviceType: Platform.isIOS ? 'ios' : 'android',
)
```

## Key Differences

### **1. Timing Differences**

#### **iOS**:
- ✅ Called after login
- ✅ Called when receiving device token
- ❌ NOT called on app start
- ❌ NOT called on user info update

#### **Android**:
- ✅ Called on app start (if logged in)
- ✅ Called after login
- ✅ Called when receiving new FCM token
- ✅ Called when updating user info

#### **Flutter**:
- ❌ NOT called on app start
- ❌ NOT called after login
- ✅ Called when receiving FCM token
- ❌ NOT called on user info update

### **2. Missing Integration in Flutter**

Flutter **thiếu integration** trong:
1. **Login flow** - không call registerToken sau khi login
2. **App startup** - không call registerToken khi app start
3. **User update** - không call registerToken khi update user info

### **3. State Tracking**

#### **Android** (Most Complete):
```kotlin
// Track registration state
PreferencesHelper.shared.putValue(Constant.Key.isRegisterToken, true)
```

#### **iOS & Flutter**:
- Không có state tracking cho registration status

## Recommendations for Flutter

### **1. Add Login Integration**
```dart
// After successful login
await NotificationService.instance.registerTokenIfAvailable();
```

### **2. Add App Startup Integration**
```dart
// In main app initialization
if (isLoggedIn && !isTokenRegistered) {
    await NotificationService.instance.registerTokenIfAvailable();
}
```

### **3. Add State Tracking**
```dart
// Track registration status like Android
final prefs = await SharedPreferences.getInstance();
await prefs.setBool('isTokenRegistered', true);
```

## Kết Luận

**Android có implementation đầy đủ nhất**, gọi registerToken ở nhiều timing points.
**iOS có implementation cơ bản**, gọi sau login và khi nhận token.
**Flutter có implementation tối thiểu**, chỉ gọi khi nhận FCM token.

**Flutter cần bổ sung integration vào login flow và app startup để match iOS/Android behavior.**

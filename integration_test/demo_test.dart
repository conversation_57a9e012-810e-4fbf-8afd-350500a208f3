import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:flutter_app/main.dart';
import 'package:flutter_app/cubit/index.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_app/utils/index.dart';
import 'package:flutter_app/widgets/src/button/index.dart';

import '../test/common.dart';


void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  testWidgets('🎬 Demo: Beta Cinemas App Flow', (WidgetTester tester) async {
    print('🚀 Starting Beta Cinemas App Demo...');

    // 1. Initialize app
    print('📱 Step 1: Launching Beta Cinemas App...');
    await initAppWidgetTest(tester);
    await tester.pumpAndSettle();

    // Ensure user is logged out for demo
    try {
      final authCubit = tester.element(find.byType(MyApp)).read<AuthC>();
      authCubit.logout();
      await tester.pumpAndSettle();
      print('✅ User logged out for demo');
    } catch (e) {
      print('⚠️ Could not access auth cubit: $e');
    }

    // Wait for app to load completely
    await tester.pump(const Duration(seconds: 3));
    await tester.pumpAndSettle();

    // 2. Show home screen
    print('🏠 Step 2: Showing Home Screen with Movie Schedule...');
    await tester.pump(const Duration(seconds: 2)); // Pause for demo

    // 3. Find and tap login button
    print('🔍 Step 3: Looking for Login Button...');
    Finder? loginButtonFinder;

    if (find.byKey(const Key('login_button_homePage')).evaluate().isNotEmpty) {
      loginButtonFinder = find.byKey(const Key('login_button_homePage'));
      print('✅ Found login button in header');
    } else if (find.byType(OutlinedButton).evaluate().isNotEmpty) {
      loginButtonFinder = find.byType(OutlinedButton).first;
      print('✅ Found login button by type');
    } else {
      // Try bottom navigation
      try {
        await tester.tap(find.text('Khác\n '));
        await tester.pumpAndSettle();
        await tester.pump(const Duration(seconds: 1));

        if (find.text('pages.login.login.Log in'.tr()).evaluate().isNotEmpty) {
          loginButtonFinder = find.text('pages.login.login.Log in'.tr());
          print('✅ Found login button in "Khác" tab');
        }
      } catch (e) {
        print('⚠️ Could not find login button: $e');
      }
    }

    if (loginButtonFinder == null) {
      print('❌ Demo stopped: Login button not found');
      return;
    }

    // 4. Navigate to login screen
    print('🎯 Step 4: Tapping Login Button...');
    await tester.tap(loginButtonFinder);
    await tester.pumpAndSettle();
    await tester.pump(const Duration(seconds: 2)); // Pause for demo

    // 5. Verify login screen elements
    print('📝 Step 5: Showing Login Screen Elements...');

    // Check if we're on login screen
    if (find.byKey(const Key('loginButton')).evaluate().isNotEmpty) {
      print('✅ Login screen loaded successfully');

      // Show form fields
      if (find.byKey(const ValueKey('Email hoặc tên đăng nhập')).evaluate().isNotEmpty) {
        print('✅ Email field found');
      }

      if (find.byKey(ValueKey('pages.login.login.Password'.tr())).evaluate().isNotEmpty) {
        print('✅ Password field found');
      }

      if (find.text('pages.login.login.Forgot password'.tr() + '?').evaluate().isNotEmpty) {
        print('✅ Forgot password link found');
      }

      // 6. Demo login attempt
      print('🧪 Step 6: Demonstrating Login Process...');

      // Demo multiple login scenarios
      print('🔐 Step 7a: Testing Invalid Credentials...');
      await tester.enterText(find.byKey(const ValueKey('Email hoặc tên đăng nhập')), '<EMAIL>');
      await tester.pump(const Duration(seconds: 1));

      await tester.enterText(find.byKey(ValueKey('pages.login.login.Password'.tr())), 'wrongpass');
      await tester.pump(const Duration(seconds: 1));

      // Hide keyboard
      await SystemChannels.textInput.invokeMethod('TextInput.hide');
      await tester.pumpAndSettle();

      // Tap login button
      await tester.tap(find.byKey(const Key('loginButton')));
      await tester.pumpAndSettle();
      await tester.pump(const Duration(seconds: 2)); // Wait for error

      print('✅ Invalid login demonstrated');

      // Clear fields and try valid credentials
      print('🔐 Step 7b: Testing Valid Credentials...');
      await tester.enterText(find.byKey(const ValueKey('Email hoặc tên đăng nhập')), '<EMAIL>');
      await tester.pump(const Duration(seconds: 1));

      await tester.enterText(find.byKey(ValueKey('pages.login.login.Password'.tr())), '123123');
      await tester.pump(const Duration(seconds: 1));

      // Hide keyboard
      await SystemChannels.textInput.invokeMethod('TextInput.hide');
      await tester.pumpAndSettle();

      // Tap login button
      await tester.tap(find.byKey(const Key('loginButton')));
      await tester.pumpAndSettle();
      await tester.pump(const Duration(seconds: 5)); // Wait for login success

      // Check for successful login
      try {
        if (find.textContaining('Chào').evaluate().isNotEmpty) {
          print('✅ Login successful - found greeting message');
          print('🏠 Step 8: Returned to Home Screen with User Greeting');
          await tester.pump(const Duration(seconds: 2)); // Show success
        } else {
          print('⚠️ Login result unclear - continuing demo');
        }
      } catch (e) {
        print('⚠️ Login check: $e');
      }

      // 7. Show social login options (if still on login screen)
      print('📱 Step 9: Showing Social Login Options...');

      // Check for Facebook login
      if (find.text('pages.login.login.log in Facebook'.tr().toUpperCase()).evaluate().isNotEmpty) {
        print('✅ Facebook login option available');
      }

      // Check for Apple login (iOS only)
      if (find.text('pages.login.login.log in apple'.tr().toUpperCase()).evaluate().isNotEmpty) {
        print('✅ Apple login option available (iOS)');
      }

      // 8. Show register option
      if (find.text('pages.login.register.Do you already have an account?'.tr()).evaluate().isNotEmpty) {
        print('✅ Register option available');

        // Demo navigation to register (if still on login screen)
        print('📝 Step 10: Demonstrating Registration Flow...');
        if (find.text('pages.login.register.Do you already have an account?'.tr()).evaluate().isNotEmpty) {
          await tester.tap(find.text('pages.login.register.Do you already have an account?'.tr()));
          await tester.pumpAndSettle();
          await tester.pump(const Duration(seconds: 2));
          print('✅ Registration screen loaded');
        } else {
          print('⚠️ Already logged in - registration demo skipped');
        }
      }

    } else {
      print('⚠️ Login screen not loaded properly');
    }

    print('🎉 Demo completed successfully!');
    print('📋 Demo Summary:');
    print('   ✅ App launched');
    print('   ✅ Home screen displayed');
    print('   ✅ Login button found and tapped');
    print('   ✅ Login screen loaded');
    print('   ✅ Form fields verified');
    print('   ✅ Invalid login demonstrated');
    print('   ✅ Valid login attempted');
    print('   ✅ Login success/failure handled');
    print('   ✅ Social login options shown');
    print('   ✅ Registration flow demonstrated');
  });
}

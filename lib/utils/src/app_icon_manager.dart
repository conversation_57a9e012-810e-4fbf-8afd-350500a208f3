import 'dart:io';
import 'package:changeicon/changeicon.dart';
import 'package:firebase_remote_config/firebase_remote_config.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// App Icon Manager - Similar to iOS-devkhai AppIconManager
/// Manages dynamic app icon changes for both Android and iOS platforms
/// Uses Firebase Remote Config exactly like iOS-devkhai
 enum AppIcon {
    defaultIcon,
    hearts,
  }
class AppIconManager {
  static const String _defaultIcon = 'default';
  static const String _heartIcon = 'Heart';

  // Android activity aliases
  static const String _androidDefaultActivity = '.MainActivity';
  static const String _androidHeartActivity = '.MainActivityHeart';

  // Firebase Remote Config key - exactly like iOS-devkhai
  static const String _remoteConfigKey = 'AppIcon_IOS_Name';

  // SharedPreferences key to store user's manual choice
  static const String _userChoiceKey = 'user_selected_app_icon';

  /// Available app icons

  /// Get current app icon
  static Future<AppIcon> getCurrentIcon() async {
    try {
      if (Platform.isIOS) {
        final currentIcon = await Changeicon.getAlternateIconName();

        switch (currentIcon) {
          case _heartIcon:
            return AppIcon.hearts;
          case null:
          case _defaultIcon:
          default:
            return AppIcon.defaultIcon;
        }
      } else if (Platform.isAndroid) {
        final currentActivity = await Changeicon.getAlternateIconName();

        switch (currentActivity) {
          case _androidHeartActivity:
            return AppIcon.hearts;
          case _androidDefaultActivity:
          default:
            return AppIcon.defaultIcon;
        }
      }

      return AppIcon.defaultIcon;
    } catch (e) {
      print('Error getting current icon: $e');
      return AppIcon.defaultIcon;
    }
  }

  /// Set app icon and save user choice
  static Future<bool> setIcon(AppIcon icon) async {
    try {
      if (Platform.isIOS) {
        String? iconName;

        switch (icon) {
          case AppIcon.defaultIcon:
            iconName = null; // null means default icon
            break;
          case AppIcon.hearts:
            iconName = _heartIcon;
            break;
        }

        print('🔧 Setting iOS icon to: "$iconName"');
        await Changeicon.setAlternateIconName(iconName);

        // Save user's manual choice
        await _saveUserChoice(icon);

        print('✅ iOS app icon changed to: ${icon.name}');
        return true;

      } else if (Platform.isAndroid) {
        String activityName;

        switch (icon) {
          case AppIcon.defaultIcon:
            activityName = _androidDefaultActivity;
            break;
          case AppIcon.hearts:
            activityName = _androidHeartActivity;
            break;
        }

        await Changeicon.setAlternateIconName(activityName);

        // Save user's manual choice
        await _saveUserChoice(icon);

        print('Android app icon changed to: ${icon.name}');
        return true;
      }

      print('Dynamic app icons not supported on this platform');
      return false;
    } catch (e) {
      print('Error setting app icon: $e');
      return false;
    }
  }

  /// Check if alternate icons are supported
  static Future<bool> isSupported() async {
    try {
      if (Platform.isIOS) {
        return await Changeicon.supportsAlternateIcons;
      } else if (Platform.isAndroid) {
        // Android supports activity aliases, so always return true
        return true;
      }

      return false;
    } catch (e) {
      print('Error checking alternate icon support: $e');
      return false;
    }
  }

  /// Get icon display name for UI
  static String getIconDisplayName(AppIcon icon) {
    switch (icon) {
      case AppIcon.defaultIcon:
        return 'Default';
      case AppIcon.hearts:
        return 'Hearts';
    }
  }

  /// Get icon asset path for preview
  static String getIconAssetPath(AppIcon icon) {
    switch (icon) {
      case AppIcon.defaultIcon:
        return 'assets/images/logo.png'; // Default app icon
      case AppIcon.hearts:
        return 'assets/icon/app_icons/Heart2x@.png';
    }
  }

  /// Get all available icons
  static List<AppIcon> getAllIcons() {
    return AppIcon.values;
  }

  /// Toggle between default and heart icon
  static Future<bool> toggleIcon() async {
    final currentIcon = await getCurrentIcon();

    switch (currentIcon) {
      case AppIcon.defaultIcon:
        return await setIcon(AppIcon.hearts);
      case AppIcon.hearts:
        return await setIcon(AppIcon.defaultIcon);
    }
  }

  /// Set heart icon (convenience method)
  static Future<bool> setHeartIcon() async {
    return await setIcon(AppIcon.hearts);
  }

  /// Set default icon (convenience method)
  static Future<bool> setDefaultIcon() async {
    return await setIcon(AppIcon.defaultIcon);
  }

  /// Check if current icon is heart icon
  static Future<bool> isHeartIcon() async {
    final currentIcon = await getCurrentIcon();
    return currentIcon == AppIcon.hearts;
  }

  /// Check if current icon is default icon
  static Future<bool> isDefaultIcon() async {
    final currentIcon = await getCurrentIcon();
    return currentIcon == AppIcon.defaultIcon;
  }

  /// Update app icon from Firebase Remote Config - exactly like iOS-devkhai
  /// This should be called when app becomes active
  static Future<void> updateAppIconFromRemoteConfig() async {
    try {
      print('🔧 Updating app icon from Firebase Remote Config...');

      // Initialize Firebase Remote Config
      final remoteConfig = FirebaseRemoteConfig.instance;

      // Set defaults - exactly like iOS-devkhai
      await remoteConfig.setDefaults({
        _remoteConfigKey: '',
      });

      // Set config settings - exactly like iOS-devkhai (minimumFetchInterval = 0)
      await remoteConfig.setConfigSettings(RemoteConfigSettings(
        fetchTimeout: const Duration(seconds: 10),
        minimumFetchInterval: Duration.zero, // Like iOS-devkhai
      ));

      // Fetch and activate - exactly like iOS-devkhai
      await remoteConfig.fetchAndActivate();

      // Evaluate and apply icon
      await _evaluateAndApplyIcon(remoteConfig);

    } catch (e) {
      print('❌ Error updating app icon from Remote Config: $e');
    }
  }

  /// Evaluate and apply icon - exactly like iOS-devkhai evaluateAndApplyIcon()
  static Future<void> _evaluateAndApplyIcon(FirebaseRemoteConfig remoteConfig) async {
    try {
      // Check if user has made a manual choice - respect it!
      final hasChoice = await hasUserChoice();
      if (hasChoice) {
        print('👤 User has made manual icon choice - skipping Remote Config update');
        return;
      }

      // Get remote icon name - exactly like iOS-devkhai
      final remoteIconName = remoteConfig.getString(_remoteConfigKey);
      final currentIcon = await getCurrentIcon();

      print('🔍 Remote icon name: "$remoteIconName"');
      print('🔍 Current icon: ${currentIcon.name}');

      // If remoteIconName is empty → revert to default icon
      if (remoteIconName.isEmpty) {
        if (currentIcon != AppIcon.defaultIcon) {
          print('🔄 Reverting to default icon...');
          await _setIconWithoutSaving(AppIcon.defaultIcon);
        }
        return;
      }

      // Map remote icon name to AppIcon enum - exactly like iOS-devkhai
      AppIcon? targetIcon;
      switch (remoteIconName.toLowerCase()) {
        case 'heart':
          targetIcon = AppIcon.hearts;
          break;
        case 'default':
        case '':
          targetIcon = AppIcon.defaultIcon;
          break;
        default:
          print('⚠️ Unknown remote icon name: $remoteIconName');
          return;
      }

      // If current icon is different from remote → change icon (without saving as user choice)
      if (currentIcon != targetIcon) {
        print('🔄 Changing icon from ${currentIcon.name} to ${targetIcon.name}...');
        final success = await _setIconWithoutSaving(targetIcon);
        if (success) {
          print('✅ Icon changed successfully to: ${targetIcon.name}');
        } else {
          print('❌ Failed to change icon to: ${targetIcon.name}');
        }
      } else {
        print('✅ Icon already matches remote config: ${currentIcon.name}');
      }

    } catch (e) {
      print('❌ Error evaluating and applying icon: $e');
    }
  }

  /// Get remote config icon name for debugging
  static Future<String> getRemoteConfigIconName() async {
    try {
      final remoteConfig = FirebaseRemoteConfig.instance;
      return remoteConfig.getString(_remoteConfigKey);
    } catch (e) {
      print('❌ Error getting remote config icon name: $e');
      return '';
    }
  }

  /// Set app icon without saving as user choice (for Remote Config)
  static Future<bool> _setIconWithoutSaving(AppIcon icon) async {
    try {
      if (Platform.isIOS) {
        String? iconName;

        switch (icon) {
          case AppIcon.defaultIcon:
            iconName = null; // null means default icon
            break;
          case AppIcon.hearts:
            iconName = _heartIcon;
            break;
        }

        print('🔧 Setting iOS icon to: "$iconName" (Remote Config)');
        await Changeicon.setAlternateIconName(iconName);
        print('✅ iOS app icon changed to: ${icon.name} (Remote Config)');
        return true;

      } else if (Platform.isAndroid) {
        String activityName;

        switch (icon) {
          case AppIcon.defaultIcon:
            activityName = _androidDefaultActivity;
            break;
          case AppIcon.hearts:
            activityName = _androidHeartActivity;
            break;
        }

        await Changeicon.setAlternateIconName(activityName);
        print('Android app icon changed to: ${icon.name} (Remote Config)');
        return true;
      }

      print('Dynamic app icons not supported on this platform');
      return false;
    } catch (e) {
      print('❌ Error setting app icon (Remote Config): $e');
      return false;
    }
  }

  /// Save user's manual icon choice
  static Future<void> _saveUserChoice(AppIcon icon) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_userChoiceKey, icon.name);
      print('💾 Saved user icon choice: ${icon.name}');
    } catch (e) {
      print('❌ Error saving user choice: $e');
    }
  }

  /// Load user's manual icon choice
  static Future<AppIcon?> _loadUserChoice() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final choiceName = prefs.getString(_userChoiceKey);

      if (choiceName == null) return null;

      switch (choiceName) {
        case 'defaultIcon':
          return AppIcon.defaultIcon;
        case 'hearts':
          return AppIcon.hearts;
        default:
          return null;
      }
    } catch (e) {
      print('❌ Error loading user choice: $e');
      return null;
    }
  }

  /// Clear user's manual choice (allow Remote Config to take over)
  static Future<void> clearUserChoice() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_userChoiceKey);
      print('🗑️ Cleared user icon choice');
    } catch (e) {
      print('❌ Error clearing user choice: $e');
    }
  }

  /// Check if user has made a manual choice
  static Future<bool> hasUserChoice() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      return prefs.containsKey(_userChoiceKey);
    } catch (e) {
      print('❌ Error checking user choice: $e');
      return false;
    }
  }
}

import 'dart:io';
import 'package:changeicon/changeicon.dart';

/// App Icon Manager - Similar to iOS-devkhai AppIconManager
/// Manages dynamic app icon changes for both Android and iOS platforms
class AppIconManager {
  static const String _defaultIcon = 'default';
  static const String _heartIcon = 'Hearts';

  // Android activity aliases
  static const String _androidDefaultActivity = '.MainActivity';
  static const String _androidHeartActivity = '.MainActivityHeart';

  /// Available app icons
  enum AppIcon {
    defaultIcon,
    hearts,
  }

  /// Get current app icon
  static Future<AppIcon> getCurrentIcon() async {
    try {
      if (Platform.isIOS) {
        final currentIcon = await Changeicon.getAlternateIconName();

        switch (currentIcon) {
          case _heartIcon:
            return AppIcon.hearts;
          case null:
          case _defaultIcon:
          default:
            return AppIcon.defaultIcon;
        }
      } else if (Platform.isAndroid) {
        final currentActivity = await Changeicon.getCurrentActivity();

        switch (currentActivity) {
          case _androidHeartActivity:
            return AppIcon.hearts;
          case _androidDefaultActivity:
          default:
            return AppIcon.defaultIcon;
        }
      }

      return AppIcon.defaultIcon;
    } catch (e) {
      print('Error getting current icon: $e');
      return AppIcon.defaultIcon;
    }
  }

  /// Set app icon
  static Future<bool> setIcon(AppIcon icon) async {
    try {
      if (Platform.isIOS) {
        String? iconName;

        switch (icon) {
          case AppIcon.defaultIcon:
            iconName = null; // null means default icon
            break;
          case AppIcon.hearts:
            iconName = _heartIcon;
            break;
        }

        await Changeicon.setAlternateIconName(iconName);
        print('iOS app icon changed to: ${icon.name}');
        return true;

      } else if (Platform.isAndroid) {
        String activityName;

        switch (icon) {
          case AppIcon.defaultIcon:
            activityName = _androidDefaultActivity;
            break;
          case AppIcon.hearts:
            activityName = _androidHeartActivity;
            break;
        }

        await Changeicon.setActivity(activityName);
        print('Android app icon changed to: ${icon.name}');
        return true;
      }

      print('Dynamic app icons not supported on this platform');
      return false;
    } catch (e) {
      print('Error setting app icon: $e');
      return false;
    }
  }

  /// Check if alternate icons are supported
  static Future<bool> isSupported() async {
    try {
      if (Platform.isIOS) {
        return await Changeicon.supportsAlternateIcons();
      } else if (Platform.isAndroid) {
        // Android supports activity aliases, so always return true
        return true;
      }

      return false;
    } catch (e) {
      print('Error checking alternate icon support: $e');
      return false;
    }
  }

  /// Get icon display name for UI
  static String getIconDisplayName(AppIcon icon) {
    switch (icon) {
      case AppIcon.defaultIcon:
        return 'Default';
      case AppIcon.hearts:
        return 'Hearts';
    }
  }

  /// Get icon asset path for preview
  static String getIconAssetPath(AppIcon icon) {
    switch (icon) {
      case AppIcon.defaultIcon:
        return 'assets/images/logo.png'; // Default app icon
      case AppIcon.hearts:
        return 'assets/icon/app_icons/Hearts2x@.png';
    }
  }

  /// Get all available icons
  static List<AppIcon> getAllIcons() {
    return AppIcon.values;
  }

  /// Toggle between default and heart icon
  static Future<bool> toggleIcon() async {
    final currentIcon = await getCurrentIcon();

    switch (currentIcon) {
      case AppIcon.defaultIcon:
        return await setIcon(AppIcon.hearts);
      case AppIcon.hearts:
        return await setIcon(AppIcon.defaultIcon);
    }
  }

  /// Set heart icon (convenience method)
  static Future<bool> setHeartIcon() async {
    return await setIcon(AppIcon.hearts);
  }

  /// Set default icon (convenience method)
  static Future<bool> setDefaultIcon() async {
    return await setIcon(AppIcon.defaultIcon);
  }

  /// Check if current icon is heart icon
  static Future<bool> isHeartIcon() async {
    final currentIcon = await getCurrentIcon();
    return currentIcon == AppIcon.hearts;
  }

  /// Check if current icon is default icon
  static Future<bool> isDefaultIcon() async {
    final currentIcon = await getCurrentIcon();
    return currentIcon == AppIcon.defaultIcon;
  }
}

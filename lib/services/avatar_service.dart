import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_app/pages/voucher/api/api_test.dart';
import 'package:image_picker/image_picker.dart';
import 'package:image/image.dart' as img;
import 'package:http/http.dart' as http;
import 'package:shared_preferences/shared_preferences.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter_app/constants/index.dart';
import 'package:flutter_app/utils/index.dart';

/// Avatar Service - tương tự iOS ImagePickerController + uploadAvatar
class AvatarService {
  static final ImagePicker _picker = ImagePicker();

  /// Show image picker with options - tương tự iOS ImagePickerController.showImagePicker
  static Future<void> showImagePicker({
    required BuildContext context,
    required Function(String?) onImageSelected,
    bool allowEdit = true,
  }) async {
    try {
      // Show action sheet like iOS
      final result = await showModalBottomSheet<ImageSource>(
        context: context,
        backgroundColor: Colors.transparent,
        builder: (context) => _buildImagePickerBottomSheet(context),
      );

      if (result != null) {
        await _pickImageFromSource(
          source: result,
          allowEdit: allowEdit,
          onImageSelected: onImageSelected,
        );
      }
    } catch (e) {
      UDialog().showError(
        title: 'Lỗi',
        text: 'Không thể mở trình chọn ảnh: $e',
      );
    }
  }

  /// Build image picker bottom sheet - tương tự iOS action sheet
  static Widget _buildImagePickerBottomSheet(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(88),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: const BoxDecoration(
              border: Border(bottom: BorderSide(color: Colors.grey, width: 0.5)),
            ),
            child: const Text(
              'Chọn ảnh đại diện',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.black87,
              ),
            ),
          ),

          // Camera option - tương tự iOS "Bt.CaptureImage".localized
          ListTile(
            leading: const Icon(Icons.camera_alt, color: Colors.blue),
            title: const Text('Chụp ảnh'),
            onTap: () => Navigator.pop(context, ImageSource.camera),
          ),

          // Gallery option - tương tự iOS "Bt.ChooseFromLibrary".localized
          ListTile(
            leading: const Icon(Icons.photo_library, color: Colors.blue),
            title: const Text('Chọn từ thư viện'),
            onTap: () => Navigator.pop(context, ImageSource.gallery),
          ),

          // Cancel button
          Container(
            margin: const EdgeInsets.all(16),
            width: double.infinity,
            child: TextButton(
              onPressed: () => Navigator.pop(context),
              style: TextButton.styleFrom(
                backgroundColor: Colors.grey.shade100,
                padding: const EdgeInsets.symmetric(vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
              ),
              child: const Text(
                'Hủy',
                style: TextStyle(
                  color: Colors.black87,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Pick image from source - tương tự iOS showImagePickerImmediate
  static Future<void> _pickImageFromSource({
    required ImageSource source,
    required bool allowEdit,
    required Function(String?) onImageSelected,
  }) async {
    try {
      final XFile? pickedFile = await _picker.pickImage(
        source: source,
        imageQuality: 80, // Compress like iOS
        maxWidth: 1024,   // Resize like iOS (1024x1024)
        maxHeight: 1024,
      );

      if (pickedFile != null) {
        // Process and resize image like iOS
        final processedImagePath = await _processImage(pickedFile.path);
        onImageSelected(processedImagePath);
      } else {
        onImageSelected(null);
      }
    } catch (e) {
      UDialog().showError(
        title: 'Lỗi',
        text: 'Không thể chọn ảnh: $e',
      );
      onImageSelected(null);
    }
  }

  /// Process and resize image - tương tự iOS image.resize(CGSize(width: 1024, height: 1024))
  static Future<String> _processImage(String imagePath) async {
    try {
      final File imageFile = File(imagePath);
      final Uint8List imageBytes = await imageFile.readAsBytes();

      // Decode image
      img.Image? image = img.decodeImage(imageBytes);
      if (image == null) return imagePath;

      // Resize to max 1024x1024 like iOS
      if (image.width > 1024 || image.height > 1024) {
        image = img.copyResize(
          image,
          width: image.width > image.height ? 1024 : null,
          height: image.height > image.width ? 1024 : null,
        );
      }

      // Encode as JPEG with 80% quality like iOS (0.8)
      final List<int> processedBytes = img.encodeJpg(image, quality: 80);

      // Save processed image
      final String processedPath = imagePath.replaceAll('.', '_processed.');
      final File processedFile = File(processedPath);
      await processedFile.writeAsBytes(processedBytes);

      return processedPath;
    } catch (e) {
      print('Error processing image: $e');
      return imagePath; // Return original if processing fails
    }
  }

  /// Upload avatar to server - tương tự iOS uploadAvatar và Android uploadAvatar
  static Future<bool> uploadAvatar({
    required String imagePath,
    required String accountId,
    required Function() onSuccess,
    required Function(String) onError,
  }) async {
    try {
      // Compress and encode image as base64 like iOS/Android
      final File imageFile = File(imagePath);

      // Compress image like Android (80% quality) and iOS (0.8 quality)
      final img.Image? originalImage = img.decodeImage(await imageFile.readAsBytes());
      if (originalImage == null) {
        onError('Không thể đọc file ảnh');
        return false;
      }

      // Compress to JPEG with 80% quality like Android
      final Uint8List compressedBytes = Uint8List.fromList(
        img.encodeJpg(originalImage, quality: 80)
      );
      final String imageBase64 = base64Encode(compressedBytes);

      // Get auth token
      final prefs = await SharedPreferences.getInstance();
      final String? token = prefs.getString(CPref.token);

      if (token == null || token.isEmpty) {
        onError('Vui lòng đăng nhập lại');
        return false;
      }

      // Get device ID like iOS/Android
      final String deviceId = await _getDeviceId();

      // Prepare request body exactly like iOS/Android
      final Map<String, dynamic> body = {
        'ImageBase64': imageBase64,
        'Extension': '.jpg',
        'DeviceId': deviceId,  // ✅ Add DeviceId like iOS/Android
      };

      // Prepare headers
      final Map<String, String> headers = {
        'Authorization': 'Bearer $token',
        'Content-Type': 'application/json',
      };

      // Make API call using same endpoint as iOS/Android: "api/v1/erp/accounts/{id}/avatar"
      final response = await http.put(
        Uri.parse('${ApiService.baseUrl}/api/v1/erp/accounts/$accountId/avatar'),
        headers: headers,
        body: jsonEncode(body),
      );

      print('🔄 Upload avatar response: ${response.statusCode}');
      print('📄 Response body: ${response.body}');

      if (response.statusCode == 200) {
        final Map<String, dynamic> responseData = jsonDecode(response.body);

        // Check response format like iOS/Android
        if (responseData['Status'] == 1 ) {
          // Get avatar data from response
          final avatarData = responseData['Object'] ?? responseData['Data'];
          if (avatarData != null && avatarData['AvatarUrl'] != null) {
            // Save new avatar URL like iOS: user.Picture = avatar.AvatarUrl
            await _updateUserAvatar(avatarData['AvatarUrl']);
            onSuccess();
            return true;
          } else {
            onSuccess(); // Success even without avatar URL
            return true;
          }
        } else {
          onError(responseData['Message'] ?? 'Không thể cập nhật ảnh đại diện');
          return false;
        }
      } else {
        onError('Lỗi server: ${response.statusCode}');
        return false;
      }
    } catch (e) {
      onError('Lỗi upload ảnh: $e');
      return false;
    }

    return false;
  }

  /// Update user avatar in local storage - tương tự iOS Global.shared.saveUser(user)
  static Future<void> _updateUserAvatar(String avatarUrl) async {
    try {
      final prefs = await SharedPreferences.getInstance();

      // Get current user data using correct key
      final String? userDataString = prefs.getString(CPref.user);
      if (userDataString != null) {
        final Map<String, dynamic> userData = jsonDecode(userDataString);

        // Update Picture field like iOS: user.Picture = avatar.AvatarUrl
        userData['Picture'] = avatarUrl;

        // Save updated user data
        await prefs.setString(CPref.user, jsonEncode(userData));

        print('✅ Updated user avatar in local storage: $avatarUrl');
      }
    } catch (e) {
      print('❌ Error updating user avatar: $e');
    }
  }

  /// Get device ID - tương tự iOS UIDevice.current.identifierForVendor?.uuidString và Android DeviceHelper.shared.deviceId()
  static Future<String> _getDeviceId() async {
    try {
      final DeviceInfoPlugin deviceInfo = DeviceInfoPlugin();
      if (Platform.isAndroid) {
        final AndroidDeviceInfo androidInfo = await deviceInfo.androidInfo;
        return androidInfo.id; // Android device ID
      } else if (Platform.isIOS) {
        final IosDeviceInfo iosInfo = await deviceInfo.iosInfo;
        return iosInfo.identifierForVendor ?? ''; // iOS identifierForVendor like iOS code
      }
      return '';
    } catch (e) {
      print('❌ Error getting device ID: $e');
      // Fallback to timestamp
      return DateTime.now().millisecondsSinceEpoch.toString();
    }
  }

  /// Clean up temporary files
  static Future<void> cleanupTempFiles() async {
    try {
      // Clean up processed image files
      // Implementation depends on your temp file strategy
    } catch (e) {
      print('Error cleaning up temp files: $e');
    }
  }
}

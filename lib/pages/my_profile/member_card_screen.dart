import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_app/constants/index.dart';
import 'package:flutter_app/core/index.dart';
import 'package:flutter_app/cubit/index.dart';
import 'package:flutter_app/models/index.dart';
import 'package:flutter_app/utils/index.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:barcode/barcode.dart';
import 'package:flutter_svg/flutter_svg.dart';

class MemberCardScreen extends StatefulWidget {
  const MemberCardScreen({super.key});

  @override
  State<MemberCardScreen> createState() => _MemberCardScreenState();
}

class _MemberCardScreenState extends State<MemberCardScreen> {
  bool _isLoading = true;
  MUser? _user;
  List<dynamic> _cardClasses = [];
  String? _currentCardClass;
  double _totalBill = 0;

  @override
  void initState() {
    super.initState();

    _loadData();
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Get user data
      final authState = context.read<AuthC>().state;
      if (authState.user != null) {
        _user = authState.user;
        _totalBill = _user?.totalBillPayment ?? 0;

        // Get card classes
        final api = RepositoryProvider.of<Api>(context);
        final cardClassResponse = await api.auth.getCardClass();

        if (cardClassResponse != null && cardClassResponse.isSuccess) {
          setState(() {
            _cardClasses = cardClassResponse.listObject ?? [];

            // Find current card class
            if (_cardClasses.isNotEmpty) {
              final currentCard = _cardClasses.firstWhere(
                (card) => card.classId == _user?.classId,
                orElse: () => _cardClasses.first,
              );

              _currentCardClass = currentCard.code;
            }
          });
        }
      }
    } catch (e) {
      print('Error loading member card data: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar:appBar(
        title: 'MemberCard.Title'.tr(),
        titleColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _user == null
              ? Center(child: Text('MemberCard.LoginRequired'.tr()))
              : SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildMemberCard(),
                      const SizedBox(height: 24),
                      _buildCardLevels(),
                      const SizedBox(height: 24),
                      _buildCardBenefits(),
                    ],
                  ),
                ),
    );
  }

  Widget _buildMemberCard() {
    final cardColor = _getCardColor();

    return Container(
      width: double.infinity,
      height: 200,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: cardColor,
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 10,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Stack(
        children: [
          // Background pattern
          const Positioned(
            right: -20,
            bottom: -20,
            child: Opacity(
              opacity: 0.1,
              child: Icon(
                Icons.local_movies,
                size: 150,
                color: Colors.white,
              ),
            ),
          ),

          // Card content
          Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'MemberCard.BetaCinema'.tr(),
                      style: const TextStyle(
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                    Text(
                      _currentCardClass ?? 'MEMBER',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: Colors.white,
                      ),
                    ),
                  ],
                ),

                const Spacer(),

                // Barcode
                if (_user?.cardNumber != null && _user!.cardNumber!.isNotEmpty)
                  Container(
                    padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Column(
                      children: [
                        SizedBox(
                          height: 50,
                          child: FutureBuilder<String>(
                            future: _generateBarcode(_user!.cardNumber!),
                            builder: (context, snapshot) {
                              if (snapshot.hasData) {
                                return SvgPicture.string(
                                  snapshot.data!,
                                  height: 50,
                                );
                              }
                              return const SizedBox(
                                height: 50,
                                child: Center(child: CircularProgressIndicator()),
                              );
                            },
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          _user!.cardNumber!,
                          style: const TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),

                const Spacer(),

                // Member info
                Text(
                  _user?.name ?? '',
                  style: const TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.white,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${'Profile.MemberSince'.tr()} ${_formatDate(_user?.createdDate ?? '')}',
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCardLevels() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'MemberCard.CardLevels'.tr(),
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ...List.generate(
          _cardClasses.length,
          (index) => _buildCardLevelItem(_cardClasses[index]),
        ),
      ],
    );
  }

  Widget _buildCardLevelItem(dynamic cardClass) {
    final isCurrentClass = cardClass.code == _currentCardClass;
    final totalPaymentCondition = cardClass.totalPaymentCondition ?? 0;
    final progress = _totalBill / (totalPaymentCondition > 0 ? totalPaymentCondition : 1);
    final clampedProgress = progress.clamp(0.0, 1.0);

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isCurrentClass ? Colors.red.withOpacity(0.1) : Colors.grey.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isCurrentClass ? Colors.red : Colors.transparent,
          width: 2,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                cardClass.code ?? 'MEMBER',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: isCurrentClass ? Colors.red : Colors.black,
                ),
              ),
              if (isCurrentClass)
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.red,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    'MemberCard.Current'.tr(),
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            cardClass.description ?? '',
            style: const TextStyle(
              fontSize: 14,
              color: Colors.black87,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${'MemberCard.MinimumSpending'.tr()} ${_formatCurrency(totalPaymentCondition)} đ',
                style: const TextStyle(
                  fontSize: 14,
                  color: Colors.black54,
                ),
              ),
              Text(
                '${(clampedProgress * 100).toInt()}%',
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: isCurrentClass ? Colors.red : Colors.black54,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: LinearProgressIndicator(
              value: clampedProgress,
              backgroundColor: Colors.grey.withOpacity(0.2),
              valueColor: AlwaysStoppedAnimation<Color>(
                isCurrentClass ? Colors.red : Colors.grey,
              ),
              minHeight: 8,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCardBenefits() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'MemberCard.Benefits'.tr(),
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        _buildBenefitItem(
          icon: Icons.local_movies,
          title: 'MemberCard.PointsForTickets'.tr(),
          description: 'MemberCard.PointsForTicketsDesc'.tr(),
        ),
        _buildBenefitItem(
          icon: Icons.fastfood,
          title: 'MemberCard.ConcessionDiscount'.tr(),
          description: 'MemberCard.ConcessionDiscountDesc'.tr(),
        ),
        _buildBenefitItem(
          icon: Icons.card_giftcard,
          title: 'MemberCard.BirthdayGift'.tr(),
          description: 'MemberCard.BirthdayGiftDesc'.tr(),
        ),
        _buildBenefitItem(
          icon: Icons.notifications,
          title: 'MemberCard.EarlyNotification'.tr(),
          description: 'MemberCard.EarlyNotificationDesc'.tr(),
        ),
      ],
    );
  }

  Widget _buildBenefitItem({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.red.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(icon, color: Colors.red),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.black87,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  List<Color> _getCardColor() {
    if (_currentCardClass == null) {
      return [Colors.grey.shade700, Colors.grey.shade900];
    }

    if (_currentCardClass!.contains('VIP')) {
      return [Colors.purple.shade700, Colors.deepPurple.shade900];
    } else if (_currentCardClass!.contains('GOLD')) {
      return [Colors.amber.shade700, Colors.orange.shade900];
    } else if (_currentCardClass!.contains('SILVER')) {
      return [Colors.blueGrey.shade400, Colors.blueGrey.shade700];
    } else {
      return [Colors.red.shade700, Colors.red.shade900];
    }
  }

  Future<String> _generateBarcode(String data) async {
    final bc = Barcode.code128();
    return bc.toSvg(
      data,
      width: 300,
      height: 80,
      fontHeight: 0,
    );
  }

  String _formatCurrency(int value) {
    return value.toString().replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]}.',
    );
  }

  String _formatDate(String dateString) {
    if (dateString.isEmpty) return '';

    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return dateString;
    }
  }
}

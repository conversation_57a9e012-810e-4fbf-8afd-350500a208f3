part of 'my_account_info.dart';

class _AccountInfo extends StatefulWidget {
  final MUser user;
  final Function(dynamic newValue)? onUpdateUser;

  const _AccountInfo({required this.user, required this.onUpdateUser});

  @override
  State<_AccountInfo> createState() => _AccountInfoState();
}

class _AccountInfoState extends State<_AccountInfo> {
  final double _widthImage = CSpace.width * 0.2;
  MUser? newUser;

  @override
  Widget build(BuildContext context) {
    return BlocListener<_EditBloc, bool>(
      listener: (context, isEdit) {
        if (isEdit) {
          // Refresh form items when switching to edit mode
          _refreshFormItems();
        }
      },
      child: WForm<MUser>(
        list: _listFormItem,
        builder: (items) {
          return CustomScrollView(
            physics: const ClampingScrollPhysics(),
            slivers: [
              SliverToBoxAdapter(
                child: Padding(
                  padding: const EdgeInsets.symmetric(horizontal: CSpace.xl5),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const VSpacer(CSpace.xl3),
                      BlocBuilder<_EditBloc, bool>(builder: (context, isEdit) {
                        if (isEdit) {
                          return editInfo(items);
                        } else {
                          return listInfo();
                        }
                      }),
                    ],
                  ),
                ),
              ),
            ],
          );
        },
      ),
    );
  }

  @override
  void initState() {
    context.read<BlocC<MUser>>().setStatus(status: AppStatus.init);

    _init();
    super.initState();
  }

  List<MFormItem> _listFormItem = [];

  Future<void> _init() async {
    newUser = widget.user.copyWith();
    widget.onUpdateUser?.call(newUser);

    // Ensure we have proper data before building form items
    await _buildFormItems();
  }

  Future<void> _buildFormItems() async {
    List<MOption> opts = [
      MOption(value: '1', label: 'Nam'),
      MOption(value: '2', label: 'Nữ'),
      MOption(value: '3', label: 'Khác'),
    ];

    // Safely get gender index with null safety
    int optIndex = -1;
    if (widget.user.gender != null) {
      optIndex = opts.indexWhere((element) => int.parse(element.value) == widget.user.gender);
    }

    _listFormItem = [
      // BASIC INFO SECTION - tương tự iOS "THÔNG TIN CƠ BẢN"

      // Email (read-only) - tương tự iOS lbEmail
      MFormItem(
        name: 'Email',
        label: 'Email',
        value: widget.user.email,
        isWantNewInput: false,
        keyBoard: EFormItemKeyBoard.email,
        enabled: false,
      ),

      // Full Name - tương tự iOS tfName
      MFormItem(
        name: 'Fullname',
        label: 'Họ và tên',
        value: widget.user.name,
        isWantNewInput: false,
        onValidator: (value, listController) {
          if (value.isEmpty) {
            return 'Vui lòng nhập họ và tên';
          }
          return null;
        },
      ),

      // Gender - tương tự iOS tfGender (moved up to match iOS order)
      MFormItem(
        name: 'Gender',
        label: 'Giới tính',
        value:
            optIndex > -1 ? opts[optIndex].label : (widget.user.genderString ?? _getGenderString(widget.user.gender)),
        code: optIndex > -1 ? opts[optIndex].value : "${widget.user.gender ?? ''}",
        type: EFormItemType.select,
        items: opts,
        isWantNewInput: false,
        required: false,
        selectLabel: (MOption item) => item.label,
        selectValue: (MOption item) => item.value,
        format: (dynamic item) => item,
        onChange: (value, listController) {
          // Update newUser when gender changes
          if (value != null) {
            newUser?.gender = int.tryParse(value.toString());
            setState(() {
              widget.onUpdateUser?.call(newUser);
            });
          }
        },
        itemSelect: (item, int index, selected) => Container(
            padding: const EdgeInsets.symmetric(horizontal: CSpace.xl3),
            child: itemList(
              title: Text(item.label,
                  style: TextStyle(fontSize: CFontSize.sm, color: selected ? Colors.white : Colors.black)),
            )),
      ),

      // Birth Date - tương tự iOS tfBirthdate
      MFormItem(
        name: 'BirthDate',
        label: 'Ngày sinh',
        value: widget.user.birthdate,
        type: EFormItemType.date,
        isWantNewInput: false,
        // selectDateType: SelectDateType.before,
        required: false,
        onChange: (value, listController) {
          newUser?.birthdate = value;
          setState(() {
            widget.onUpdateUser?.call(newUser);
          });
        },
        onValidator: (value, listController) {
          if (value == null || value.isEmpty) return null;

          try {
            // Parse từ dd/MM/yyyy sang DateTime
            final parts = value.split('/');
            if (parts.length != 3) return 'Ngày không hợp lệ';

            final day = int.parse(parts[0]);
            final month = int.parse(parts[1]);
            final year = int.parse(parts[2]);

            final selectedDate = DateTime(year, month, day);
            final now = DateTime.now();

            // Kiểm tra chênh lệch năm
            final differenceInYears = now.year - selectedDate.year;

            // Nếu cùng năm nhưng chưa đến ngày thì giảm bớt 1 năm
            if (differenceInYears == 10 &&
                (now.month < selectedDate.month || (now.month == selectedDate.month && now.day < selectedDate.day))) {
              return 'Năm sinh không hợp lệ';
            }

            if (differenceInYears < 10) {
              return 'Năm sinh không hợp lệ';
            }

            return null; // Hợp lệ
          } catch (e) {
            return 'Năm sinh không hợp lệ';
          }
        },
      ),

      // CONTACT INFO SECTION - tương tự iOS "THÔNG TIN LIÊN HỆ"

      // Personal ID/CMND - tương tự iOS cmndTextField (moved to contact section)
      MFormItem(
        name: 'PersonalId',
        label: 'CMND/CCCD',
        value: widget.user.personalId ?? '',
        isWantNewInput: false,
        required: false,
        keyBoard: EFormItemKeyBoard.phone,
        onValidator: (value, listController) {
          if (value.isNotEmpty && value.length < 9) {
            return 'CMND/CCCD không hợp lệ';
          }
          return null;
        },
      ),

      // Phone Number - tương tự iOS tfPhoneNumber (moved to contact section)
      MFormItem(
        name: 'PhoneOffice',
        label: 'Số điện thoại',
        value: widget.user.phoneNumber,
        keyBoard: EFormItemKeyBoard.phone,
        onValidator: (value, listController) {
          if (value.isEmpty) {
            return 'Vui lòng nhập số điện thoại';
          } else if ((value as String).length != 10 || !RegExp(r'^(?:[+0]9)?[0-9]{10,12}$').hasMatch(value)) {
            return 'Số điện thoại không hợp lệ';
          }
          return null;
        },
        isWantNewInput: false,
      ),
      // City - matches iOS tfCity and Android selectionCity
      MFormItem(
        name: 'AddressCityId',
        label: 'Tỉnh/Thành phố',
        code: '${widget.user.addressCityId}|${widget.user.addressCity}',
        value: widget.user.addressCity ?? '',
        required: false,
        isWantNewInput: false,
        suffix: const Icon(Icons.keyboard_arrow_down),
        type: EFormItemType.select,
        api: (filter, page, size, sort) => RepositoryProvider.of<Api>(context).city.getListCity(filter: {...filter
        }),
        selectLabel: (MCityModel item) => item.name,
        selectValue: (MCityModel item) => '${item.id}|${item.name}',
        // Include both ID and name
        format: (dynamic item) => MCityModel.fromJson(item),
        onChange: (value, listController) {
          // Clear district when city changes - matches iOS/Android behavior
          listController['AddressDistrictId']?.clear();
          if (value != null && value.toString().contains('|')) {
            final parts = value.toString().split('|');
            newUser?.addressCityId = parts[0]; // Keep as String
            newUser?.addressCity = parts[1];
          }
          setState(() {
            widget.onUpdateUser?.call(newUser);
          });
        },
        itemSelect: (item, index, selected) => Container(
            padding: const EdgeInsets.symmetric(horizontal: CSpace.xl3),
            child: ListTile(
              title: Text(
                item.name,
                style: selected ? const TextStyle(color: Colors.white) : null,
              ),
              selected: selected,
            )),
      ),
      // District - matches iOS tfDistrict and Android selectionDistrict
      MFormItem(
        name: 'AddressDistrictId',
        label: 'Quận/Huyện',
        value: widget.user.addressDistrict ?? '',
        code: '${widget.user.addressDistrictId}|${widget.user.addressDistrict}',
        isWantNewInput: false,
        required: false,
        suffix: const Icon(Icons.keyboard_arrow_down),
        type: EFormItemType.select,
        api: (filter, page, size, sort) {
          // Get cityId from current city selection or original user data
          String? cityId = newUser?.addressCityId?.toString() ?? widget.user.addressCityId?.toString();
          if (cityId == null || cityId.isEmpty) {
            return Future.value(null); // No city selected, can't load districts
          }
          return RepositoryProvider.of<Api>(context).city.getDistrictOfCity(cityId: cityId);
        },
        selectLabel: (MCityModel item) => item.name,
        selectValue: (MCityModel item) => '${item.id}|${item.name}',
        // Include both ID and name
        format: (dynamic item) => MCityModel.fromJson(item),
        onChange: (value, listController) {
          if (value != null && value.toString().contains('|')) {
            final parts = value.toString().split('|');
            newUser?.addressDistrictId = parts[0]; // Keep as String
            newUser?.addressDistrict = parts[1];
          }
          setState(() {
            widget.onUpdateUser?.call(newUser);
          });
        },
        itemSelect: (item, index, selected) => Container(
            padding: const EdgeInsets.symmetric(horizontal: CSpace.xl3),
            child: ListTile(
              title: Text(
                item.name,
                style: selected ? const TextStyle(color: Colors.white) : null,
              ),
              selected: selected,
            )),
      ),

      // Address - tương tự iOS tfAddress
      MFormItem(
        name: 'AddressStreet',
        label: 'Địa chỉ',
        value: widget.user.addressStreet ?? '',
        isWantNewInput: false,
        required: false,
      ),
    ];
  }

  /// Refresh form items to ensure correct display values
  void _refreshFormItems() async {
    // Rebuild form items with fresh data
    await _buildFormItems();
    if (mounted) {
      setState(() {
        // Trigger rebuild with updated form items
      });
    }
  }

  /// Get gender string from gender integer
  String _getGenderString(int? gender) {
    switch (gender) {
      case 1:
        return 'Nam';
      case 2:
        return 'Nữ';
      case 3:
        return 'Khác';
      default:
        return '';
    }
  }

  Widget editInfo(Map<String, Widget> items) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Basic Info Section - tương tự iOS "THÔNG TIN CƠ BẢN"
        _buildSectionTitle('AccountInfo.BasicInfo'.tr()),
        items['Email'] ?? const SizedBox.shrink(),
        items['Fullname'] ?? const SizedBox.shrink(),
        items['Gender'] ?? const SizedBox.shrink(),
        items['BirthDate'] ?? const SizedBox.shrink(),

        // Contact Info Section - tương tự iOS "THÔNG TIN LIÊN HỆ"
        const VSpacer(16),
        _buildSectionTitle('AccountInfo.ContactInfo'.tr()),
        items['PersonalId'] ?? const SizedBox.shrink(),
        items['PhoneOffice'] ?? const SizedBox.shrink(),
        items['AddressCityId'] ?? const SizedBox.shrink(),
        items['AddressDistrictId'] ?? const SizedBox.shrink(),
        items['AddressStreet'] ?? const SizedBox.shrink(),
      ],
    );
  }

  Widget listInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Basic Info Section - tương tự iOS "THÔNG TIN CƠ BẢN"
        _buildSectionTitle('AccountInfo.BasicInfo'.tr()),
        info(title: 'Email', content: widget.user.email ?? ''),
        const VSpacer(8),
        info(title: 'AccountInfo.FullName'.tr(), content: widget.user.name),
        const VSpacer(8),
        info(
            title: 'AccountInfo.Gender'.tr(),
            content: widget.user.genderString ?? _getGenderString(widget.user.gender)),
        const VSpacer(8),
        info(title: 'AccountInfo.BirthDate'.tr(), content: Convert.date(widget.user.birthdate ?? '')),

        // Contact Info Section - tương tự iOS "THÔNG TIN LIÊN HỆ"
        // Always show contact info section since phone number is required
        const VSpacer(16),
        _buildSectionTitle('AccountInfo.ContactInfo'.tr()),
        info(title: 'Register.PersonalId'.tr(), content: widget.user.personalId ?? ''),
        const VSpacer(8),

        info(title: 'AccountInfo.PhoneNumber'.tr(), content: widget.user.phoneNumber?.toString() ?? ''),
        const VSpacer(8),
        info(title: 'AccountInfo.Province'.tr(), content: widget.user.addressCity ?? ''),

        const VSpacer(8),
        info(title: 'AccountInfo.District'.tr(), content: widget.user.addressDistrict ?? ''),

        const VSpacer(8),
        info(title: 'AccountInfo.Address'.tr(), content: widget.user.addressStreet ?? ''),
      ],
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12.0),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: CColor.primary,
        ),
      ),
    );
  }

  Widget info({required String title, required String content}) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(title, style: TextStyle(fontWeight: FontWeight.w500, fontSize: CFontSize.lg, color: CColor.black)),
        const VSpacer(1),
        Container(
            width: CSpace.width,
            decoration: BoxDecoration(
                color: CColor.black[0], border: const Border(bottom: BorderSide(color: Colors.black12, width: 1))),
            padding: const EdgeInsets.fromLTRB(0, 10, 10, 10),
            child: Text(content,
                style: const TextStyle(color: Colors.black, fontSize: CFontSize.lg, fontWeight: FontWeight.bold))),
        const VSpacer(12),
      ],
    );
  }

  final ImagePicker _picker = ImagePicker();

  /// Build avatar widget with proper URL construction - tương tự Android/iOS
  Widget _buildAvatarWidget() {
    // Get picture from user - tương tự Android user.Picture và iOS user.Picture
    final String? picturePath = widget.user.picture ?? widget.user.avatarUrl;

    if (picturePath != null && picturePath.isNotEmpty) {
      // Build full URL like Android/iOS
      final String fullAvatarUrl = '${ApiService.baseUrlImage}$picturePath';

      if (fullAvatarUrl.isNotEmpty) {
        return Image.network(
          fullAvatarUrl,
          errorBuilder: (context, error, stackTrace) {
            // Fallback to initials if image fails to load
            return Text(
              widget.user.name.length >= 2 ? widget.user.name.substring(0, 2) : widget.user.name,
              style: const TextStyle(color: Colors.white),
            );
          },
        );
      }
    }

    // Show initials if no avatar
    return Text(
      widget.user.name.length >= 2 ? widget.user.name.substring(0, 2) : widget.user.name,
      style: const TextStyle(color: Colors.white),
    );
  }
}

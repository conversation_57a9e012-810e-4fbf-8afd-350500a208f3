import 'package:flutter/material.dart';
import 'package:flutter_app/core/index.dart';
import 'package:flutter_app/cubit/index.dart';
import 'package:flutter_app/models/index.dart';
import 'package:flutter_app/models/transaction_history_model.dart';
import 'package:flutter_app/pages/cinema/payment/transaction_detail_screen.dart';
import 'package:flutter_app/utils/index.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

/// Transaction History Screen - tương tự iOS TranferHistoryViewController
class TransactionHistoryScreen extends StatefulWidget {
  const TransactionHistoryScreen({super.key});

  @override
  State<TransactionHistoryScreen> createState() => _TransactionHistoryScreenState();
}

class _TransactionHistoryScreenState extends State<TransactionHistoryScreen> {
  bool _isLoading = true;
  MUser? _user;
  List<TransactionHistoryModel> _transactions = [];

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// Load transaction data - tương tự iOS getData()
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Get user data
      final authState = context.read<AuthC>().state;
      if (authState.user != null) {
        _user = authState.user;
        final userId = _user?.accountId ?? _user?.id ?? '';

        if (userId.isEmpty) {
          print('❌ User ID is empty');
          return;
        }

        // Get transaction history - tương tự iOS AccountProvider.rx.request(.getTransactionHistory(userId))
        final api = RepositoryProvider.of<Api>(context);
        final transactionResponse = await api.auth.getTransactionHistory(userId);

        if (transactionResponse != null ) {
          final List<dynamic> rawTransactions = transactionResponse.data['content'] ?? [];
          setState(() {
            _transactions = rawTransactions
                .map((json) => TransactionHistoryModel.fromJson(json))
                .toList();
          });
          print('✅ Loaded ${_transactions.length} transactions');
        } else {
          print('❌ Transaction API failed: ${transactionResponse?.message}');
        }
      }
    } catch (e) {
      print('❌ Error loading transaction history: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: appBar(
        title: 'Lịch sử giao dịch', // tương tự iOS "Transaction History"
        titleColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _user == null
              ? const Center(child: Text('Vui lòng đăng nhập để xem lịch sử giao dịch'))
              : _transactions.isEmpty
                  ? _buildEmptyState()
                  : _buildTransactionList(),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.receipt_long,
            size: 80,
            color: Colors.grey.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          const Text(
            'Chưa có giao dịch nào',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _loadData,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.red,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('Làm mới'),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionList() {
    return RefreshIndicator(
      onRefresh: _loadData,
      color: Colors.red,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _transactions.length,
        itemBuilder: (context, index) {
          final transaction = _transactions[index];
          return _buildTransactionItem(transaction);
        },
      ),
    );
  }

  /// Build transaction item - tương tự iOS TransactionHistoryCell
  Widget _buildTransactionItem(TransactionHistoryModel transaction) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
      ),
      child: InkWell(
        onTap: () => _navigateToTransactionDetail(transaction),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Film name - tương tự iOS lbFilmName
              Text(
                transaction.filmName ?? 'Giao dịch',
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 8),

              // Cinema name - tương tự iOS lbFilmCinema
              Row(
                children: [
                  const Icon(Icons.movie, size: 16, color: Colors.grey),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      transaction.cinemaName ?? 'N/A',
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.black87,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),

              // Show time - tương tự iOS lbFilmDate
              Row(
                children: [
                  const Icon(Icons.calendar_today, size: 16, color: Colors.grey),
                  const SizedBox(width: 8),
                  Text(
                    transaction.formattedShowTime,
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),

              // Invoice ID - tương tự iOS transaction ID
              Row(
                children: [
                  const Icon(Icons.confirmation_number, size: 16, color: Colors.grey),
                  const SizedBox(width: 8),
                  Text(
                    'Mã giao dịch: ${transaction.invoiceId ?? 'N/A'}',
                    style: const TextStyle(
                      fontSize: 14,
                      color: Colors.black87,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),

              // Total payment - tương tự iOS lbMoney
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  // Points if available - tương tự iOS lbPoint
                  if (transaction.quantityPoint != null && transaction.quantityPoint! > 0)
                    Text(
                      '+${transaction.quantityPoint} điểm',
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.green,
                        fontWeight: FontWeight.w500,
                      ),
                    )
                  else
                    const SizedBox.shrink(),

                  Text(
                    '${transaction.formattedTotalPayment} đ',
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: Colors.red,
                    ),
                  ),
                ],
              ),

              // Waiting indicator if needed - tương tự iOS waitingView
              if (transaction.isWaiting) ...[
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.orange.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: const Text(
                    'ĐANG XỬ LÝ',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.orange,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  /// Navigate to transaction detail - tương tự iOS push TransactionDetailViewController
  void _navigateToTransactionDetail(TransactionHistoryModel transaction) {
    if (transaction.invoiceId == null || transaction.invoiceId!.isEmpty) {
      UDialog().showError(
        title: 'Lỗi',
        text: 'Không tìm thấy mã giao dịch',
      );
      return;
    }

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TransactionDetailScreen(
          transactionId: transaction.invoiceId!,
          userId: _user?.id ?? _user?.accountId ?? '',
          transactionItem: transaction,
        ),
      ),
    );
  }
}

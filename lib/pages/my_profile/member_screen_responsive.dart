import 'dart:io';
import 'dart:math' as math;
import 'package:device_info_plus/device_info_plus.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_app/core/index.dart';
import 'package:flutter_app/cubit/index.dart';
import 'package:flutter_app/models/index.dart';
import 'package:flutter_app/pages/my_profile/member_card_list_screen.dart';
import 'package:flutter_app/pages/my_profile/reward_points_screen.dart';
import 'package:flutter_app/pages/my_profile/transaction_history_screen.dart';
import 'package:flutter_app/service/notification_service.dart';
import 'package:flutter_app/utils/index.dart';
import 'package:flutter_app/services/avatar_service.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:barcode/barcode.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '../../constants/index.dart';
import '../voucher/api/api_test.dart';

/// Responsive Member Screen - tương tự iOS MemberViewController với responsive design
class ResponsiveMemberScreen extends StatefulWidget {
  const ResponsiveMemberScreen({super.key});

  @override
  State<ResponsiveMemberScreen> createState() => _ResponsiveMemberScreenState();
}

class _ResponsiveMemberScreenState extends State<ResponsiveMemberScreen> {
  bool _isLoading = true;
  MUser? _user;
  String? _cardClass;
  double _totalBill = 0;
  int _totalPoint = 0;
  int _remainingPoint = 0;
  String _remainingPointDate = '';
  double _progress = 0.0;

  // Responsive breakpoints
  static const double _tabletBreakpoint = 600;
  static const double _desktopBreakpoint = 1200;

  // Menu items - tương tự iOS initMenu()
  late List<MenuItemData> _menuItems;

  @override
  void initState() {
    super.initState();
    _initMenuItems();
    _loadData();
  }

  void _initMenuItems() {
    _menuItems = [
      MenuItemData(
        title: 'Member.BetaPoint'.tr(),
        icon: Icons.stars,
        onTap: _navigateToRewardPoints,
      ),
      MenuItemData(
        title: 'Member.Intro'.tr(),
        icon: Icons.people,
        onTap: _navigateToIntroFriends,
      ),
      MenuItemData(
        title: 'Member.TransactionHistory'.tr(),
        icon: Icons.history,
        onTap: _navigateToTransactionHistory,
      ),
      MenuItemData(
        title: 'Member.MemberCard'.tr(),
        icon: Icons.credit_card,
        onTap: _navigateToMemberCard,
      ),
      MenuItemData(
        title: 'Member.AccountInfo'.tr(),
        icon: Icons.person,
        onTap: () {
          context.pushNamed(CRoute.myAccountInfo);
        },
      ),
      MenuItemData(
        title: 'Member.ChangePass'.tr(),
        icon: Icons.lock,
        onTap: () {
          context.pushNamed(CRoute.myAccountPass);
        },
      ),
    ];
  }

  /// Get responsive dimensions based on screen size
  ResponsiveDimensions _getResponsiveDimensions() {
    final screenWidth = MediaQuery.of(context).size.width;
    final screenHeight = MediaQuery.of(context).size.height;
    final isTablet = screenWidth >= _tabletBreakpoint;
    final isDesktop = screenWidth >= _desktopBreakpoint;

    return ResponsiveDimensions(
      // Header height: 15% on mobile, max 150px on tablet/desktop
      headerHeight: isTablet ? math.min(screenHeight * 0.12, 150) : math.min(screenHeight * 0.15, 120),

      // Avatar size: 20% on mobile, max 100px on tablet/desktop
      avatarSize: isTablet ? math.min(screenWidth * 0.15, 100) : math.min(screenWidth * 0.2, 80),

      // Padding: Larger on tablet/desktop
      contentPadding: isTablet ? 24.0 : 16.0,

      // Card margin: Larger on tablet/desktop
      cardMargin: isTablet ? 16.0 : 8.0,

      // Max content width for desktop
      maxContentWidth: isDesktop ? 800.0 : double.infinity,

      // Font sizes
      titleFontSize: isTablet ? 24.0 : 20.0,
      bodyFontSize: isTablet ? 18.0 : 16.0,
      captionFontSize: isTablet ? 16.0 : 14.0,
    );
  }

  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
     await context.read<AuthC>().check(context: context);
      final authState = context.read<AuthC>().state;
      if (authState.user != null) {
        _user = authState.user;

        // Get card class
        final api = RepositoryProvider.of<Api>(context);
        final cardClassResponse = await api.auth.getCardClass();

        if (cardClassResponse != null) {
          final cardClasses = cardClassResponse.data['content'] ?? [];

          if (cardClasses.isNotEmpty) {
            // Find current card class
            final currentCard = cardClasses.firstWhere(
              (card) => card['classId'] == _user?.classId,
              orElse: () => cardClasses.first,
            );

            // Find top card class
            final topCard = cardClasses.last;

            setState(() {
              _cardClass = currentCard['code'];
              _totalBill = _user?.totalBillPayment ?? 0;
              _totalPoint = _user?.totalPoint ?? 0;
              _remainingPoint = _user?.almostExpiredPoint ?? 0;
              _remainingPointDate = _user?.almostExpiredPointDate ?? '';

              // Calculate progress
              final vipMoney = topCard['totalPaymentCondition'] ?? 3000000;
              if (_cardClass != null && _cardClass!.contains('VIP')) {
                _progress = 1.0;
              } else {
                _progress = _totalBill / vipMoney;
              }
            });
          }
        }
      }
    } catch (e) {
      print('Error loading member data: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final dimensions = _getResponsiveDimensions();
    const Color primaryBlue = Color(0xFF2196F3);

    return Scaffold(
      backgroundColor: Colors.white,
      appBar: appBar(
        title: 'Profile.BetaMember'.tr(),
        backgroundColor: primaryBlue,
        titleColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _user == null
              ? Center(child: Text('Profile.LoginRequired'.tr()))
              : SafeArea(
                  // ✅ Safe area handling
                  child: Center(
                    child: ConstrainedBox(
                      constraints: BoxConstraints(
                        maxWidth: dimensions.maxContentWidth, // ✅ Max width for desktop
                      ),
                      child: SingleChildScrollView(
                        child: Column(
                          children: [
                            _buildResponsiveMemberHeader(dimensions),
                            _buildResponsiveMenuItems(dimensions),
                            SizedBox(height: dimensions.contentPadding),
                            _buildResponsiveLogoutButton(dimensions),
                            SizedBox(height: dimensions.contentPadding * 2),
                          ],
                        ),
                      ),
                    ),
                  ),
                ),
    );
  }

  // Navigation methods
  void _navigateToRewardPoints() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const RewardPointsScreen()),
    );
  }

  void _navigateToIntroFriends() {
    UDialog().showError(
      title: 'Profile.Notification'.tr(),
      text: 'Profile.FeatureInDevelopment'.tr(),
    );
  }

  void _navigateToTransactionHistory() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const TransactionHistoryScreen()),
    );
  }

  void _navigateToMemberCard() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => BlocProvider(
          create: (context) => BlocC(),
          child: const MemberCardListScreen(),
        ),
      ),
    );
  }

  void _showDeleteAccountConfirmation() {
    UDialog().showConfirm(
      title: 'Profile.DeleteAccount'.tr(),
      body: Text(
        'Profile.DeleteAccountConfirm'.tr(),
        textAlign: TextAlign.center,
      ),
      btnOkText: 'Profile.DeleteAccount'.tr(),
      btnOkOnPress: () async {
        final preferences = await SharedPreferences.getInstance();
        await preferences.setString('delete_account', _user?.id ?? _user?.accountId ?? '');
        _logout();
      },
    );
  }

  Future<String> _getDeviceId() async {
    final deviceInfo = DeviceInfoPlugin();

    if (Platform.isIOS) {
      final iosInfo = await deviceInfo.iosInfo;
      return iosInfo.identifierForVendor ?? 'unknown_ios_device';
    } else if (Platform.isAndroid) {
      final androidInfo = await deviceInfo.androidInfo;
      return androidInfo.id;
    }

    return 'unknown_device';
  }

  Future<void> _logout() async {
    context.read<AuthC>().logout();
    final notifi =  NotificationService();
    notifi.unregisterFCMToken();
    context.pop();
    context.pop();
  }

  /// Handle avatar tap - tương tự iOS changeAvatarBtPressed
  void _handleAvatarTap() {
    if (_user?.id == null && _user?.accountId == null) {
      UDialog().showError(
        title: 'Profile.Error'.tr(),
        text: 'Profile.LoginRequiredAvatar'.tr(),
      );
      return;
    }

    AvatarService.showImagePicker(
      context: context,
      allowEdit: true,
      onImageSelected: (imagePath) {
        if (imagePath != null) {
          _uploadAvatar(imagePath);
        }
      },
    );
  }

  /// Upload avatar - tương tự iOS uploadAvatar
  void _uploadAvatar(String imagePath) {
    // Show loading state
    setState(() {
      _isLoading = true;
    });

    AvatarService.uploadAvatar(
      imagePath: imagePath,
      accountId: _user?.accountId ?? _user?.id ?? '', // ✅ Use AccountId first like iOS/Android
      onSuccess: () {
        // Hide loading
        setState(() {
          _isLoading = false;
        });

        // Show success message
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Profile.AvatarUpdateSuccess'.tr()),
            backgroundColor: Colors.green,
          ),
        );

        // Refresh user data like iOS loadCurrentAvatar()
        _refreshUserData();
      },
      onError: (error) {
        // Hide loading
        setState(() {
          _isLoading = false;
        });

        // Show error message
        UDialog().showError(
          title: 'Profile.Error'.tr(),
          text: error,
        );
      },
    );
  }

  /// Refresh user data after avatar update - tương tự iOS loadCurrentAvatar()
  Future<void> _refreshUserData() async {
    try {
      // Refresh auth state to get updated user data
      await context.read<AuthC>().check(context: context);

      // Reload local data
      await _loadData();
    } catch (e) {
      print('Error refreshing user data: $e');
    }
  }

  // Helper methods
  String _formatCurrency(int value) {
    return value.toString().replaceAllMapped(
          RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
          (Match m) => '${m[1]}.',
        );
  }

  String _formatDate(String dateString) {
    if (dateString.isEmpty) return '';
    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return dateString;
    }
  }

  Future<String> _generateBarcode(String data) async {
    final bc = Barcode.code128();
    return bc.toSvg(data, width: 300, height: 80, fontHeight: 0);
  }

  /// Build responsive member header - tương tự iOS headerView
  Widget _buildResponsiveMemberHeader(ResponsiveDimensions dimensions) {
    const Color primaryBlue = Color(0xFF2196F3);
    const Color darkBlue = Color(0xFF1976D2);

    return Column(
      children: [
        // Banner section - Responsive height
        SizedBox(
          height: dimensions.headerHeight,
          child: Stack(
            children: [
              // Background banner
              Container(
                height: dimensions.headerHeight,
                decoration: const BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [primaryBlue, darkBlue],
                  ),
                ),
                child: (_user?.avatarUrl != null && _user!.avatarUrl.isNotEmpty) ||
                    (_user?.picture != null && _user!.picture!.isNotEmpty)
                    ? Container(
                        decoration: BoxDecoration(
                          image: _buildAvatarImage()
                        ),
                      )
                    : null,
              ),
              // Gradient overlay
              Container(
                height: dimensions.headerHeight,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      primaryBlue.withValues(alpha: 0.6),
                      darkBlue.withValues(alpha: 0.8),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),

        // White card container - Responsive overlap
        Transform.translate(
          offset: Offset(0, -dimensions.headerHeight * 0.3), // Responsive overlap
          child: Container(
            margin: EdgeInsets.symmetric(horizontal: dimensions.cardMargin),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Padding(
              padding: EdgeInsets.all(dimensions.contentPadding),
              child: Column(
                children: [
                  // Avatar and name - Responsive sizes
                  Column(
                    children: [
                      GestureDetector(
                        onTap: _handleAvatarTap, // ✅ Handle avatar tap like iOS
                        child: Stack(
                          children: [
                            Container(
                              width: dimensions.avatarSize,
                              height: dimensions.avatarSize,
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                image: _user?.avatarUrl != null && _user!.avatarUrl.isNotEmpty
                                    ? _buildAvatarImage()
                                    : null,
                                color: Colors.white,
                              ),
                              child: _user?.avatarUrl == null || _user!.avatarUrl.isEmpty
                                  ? Center(
                                      child: Text(
                                        _user?.name.isNotEmpty == true ? _user!.name.substring(0, 1).toUpperCase() : '',
                                        style: TextStyle(
                                          fontSize: dimensions.avatarSize * 0.4, // Responsive font
                                          color: darkBlue,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    )
                                  : null,
                            ),
                            // Camera icon overlay - tương tự iOS camera indicator
                            Positioned(
                              bottom: 0,
                              right: 0,
                              child: Container(
                                width: dimensions.avatarSize * 0.3,
                                height: dimensions.avatarSize * 0.3,
                                decoration: BoxDecoration(
                                  color: darkBlue,
                                  shape: BoxShape.circle,
                                  border: Border.all(color: Colors.white, width: 2),
                                ),
                                child: Icon(
                                  Icons.camera_alt,
                                  color: Colors.white,
                                  size: dimensions.avatarSize * 0.15,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      SizedBox(height: dimensions.contentPadding * 0.5),

                      // Name and VIP badge
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            _user?.name ?? '',
                            style: TextStyle(
                              fontSize: dimensions.titleFontSize,
                              fontWeight: FontWeight.bold,
                              color: Colors.black,
                            ),
                          ),
                          if (_cardClass != null && _cardClass!.contains('VIP')) ...[
                            const SizedBox(width: 8),
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                              decoration: BoxDecoration(
                                color: Colors.amber,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                'VIP',
                                style: TextStyle(
                                  fontSize: dimensions.captionFontSize * 0.8,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.black,
                                ),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ],
                  ),

                  SizedBox(height: dimensions.contentPadding),

                  // Barcode - Responsive
                  if (_user?.cardNumber != null)
                    Column(
                      children: [
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceAround,
                          children: [
                            Text(
                              'Member.MemberCard'.tr(),
                              style: TextStyle(fontSize: dimensions.bodyFontSize),
                            ),
                            Text(
                              _user!.cardNumber!,
                              style: TextStyle(
                                fontSize: dimensions.bodyFontSize + 2,
                                fontWeight: FontWeight.bold,
                                letterSpacing: 1.2,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(
                          height: math.min(80, dimensions.avatarSize), // Responsive barcode height
                          child: FutureBuilder<String>(
                            future: _generateBarcode(_user!.cardNumber!),
                            builder: (context, snapshot) {
                              if (snapshot.hasData) {
                                return SvgPicture.string(
                                  snapshot.data!,
                                  height: math.min(80, dimensions.avatarSize),
                                  width: double.infinity,
                                );
                              }
                              return SizedBox(
                                height: math.min(80, dimensions.avatarSize),
                                child: const Center(child: CircularProgressIndicator()),
                              );
                            },
                          ),
                        ),
                      ],
                    ),

                  SizedBox(height: dimensions.contentPadding),

                  // Stats section - Responsive
                  Container(
                    padding: EdgeInsets.symmetric(
                      vertical: dimensions.contentPadding * 0.6,
                      horizontal: dimensions.contentPadding,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.15),
                      borderRadius: BorderRadius.circular(10),
                      border: Border.all(color: Colors.black, width: 0.5),
                    ),
                    child: Row(
                      children: [
                        Expanded(
                          child: _buildResponsiveStatItem(
                            title: 'Member.TotalSpent'.tr(),
                            value: '${_formatCurrency(_totalBill.toInt())} đ',
                            dimensions: dimensions,
                          ),
                        ),
                        Container(
                          width: 0.5,
                          height: dimensions.avatarSize * 0.6,
                          color: Colors.black,
                        ),
                        Expanded(
                          child: _buildResponsiveStatItem(
                            title: 'Member.TotalPoint'.tr(),
                            value: '$_totalPoint',
                            dimensions: dimensions,
                          ),
                        ),
                      ],
                    ),
                  ),

                  SizedBox(height: dimensions.contentPadding),

                  // Progress bar - Responsive
                  Container(
                    padding: EdgeInsets.all(dimensions.contentPadding),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.15),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(color: Colors.white.withValues(alpha: 0.3), width: 1),
                    ),
                    child: Column(
                      children: [
                        RichText(
                          textAlign: TextAlign.center,
                          text: TextSpan(
                            style: TextStyle(
                              fontSize: dimensions.captionFontSize,
                              color: Colors.black,
                              fontWeight: FontWeight.w500,
                            ),
                            children: [
                              TextSpan(
                                text: _progress >= 1.0 ? 'Profile.VIPHighest'.tr() : 'Profile.NeedMoreSpending'.tr(),
                              ),
                              if (_progress < 1.0)
                                TextSpan(
                                  text: ' ${_formatCurrency((3000000 * (1 - _progress)).toInt())} đ',
                                  style: const TextStyle(
                                    fontWeight: FontWeight.bold,
                                    color: Colors.red,
                                  ),
                                ),
                              if (_progress < 1.0) TextSpan(text: ' ${'Profile.ToUpgradeVIP'.tr()}'),
                            ],
                          ),
                        ),
                        SizedBox(height: dimensions.contentPadding * 0.5),
                        ClipRRect(
                          borderRadius: BorderRadius.circular(6),
                          child: LinearProgressIndicator(
                            value: _progress,
                            backgroundColor: Colors.grey,
                            valueColor: const AlwaysStoppedAnimation<Color>(Colors.amber),
                            minHeight: 8,
                          ),
                        ),
                        SizedBox(height: dimensions.contentPadding * 0.5),
                        Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              '${_formatCurrency(_totalBill.toInt())} đ',
                              style: TextStyle(
                                fontSize: dimensions.captionFontSize,
                                color: Colors.black,
                              ),
                            ),
                            Text(
                              '${_formatCurrency(3000000)} đ',
                              style: TextStyle(
                                fontSize: dimensions.captionFontSize,
                                color: Colors.black,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),

                  // Remaining points warning - Responsive
                  if (_remainingPoint > 0)
                    Padding(
                      padding: EdgeInsets.only(top: dimensions.contentPadding),
                      child: Container(
                        padding: EdgeInsets.all(dimensions.contentPadding * 0.75),
                        decoration: BoxDecoration(
                          color: Colors.amber.shade50,
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(color: Colors.amber.shade200, width: 1),
                        ),
                        child: Row(
                          children: [
                            const Icon(Icons.warning_amber_rounded, color: Colors.orange, size: 24),
                            SizedBox(width: dimensions.contentPadding * 0.75),
                            Expanded(
                              child: Text(
                                'Profile.PointsExpiring'.tr(namedArgs: {
                                  'points': _remainingPoint.toString(),
                                  'date': _formatDate(_remainingPointDate)
                                }),
                                style: TextStyle(
                                  fontSize: dimensions.captionFontSize,
                                  fontWeight: FontWeight.w500,
                                  color: Colors.black87,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Build responsive stat item
  Widget _buildResponsiveStatItem({
    required String title,
    required String value,
    required ResponsiveDimensions dimensions,
  }) {
    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: dimensions.captionFontSize,
            color: Colors.black,
            fontWeight: FontWeight.w500,
          ),
          textAlign: TextAlign.center,
        ),
        SizedBox(height: dimensions.contentPadding * 0.25),
        Text(
          value,
          style: TextStyle(
            fontSize: dimensions.bodyFontSize,
            fontWeight: FontWeight.bold,
            color: const Color(0xFF1976D2),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// Build responsive menu items - tương tự iOS TableView
  Widget _buildResponsiveMenuItems(ResponsiveDimensions dimensions) {
    return Padding(
      padding: EdgeInsets.all(dimensions.cardMargin),
      child: Column(
        children: List.generate(
          _menuItems.length,
          (index) => _buildResponsiveMenuItem(_menuItems[index], dimensions),
        ),
      ),
    );
  }

  /// Build responsive menu item - tương tự iOS TableViewCell
  Widget _buildResponsiveMenuItem(MenuItemData item, ResponsiveDimensions dimensions) {
    const Color primaryBlue = Color(0xFF2196F3);

    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: item.onTap,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: EdgeInsets.symmetric(
            vertical: dimensions.contentPadding,
            horizontal: dimensions.contentPadding * 0.25,
          ),
          decoration: const BoxDecoration(
            border: Border(
              bottom: BorderSide(
                color: Color(0xFFE0E0E0),
                width: 0.5,
              ),
            ),
          ),
          child: Row(
            children: [
              Container(
                padding: EdgeInsets.all(dimensions.contentPadding * 0.5),
                decoration: BoxDecoration(
                  color: primaryBlue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  item.icon,
                  color: primaryBlue,
                  size: dimensions.bodyFontSize + 4,
                ),
              ),
              SizedBox(width: dimensions.contentPadding),
              Expanded(
                child: Text(
                  item.title,
                  style: TextStyle(
                    fontSize: dimensions.bodyFontSize,
                    fontWeight: FontWeight.w500,
                    color: const Color(0xFF333333),
                  ),
                ),
              ),
              Icon(
                Icons.arrow_forward_ios,
                size: dimensions.captionFontSize,
                color: const Color(0xFF999999),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build responsive logout button
  Widget _buildResponsiveLogoutButton(ResponsiveDimensions dimensions) {
    const Color primaryBlue = Color(0xFF2196F3);

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: dimensions.contentPadding),
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: primaryBlue.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: ElevatedButton(
          onPressed: () {
            UDialog().showConfirm(
              title: 'Member.Logout'.tr(),
              text: 'Profile.LogoutConfirm'.tr(),
              btnOkText: 'Member.Logout'.tr(),
              btnOkColor: primaryBlue,
              btnOkOnPress: _logout,
            );
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: primaryBlue,
            foregroundColor: Colors.white,
            padding: EdgeInsets.symmetric(vertical: dimensions.contentPadding + 2),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            elevation: 0,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.logout, size: dimensions.bodyFontSize + 4),
              SizedBox(width: dimensions.contentPadding * 0.5),
              Text(
                'Member.Logout'.tr(),
                style: TextStyle(
                  fontSize: dimensions.bodyFontSize,
                  fontWeight: FontWeight.bold,
                  letterSpacing: 0.5,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Build avatar image decoration - tương tự Android toImageUrl() và iOS URL construction
  DecorationImage? _buildAvatarImage() {
    // Get picture from user - tương tự Android user.Picture và iOS user.Picture
    final String? picturePath = _user?.picture ?? _user?.avatarUrl;

    if (picturePath == null || picturePath.isEmpty) {
      return null;
    }

    // Build full URL like Android/iOS
    final String fullAvatarUrl = '${ApiService.baseUrlImage}$picturePath';

    if (fullAvatarUrl.isEmpty) {
      return null;
    }

    return DecorationImage(
      image: NetworkImage(fullAvatarUrl),
      fit: BoxFit.cover,
    );
  }

  /// Check if should show initials instead of avatar
  bool _shouldShowInitials() {
    final String? picturePath = _user?.picture ?? _user?.avatarUrl;
    return picturePath == null || picturePath.isEmpty;
  }
}

/// Responsive dimensions class
class ResponsiveDimensions {
  final double headerHeight;
  final double avatarSize;
  final double contentPadding;
  final double cardMargin;
  final double maxContentWidth;
  final double titleFontSize;
  final double bodyFontSize;
  final double captionFontSize;

  ResponsiveDimensions({
    required this.headerHeight,
    required this.avatarSize,
    required this.contentPadding,
    required this.cardMargin,
    required this.maxContentWidth,
    required this.titleFontSize,
    required this.bodyFontSize,
    required this.captionFontSize,
  });
}

class MenuItemData {
  final String title;
  final IconData icon;
  final VoidCallback onTap;

  MenuItemData({
    required this.title,
    required this.icon,
    required this.onTap,
  });
}

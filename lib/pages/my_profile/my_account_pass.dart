import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';

import '/constants/index.dart';
import '/core/index.dart';
import '/cubit/index.dart';
import '/models/index.dart';
import '/utils/index.dart';
import '/widgets/index.dart';

class MyAccountPass extends StatefulWidget {
  const MyAccountPass({super.key});

  @override
  State<MyAccountPass> createState() => _MyAccountPassState();
}

class _MyAccountPassState extends State<MyAccountPass> {
  final Map<String, TextEditingController> _lstControllers = {};
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: appBar(title: 'Profile.ChangePassword'.tr(),titleColor: Colors.white),
      body: CustomScrollView(
        slivers: [
          SliverPadding(
            padding: const EdgeInsets.symmetric(horizontal: CSpace.xl3).copyWith(top:CSpace.xl3),
            sliver: SliverToBoxAdapter(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // SizedBox(height: 160, child: CIcon.resetPassword),
                  WForm<MUser>(
                    list: _listFormItem,
                    onInit: (items) {
                      _lstControllers.addAll(items);
                    },
                  ),
                  const VSpacer(CSpace.xl3 * 2),
                  WButton(
                    onPressed: () async {
                      context.read<BlocC<MUser>>().submit(
                            api: (value, _, __, ___) async {
                              // Transform parameters to match Android/iOS API
                              final body = {
                                'UserName': context.read<AuthC>().state.user?.email,  // ✅ Match Android/iOS
                                'OldPassWord': value['oldPassword'],  // ✅ Match Android/iOS
                                'PassWord': value['password'],  // ✅ Match Android/iOS
                                // 'DeviceId': deviceId,  // ✅ Match Android/iOS
                              };

                              var result = await RepositoryProvider.of<Api>(context).auth.updatePassword(body: body);
                              if(result != null){
                                context.pop();
                              }
                            },
                        onlyApi: true,
                            submit: (_) {
                              _lstControllers.forEach((key, value) {
                                value.clear();
                              });
                              Future.delayed(const Duration(milliseconds: 30), () {
                                context.read<BlocC<MUser>>().savedStatus(status: AppStatus.init);
                              });
                            },
                          );
                    },
                    style: ElevatedButton.styleFrom(
                      foregroundColor: Colors.white,
                      backgroundColor: CColor.blue,
                      textStyle: const TextStyle(
                        fontSize: CFontSize.lg
                      )
                    ),
                    child: Text('Profile.ChangePassword'.tr()),
                  ),
                ],
              ),
            ),
          )
        ],
      ),
    );
  }

  @override
  void initState() {
    _init();
    super.initState();
  }


  List<MFormItem> _listFormItem = [];
  Future<void> _init() async {
    RegExp regExp = RegExp(
      r"^.{6,}$",
      caseSensitive: false,
      multiLine: false,
    );
    _listFormItem = [
      MFormItem(
        name: 'oldPassword',
        // label: 'Mật khẩu hiện tại',
        icon: 'assets/form/password.svg',
        hintText: 'Profile.CurrentPassword'.tr(),
        password: true,
      ),
      MFormItem(
          name: 'password',
          // label: 'Mật khẩu mới',
          icon: 'assets/form/password.svg',
          hintText: 'Profile.NewPassword'.tr(),
          password: true,
          onValidator: (value, listController) {
            if (value != null && !regExp.hasMatch(value)) {
              return 'Profile.PasswordMinLength'.tr();
            }
            return null;
          }),
      MFormItem(
          name: 'confirmPassword',
          // label: 'Xác nhận mật khẩu mới',
          hintText: 'Profile.ConfirmPassword'.tr(),
          icon: 'assets/form/password.svg',
          password: true,
          onValidator: (value, listController) {
            if (value != null && listController['password']!.text != '' && value != listController['password']!.text) {
              return 'Profile.PasswordNotMatch'.tr();
            }
            return null;
          }),
    ];
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_app/core/index.dart';
import 'package:flutter_app/models/point_history_model.dart';
import 'package:flutter_app/service/index.dart';
import 'package:flutter_app/utils/index.dart';
import 'package:flutter_app/widgets/index.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

/// Point History Screen - matches iOS HistoryVoucherViewController with point type
class PointHistoryScreen extends StatefulWidget {
  const PointHistoryScreen({super.key});

  @override
  State<PointHistoryScreen> createState() => _PointHistoryScreenState();
}

class _PointHistoryScreenState extends State<PointHistoryScreen> {
  bool _isLoading = true;
  List<PointHistoryModel> _pointHistories = [];
  String? _error;

  @override
  void initState() {
    super.initState();
    _loadPointHistories();
  }

  /// Load point histories - matches iOS getPointHistories()
  Future<void> _loadPointHistories() async {
    try {
      setState(() {
        _isLoading = true;
        _error = null;
      });

      final response = await RepositoryProvider.of<Api>(context).auth.getPointHistory();

      if (response?.data != null) {
        final List<dynamic> data = response!.data['Data'] ?? response.data['content'] ?? [];

        setState(() {
          _pointHistories = data
              .map((item) => PointHistoryModel.fromJson(item))
              .toList();
          _isLoading = false;
        });
      } else {
        setState(() {
          _error = 'Không thể tải lịch sử điểm';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = 'Lỗi: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: appBar(
        title: 'Lịch sử điểm', // matches iOS point history title
        titleColor: Colors.white,
      ),
      backgroundColor: const Color(0xffdfdede),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? _buildErrorState()
              : _pointHistories.isEmpty
                  ? _buildEmptyState()
                  : _buildPointHistoryList(),
    );
  }

  /// Build error state
  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(Icons.error_outline, size: 64, color: Colors.grey),
          const SizedBox(height: 16),
          Text(
            _error!,
            style: const TextStyle(fontSize: 16, color: Colors.grey),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),
          ElevatedButton(
            onPressed: _loadPointHistories,
            child: const Text('Thử lại'),
          ),
        ],
      ),
    );
  }

  /// Build empty state
  Widget _buildEmptyState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.history, size: 64, color: Colors.grey),
          SizedBox(height: 16),
          Text(
            'Chưa có lịch sử sử dụng điểm',
            style: TextStyle(fontSize: 16, color: Colors.grey),
          ),
        ],
      ),
    );
  }

  /// Build point history list - matches iOS tableView with HistoryPointTableViewCell
  Widget _buildPointHistoryList() {
    return RefreshIndicator(
      onRefresh: _loadPointHistories,
      color: Colors.blue,
      child: ListView.builder(
        padding: const EdgeInsets.all(8),
        itemCount: _pointHistories.length,
        itemBuilder: (context, index) {
          final history = _pointHistories[index];
          return _buildPointHistoryItem(history);
        },
      ),
    );
  }

  /// Build point history item - matches iOS HistoryPointTableViewCell
  Widget _buildPointHistoryItem(PointHistoryModel history) {
    final pointStatus = history.pointStatus;
    final isPositive = pointStatus == PointStatusType.save || pointStatus == PointStatusType.receive;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // Status icon - matches iOS color coding
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: pointStatus.color.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Icon(
                pointStatus.icon,
                color: pointStatus.color,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),

            // Content
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Status name and date
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        pointStatus.description,
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                      Text(
                        history.dateString,
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 4),

                  // Account name if applicable - matches iOS showName logic
                  if (history.showName && history.accountName != null && history.accountName!.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.only(bottom: 4),
                      child: Text(
                        'Tài khoản: ${history.accountName}',
                        style: const TextStyle(
                          fontSize: 14,
                          color: Colors.grey,
                        ),
                      ),
                    ),

                  // Point amount
                  Text(
                    '${isPositive ? '+' : '-'}${history.point ?? 0} điểm',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: isPositive ? Colors.green : Colors.red,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

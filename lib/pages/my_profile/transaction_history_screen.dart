import 'package:flutter/material.dart';
import 'package:flutter_app/constants/index.dart';
import 'package:flutter_app/core/index.dart';
import 'package:flutter_app/cubit/index.dart';
import 'package:flutter_app/models/index.dart';
import 'package:flutter_app/models/transaction_history_model.dart';
import 'package:flutter_app/pages/cinema/payment/transaction_detail_screen.dart';
import 'package:flutter_app/utils/index.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'transaction_detail_screen_ios_style.dart';
import 'package:url_launcher/url_launcher.dart';

/// Transaction History Screen - tương tự iOS TranferHistoryViewController
class TransactionHistoryScreen extends StatefulWidget {
  const TransactionHistoryScreen({super.key});

  @override
  State<TransactionHistoryScreen> createState() => _TransactionHistoryScreenState();
}

class _TransactionHistoryScreenState extends State<TransactionHistoryScreen> {
  bool _isLoading = true;
  MUser? _user;
  List<TransactionHistoryModel> _transactions = [];

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// Load transaction data - tương tự iOS getData()
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Get user data
      final authState = context.read<AuthC>().state;
      if (authState.user != null) {
        _user = authState.user;
        final userId = _user?.accountId ?? _user?.id ?? '';

        if (userId.isEmpty) {
          print('❌ User ID is empty');
          return;
        }

        // Get transaction history - tương tự iOS AccountProvider.rx.request(.getTransactionHistory(userId))
        final api = RepositoryProvider.of<Api>(context);
        final transactionResponse = await api.auth.getTransactionHistory(userId);

        if (transactionResponse != null) {
          final List<dynamic> rawTransactions = transactionResponse.data['content'] ?? [];
          setState(() {
            _transactions = rawTransactions.map((json) => TransactionHistoryModel.fromJson(json)).toList();
          });
          print('✅ Loaded ${_transactions.length} transactions');
        } else {
          print('❌ Transaction API failed: ${transactionResponse?.message}');
        }
      }
    } catch (e) {
      print('❌ Error loading transaction history: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: appBar(
        title: 'Lịch sử giao dịch', // tương tự iOS "Transaction History"
        titleColor: Colors.white,
      ),
      backgroundColor: Color(0xffdfdede),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _user == null
              ? const Center(child: Text('Vui lòng đăng nhập để xem lịch sử giao dịch'))
              : _transactions.isEmpty
                  ? _buildEmptyState()
                  : _buildTransactionList(),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.receipt_long,
            size: 80,
            color: Colors.grey.withValues(alpha: 0.5),
          ),
          const SizedBox(height: 16),
          const Text(
            'Chưa có giao dịch nào',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionList() {
    return RefreshIndicator(
      onRefresh: _loadData,
      color: Colors.blue,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const Padding(
            padding: EdgeInsets.all(8.0),
            child: Text(
              "Hiển thị giao dịch trong 3 tháng gần nhất. Vui lòng truy cập website để xem toàn bộ lịch sử giao dịch",
              style: TextStyle(fontSize: CFontSize.base, color: Colors.black54),
              textAlign: TextAlign.center,
            ),
          ),
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(8),
              itemCount: _transactions.length,
              itemBuilder: (context, index) {
                final transaction = _transactions[index];
                return _buildTransactionItem(transaction);
              },
            ),
          ),
        ],
      ),
    );
  }

  /// Build transaction item - tương tự iOS TransactionHistoryCell
  Widget _buildTransactionItem(TransactionHistoryModel transaction) {
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(5),
      ),
      child: InkWell(
        onTap: () => _showTransactionDetails(transaction),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(12),
          child: Container(
            height: 120,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  flex: 2,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      // Film name - tương tự iOS lbFilmName
                      Text(
                        transaction.filmName ?? '--',
                        style:
                            const TextStyle(fontSize: CFontSize.xl, fontFamily: 'Oswald', fontWeight: FontWeight.bold),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 8),

                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            transaction.formattedShowTime,
                            style: const TextStyle(fontSize: 15, color: Colors.black87),
                          ),
                          const SizedBox(height: 4),
                          Text(transaction.cinemaName ?? 'N/A',
                              style: const TextStyle(fontSize: 15, color: Colors.black87),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis),
                        ],
                      ),

                      // Waiting indicator if needed - tương tự iOS waitingView
                      if (transaction.isWaiting) ...[
                        const SizedBox(height: 8),
                        Container(
                          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                          decoration: BoxDecoration(
                            color: Colors.orange.withValues(alpha: 0.1),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: const Text(
                            'ĐANG XỬ LÝ',
                            style: TextStyle(
                              fontSize: 12,
                              color: Colors.orange,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
                Container(width: 2, height: 100, color: Colors.grey),
                Expanded(
                  flex: 1,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      Text(
                        Convert.price(transaction.totalPayment ?? 0),
                        style: const TextStyle(fontFamily: 'Oswald', fontSize: CFontSize.xl, color: Colors.indigo),
                      ),
                      Column(
                        children: [
                          const Text('Điểm tích luỹ', style: const TextStyle(fontSize: 14)),
                          if (transaction.quantityPoint != null && transaction.quantityPoint! > 0)
                            Text(
                              '${transaction.quantityPoint}',
                              style: const TextStyle(
                                fontSize: 16,
                                color: Colors.red,
                                fontWeight: FontWeight.w500,
                              ),
                            )
                          else
                            const SizedBox.shrink(),
                        ],
                      ),
                      Column(
                        children: [
                          const Text('Thời hạn của điểm ',overflow: TextOverflow.ellipsis,),
                          if (transaction.dateExpiredPoint != null)
                            Text(
                              Convert.date(transaction.dateExpiredPoint ?? ""),
                              style: const TextStyle(
                                fontSize: 16,
                                color: Colors.red,
                                fontWeight: FontWeight.w500,
                              ),
                            )
                          else
                            const SizedBox.shrink(),
                        ],
                      ),
                    ],
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Navigate to transaction detail - tương tự iOS push TransactionDetailViewController
  void _navigateToTransactionDetail(TransactionHistoryModel transaction) {
    // TODO: Create TransactionDetailScreen
    UDialog().showError(
      title: 'Thông báo',
      text: 'Chi tiết giao dịch đang được phát triển',
    );
  }

  void _showTransactionDetails(TransactionHistoryModel transaction) {
    // Handle special case like iOS: if Invoice_Id == "00000000-0000-0000-0000-000000000000"
    if (transaction.invoiceId == "00000000-0000-0000-0000-000000000000") {
      // Open payment URL like iOS
      if (transaction.airpayLandingUrl != null && transaction.airpayLandingUrl!.isNotEmpty) {
        _openPaymentUrl(transaction.airpayLandingUrl!);
        return;
      }
      // Check other payment URLs
      if (transaction.momoLandingUrl != null && transaction.momoLandingUrl!.isNotEmpty) {
        _openPaymentUrl(transaction.momoLandingUrl!);
        return;
      }
      if (transaction.zaloLandingUrl != null && transaction.zaloLandingUrl!.isNotEmpty) {
        _openPaymentUrl(transaction.zaloLandingUrl!);
        return;
      }
    }

    // Navigate to transaction detail screen for normal transactions
    if (transaction.invoiceId == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Không tìm thấy mã giao dịch')),
      );
      return;
    }

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TransactionDetailScreenIOSStyle(
          transactionId: transaction.invoiceId ?? "",
          userId: _user?.id ?? _user?.accountId ?? '',
          item: transaction, // Pass transaction item like iOS
        ),
      ),
    );
  }

  /// Open payment URL like iOS UIApplication.shared.open(url)
  void _openPaymentUrl(String url) async {
    try {
      // Use url_launcher to open payment URL
      // This mimics iOS UIApplication.shared.open(url) behavior
      print('🔗 Opening payment URL: $url');

      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        throw 'Could not launch $url';
      }
    } catch (e) {
      print('❌ Error opening payment URL: $e');
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Không thể mở liên kết thanh toán')),
      );
    }
  }

  void _showTransactionDetailsModal(dynamic transaction) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) {
        return Container(
          padding: const EdgeInsets.all(24),
          constraints: BoxConstraints(
            maxHeight: MediaQuery.of(context).size.height * 0.8,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Chi tiết giao dịch',
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  IconButton(
                    icon: const Icon(Icons.close),
                    onPressed: () => Navigator.pop(context),
                  ),
                ],
              ),
              const Divider(),
              Expanded(
                child: SingleChildScrollView(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildDetailItem('Phim', transaction.filmName ?? 'N/A'),
                      _buildDetailItem('Rạp', transaction.cinemaName ?? 'N/A'),
                      _buildDetailItem('Suất chiếu', _formatDateTime(transaction.showTime ?? '')),
                      _buildDetailItem('Ghế', transaction.seats ?? 'N/A'),
                      _buildDetailItem('Mã giao dịch', transaction.transactionId ?? 'N/A'),
                      _buildDetailItem('Ngày giao dịch', _formatDateTime(transaction.createdDate ?? '')),
                      _buildDetailItem('Trạng thái', _getStatusText(transaction.status)),
                      _buildDetailItem('Phương thức thanh toán', transaction.paymentMethod ?? 'N/A'),
                      _buildDetailItem('Tổng tiền', '${_formatCurrency(transaction.totalAmount ?? 0)} đ'),
                      if (transaction.tickets != null && transaction.tickets.isNotEmpty) ...[
                        const SizedBox(height: 16),
                        const Text(
                          'Chi tiết vé',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        ...transaction.tickets.map<Widget>((ticket) => _buildTicketItem(ticket)).toList(),
                      ],
                      if (transaction.combos != null && transaction.combos.isNotEmpty) ...[
                        const SizedBox(height: 16),
                        const Text(
                          'Bắp nước',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const SizedBox(height: 8),
                        ...transaction.combos.map<Widget>((combo) => _buildComboItem(combo)).toList(),
                      ],
                    ],
                  ),
                ),
              ),
              if (transaction.status == 'completed')
                Padding(
                  padding: const EdgeInsets.only(top: 16),
                  child: SizedBox(
                    width: double.infinity,
                    child: ElevatedButton(
                      onPressed: () {
                        // TODO: Implement download ticket functionality
                        UDialog().showError(
                          title: 'Thông báo',
                          text: 'Tính năng đang được phát triển',
                        );
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: Colors.red,
                        foregroundColor: Colors.white,
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text(
                        'Tải vé',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDetailItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 140,
            child: Text(
              label,
              style: const TextStyle(
                fontSize: 14,
                color: Colors.grey,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTicketItem(dynamic ticket) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Ghế ${ticket.seatName ?? 'N/A'}',
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                ticket.ticketTypeName ?? 'Vé thường',
                style: const TextStyle(
                  fontSize: 12,
                  color: Colors.grey,
                ),
              ),
            ],
          ),
          Text(
            '${_formatCurrency(ticket.price ?? 0)} đ',
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildComboItem(dynamic combo) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  combo.name ?? 'Combo',
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'Số lượng: ${combo.quantity ?? 1}',
                  style: const TextStyle(
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
          Text(
            '${_formatCurrency(combo.price ?? 0)} đ',
            style: const TextStyle(
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Color _getStatusColor(String? status) {
    if (status == null) return Colors.grey;

    switch (status.toLowerCase()) {
      case 'completed':
        return Colors.green;
      case 'pending':
        return Colors.orange;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _getStatusText(String? status) {
    if (status == null) return 'Không xác định';

    switch (status.toLowerCase()) {
      case 'completed':
        return 'Hoàn thành';
      case 'pending':
        return 'Đang xử lý';
      case 'cancelled':
        return 'Đã hủy';
      default:
        return status;
    }
  }

  String _formatCurrency(int value) {
    return value.toString().replaceAllMapped(
          RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
          (Match m) => '${m[1]}.',
        );
  }

  String _formatDateTime(String dateTimeString) {
    if (dateTimeString.isEmpty) return '';

    try {
      final dateTime = DateTime.parse(dateTimeString);
      return DateFormat('dd/MM/yyyy HH:mm').format(dateTime);
    } catch (e) {
      return dateTimeString;
    }
  }
}

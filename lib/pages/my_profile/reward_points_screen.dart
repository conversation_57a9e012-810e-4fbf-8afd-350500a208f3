import 'package:flutter/material.dart';
import 'package:flutter_app/core/index.dart';
import 'package:flutter_app/cubit/index.dart';
import 'package:flutter_app/models/index.dart';
import 'package:flutter_app/pages/my_profile/point_history_screen.dart';
import 'package:flutter_app/pages/my_profile/transaction_history_screen.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../utils/index.dart';

/// Beta Point screen - tương tự iOS RewardPointsViewController
class RewardPointsScreen extends StatefulWidget {
  const RewardPointsScreen({super.key});

  @override
  State<RewardPointsScreen> createState() => _RewardPointsScreenState();
}

class _RewardPointsScreenState extends State<RewardPointsScreen> {
  bool _isLoading = true;
  MUser? _user;
  Map<String, dynamic>? _pointData;
  List<dynamic> _pointHistory = [];

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  /// Load point data - tương tự iOS getPoints()
  Future<void> _loadData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // Get user data
      final authState = context.read<AuthC>().state;
      if (authState.user != null) {
        _user = authState.user;

        // Get point data - tương tự iOS AccountProvider.rx.request(.getPoints(userId))
        final api = RepositoryProvider.of<Api>(context);
        final pointResponse = await api.auth.getPoints(_user?.accountId ?? _user?.id ?? "");

        if (pointResponse != null) {
          setState(() {
            _pointData = pointResponse.data;
          });
        }
      }
    } catch (e) {
      print('Error loading reward points data: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: appBar(
        title: 'BetaPoint', // tương tự iOS "BetaPoint.Title"
        titleColor: Colors.white,
        actions: [
          // History button - tương tự iOS historyButton
          IconButton(
            icon: const Icon(Icons.history, color: Colors.white),
            onPressed: () => _navigateToPointHistory(),
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _user == null
              ? const Center(child: Text('Vui lòng đăng nhập để xem thông tin điểm thưởng'))
              : SingleChildScrollView(
                  child: Column(
                    children: [
                      _buildPointSummary(),
                      const SizedBox(height: 20),
                      // Donate button - tương tự iOS donateButton (hidden)
                      // _buildDonateButton(),
                    ],
                  ),
                ),
    );
  }

  /// Build point summary - tương tự iOS RewardPointsViewController layout
  Widget _buildPointSummary() {
    final totalAccumulated = _pointData?['TotalAccumulatedPoints'] ?? 0;
    final totalSpent = _pointData?['TotalSpentPoints'] ?? 0;
    final totalPoint = _pointData?['TotalPoint'] ?? 0;
    final almostExpired = _pointData?['AlmostExpiredPoint'] ?? 0;
    final expiredDate = _pointData?['AlmostExpiredPointDate'] ?? '';

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Total Accumulated Points - tương tự iOS lbTotalPoint
          _buildPointRow(
            'Tổng điểm tích lũy',
            totalAccumulated.toString(),
            Colors.blue,
            Icons.trending_up,
          ),
          const Divider(height: 24),

          // Total Spent Points - tương tự iOS lbUsedPoint
          _buildPointRow(
            'Tổng điểm đã sử dụng',
            totalSpent.toString(),
            Colors.orange,
            Icons.shopping_cart,
          ),
          const Divider(height: 24),

          // Current Points - tương tự iOS lbCurrentPoint
          _buildPointRow(
            'Điểm hiện tại',
            totalPoint.toString(),
            Colors.green,
            Icons.account_balance_wallet,
            isHighlight: true,
          ),

          // Almost Expired Points - tương tự iOS lbRemainPoint.remainPoint
          if (almostExpired > 0) ...[
            const Divider(height: 24),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.orange.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  const Icon(Icons.warning, color: Colors.orange, size: 20),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '$almostExpired điểm sẽ hết hạn vào ${_formatDate(expiredDate)}',
                      style: const TextStyle(
                        fontSize: 14,
                        color: Colors.orange,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Build point row - helper method
  Widget _buildPointRow(String label, String value, Color color, IconData icon, {bool isHighlight = false}) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: color, size: 20),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Text(
            label,
            style: TextStyle(
              fontSize: isHighlight ? 16 : 14,
              fontWeight: isHighlight ? FontWeight.w600 : FontWeight.w500,
              color: Colors.black87,
            ),
          ),
        ),
        Text(
          value,
          style: TextStyle(
            fontSize: isHighlight ? 20 : 18,
            fontWeight: FontWeight.bold,
            color: isHighlight ? color : Colors.black87,
          ),
        ),
      ],
    );
  }

  /// Navigate to point history - tương tự iOS historyVoucher()
  void _navigateToPointHistory() {
    // Navigate to dedicated point history screen - matches iOS HistoryVoucherViewController with point type
    Navigator.push(context, MaterialPageRoute(builder: (context) => const PointHistoryScreen()));
  }

  String _formatDate(String dateString) {
    if (dateString.isEmpty) return '';

    try {
      final date = DateTime.parse(dateString);
      return '${date.day}/${date.month}/${date.year}';
    } catch (e) {
      return dateString;
    }
  }
}

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_app/pages/Movie_schedule/index.dart';
import 'package:flutter_app/pages/other_tab/index.dart';
import 'package:flutter_app/pages/promotion/index.dart';
import 'package:flutter_app/pages/voucher/index.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../constants/index.dart';
import '../../cubit/index.dart';
import '../Movie_schedule/model/Film_model.dart';
import '../cinema/index.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _pages.elementAt(selectIndex),
      bottomNavigationBar: BottomNavigationBar(
        unselectedItemColor: CColor.black.shade400,
        type: BottomNavigationBarType.fixed,
        unselectedLabelStyle: const TextStyle(fontSize: CFontSize.sm),
        selectedLabelStyle: const TextStyle(fontSize: CFontSize.sm),
        selectedItemColor: CColor.blue,
        currentIndex: selectIndex,
        onTap: (value) {
          setState(() {
            selectIndex = value;
          });
        },
        items: [
          BottomNavigationBarItem(icon: imageIcon(0, 'selected', 'select'), label: "Tab1".tr()),
          BottomNavigationBarItem(icon: imageIcon(1, 'cinema', 'Cinema'), label: "Tab2".tr()),
          BottomNavigationBarItem(icon: imageIcon(2, 'voucher', 'Voucher'), label: "Tab3".tr()),
          BottomNavigationBarItem(icon: imageIcon(3, 'khuyenmai', 'Khuyenmai'), label: "Tab4".tr()),
          BottomNavigationBarItem(icon: imageIcon(4, 'other', 'Other'), label: "Tab5".tr()),
        ],
      ),
    );
  }

  int selectIndex = 0;

  Widget imageIcon(int index, String name, String name1) {
    return Image.asset(
      'assets/icon/tabbar/${index == selectIndex ? name : 'un$name1'}@3x.png',
      width: 24,
      height: 24,
    );
  }

  final List<Widget> _pages = <Widget>[
    BlocProvider(create: (context) => BlocC<FilmModel>(), child: const MovieSchedule()),
    const ListFilmScreen(),

    const MyVoucherScreen(),
    const PromotionPage(),
    const OtherScreen(),
  ];

  @override
  void initState() {
    super.initState();
    // Role.initState(context);
    super.initState();
  }
}

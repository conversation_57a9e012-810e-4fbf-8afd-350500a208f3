import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_app/pages/voucher/api/api_test.dart';
import 'package:flutter_app/pages/voucher/model/voucher_model.dart';
import 'package:flutter_app/utils/index.dart';


class DonateVoucherCell extends StatelessWidget {
  final ShortUser user;
  final VoidCallback onDonate;

  const DonateVoucherCell({
    Key? key,
    required this.user,
    required this.onDonate,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          ClipOval(
            child: CachedNetworkImage(
              imageUrl:'${ApiService.baseUrlImage}${user.avatar}' ,
              width: 48,
              height: 48,
              fit: BoxFit.cover,
              placeholder: (context, url) => const CircularProgressIndicator(),
              errorWidget: (context, url, error) => const Icon(Icons.error),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  user.name,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  user.email,
                  style: const TextStyle(
                    fontSize: 14,
                    color: Colors.grey,
                  ),
                ),
              ],
            ),
          ),
          ElevatedButton(
            onPressed: onDonate,
            style: ElevatedButton.styleFrom(
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
              ),
            ),
            child: const Text('Donate'),
          ),
        ],
      ),
    );
  }
}

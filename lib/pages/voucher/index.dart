import 'package:flutter/material.dart';
import 'package:flutter_app/constants/index.dart';
import 'package:flutter_app/core/index.dart';
import 'package:flutter_app/pages/voucher/_add_voucher_screen.dart';
import 'package:flutter_app/pages/voucher/_history_voucher_screen.dart';
import 'package:flutter_app/pages/voucher/_use_voucher_screen.dart';
import 'package:flutter_app/pages/voucher/api/api_test.dart';
import 'package:flutter_app/pages/voucher/free_voucher_screen.dart';
import 'package:flutter_app/pages/voucher/widget/my_voucher_cell.dart';
import 'package:go_router/go_router.dart';

import 'model/voucher_model.dart';

class MyVoucherScreen extends StatefulWidget {
  const MyVoucherScreen({super.key});

  @override
  State<MyVoucherScreen> createState() => _MyVoucherScreenState();
}

class _MyVoucherScreenState extends State<MyVoucherScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final List<String> _tabs = ["Khả dụng", "Đã sử dụng", "Hết hạn"];
  List<VoucherModel> _availableVouchers = [];
  List<VoucherModel> _usedVouchers = [];
  List<VoucherModel> _expiredVouchers = [];
  bool _isLoading = true;
  bool _isLoggedIn = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: _tabs.length, vsync: this);
    _checkLoginStatus();
    _loadVouchers();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _checkLoginStatus() async {
    try {
      final isLoggedIn = await ApiService.isLoggedIn();
      setState(() {
        _isLoggedIn = isLoggedIn;
      });
    } catch (e) {
      setState(() {
        _isLoggedIn = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Lỗi kiểm tra đăng nhập: $e')),
      );
    }
  }

  Future<void> _loadVouchers() async {
    if (!_isLoggedIn) {
      setState(() {
        _isLoading = false;
      });
      return;
    }

    try {
      setState(() {
        _isLoading = true;
      });

      final vouchers = await ApiService.getVouchers();

      // Filter vouchers by status
      final available = vouchers.where((v) => v.VoucherStatus == 0).toList();
      final used = vouchers.where((v) => v.VoucherStatus == 1).toList();
      final expired = vouchers.where((v) => v.VoucherStatus == 2).toList();

      setState(() {
        _availableVouchers = available;
        _usedVouchers = used;
        _expiredVouchers = expired;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to load vouchers: $e')),
      );
    }
  }

  void _navigateToAddVoucher() async {
    // Navigate to add voucher screen
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const AddVoucherScreen(),
      ),
    );

    // If voucher was added successfully, refresh the list
    if (result == true) {
      _loadVouchers();
    }
  }

  void _navigateToVoucherHistory() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const HistoryVoucherScreen(),
      ),
    );
  }

  void _navigateToFreeVouchers() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const FreeVoucherScreen(),
      ),
    );
  }

  void _navigateToDonateVoucher() {
    // Show donate voucher dialog
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildDonateVoucherBottomSheet(),
    );
  }

  Widget _buildDonateVoucherBottomSheet() {
    final TextEditingController emailController = TextEditingController();
    final TextEditingController voucherCodeController = TextEditingController();

    return Container(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(16),
          topRight: Radius.circular(16),
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              "Tặng voucher",
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 24),
            TextField(
              controller: voucherCodeController,
              decoration: InputDecoration(
                labelText: "Mã voucher",
                hintText: "Nhập mã voucher bạn muốn tặng",
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                prefixIcon: const Icon(Icons.card_giftcard),
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: emailController,
              decoration: InputDecoration(
                labelText: "Email người nhận",
                hintText: "Nhập email người bạn muốn tặng",
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                prefixIcon: const Icon(Icons.email),
              ),
              keyboardType: TextInputType.emailAddress,
            ),
            const SizedBox(height: 24),
            SizedBox(
              width: double.infinity,
              height: 48,
              child: ElevatedButton(
                onPressed: () {
                  if (voucherCodeController.text.isNotEmpty && emailController.text.isNotEmpty) {
                    _donateVoucherByCode(voucherCodeController.text, emailController.text);
                  }
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: CColor.blue,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text("Tặng voucher"),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _donateVoucherByCode(String voucherCode, String email) async {
    Navigator.pop(context); // Close bottom sheet

    setState(() {
      _isLoading = true;
    });

    try {
      final success = await ApiService.donateVoucher(voucherCode, email);

      setState(() {
        _isLoading = false;
      });

      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text("Tặng voucher thành công")),
        );

        _loadVouchers(); // Refresh vouchers list
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text("Không thể tặng voucher")),
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text("Lỗi tặng voucher: $e")),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: appBar(
        titleColor: Colors.white,
        leadingWidth: 0,
        title: 'Voucher của tôi',
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: !_isLoggedIn ? () => context.pushNamed(CRoute.login) : _navigateToAddVoucher,
            tooltip: 'Thêm voucher',
          ),
          IconButton(
            icon: const Icon(Icons.history),
            onPressed: !_isLoggedIn ? () => context.pushNamed(CRoute.login) :_navigateToVoucherHistory,
            tooltip: 'Lịch sử voucher',
          ),
        ],
      ),
      body: Column(
        children: [
          Expanded(
            child: _isLoading
                ? const Center(
                    child: CircularProgressIndicator(
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.red),
                    ),
                  )
                : !_isLoggedIn
                    ? _buildNotLoggedInView()
                    : TabBarView(
                        controller: _tabController,
                        children: [
                          // Available Vouchers Tab
                          _buildVoucherList(_availableVouchers),

                          // Used Vouchers Tab
                          _buildVoucherList(_usedVouchers),

                          // Expired Vouchers Tab
                          _buildVoucherList(_expiredVouchers),
                        ],
                      ),
          ),
        ],
      ),
      //   floatingActionButton: _isLoggedIn
      //       ? FloatingActionButton(
      //           onPressed: _navigateToAddVoucher,
      //           backgroundColor: Colors.red,
      //           child: const Icon(Icons.add),
      //         )
      //       : null,
    );
  }

  Widget _buildVoucherList(List<VoucherModel> vouchers) {
    if (vouchers.isEmpty) {
      return _buildEmptyView();
    }

    return RefreshIndicator(
      onRefresh: _loadVouchers,
      color: Colors.red,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: vouchers.length,
        itemBuilder: (context, index) {
          final voucher = vouchers[index];
          return MyVoucherCell(
            voucher: voucher,
            onUse: () => _useVoucher(index),
            onDonate: () => _donateVoucher(index),
          );
        },
      ),
    );
  }

  Widget _buildNotLoggedInView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.account_circle_outlined,
            size: 80,
            color: Colors.grey.shade300,
          ),
          const SizedBox(height: 16),
          const Text(
            'Bạn chưa đăng nhập',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Đăng nhập để xem và sử dụng voucher của bạn',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          SizedBox(
            width: 200,
            height: 48,
            child: ElevatedButton.icon(
              onPressed: () {
                context.pushNamed(CRoute.login);
              },
              icon: const Icon(Icons.login),
              label: const Text('Đăng nhập'),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(24),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.card_giftcard,
            size: 80,
            color: Colors.grey.shade300,
          ),
          const SizedBox(height: 16),
          const Text(
            'Chưa có voucher nào',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            'Nhận voucher miễn phí hoặc thêm voucher mới',
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Future<void> _useVoucher(int index) async {
    try {
      final voucher = _availableVouchers[index];
      final voucherCode = voucher.VoucherCode;
      if (voucherCode == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Mã voucher không hợp lệ')),
        );
        return;
      }

      // Navigate to use voucher screen
      Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => UseVoucherScreen(voucher: voucher),
        ),
      ).then((_) => _loadVouchers());
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Lỗi: $e')),
      );
    }
  }

  Future<void> _donateVoucher(int index) async {
    final voucher = _availableVouchers[index];

    if (!voucher.IsAvaiableForGift) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Voucher này không thể tặng cho người khác')),
      );
      return;
    }

    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => DonateVoucherScreen(
          voucher: voucher,
        ),
      ),
    );

    if (result == true) {
      _loadVouchers();
    }
  }
}

class DonateVoucherScreen extends StatefulWidget {
  final VoucherModel voucher;

  const DonateVoucherScreen({
    super.key,
    required this.voucher,
  });

  @override
  State<DonateVoucherScreen> createState() => _DonateVoucherScreenState();
}

class _DonateVoucherScreenState extends State<DonateVoucherScreen> {
  final _emailController = TextEditingController();
  bool _isLoading = false;

  @override
  void dispose() {
    _emailController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Donate Voucher'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Voucher Code: ${widget.voucher.VoucherCode}',
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: _emailController,
              decoration: const InputDecoration(
                labelText: 'Recipient Email',
                border: OutlineInputBorder(),
              ),
              keyboardType: TextInputType.emailAddress,
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _donateVoucher,
                child: _isLoading ? const CircularProgressIndicator() : const Text('Donate'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _donateVoucher() async {
    if (_emailController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter recipient email')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final voucherCode = widget.voucher.VoucherCode;
      if (voucherCode == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Invalid voucher code')),
        );
        return;
      }

      final success = await ApiService.donateVoucher(
        voucherCode,
        _emailController.text,
      );

      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Voucher donated successfully')),
        );
        Navigator.pop(context, true);
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Failed to donate voucher')),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Error: $e')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }
}

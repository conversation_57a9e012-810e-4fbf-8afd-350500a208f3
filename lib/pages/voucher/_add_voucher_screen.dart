import 'package:flutter/material.dart';
import 'package:flutter_app/pages/voucher/api/api_test.dart';
import 'package:flutter_app/pages/voucher/_scan_bar_code.dart';
import 'package:flutter_app/constants/index.dart';
import 'package:easy_localization/easy_localization.dart';

import '../../core/index.dart';

class AddVoucherScreen extends StatefulWidget {
  const AddVoucherScreen({super.key});

  @override
  State<AddVoucherScreen> createState() => _AddVoucherScreenState();
}

class _AddVoucherScreenState extends State<AddVoucherScreen> with TickerProviderStateMixin {
  final TextEditingController _codeController = TextEditingController();
  final TextEditingController _pinController = TextEditingController();
  final FocusNode _codeFocusNode = FocusNode();
  final FocusNode _pinFocusNode = FocusNode();
  bool _isLoading = false;
  String? _errorMessage;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _slideAnimation = Tween<Offset>(begin: const Offset(0, 0.3), end: Offset.zero).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutBack),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _codeController.dispose();
    _pinController.dispose();
    _codeFocusNode.dispose();
    _pinFocusNode.dispose();
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _registerVoucher() async {
    final code = _codeController.text.trim();
    final pin = _pinController.text.trim();

    if (code.isEmpty) {
      setState(() {
        _errorMessage = 'Voucher.EnterVoucherCodeError'.tr();
      });
      _codeFocusNode.requestFocus();
      return;
    }

    if (pin.isEmpty) {
      setState(() {
        _errorMessage = 'Voucher.EnterPinError'.tr();
      });
      _pinFocusNode.requestFocus();
      return;
    }

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Simulate API call with both code and pin
      final success = await ApiService.registerVoucher(code);

      if (success) {
        if (mounted) {
          // Show success message with animation
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Row(
                children: [
                  const Icon(Icons.check_circle, color: Colors.white),
                  const SizedBox(width: 8),
                  Text('Voucher.RegisterSuccess'.tr()),
                ],
              ),
              backgroundColor: Colors.green,
              behavior: SnackBarBehavior.floating,
              shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
            ),
          );
          Navigator.pop(context, true);
        }
      } else {
        setState(() {
          _errorMessage = 'Voucher.RegisterError'.tr();
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Voucher.Error'.tr(args: [e.toString()]);
        _isLoading = false;
      });
    }
  }

  void _scanBarcode() async {
    try {
      // Navigate to scan screen
      final result = await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const ScanBarCodeScreen(),
        ),
      );

      if (result != null && result is String && result.isNotEmpty) {
        setState(() {
          _codeController.text = result;
          _errorMessage = null;
        });

        // Show success feedback
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.qr_code_scanner, color: Colors.white),
                const SizedBox(width: 8),
                Text('Voucher.ScanSuccess'.tr()),
              ],
            ),
            backgroundColor: CColor.green,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
          ),
        );

        // Auto focus to PIN field if code is filled
        if (_codeController.text.isNotEmpty) {
          _pinFocusNode.requestFocus();
        }
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.error_outline, color: Colors.white),
              const SizedBox(width: 8),
              Text('Voucher.ScanError'.tr(args: [e.toString()])),
            ],
          ),
          backgroundColor: CColor.danger,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: appBar(
        title: 'Voucher.AddVoucherTitle'.tr(),
        titleColor: Colors.white,
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: SingleChildScrollView(
            child: Padding(
              padding: const EdgeInsets.all(24.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.stretch,
                children: [
                  // Header section with improved design
                  Container(
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(16),
                          decoration: BoxDecoration(
                            color: CColor.danger.shade50,
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            Icons.card_giftcard,
                            size: 48,
                            color: CColor.danger,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'Voucher.AddNewVoucher'.tr(),
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: CColor.danger,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'Voucher.EnterVoucherAndPin'.tr(),
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey.shade600,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Input fields container
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.05),
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        // Voucher code input
                        TextField(
                          controller: _codeController,
                          focusNode: _codeFocusNode,
                          decoration: InputDecoration(
                            labelText: 'Voucher.VoucherCodeField'.tr(),
                            hintText: 'Voucher.EnterVoucherCodeHint'.tr(),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(color: Colors.grey.shade300),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(color: CColor.danger, width: 2),
                            ),
                            prefixIcon: Icon(Icons.card_giftcard, color: CColor.danger),
                            suffixIcon: IconButton(
                              icon: Icon(Icons.qr_code_scanner, color: CColor.danger),
                              onPressed: _scanBarcode,
                              tooltip: 'Voucher.ScanQR'.tr(),
                            ),
                            errorText: _errorMessage,
                            errorStyle: TextStyle(color: CColor.danger),
                            floatingLabelStyle: TextStyle(color: CColor.danger),
                          ),
                          onChanged: (_) {
                            if (_errorMessage != null) {
                              setState(() {
                                _errorMessage = null;
                              });
                            }
                          },
                          style: const TextStyle(fontSize: 16),
                          textCapitalization: TextCapitalization.characters,
                        ),
                        const SizedBox(height: 16),

                        // PIN code input
                        TextField(
                          controller: _pinController,
                          focusNode: _pinFocusNode,
                          decoration: InputDecoration(
                            labelText: 'Voucher.PinCode'.tr(),
                            hintText: 'Voucher.EnterPinHint'.tr(),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(color: Colors.grey.shade300),
                            ),
                            focusedBorder: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide(color: CColor.danger, width: 2),
                            ),
                            prefixIcon: Icon(Icons.lock_outline, color: CColor.danger),
                            floatingLabelStyle: TextStyle(color: CColor.danger),
                          ),
                          style: const TextStyle(fontSize: 16),
                          obscureText: true,
                          keyboardType: TextInputType.number,
                        ),
                        const SizedBox(height: 24),

                        // Register button
                        SizedBox(
                          width: double.infinity,
                          height: 50,
                          child: ElevatedButton.icon(
                            onPressed: _isLoading ? null : _registerVoucher,
                            icon: _isLoading
                                ? SizedBox(
                                    height: 20,
                                    width: 20,
                                    child: CircularProgressIndicator(
                                      color: Colors.white,
                                      strokeWidth: 2,
                                    ),
                                  )
                                : const Icon(Icons.add),
                            label: Text(_isLoading ? 'Đang xử lý...' : 'Thêm voucher'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: CColor.danger,
                              foregroundColor: Colors.white,
                              disabledBackgroundColor: Colors.grey.shade300,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              elevation: 2,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Notes section
                  Container(
                    padding: const EdgeInsets.all(20),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(16),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black12,
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.info_outline, color: CColor.danger, size: 20),
                            const SizedBox(width: 8),
                            Text(
                              'Lưu ý quan trọng:',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                                color: CColor.danger,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 16),
                        _buildNoteItem('Mã voucher và PIN có thể được tìm thấy trên thẻ quà tặng hoặc email khuyến mãi'),
                        _buildNoteItem('Mã voucher phân biệt chữ hoa và chữ thường'),
                        _buildNoteItem('Mỗi mã voucher chỉ có thể sử dụng một lần'),
                        _buildNoteItem('Voucher có thể có thời hạn sử dụng, vui lòng kiểm tra kỹ'),
                        _buildNoteItem('Liên hệ hỗ trợ nếu gặp vấn đề khi đăng ký voucher'),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNoteItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: const EdgeInsets.only(top: 6, right: 12),
            width: 6,
            height: 6,
            decoration: BoxDecoration(
              color: CColor.danger,
              shape: BoxShape.circle,
            ),
          ),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                color: Colors.grey.shade700,
                fontSize: 14,
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_app/constants/index.dart';
import 'package:flutter_app/pages/voucher/model/voucher_model.dart';
import 'package:flutter_app/core/index.dart';
import 'package:intl/intl.dart';

import 'api/api_test.dart';

class HistoryVoucherScreen extends StatefulWidget {
  const HistoryVoucherScreen({super.key});

  @override
  State<HistoryVoucherScreen> createState() => _HistoryVoucherScreenState();
}

class _HistoryVoucherScreenState extends State<HistoryVoucherScreen> with TickerProviderStateMixin {
  List<VoucherModel> _vouchers = [];
  bool _isLoading = true;
  String _selectedFilter = 'all'; // all, used, expired
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _loadVoucherHistory();
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _loadVoucherHistory() async {
    try {
      setState(() {
        _isLoading = true;
      });

      final vouchers = await ApiService.getVoucherHistory();

      setState(() {
        _vouchers = vouchers;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                const Icon(Icons.error_outline, color: Colors.white),
                const SizedBox(width: 8),
                Expanded(child: Text('Lỗi tải lịch sử: $e')),
              ],
            ),
            backgroundColor: CColor.danger,
            behavior: SnackBarBehavior.floating,
            shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
          ),
        );
      }
    }
  }

  List<VoucherModel> get _filteredVouchers {
    switch (_selectedFilter) {
      case 'used':
        return _vouchers.where((v) => v.VoucherStatus == 1).toList();
      case 'expired':
        return _vouchers.where((v) => v.VoucherStatus == 2).toList();
      default:
        return _vouchers;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: appBar(
        title: 'Lịch sử voucher',
        titleColor: Colors.white
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: Column(
          children: [
            // Filter section
            Container(
              margin: const EdgeInsets.all(16),
              padding: const EdgeInsets.all(4),
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(12),
                boxShadow: const [
                  BoxShadow(
                    color: Colors.black12,
                    blurRadius: 8,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
              child: Row(
                children: [
                  _buildFilterButton('all', 'Tất cả', Icons.list),
                  _buildFilterButton('used', 'Đã dùng', Icons.check_circle),
                  _buildFilterButton('expired', 'Hết hạn', Icons.access_time),
                ],
              ),
            ),

            // Content
            Expanded(
              child: _isLoading
                  ? Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(CColor.danger),
                          ),
                          const SizedBox(height: 16),
                          Text(
                            'Đang tải lịch sử...',
                            style: TextStyle(
                              color: Colors.grey.shade600,
                              fontSize: 16,
                            ),
                          ),
                        ],
                      ),
                    )
                  : _buildBody(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterButton(String filter, String label, IconData icon) {
    final isSelected = _selectedFilter == filter;
    return Expanded(
      child: GestureDetector(
        onTap: () {
          setState(() {
            _selectedFilter = filter;
          });
        },
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 8),
          decoration: BoxDecoration(
            color: isSelected ? CColor.danger : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 18,
                color: isSelected ? Colors.white : Colors.grey.shade600,
              ),
              const SizedBox(width: 6),
              Text(
                label,
                style: TextStyle(
                  color: isSelected ? Colors.white : Colors.grey.shade600,
                  fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBody() {
    final displayVouchers = _filteredVouchers;

    if (displayVouchers.isEmpty) {
      return _buildEmptyState();
    }

    return RefreshIndicator(
      onRefresh: _loadVoucherHistory,
      color: CColor.danger,
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        itemCount: displayVouchers.length,
        itemBuilder: (context, index) {
          final voucher = displayVouchers[index];
          return Container(
            margin: const EdgeInsets.only(bottom: 12),
            child: _buildHistoryVoucherCard(voucher),
          );
        },
      ),
    );
  }

  Widget _buildEmptyState() {
    String title, subtitle;
    IconData icon;

    switch (_selectedFilter) {
      case 'used':
        title = "Chưa có voucher đã sử dụng";
        subtitle = "Các voucher đã sử dụng sẽ hiển thị ở đây";
        icon = Icons.check_circle_outline;
        break;
      case 'expired':
        title = "Chưa có voucher hết hạn";
        subtitle = "Các voucher hết hạn sẽ hiển thị ở đây";
        icon = Icons.access_time;
        break;
      default:
        title = "Không có lịch sử voucher";
        subtitle = "Lịch sử các voucher sẽ hiển thị ở đây";
        icon = Icons.history;
    }

    return Center(
      child: Container(
        margin: const EdgeInsets.all(32),
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(16),
          boxShadow: const [
            BoxShadow(
              color: Colors.black12,
              blurRadius: 10,
              offset: Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                shape: BoxShape.circle,
              ),
              child: Icon(
                icon,
                size: 48,
                color: Colors.grey.shade400,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: Colors.black87,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade600,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 20),
            ElevatedButton.icon(
              onPressed: _loadVoucherHistory,
              icon: const Icon(Icons.refresh, size: 18),
              label: const Text('Làm mới'),
              style: ElevatedButton.styleFrom(
                backgroundColor: CColor.danger,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHistoryVoucherCard(VoucherModel voucher) {
    // Get status info
    Color statusColor;
    String statusText;
    IconData statusIcon;

    switch (voucher.VoucherStatus) {
      case 1:
        statusColor = CColor.blue;
        statusText = "Đã sử dụng";
        statusIcon = Icons.check_circle;
        break;
      case 2:
        statusColor = CColor.danger;
        statusText = "Hết hạn";
        statusIcon = Icons.access_time;
        break;
      default:
        statusColor = Colors.grey;
        statusText = "Không xác định";
        statusIcon = Icons.help_outline;
    }

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: const [
          BoxShadow(
            color: Colors.black12,
            blurRadius: 8,
            offset: Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: statusColor == CColor.blue ? CColor.blue.shade50 :
                     statusColor == CColor.danger ? CColor.danger.shade50 :
                     Colors.grey.shade50,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(statusIcon, color: statusColor, size: 20),
                const SizedBox(width: 8),
                Text(
                  statusText,
                  style: TextStyle(
                    color: statusColor,
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
                const Spacer(),
                Text(
                  voucher.VoucherCode ?? 'N/A',
                  style: TextStyle(
                    color: statusColor,
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),

          // Content
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  voucher.VoucherPackageName ?? 'Voucher',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (voucher.Description != null && voucher.Description!.isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Text(
                    voucher.Description!,
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                ],
                const SizedBox(height: 12),
                Row(
                  children: [
                    Icon(Icons.calendar_today, size: 16, color: Colors.grey.shade500),
                    const SizedBox(width: 6),
                    Text(
                      _formatDate(voucher.ExpirationDate),
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(String? dateString) {
    if (dateString == null || dateString.isEmpty) {
      return 'Không có thông tin';
    }

    try {
      final date = DateTime.parse(dateString);
      final formatter = DateFormat('dd/MM/yyyy HH:mm');
      return formatter.format(date);
    } catch (e) {
      return 'Ngày không hợp lệ';
    }
  }
}

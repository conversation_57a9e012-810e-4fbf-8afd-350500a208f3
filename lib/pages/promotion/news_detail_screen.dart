import 'package:flutter/material.dart';
import 'package:flutter_app/constants/index.dart';
import 'package:flutter_app/core/index.dart';
import 'package:flutter_app/models/index.dart';
import 'package:flutter_app/pages/voucher/api/api_test.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:easy_localization/easy_localization.dart';

class NewsDetailScreen extends StatelessWidget {
  final PromotionItem item;

  const NewsDetailScreen({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: appBar(
        title: item.title ?? 'Chi tiết',
        titleColor: Colors.white,
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header image
            if (item.imageUrl != null)
              Image.network(
                item.imageUrl!,
                width: double.infinity,
                height: 200,
                fit: BoxFit.fill,
                errorBuilder: (context, error, stackTrace) {
                  return Container(
                    width: double.infinity,
                    height: 200,
                    color: Colors.grey[300],
                    child: const Icon(Icons.image_not_supported, size: 50),
                  );
                },
              ),

            // Title and content
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title
                  Text(
                    item.title,
                    style: const TextStyle(
                      fontSize: 22,
                      fontWeight: FontWeight.bold
                    ),
                   textAlign: TextAlign.center,
                  ),

                  // Date
                  if (item.publishDate != null)
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8.0),
                      child: Center(
                        child: Text(
                          item.publishDate!,
                          style: TextStyle(
                            color: Colors.grey[600],
                            fontSize: 14,
                          )
                        ),
                      ),
                    ),

                  // Summary
                  if (item.summary.isNotEmpty)
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: 8.0),
                      child: Text(
                        item.summary,
                        style: TextStyle(
                          fontWeight: FontWeight.w500,
                          fontSize: 16,
                          color: Colors.grey[800],
                        ),
                      ),
                    ),

                  const Divider(height: 15),

                  // Content
                  if (item.content != null && item.content!.isNotEmpty)...[
                    _buildHtmlContent(item.fullContent),
                    const VSpacer(56)
                  ]
                  else
                    const Text('Không có nội dung chi tiết'),
                ],
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: item.newsURI != null
          ? Padding(
              padding: const EdgeInsets.symmetric(horizontal: 0),
              child: SizedBox(
                width: double.infinity,
                height: 56, // giống FAB.extended
                child: InkWell(
                  onTap: () {
                    Share.share('${ApiService.baseURLWeb}${item.newsURI}');
                  },
                  child: Container(
                    decoration: const BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Colors.indigoAccent, // Màu bắt đầu
                          Colors.blue,
                          Colors.lightBlueAccent // Màu kết thúc
                        ],
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                      ),
                    ),
                    child:  Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(Icons.share, color: Colors.white),
                        SizedBox(width: 8),
                        Text(
                          'Promotion.Share'.tr(),
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            )
          : null,
      floatingActionButtonLocation: FloatingActionButtonLocation.centerDocked,
    );
  }

  Widget _buildHtmlContent(String htmlContent) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return Html(
          data: htmlContent,
          style: {
            "body": Style(fontSize: FontSize(16.0), lineHeight: LineHeight(1.5), fontFamily: "SourceSansPro"
                // maxWidth: constraints.maxWidth, // thêm dòng này
                ),
            "p": Style(margin: Margins(bottom: Margin(8)), fontFamily: "SourceSansPro"),
            "img": Style(
              width: Width(constraints.maxWidth), // thay đổi này
              height:  Height(300), // tự động điều chỉnh chiều cao
              alignment: Alignment.center,
            ),
          },
          onLinkTap: (url, _, __) {
            if (url != null) {
              _launchUrl(url);
            }
          },
        );
      },
    );
  }

  Future<void> _launchUrl(String url) async {
    final Uri uri = Uri.parse('${ApiService.baseURLWeb}$url');
    if (!await launchUrl(uri, mode: LaunchMode.externalApplication)) {
      throw Exception('Could not launch $url');
    }
  }
}

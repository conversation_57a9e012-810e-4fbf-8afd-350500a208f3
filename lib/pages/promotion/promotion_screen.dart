import 'package:flutter/material.dart';
import 'package:flutter_app/constants/index.dart';
import 'package:flutter_app/models/index.dart';
import 'package:flutter_app/utils/index.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:easy_localization/easy_localization.dart';

import '../../core/index.dart';

class PromotionScreen extends StatefulWidget {
  final bool isHomePage ;
  const PromotionScreen({super.key, this.isHomePage = true});

  @override
  State<PromotionScreen> createState() => _PromotionScreenState();
}

class _PromotionScreenState extends State<PromotionScreen> with SingleTickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = true;
  List<PromotionCategory> _promotionCategories = [];
  List<PromotionCategory> _newsCategories = [];
  String? _error;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _fetchCategories();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _fetchCategories() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Get promotion categories
      final promotionResult = await RepositoryProvider.of<Api>(context).promotion.getPromotionCategories();
      if (promotionResult != null) {
        final List<dynamic> promotionData = promotionResult.data['content'] ?? [];
        setState(() {
          _promotionCategories = promotionData.map((item) => PromotionCategory.fromJson(item)).toList();
        });
      }

      // Get news categories
      final newsResult = await RepositoryProvider.of<Api>(context).promotion.getNewsCategories();
      if (newsResult != null) {
        final List<dynamic> newsData = newsResult.data['content'] ?? [];
        final List<PromotionCategory> allCategories = newsData.map((item) => PromotionCategory.fromJson(item)).toList();

        // Phân loại danh mục theo tên
        setState(() {
          List<PromotionCategory> allCategories = newsData.map((item) => PromotionCategory.fromJson(item)).toList();

          setState(() {
            // _promotionCategories = allCategories
            //     .where((item) => item.name?.contains('Khuyến mãi') ?? false)
            //     .toList();

            _newsCategories = allCategories
                .where((item) => item.name?.contains('Tin') ?? false)
                .toList()
                ;
          });

          // _newsCategories = newsData.map((item) => PromotionCategory.fromJson(item)).toList();
        });
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _error = 'Error loading categories: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: appBar(
        title: 'Tin mới và Ưu đãi',
        titleColor: Colors.white,
        leadingWidth: widget.isHomePage ? 0 : null,
        titleStyle: const TextStyle(fontSize: 22, fontWeight: FontWeight.bold),
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? Center(child: Text(_error!))
              : Column(
                  children: [
                    SizedBox(
                      height: 40,
                      child: TabBar(
                        controller: _tabController,
                        labelColor: Colors.black,
                        labelStyle: const TextStyle(fontWeight: FontWeight.w600, fontSize: 16, fontFamily: 'Oswald'),
                        unselectedLabelColor: Colors.grey,
                        indicatorColor: Colors.brown,
                        tabs: [
                          Tab(text: 'Promotion.NewPromotions'.tr()),
                          Tab(text: 'Promotion.SideNews'.tr()),
                        ],
                      ),
                    ),
                    Expanded(
                      child: TabBarView(
                        controller: _tabController,
                        children: [
                          // Promotion Tab
                          _buildCategoryList(_promotionCategories),

                          // News Tab
                          _buildCategoryList(_newsCategories),
                        ],
                      ),
                    ),
                  ],
                ),
    );
  }

  Widget _buildCategoryList(List<PromotionCategory> categories) {
    if (categories.isEmpty) {
      return const Center(child: Text('Không có dữ liệu'));
    }

    return RefreshIndicator(
      onRefresh: _fetchCategories,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Column(
          children: categories.map((category) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // News list for this category
                NewsListWidget(categoryId: category.categoryId ?? ''),
                const SizedBox(height: 8),
              ],
            );
          }).toList(),
        ),
      ),
    );
  }
}

class NewsListWidget extends StatefulWidget {
  final String categoryId;

  const NewsListWidget({Key? key, required this.categoryId}) : super(key: key);

  @override
  State<NewsListWidget> createState() => _NewsListWidgetState();
}

class _NewsListWidgetState extends State<NewsListWidget> {
  bool _isLoading = true;
  List<PromotionItem> _items = [];
  String? _error;

  @override
  void initState() {
    super.initState();
    _fetchNews();
  }

  Future<void> _fetchNews() async {
    if (widget.categoryId.isEmpty) {
      setState(() {
        _isLoading = false;
        _error = 'Invalid category ID';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final result =
          await RepositoryProvider.of<Api>(context).promotion.getItemsByCategory(categoryId: widget.categoryId);

      if (result != null) {
        final List<dynamic> newsData = result.data['content'] ?? [];
        setState(() {
          _items = newsData.map((item) => PromotionItem.fromJson(item)).toList();
          _isLoading = false;
        });
      } else {
        setState(() {
          _isLoading = false;
          _error = 'Failed to load news';
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _error = 'Error: $e';
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_error != null) {
      return Center(child: Text(_error!));
    }

    if (_items.isEmpty) {
      return const Center(child: Text('Không có dữ liệu'));
    }

    return ListView.builder(
      itemCount: _items.length,
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemBuilder: (context, index) {
        final item = _items[index];
        return NewsItemCard(item: item);
      },
    );
  }
}

class NewsItemCard extends StatelessWidget {
  final PromotionItem item;

  const NewsItemCard({Key? key, required this.item}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
      child: InkWell(
        onTap: () {
          context.pushNamed(CRoute.newsDetail, extra: item);
        },
        child: Padding(
          padding: const EdgeInsets.all(8.0),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              // Image
              Expanded(
                  child: item.imageUrl != null
                      ? Image.network(
                          item.imageUrl!,
                          width: 120,
                          height: 80,
                          fit: BoxFit.cover,
                          errorBuilder: (context, error, stackTrace) {
                            return Container(
                              width: 120,
                              height: 80,
                              color: Colors.grey[300],
                              child: const Icon(Icons.image_not_supported),
                            );
                          },
                        )
                      : Container(
                          width: 120,
                          height: 80,
                          color: Colors.grey[300],
                          child: const Icon(Icons.image),
                        )),
              const SizedBox(width: 12),
              // Content
              Expanded(
                child: Text(
                  item.title,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 18,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

import 'package:flutter_app/pages/Movie_schedule/model/Film_model.dart';

class CinemaModel {
  final String? CinemaId;
  final String name;
  final String? pictureURL;

  CinemaModel({
    this.CinemaId,
    required this.name,
    this.pictureURL,
  });

  String getName() => name;

  factory CinemaModel.fromJson(Map<String, dynamic> json) {
    return CinemaModel(
      CinemaId: json['CinemaId'],
      name: json['Name'],
      pictureURL: json['PictureURL'],
    );
  }
}

/// Model for cinema regions/provinces - matches iOS/Android CinemaProvinceModel
class CinemaProvinceModel {
  final String? cityId;
  final String? cityName;
  final List<Cinema>? listCinema;

  CinemaProvinceModel({
    this.cityId,
    this.cityName,
    this.listCinema,
  });

  factory CinemaProvinceModel.fromJson(Map<String, dynamic> json) {
    return CinemaProvinceModel(
      cityId: json['CityId'] as String?,
      cityName: json['CityName'] as String?,
      listCinema: json['ListCinema'] != null
          ? (json['ListCinema'] as List)
              .map((item) => Cinema.fromJson(item))
              .toList()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'CityId': cityId,
      'CityName': cityName,
      'ListCinema': listCinema?.map((cinema) => cinema.toJson()).toList(),
    };
  }
}

class ShowModel {
  final String? showId;
  final String? cinemaId;
  final String? screenId;
  final String? filmId;
  final DateTime? startTime;
  final DateTime? endTime;
  final int? cleanupTime;
  final int? trailerTime;
  final int? filmDuration;
  final String? priceCardId;
  final bool? status;
  final String? order;
  final int? totalSeat;
  final int? seatSolded;
  final DateTime? dateShow;
  final int? startWeek;
  final int? day;
  final int? isApprove;
  final int? timeToLock;
  final DateTime? startDate;
  final DateTime? timeLockDate;
  final String? screenClass;
  final String? screenDesc;
  final String? screenTitle;
  final String? screenImageUrl;
  final bool isShowScreenIntro;

  // VIP/Zoom confirmation fields
  final String? message;
  final String? pathImage;

  ShowModel({
    this.showId,
    this.startDate,
    this.cinemaId,
    this.screenId,
    this.filmId,
    this.startTime,
    this.endTime,
    this.cleanupTime,
    this.trailerTime,
    this.filmDuration,
    this.priceCardId,
    this.status,
    this.order,
    this.totalSeat,
    this.seatSolded,
    this.dateShow,
    this.startWeek,
    this.day,
    this.isApprove,
    this.timeToLock,
    this.timeLockDate,
    this.screenClass,
    this.screenDesc,
    this.screenTitle,
    this.screenImageUrl,
    this.isShowScreenIntro = false,
    this.message,
    this.pathImage,
  });

  DateTime? getStartDate() => dateShow;

  DateTime? getTimeLockDate() => timeLockDate;

  factory ShowModel.fromJson(Map<String, dynamic> json) {
      return ShowModel(
        showId: json['ShowId'] as String?,
        startDate: DateTime.tryParse(json['StartDate'] ?? ''),
        timeLockDate: DateTime.tryParse(json['TimeLockDate'] ?? ''),
        cinemaId: json['CinemaId'] as String?,
        screenId: json['ScreenId'] as String?,
        filmId: json['FilmId'] as String?,
        startTime: DateTime.tryParse(json['StartTime'] ?? ''),
        endTime: DateTime.tryParse(json['EndTime'] ?? ''),
        cleanupTime: json['CleanupTime'] is int ? json['CleanupTime'] : int.tryParse(json['CleanupTime']?.toString() ?? ''),
        trailerTime: json['TrailerTime'] is int ? json['TrailerTime'] : int.tryParse(json['TrailerTime']?.toString() ?? ''),
        filmDuration: json['FilmDuration'] is int ? json['FilmDuration'] : int.tryParse(json['FilmDuration']?.toString() ?? ''),
        priceCardId: json['PriceCardId'] as String?,
        status: json['Status'] is bool ? json['Status'] : (json['Status']?.toString().toLowerCase() == 'true'),
        order: json['Order']?.toString(),
        totalSeat: json['TotalSeat'] is int ? json['TotalSeat'] : int.tryParse(json['TotalSeat']?.toString() ?? ''),
        seatSolded: json['SeatSolded'] is int ? json['SeatSolded'] : int.tryParse(json['SeatSolded']?.toString() ?? ''),
        dateShow: DateTime.tryParse(json['DateShow'] ?? ''),
        startWeek: json['StartWeek'] is int ? json['StartWeek'] : int.tryParse(json['StartWeek']?.toString() ?? ''),
        day: json['Day'] is int ? json['Day'] : int.tryParse(json['Day']?.toString() ?? ''),
        isApprove: json['IsApprove'] is int ? json['IsApprove'] : int.tryParse(json['IsApprove']?.toString() ?? ''),
        timeToLock: json['TimeToLock'] is int ? json['TimeToLock'] : int.tryParse(json['TimeToLock']?.toString() ?? ''),
        screenClass: json['ScreenClass'] as String?,
        screenDesc: json['ScreenDesc'] as String?,
        screenTitle: json['ScreenTitle'] as String?,
        screenImageUrl: json['ScreenImageUrl'] as String?,
        isShowScreenIntro: json['IsShowScreenIntro'] is bool ? json['IsShowScreenIntro'] : (json['IsShowScreenIntro']?.toString().toLowerCase() == 'true'),
        // VIP/Zoom confirmation fields
        message: json['Message'] as String?,
        pathImage: json['PathImage'] as String?,
      );
    }

    /// Check if this showtime requires VIP/Zoom confirmation
    bool get requiresConfirmation => message != null && message!.isNotEmpty;

    /// Check if confirmation has image
    bool get hasConfirmationImage => pathImage != null && pathImage!.isNotEmpty;
}

class ShowFilmModel {
  final String? filmId;
  final String? name;
  final String? nameF;
  final String? filmGenreName;
  final String? filmGenreNameF;
  final int? duration;
  final String? mainPosterUrl;
  final String? trailerURL;
  final bool isHot;
  final List<ListFilmModel>? listFilm;
  final double height;
  bool isExpanded;
  final String? filmRestrictAgeName;

  ShowFilmModel({
    this.filmId,
    this.name,
    this.nameF,
    this.filmGenreName,
    this.filmGenreNameF,
    this.duration,
    this.mainPosterUrl,
    this.trailerURL,
    this.isHot = false,
    this.isExpanded = false,
    this.listFilm,
    this.height = 0,
    this.filmRestrictAgeName,
  });

  String? getName() => name;

  String? getFilmGenre() => filmGenreName;

  factory ShowFilmModel.fromJson(Map<String, dynamic> json) {
    return ShowFilmModel(
      filmId: json['FilmId'],
      name: json['Name'],
      nameF: json['Name_F'],
      filmGenreName: json['FilmGenreName'],
      filmGenreNameF: json['FilmGenreName_F'],
      duration: json['Duration'],
      mainPosterUrl: json['MainPosterUrl'],
      trailerURL: json['TrailerURL'],
      isHot: json['IsHot'] ?? false,
      filmRestrictAgeName: json['FilmRestrictAgeName'] as String?,
      listFilm: (json['ListFilm'] as List<dynamic>?)?.map((e) => ListFilmModel.fromJson(e)).toList(),
    );
  }
}

class CityCinemaGroup {
  final String? cityId;
  final String? cityName;
  final int? order;
  List<Cinema>? listCinema;
  bool isExpanded;

  CityCinemaGroup({
    this.cityId,
    this.cityName,
    this.order,
    this.listCinema,
    this.isExpanded = false,
  });

  factory CityCinemaGroup.fromJson(Map<String, dynamic> json) {
    return CityCinemaGroup(
      cityId: json['CityId'] as String?,
      cityName: json['CityName'] as String?,
      order: json['Order'] as int?,
      listCinema: (json['ListCinema'] as List?)?.map((x) => Cinema.fromJson(x)).toList(),
    );
  }
}

class Cinema {
  final String? cinemaId;
  final String? cinemaTypeId;
  final String? name;
  final String? code;
  final String? address;
  final String? nameF;
  final String? addressF;
  final DateTime? startDate;
  final DateTime? endDate;
  final bool? status;
  final int? order;
  final int? catalogOrder;
  final String? phoneNumber;
  final String? cityId;
  final String? cityName;
  final String? latitude;
  final String? longitude;
  final String? picture;
  final String? newsId;
  double? distance; // Distance from user's location in kilometers

  Cinema({
    this.cinemaId,
    this.cinemaTypeId,
    this.name,
    this.code,
    this.address,
    this.nameF,
    this.addressF,
    this.startDate,
    this.endDate,
    this.status,
    this.order,
    this.catalogOrder,
    this.phoneNumber,
    this.cityId,
    this.cityName,
    this.latitude,
    this.longitude,
    this.picture,
    this.newsId,
    this.distance,
  });

  factory Cinema.fromJson(Map<String, dynamic> json) {
    return Cinema(
      cinemaId: json['CinemaId'] as String?,
      cinemaTypeId: json['CinemaTypeId'] as String?,
      name: json['Name'] as String?,
      code: json['Code'] as String?,
      address: json['Address'] as String?,
      nameF: json['Name_F'] as String?,
      addressF: json['Address_F'] as String?,
      startDate: json['StartDate'] != null ? DateTime.tryParse(json['StartDate']) : null,
      endDate: json['EndDate'] != null ? DateTime.tryParse(json['EndDate']) : null,
      status: json['Status'] as bool?,
      order: json['Order'] as int?,
      catalogOrder: json['CatalogOrder'] as int?,
      phoneNumber: json['PhoneNumber'] as String?,
      cityId: json['CityId'] as String?,
      cityName: json['CityName'] as String?,
      latitude: json['Latitude'] as String?,
      longitude: json['Longtitude'] as String?,
      picture: json['Picture'] as String?,
      newsId: json['NewsId'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'CinemaId': cinemaId,
      'CinemaTypeId': cinemaTypeId,
      'Name': name,
      'Code': code,
      'Address': address,
      'Name_F': nameF,
      'Address_F': addressF,
      'StartDate': startDate?.toIso8601String(),
      'EndDate': endDate?.toIso8601String(),
      'Status': status,
      'Order': order,
      'CatalogOrder': catalogOrder,
      'PhoneNumber': phoneNumber,
      'CityId': cityId,
      'CityName': cityName,
      'Latitude': latitude,
      'Longtitude': longitude,
      'Picture': picture,
      'NewsId': newsId,
    };
  }

  // Get latitude as double
  double? get latitudeAsDouble => latitude != null ? double.tryParse(latitude!) : null;

  // Get longitude as double
  double? get longitudeAsDouble => longitude != null ? double.tryParse(longitude!) : null;

  // Check if cinema has valid coordinates
  bool get hasValidCoordinates => latitudeAsDouble != null && longitudeAsDouble != null;

  // Get formatted distance string
  String? get formattedDistance {
    if (distance == null) return null;

    if (distance! < 1) {
      // Convert to meters if less than 1 km
      int meters = (distance! * 1000).round();
      return '$meters m';
    } else if (distance! < 10) {
      // Show one decimal place if less than 10 km
      return '${distance!.toStringAsFixed(1)} km';
    } else {
      // Round to nearest km if 10 km or more
      return '${distance!.round()} km';
    }
  }
}

class ShowFilmInCinema {
  FilmModel? film;
  List<ShowModel>? listShow;

  ShowFilmInCinema({
    this.film,
    this.listShow,
  });

  factory ShowFilmInCinema.fromJson(Map<String, dynamic> json) {
    return ShowFilmInCinema(
      film: FilmModel.fromJson(json['Film']),
      listShow: (json['ShowModel'] as List<dynamic>?)?.map((e) => ShowModel.fromJson(e)).toList(),
    );
  }

  ShowFilmInCinema copyWith({
    FilmModel? film,
    List<ShowModel>? listShow,
  }) {
    return ShowFilmInCinema(
      film: film ?? this.film,
      listShow: listShow ?? this.listShow,
    );
  }
}

import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_app/utils/index.dart';
import '../../../constants/index.dart';
import '../../../core/index.dart';
import '../model/cinema_model.dart';

/// VIP/Zoom Confirmation Dialog
/// Hiển thị dialog xác nhận khi user chọn suất chiếu đặc biệt (VIP/Zoom)
/// Tương tự như iOS implementation
class VipZoomConfirmationDialog extends StatelessWidget {
  final ShowModel showTime;
  final String cinemaName;
  final String filmName;
  final VoidCallback onConfirm;
  final VoidCallback onCancel;

  const VipZoomConfirmationDialog({
    super.key,
    required this.showTime,
    required this.cinemaName,
    required this.filmName,
    required this.onConfirm,
    required this.onCancel,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
      ),
      child: Container(
        constraints: const BoxConstraints(
          maxWidth: 400,
          maxHeight: 600,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Content
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 8),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 16),
                    _buildConfirmationImage(),

                    const SizedBox(height: 16),

                    // Message
                    _buildMessage(),
                  ],
                ),
              ),
            ),
            // Action buttons
            _buildActionButtons(context),
          ],
        ),
      ),
    );
  }

  Widget _buildConfirmationImage() {
    return Container(
      width: double.infinity,
      height: 200,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(0),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(0),
        child: CachedNetworkImage(
          imageUrl: showTime.screenImageUrl!,
          fit: BoxFit.cover,
          placeholder: (context, url) => Container(
            color: Colors.grey.shade100,
            child: const Center(
              child: CircularProgressIndicator(),
            ),
          ),
          errorWidget: (context, url, error) => Container(
            color: Colors.grey.shade100,
            child: const Center(
              child: Icon(
                Icons.image_not_supported,
                color: Colors.grey,
                size: 48,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildMessage() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          showTime.screenTitle ?? '',
          style: const TextStyle(
            fontSize: 18,
            height: 1.5,
            color: Colors.blueAccent,
            fontFamily: 'Oswald',
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          showTime.screenDesc ?? '',
          style: const TextStyle(
            fontSize: 16,
            height: 1.5,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons(BuildContext context) {
    return Container(
        padding: const EdgeInsets.all(8),
        child: Row(
          children: [
            Expanded(
              child: InkWell(
                onTap: onCancel,
                highlightColor: Colors.white,
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: CSpace.xl),
                  child: Text(
                    'Hủy',
                    style: TextStyle(
                      color: CColor.black.shade300,
                      fontSize: CFontSize.base,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ),
            line(width: 1, height: 33),
            Expanded(
              child: InkWell(
                onTap: onConfirm,
                highlightColor: Colors.white,
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: CSpace.xl),
                  child: Text(
                    'Xác nhận',
                    style: TextStyle(
                      color: CColor.blue,
                      fontSize: CFontSize.base,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
            ),
          ],
        ));
  }

  /// Static method để hiển thị dialog
  static Future<bool?> show({
    required BuildContext context,
    required ShowModel showTime,
    required String cinemaName,
    required String filmName,
  }) {
    return showDialog<bool>(
      context: context,
      barrierDismissible: false,
      builder: (context) => VipZoomConfirmationDialog(
        showTime: showTime,
        cinemaName: cinemaName,
        filmName: filmName,
        onConfirm: () => Navigator.of(context).pop(true),
        onCancel: () => Navigator.of(context).pop(false),
      ),
    );
  }
}

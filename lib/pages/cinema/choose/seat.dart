import 'dart:async';
import 'dart:io';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_app/core/index.dart';
import 'package:flutter_app/pages/Movie_schedule/model/Film_model.dart';
import 'package:flutter_app/pages/cinema/choose/native_signalr_service.dart' as native;
import 'package:flutter_app/pages/cinema/choose/signal_connect.dart';
import 'package:flutter_app/pages/cinema/model/cinema_model.dart';
import 'package:flutter_app/pages/cinema/model/list_seat_model.dart';
import 'package:flutter_app/pages/cinema/model/seat_model.dart';
import 'package:flutter_app/pages/cinema/model/ticket_type.dart';
import 'package:flutter_app/pages/voucher/api/api_test.dart';
import 'package:flutter_app/service/language_service.dart';
import 'package:flutter_app/pages/cinema/payment/android_style_webview_payment.dart';
import 'package:flutter_app/pages/cinema/payment/ios_style_webview_payment.dart';
import 'package:flutter_app/pages/cinema/widget/seat_grid.dart';
import 'package:flutter_app/services/signalr_classic/signalr_classic_service.dart' as classic;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';

import '../../../constants/index.dart';
import '../../../cubit/index.dart';
import '../../../utils/index.dart';
import '../../other_tab/setting/policy_screen.dart';
import '../utils/seat_combo_helper.dart';

class ChooseSeatScreen extends StatefulWidget {
  final String? cinemaId;
  final String? cinemaName;
  final ShowModel? showTime;
  final FilmModel? film;

  const ChooseSeatScreen({
    super.key,
    this.cinemaId,
    this.cinemaName,
    this.showTime,
    this.film,
  });

  @override
  State<ChooseSeatScreen> createState() => _ChooseSeatScreenState();
}

class _ChooseSeatScreenState extends State<ChooseSeatScreen> {
  ListSeatModel? _listSeat;
  List<SeatModel> _selectedSeats = [];
  bool _isLoading = true;
  String? _error;
  int _totalPrice = 0;
  Timer? _timer;
  int _remainingTime = 600; // 5 minutes in seconds
  bool _isConnected = true;
  double timeStartBooking = DateTime.now().millisecondsSinceEpoch / 1000;

  // Connection monitoring
  Timer? _connectionMonitorTimer;
  int _reconnectAttempts = 0;
  static const int _maxReconnectAttempts = 5;

  // Cancellation mechanism
  bool _isDisposed = false;
  bool _isConnecting = false;

  // SignalR services - properly declared
  final SignalRService _signalRService = SignalRService.instance;

  // Use the native SignalRService on iOS
  final native.NativeSeatSignalRService _nativeSignalRService = native.NativeSeatSignalRService.instance;

  // Use the SignalRClassicService for SignalR Classic
  final classic.SignalRClassicService _signalRClassicService = classic.SignalRClassicService();
  bool isEnglish = false;

  @override
  void initState() {
    super.initState();
    getSeatData();
    // _initSignalR();
    startTimer();
    context.read<AuthC>().check(context: context);
  }

  @override
  void dispose() {
    _timer?.cancel();
    // Stop the appropriate SignalR service
    // Stop the appropriate SignalR service
    if (Platform.isIOS) {
      _nativeSignalRService.dispose();
    } else {
      // Try to disconnect SignalR Classic first
      try {
        if (_signalRClassicService.isConnected) {
          _signalRClassicService.disconnect();
        }
      } catch (e) {
        print("Error disconnecting SignalR Classic: $e");
      }

      // Set disposal flag to stop all ongoing operations
      _isDisposed = true;
      _isConnecting = false;

      _timer?.cancel();
      _connectionMonitorTimer?.cancel();

      // Cleanup SignalR connections with timeout
      _cleanupSignalRConnections();

      super.dispose();
    }
  }

  /// Cleanup SignalR connections with proper timeout and error handling
  Future<void> _cleanupSignalRConnections() async {
    print("Starting SignalR cleanup...");

    try {
      if (Platform.isIOS) {
        // iOS cleanup
        await _cleanupNativeSignalR();
      } else {
        // Android cleanup
        await _cleanupAndroidSignalR();
      }
    } catch (e) {
      print("Error during SignalR cleanup: $e");
    }

    print("SignalR cleanup completed");
  }

  /// Cleanup native SignalR on iOS with timeout
  Future<void> _cleanupNativeSignalR() async {
    try {
      print("Cleaning up native SignalR...");

      // // Remove listeners first
      // _nativeSignalRService.removeConnectionListener(_onNativeSignalRConnectionState);
      // _nativeSignalRService.removeDataListener(_onNativeSeatUpdate);

      // Dispose with timeout
      await Future.any([
        Future(() => _nativeSignalRService.dispose()),
        Future.delayed(const Duration(seconds: 5)), // 5 second timeout
      ]);

      print("Native SignalR cleanup completed");
    } catch (e) {
      print("Error cleaning up native SignalR: $e");
    }
  }

  /// Cleanup Android SignalR with timeout
  Future<void> _cleanupAndroidSignalR() async {
    try {
      print("Cleaning up Android SignalR...");

      // Cleanup SignalR Classic first
      await _cleanupSignalRClassic();

      // Cleanup Dart SignalR
      await _cleanupDartSignalR();

      print("Android SignalR cleanup completed");
    } catch (e) {
      print("Error cleaning up Android SignalR: $e");
    }
  }

  /// Cleanup SignalR Classic with timeout
  Future<void> _cleanupSignalRClassic() async {
    try {
      if (_signalRClassicService.isConnected) {
        print("Disconnecting SignalR Classic...");

        // Leave group first if connected
        if (widget.showTime?.showId != null) {
          try {
            await Future.any([
              _signalRClassicService.leaveGroup(widget.showTime!.showId!),
              Future.delayed(const Duration(seconds: 3)), // 3 second timeout
            ]);
            print("Left SignalR Classic group");
          } catch (e) {
            print("Error leaving SignalR Classic group: $e");
          }
        }

        // Disconnect with timeout
        await Future.any([
          _signalRClassicService.disconnect(),
          Future.delayed(const Duration(seconds: 5)), // 5 second timeout
        ]);

        print("SignalR Classic disconnected");
      }

      // Dispose the service
      _signalRClassicService.dispose();
      print("SignalR Classic disposed");
    } catch (e) {
      print("Error cleaning up SignalR Classic: $e");
    }
  }

  /// Cleanup Dart SignalR with timeout
  Future<void> _cleanupDartSignalR() async {
    try {
      print("Cleaning up Dart SignalR...");

      // Remove listeners first
      // _signalRService.removeConnectionListener(_onSignalRConnectionState);
      // _signalRService.removeDataListener(_onSeatUpdate);

      // Stop with timeout
      await Future.any([
        Future(() => _signalRService.stop()),
        Future.delayed(const Duration(seconds: 5)), // 5 second timeout
      ]);

      print("Dart SignalR stopped");
    } catch (e) {
      print("Error cleaning up Dart SignalR: $e");
    }
  }

  /// Initialize SignalR connection
  Future<void> _initSignalR() async {
    // Check disposal before starting
    if (_isDisposed) {
      print("🔴 SignalR initialization cancelled - widget disposed");
      return;
    }

    if (Platform.isIOS) {
      // Use native implementation on iOS
      initNativeSignalR();
    } else {
      // Try to use SignalR Classic first
      try {
        await _initSignalRClassic();
      } catch (e) {
        // Check disposal before fallback
        if (_isDisposed) {
          print("🔴 SignalR fallback cancelled - widget disposed");
          return;
        }

        print("Error initializing SignalR Classic: $e");
      }
    }
  }

  /// Initialize SignalR Classic connection with timeout and retry
  Future<void> _initSignalRClassic() async {
    try {
      print("Initializing SignalR Classic...");

      // Add connection listener
      _signalRClassicService.addConnectionListener((isConnected) {
        setState(() {
          _isConnected = isConnected;
        });

        if (isConnected) {
          print('SignalR Classic connected with ID: ${_signalRClassicService.connectionId}');
        } else {
          print('SignalR Classic disconnected');
        }
      });

      // Add data listener
      _signalRClassicService.addDataListener((data) async {
        print(
            'SignalR Classic data received: ${data.connectionId}, ${data.showId}, ${data.seatIndex}, ${data.seatStatus}');

        // Validate the message is for the current show
        if (data.showId != widget.showTime?.showId) {
          print("Ignoring message for different show: ${data.showId} (current: ${widget.showTime?.showId})");
          return;
        }

        // Only process updates from other users
        if (_signalRClassicService.connectionId != data.connectionId) {
          print(
              "Processing seat update from user ${data.connectionId}: Seat index ${data.seatIndex}, status ${data.seatStatus}");
          setSeatIndex(data.seatIndex, data.seatStatus);
        } else {
          print("Ignoring own seat update: Seat index ${data.seatIndex}, status ${data.seatStatus}");
        }

        // Add connection listener
        _signalRClassicService.addConnectionListener((isConnected) {
          // Check disposal before processing connection state
          if (_isDisposed) {
            print("🔴 SignalR Classic connection listener ignored - widget disposed");
            return;
          }

          setState(() {
            _isConnected = isConnected;
          });

          if (isConnected) {
            print('SignalR Classic connected with ID: ${_signalRClassicService.connectionId}');

            // Auto join group when connected
            if (widget.showTime?.showId != null && !_isDisposed) {
              joinSignalRClassicGroup();
            }
          } else {
            print('SignalR Classic disconnected');
          }
        });

        // Add data listener
        _signalRClassicService.addDataListener((data) {
          // Check disposal before processing data
          if (_isDisposed) {
            print("🔴 SignalR Classic data listener ignored - widget disposed");
            return;
          }

          print(
              'SignalR Classic data received: ${data.connectionId}, ${data.showId}, ${data.seatIndex}, ${data.seatStatus}');

          // Validate the message is for the current show
          if (data.showId != widget.showTime?.showId) {
            print("Ignoring message for different show: ${data.showId} (current: ${widget.showTime?.showId})");
            return;
          }

          // Only process updates from other users
          if (_signalRClassicService.connectionId != data.connectionId) {
            print(
                "Processing seat update from user ${data.connectionId}: Seat index ${data.seatIndex}, status ${data.seatStatus}");
            setSeatIndex(data.seatIndex, data.seatStatus);
          } else {
            print("Ignoring own seat update: Seat index ${data.seatIndex}, status ${data.seatStatus}");
          }
        });

        // Check disposal before connecting
        if (_isDisposed) {
          print("🔴 SignalR Classic connection cancelled before connect - widget disposed");
          return;
        }

        // Connect to the SignalR hub with timeout and retry
        final connected = await connectSignalRClassicWithRetry();

        // Check disposal after connection attempt
        if (_isDisposed) {
          print("🔴 SignalR Classic initialization cancelled after connect - widget disposed");
          return;
        }

        if (!connected) {
          throw Exception("Failed to connect to SignalR Classic after retries");
        }
        print("SignalR Classic initialization completed successfully");
      });
    } catch (e) {
      if (_isDisposed) {
        print("🔴 SignalR Classic initialization cancelled due to disposal: $e");
        return;
      }
      print("Error initializing SignalR Classic: $e");
      rethrow;
    }
  }

  /// Connect SignalR Classic with retry mechanism
  Future<bool> connectSignalRClassicWithRetry({int maxRetries = 3}) async {
    _isConnecting = true;

    for (int attempt = 1; attempt <= maxRetries; attempt++) {
      // Check if disposed before each attempt
      if (_isDisposed) {
        print("🔴 SignalR Classic connection cancelled - widget disposed");
        _isConnecting = false;
        return false;
      }

      try {
        print("SignalR Classic connection attempt $attempt/$maxRetries");

        // Connect with timeout and disposal check
        final connected = await Future.any([
          _signalRClassicService.connect(
            ApiService.signalRUrl,
            hubName: 'chooseSeatHub',
            maxRetries: 1, // Single attempt per retry cycle
          ),
          Future.delayed(const Duration(seconds: 30)).then((_) => false), // 30 second timeout
          waitForDisposal(), // Cancel if disposed
        ]);

        // Check disposal after connection attempt
        if (_isDisposed) {
          print("🔴 SignalR Classic connection cancelled after attempt - widget disposed");
          _isConnecting = false;
          return false;
        }

        if (connected == true) {
          print("SignalR Classic connected successfully on attempt $attempt");
          _isConnecting = false;
          return true;
        } else if (connected == null) {
          print("🔴 SignalR Classic connection cancelled - widget disposed");
          _isConnecting = false;
          return false;
        } else {
          print("SignalR Classic connection failed on attempt $attempt");
        }
      } catch (e) {
        print("SignalR Classic connection error on attempt $attempt: $e");

        // Check disposal after error
        if (_isDisposed) {
          print("🔴 SignalR Classic connection cancelled after error - widget disposed");
          _isConnecting = false;
          return false;
        }
      }

      // Wait before retry (exponential backoff) with disposal check
      if (attempt < maxRetries && !_isDisposed) {
        final delay = Duration(seconds: attempt * 2);
        print("Waiting ${delay.inSeconds} seconds before retry...");

        // Wait with disposal check
        await Future.any([
          Future.delayed(delay),
          waitForDisposal(),
        ]);

        // Check disposal after delay
        if (_isDisposed) {
          print("🔴 SignalR Classic retry cancelled - widget disposed");
          _isConnecting = false;
          return false;
        }
      }
    }

    print("All SignalR Classic connection attempts failed");
    _isConnecting = false;
    return false;
  }

  /// Wait for disposal flag to be set
  Future<bool?> waitForDisposal() async {
    while (!_isDisposed) {
      await Future.delayed(const Duration(milliseconds: 100));
    }
    return null; // Return null to indicate cancellation
  }

  /// Join SignalR Classic group with timeout
  Future<void> joinSignalRClassicGroup() async {
    if (!_signalRClassicService.isConnected || widget.showTime?.showId == null) {
      print("Cannot join SignalR Classic group: Not connected or show ID is null");
      return;
    }

    try {
      print("Joining SignalR Classic group: ${widget.showTime!.showId!}");

      // Join group with timeout
      final success = await Future.any([
        _signalRClassicService.joinGroup(widget.showTime!.showId!),
        Future.delayed(const Duration(seconds: 10)).then((_) => false), // 10 second timeout
      ]);

      if (success) {
        print("Successfully joined SignalR Classic group: ${widget.showTime!.showId!}");
      } else {
        print("Failed to join SignalR Classic group: timeout");
      }
    } catch (e) {
      print("Error joining SignalR Classic group: $e");
    }
  }

  /// Initialize native SignalR connection on iOS
  Future<void> initNativeSignalR() async {
    // Add connection listener to track connection state
    _nativeSignalRService.addConnectionListener(onNativeSignalRConnectionState);

    // Add data listener to receive seat updates
    _nativeSignalRService.addDataListener(onNativeSeatUpdate);

    try {
      // Start the SignalR connection
      // Sử dụng URL chính xác như trong iOS: Config.SignalRURL = "https://www.betacinemas.vn/signalr/hubs"
      await _nativeSignalRService.startSignalR(customUrl: 'https://www.betacinemas.vn/signalr/hubs');

      // Không cần gọi joinGroup như trong iOS
      // iOS không gọi phương thức joinGroup mà chỉ đăng ký lắng nghe sự kiện broadcastMessage
      // và lọc tin nhắn dựa trên showId
      print("SignalR connection established - listening for updates for show ID: ${widget.showTime?.showId}");
    } catch (e) {
      print("Error initializing native SignalR: $e");
      showConnectionError("Không thể kết nối đến máy chủ. Vui lòng thử lại sau.");
    }
  }

  /// Handle connection state changes for Dart implementation
  void onSignalRConnectionState(bool isConnected) {
    setState(() {
      _isConnected = isConnected;
    });

    if (isConnected) {
      print('SignalR connected');
      // Join the group when connected
      if (widget.showTime?.showId != null) {
        joinGroup();
      }
    } else {
      print('SignalR disconnected');
    }
  }

  /// Handle connection state changes for native implementation
  void onNativeSignalRConnectionState(bool isConnected) {
    print('Native SignalR connection state changed: $isConnected (previous state: $_isConnected)');

    setState(() {
      _isConnected = isConnected;
    });

    if (isConnected) {
      print('Native SignalR connected');
      // Không cần gọi joinGroup như trong iOS
      // Chỉ cần lắng nghe sự kiện broadcastMessage
      print("Ready to receive updates for show ID: ${widget.showTime?.showId}");

      // In ra thông tin kết nối chi tiết
      final connectionId = _nativeSignalRService.connectionId;
      print("Connection ID seat: $connectionId");

      // Kiểm tra xem trạng thái kết nối có đồng bộ không
      print(
          "Connection state check - Native service: ${_nativeSignalRService.isConnected}, Local variable: $_isConnected");
    } else {
      print('Native SignalR disconnected');
    }
  }

  /// Handle seat updates from Dart SignalR
  void onSeatUpdate(SeatSignalrResponse data) {
    print('Seat update received: ${data.collectionId}, ${data.showId}, ${data.seatIndex}, ${data.seatStatus}');

    // Validate the message is for the current show
    if (data.showId != widget.showTime?.showId) {
      print("Ignoring message for different show: ${data.showId} (current: ${widget.showTime?.showId})");
      return;
    }

    // Only process updates from other users
    if (_signalRService.connectionId() != data.collectionId) {
      print(
          "Processing seat update from user ${data.collectionId}: Seat index ${data.seatIndex}, status ${data.seatStatus}");
      setSeatIndex(data.seatIndex, data.seatStatus);
    } else {
      print("Ignoring own seat update: Seat index ${data.seatIndex}, status ${data.seatStatus}");
    }
  }

  /// Handle seat updates from native SignalR
  void onNativeSeatUpdate(native.SeatSignalrResponse data) {
    print('Native seat update received: ${data.connectionId}, ${data.showId}, ${data.seatIndex}, ${data.seatStatus}');

    // Validate the message is for the current show
    if (data.showId != widget.showTime?.showId) {
      print("Ignoring message for different show: ${data.showId} (current: ${widget.showTime?.showId})");
      return;
    }

    // Only process updates from other users
    if (_nativeSignalRService.connectionId != data.connectionId) {
      print(
          "Processing seat update from user ${data.connectionId}: Seat index ${data.seatIndex}, status ${data.seatStatus}");
      setSeatIndex(data.seatIndex, data.seatStatus);
    } else {
      print("Ignoring own seat update: Seat index ${data.seatIndex}, status ${data.seatStatus}");
    }
  }

  /// Updates the status of a seat based on its index
  /// This is called when receiving updates from other users via SignalR
  ///
  /// @param seatIndex The index of the seat to update
  /// @param status The new status of the seat (0 = empty, 1 = selecting, 2 = sold)
  void setSeatIndex(int seatIndex, int status) {
    if (_listSeat == null || _listSeat!.screen == null || _listSeat!.screen!.seatPosition == null) {
      print("Cannot update seat: Seat data not loaded");
      return;
    }

    // Find the seat with the given index
    SeatModel? targetSeat;
    for (var row in _listSeat!.screen!.seatPosition!) {
      for (var seat in row) {
        if (seat.seatIndex == seatIndex) {
          targetSeat = seat;
          break;
        }
      }
      if (targetSeat != null) break;
    }

    if (targetSeat == null) {
      print("Cannot update seat: Seat with index $seatIndex not found");
      return;
    }

    // Update the seat status
    setState(() {
      if (targetSeat != null) {
        if (status == SeatSoldStatus.EMPTY.index) {
          // If the seat is being deselected, remove it from selected seats
          _selectedSeats.removeWhere((s) => s.seatNumber == targetSeat!.seatNumber);
          targetSeat.soldStatus = SeatSoldStatus.EMPTY;

          // If it's a couple seat, also update its partner
          if (targetSeat.coupleSeat != null) {
            _selectedSeats.removeWhere((s) => s.seatNumber == targetSeat?.coupleSeat!.seatNumber);
            targetSeat.coupleSeat!.soldStatus = SeatSoldStatus.EMPTY;
          }
        } else if (status == SeatSoldStatus.SELECTING.index) {
          // If the seat is being selected by someone else, mark it as selecting
          targetSeat.soldStatus = SeatSoldStatus.SELECTING;

          // If it's a couple seat, also update its partner
          if (targetSeat.coupleSeat != null) {
            targetSeat.coupleSeat!.soldStatus = SeatSoldStatus.SELECTING;
          }
        } else if (status == SeatSoldStatus.SOLD.index) {
          // If the seat is being marked as sold, update its status
          targetSeat.soldStatus = SeatSoldStatus.SOLD;

          // If it's a couple seat, also update its partner
          if (targetSeat.coupleSeat != null) {
            targetSeat.coupleSeat!.soldStatus = SeatSoldStatus.SOLD;
          }
        }
      }
    });

    // Update the total price if needed
    updateTotalPrice();
  }

  /// Join the seat selection group for real-time updates (Dart implementation)
  Future<void> joinGroup() async {
    if (!_isConnected || widget.showTime?.showId == null) {
      print("Cannot join group: Not connected or show ID is null");
      return;
    }

    try {
      print("Joining group: ${widget.showTime!.showId!}");

      // Use the invoke method to join the group
      await _signalRService.invoke("JoinGroup", [widget.showTime!.showId!]);

      print("Successfully joined group: ${widget.showTime!.showId!}");
    } catch (e) {
      print("Error joining group: $e");

      // If joining fails, try again after a delay
      if (mounted && _isConnected) {
        Future.delayed(const Duration(seconds: 3), () {
          if (mounted && _isConnected) {
            joinGroup();
          }
        });
      }
    }
  }

  /// Shows a connection error message to the user
  void showConnectionError(String message) {
    if (!mounted) return;

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 5),
        action: SnackBarAction(
          label: 'Thử lại',
          onPressed: () {
            _initSignalR();
          },
        ),
      ),
    );
  }

  void startTimer() {
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      setState(() {
        if (_remainingTime > 0) {
          _remainingTime--;
        } else {
          _timer?.cancel();
          showTimeoutDialog();
        }
      });
    });
  }

  void showTimeoutDialog() {
    UDialog().showError(
      title: 'Hết thời gian',
      text: 'Thời gian chọn ghế đã hết. Vui lòng thử lại.',
      onTap: () {
        Navigator.of(context).pop();
        Navigator.of(context).pop();
      },
    );
    // showDialog(
    //   context: context,
    //   barrierDismissible: false,
    //   builder: (context) => AlertDialog(
    //     title: const Text('Hết thời gian'),
    //     content: const Text('Thời gian chọn ghế đã hết. Vui lòng thử lại.'),
    //     actions: [
    //       TextButton(
    //         onPressed: () {
    //           Navigator.of(context).pop();
    //           Navigator.of(context).pop();
    //         },
    //         child: const Text('OK'),
    //       ),
    //     ],
    //   ),
    // );
  }

  Future<void> getSeatData() async {
    isEnglish = await LanguageService().isEnglish();
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final api = RepositoryProvider.of<Api>(context).film;
      final response = await api.getShowSeat(id: widget.showTime?.showId ?? '');

      if (response != null) {
        final seatData = ListSeatModel.fromJson(response.data);

        // Process couple seats to link them together
        processCoupleSeatLinks(seatData);

        setState(() {
          _listSeat = seatData;
          // _selectedSeats = seatData.showSeats!.sublist(0,1);
          _isLoading = false;
        });
      } else {
        setState(() {
          _isLoading = false;
          _error = "Không thể tải thông tin ghế ngồi";
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _error = "Lỗi: $e";
      });
    }
  }

  // Process and link couple seats based on their position in the grid
  void processCoupleSeatLinks(ListSeatModel listSeat) {
    if (listSeat.screen?.seatPosition == null) return;

    for (var row in listSeat.screen!.seatPosition!) {
      for (int i = 0; i < row.length; i++) {
        final seat = row[i];

        // Skip if not a couple seat or already processed
        if (seat.seatTypeEnum != SeatType.COUPLE || seat.coupleSeat != null) continue;

        // Find the adjacent seat that forms the couple
        // Check the next seat in the row
        if (i < row.length - 1) {
          final nextSeat = row[i + 1];
          if (nextSeat.seatTypeEnum == SeatType.COUPLE &&
              nextSeat.coupleSeat == null &&
              areAdjacentSeatNumbers(seat.seatNumber, nextSeat.seatNumber)) {
            // Link the seats
            seat.coupleSeat = nextSeat;
            nextSeat.coupleSeat = seat;
          }
        }
      }
    }
  }

  // Check if two seat numbers are adjacent (e.g., A1 and A2)
  bool areAdjacentSeatNumbers(String? seat1, String? seat2) {
    if (seat1 == null || seat2 == null || seat1.isEmpty || seat2.isEmpty) return false;
    // Check if they have the same row letter
    if (seat1.substring(0, 1) != seat2.substring(0, 1)) return false;
    // Extract the seat numbers
    int? num1 = int.tryParse(seat1.substring(1));
    int? num2 = int.tryParse(seat2.substring(1));

    if (num1 == null || num2 == null) return false;

    // Check if they are consecutive numbers
    return (num1 + 1 == num2) || (num2 + 1 == num1);
  }

  /// Sends a seat status update to the SignalR hub
  /// This matches the iOS implementation's sendSeat method
  ///
  /// @param seat The seat being updated
  /// @param status The new status of the seat (0 = empty, 1 = selecting, 2 = sold)
  Future<void> sendSeat(SeatModel seat, int status) async {
    // Kiểm tra trạng thái kết nối từ native code thay vì biến _isConnected
    bool isConnectedNow = Platform.isIOS ? _nativeSignalRService.isConnected : _isConnected;

    if (!isConnectedNow) {
      print(
          "Cannot send seat update: SignalR not connected (native: ${_nativeSignalRService.isConnected}, local: $_isConnected)");
      return;
    }

    try {
      print("Sending seat update: Seat ${seat.seatNumber} (${seat.seatIndex}) to status $status");

      if (Platform.isIOS) {
        // Use the native SignalR service on iOS
        await _nativeSignalRService.sendSeat(widget.showTime?.showId ?? "", seat.seatIndex ?? 0, status);
      } else {
        // Try to use SignalR Classic first
        try {
          if (_signalRClassicService.isConnected) {
            await _signalRClassicService.sendSeat(widget.showTime?.showId ?? "", seat.seatIndex ?? 0, status);
          } else {
            // Fall back to Dart SignalR service
            await _signalRService.sendSeat(widget.showTime?.showId ?? "", seat.seatIndex ?? 0, status);
          }
        } catch (e) {
          // Fall back to Dart SignalR service
          print("Error sending seat update with SignalR Classic: $e");
          await _signalRService.sendSeat(widget.showTime?.showId ?? "", seat.seatIndex ?? 0, status);
        }
      }

      print("Seat update sent successfully");
    } catch (e) {
      print("Error sending seat update: $e");

      // Show error to user
      if (mounted && _isConnected) {
        UDialog().showError(text: "Lỗi cập nhật ghế: $e");
      }
    }
  }

  void selectSeat(SeatModel seat) {
    // Kiểm tra trạng thái kết nối từ native code thay vì biến _isConnected
    bool isConnectedNow = /* Platform.isIOS ? _nativeSignalRService.isConnected :*/ _isConnected;
    if (!isConnectedNow) {
      print(
          "Cannot select seat: SignalR not connected (native: ${_nativeSignalRService.isConnected}, local: $_isConnected)");
      // ScaffoldMessenger.of(context).showSnackBar(
      //   const SnackBar(content: Text('Không thể kết nối đến máy chủ. Vui lòng thử lại sau.')),
      // );
      UDialog().showError(
        text: 'Không thể kết nối đến máy chủ. Vui lòng đợi một lúc hoặc thử lại sau.',
      );
      return;
    }

    if (seat.soldStatus != SeatSoldStatus.EMPTY) return;
    if (seat.isWay == true || seat.isBroken == true || seat.isNotUsed == true) return;

    // Check if we've reached the maximum number of seats
    if (_selectedSeats.length >= 8 || (seat.seatTypeEnum == SeatType.COUPLE && _selectedSeats.length >= 7)) {
      UDialog().showError(
        title: 'Lỗi chọn ghế',
        text: 'Bạn chỉ có thể chọn tối đa 8 ghế (bao gồm cả ghế đôi)',
      );
      // ScaffoldMessenger.of(context).showSnackBar(
      //   const SnackBar(content: Text('Bạn chỉ có thể chọn tối đa 8 ghế')),
      // );
      return;
    }

    // Check if selecting this seat would leave a single seat between occupied seats
    if (!checkValidSeatSelection(seat)) {
      return;
    }

    setState(() {
      _selectedSeats.add(seat);
      sendSeat(seat, SeatSoldStatus.SELECTING.index);

      if (seat.coupleSeat != null) {
        _selectedSeats.add(seat.coupleSeat!);
        sendSeat(seat.coupleSeat!, SeatSoldStatus.SELECTING.index);
      }
    });

    updateTotalPrice();
  }

  bool checkValidSeatSelection(SeatModel seat) {
    // Tìm hàng ghế chứa ghế hiện tại
    List<SeatModel>? seatRow;
    int seatIndex = -1;

    for (var row in _listSeat!.screen!.seatPosition!) {
      final index = row.indexWhere((s) => s.seatIndex == seat.seatIndex);
      if (index != -1) {
        seatRow = row;
        seatIndex = index;
        break;
      }
    }

    if (seatRow == null || seatIndex == -1) return true;

    // Tạo một bản sao của danh sách ghế đã chọn và thêm ghế hiện tại vào
    final List<SeatModel> simulatedSelectedSeats = List.from(_selectedSeats);
    simulatedSelectedSeats.add(seat);

    // Nếu là ghế đôi, thêm cả ghế đôi vào danh sách mô phỏng
    if (seat.coupleSeat != null) {
      simulatedSelectedSeats.add(seat.coupleSeat!);
    }

    // Tạo một bản sao của hàng ghế với trạng thái mô phỏng
    final List<SeatModel> simulatedRow = List.from(seatRow);
    for (var selectedSeat in simulatedSelectedSeats) {
      final index = simulatedRow.indexWhere((s) => s.seatIndex == selectedSeat.seatIndex);
      if (index != -1) {
        simulatedRow[index] = selectedSeat.copyWith(soldStatus: SeatSoldStatus.SELECTING);
      }
    }

    // Kiểm tra 1: Không để ghế trống ở giữa
    // Tìm các nhóm ghế đã chọn trong hàng
    final List<List<int>> selectedGroups = [];
    List<int> currentGroup = [];

    for (int i = 0; i < simulatedRow.length; i++) {
      final seat = simulatedRow[i];

      // Nếu ghế đang được chọn hoặc đã bán
      if (seat.soldStatus != SeatSoldStatus.EMPTY && !seat.isWay && !seat.isBroken && !seat.isNotUsed) {
        currentGroup.add(i);
      } else if (currentGroup.isNotEmpty) {
        // Kết thúc nhóm hiện tại
        selectedGroups.add(List.from(currentGroup));
        currentGroup = [];
      }
    }

    // Thêm nhóm cuối cùng nếu có
    if (currentGroup.isNotEmpty) {
      selectedGroups.add(currentGroup);
    }

    // Kiểm tra ghế trống giữa các nhóm
    for (int i = 0; i < selectedGroups.length - 1; i++) {
      final group1 = selectedGroups[i];
      final group2 = selectedGroups[i + 1];

      // Nếu có đúng 1 ghế trống giữa 2 nhóm
      if (group1.last + 2 == group2.first) {
        final emptySeat = simulatedRow[group1.last + 1];
        if (emptySeat.soldStatus == SeatSoldStatus.EMPTY &&
            !emptySeat.isWay &&
            !emptySeat.isBroken &&
            !emptySeat.isNotUsed) {
          String seatName = emptySeat.seatNumber ?? '';
          UDialog().showError(title: 'Lỗi chọn ghế', text: 'Không thể để trống ghế $seatName giữa các ghế đã chọn');
          return false;
        }
      }
    }

    // Kiểm tra 2: Không để ghế trống ở bên trái hoặc bên phải
    for (final group in selectedGroups) {
      // Kiểm tra số ghế trống bên trái
      int leftEmptyCount = 0;
      int index = group.first - 1;
      while (index >= 0) {
        final seat = simulatedRow[index];
        if (seat.soldStatus == SeatSoldStatus.EMPTY && !seat.isWay && !seat.isBroken && !seat.isNotUsed) {
          leftEmptyCount++;
        } else {
          break;
        }
        index--;
      }

      // Kiểm tra số ghế trống bên phải
      int rightEmptyCount = 0;
      index = group.last + 1;
      while (index < simulatedRow.length) {
        final seat = simulatedRow[index];
        if (seat.soldStatus == SeatSoldStatus.EMPTY && !seat.isWay && !seat.isBroken && !seat.isNotUsed) {
          rightEmptyCount++;
        } else {
          break;
        }
        index++;
      }

      // Nếu có đúng 1 ghế trống ở bên trái và có ít nhất 1 ghế trống ở bên phải
      if (leftEmptyCount == 1 && rightEmptyCount > 0) {
        String seatName = simulatedRow[group.first - 1].seatNumber ?? '';
        UDialog().showError(
          title: 'Lỗi chọn ghế',
          text: 'Không thể để trống ghế $seatName ở bên trái của các ghế đã chọn',
        );
        return false;
      }

      // Nếu có đúng 1 ghế trống ở bên phải và có ít nhất 1 ghế trống ở bên trái
      if (rightEmptyCount == 1 && leftEmptyCount > 0) {
        String seatName = simulatedRow[group.last + 1].seatNumber ?? '';
        UDialog().showError(
          title: 'Lỗi chọn ghế',
          text: 'Không thể để trống ghế $seatName ở bên phải của các ghế đã chọn',
        );
        return false;
      }
    }

    return true;
  }

  void deselectSeat(SeatModel seat) {
    // Kiểm tra trạng thái kết nối từ native code thay vì biến _isConnected
    bool isConnectedNow = /*Platform.isIOS ? _nativeSignalRService.isConnected :*/ _isConnected;

    if (!isConnectedNow) {
      print(
          "Cannot deselect seat: SignalR not connected (native: ${_nativeSignalRService.isConnected}, local: $_isConnected)");
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Không thể kết nối đến máy chủ. Vui lòng thử lại sau.')),
      );
      return;
    }

    setState(() {
      _selectedSeats.removeWhere((s) => s.seatNumber == seat.seatNumber);
      sendSeat(seat, SeatSoldStatus.EMPTY.index);

      if (seat.coupleSeat != null) {
        _selectedSeats.removeWhere((s) => s.seatNumber == seat.coupleSeat!.seatNumber);
        sendSeat(seat.coupleSeat!, SeatSoldStatus.EMPTY.index);
      }
    });

    updateTotalPrice();
  }

  void updateTotalPrice() {
    print('💰 === PRICE CALCULATION START ===');
    print('💰 Selected seats count: ${_selectedSeats.length}');

    int total = 0;
    Set<String> processedCoupleSeats = {}; // Track processed couple seats to avoid double counting

    // Debug ticket types
    print('💰 Available ticket types:');
    _listSeat?.ticketTypes?.forEach((ticket) {
      print('💰   - ${ticket.seatTypeId}: ${ticket.price}đ (Normal: ${ticket.isNormal}, VIP: ${ticket.isVip}, Couple: ${ticket.isCouple})');
    });

    for (var seat in _selectedSeats) {
      print('💰 Processing seat: ${seat.seatNumber} - Type: ${seat.seatTypeEnum}');

      if (seat.seatTypeEnum == SeatType.NORMAL) {
        final normalTicket =
            _listSeat?.ticketTypes?.firstWhere((ticket) => ticket.isNormal, orElse: () => TicketType(price: 0));
        total += normalTicket?.price ?? 0;
        print('💰 Added Normal seat: ${seat.seatNumber} - Price: ${normalTicket?.price ?? 0} - Running total: $total');
      } else if (seat.seatTypeEnum == SeatType.VIP) {
        final vipTicket =
            _listSeat?.ticketTypes?.firstWhere((ticket) => ticket.isVip, orElse: () => TicketType(price: 0));
        total += vipTicket?.price ?? 0;
        print('💰 Added VIP seat: ${seat.seatNumber} - Price: ${vipTicket?.price ?? 0} - Running total: $total');
      } else if (seat.seatTypeEnum == SeatType.COUPLE) {
        // For couple seats, only count once per pair (like iOS/Android logic)
        final seatKey = seat.seatNumber ?? '';
        if (!processedCoupleSeats.contains(seatKey)) {
          final coupleTicket =
              _listSeat?.ticketTypes?.firstWhere((ticket) => ticket.isCouple, orElse: () => TicketType(price: 0));
          total += coupleTicket?.price ?? 0;
          print('💰 Added Couple seat: ${seat.seatNumber} - Price: ${coupleTicket?.price ?? 0} - Running total: $total');

          // Mark both seats in the couple as processed
          processedCoupleSeats.add(seatKey);
          if (seat.coupleSeat?.seatNumber != null) {
            processedCoupleSeats.add(seat.coupleSeat!.seatNumber!);
          }
        } else {
          print('💰 Skipped Couple seat: ${seat.seatNumber} - Already counted');
        }
      }
    }

    print('💰 FINAL Total price calculated: $total (${_selectedSeats.length} seats selected)');
    print('💰 === PRICE CALCULATION END ===');

    setState(() {
      _totalPrice = total;
    });
  }

  void proceedToPayment() {
    if (_selectedSeats.isEmpty) {
      UDialog().showError(
        title: 'Lỗi chọn ghế',
        text: 'Vui lòng chọn ít nhất một ghế trước khi thanh toán',
      );
      return;
    }

    // Check age restriction if needed - match với iOS/Android
    String? ageText;
    final restrictAge = widget.film?.FilmRestrictAgeName ?? "";
    if (restrictAge.contains("13")) {
      ageText = "13 tuổi trở lên";
    } else if (restrictAge.contains("16")) {
      ageText = "16 tuổi trở lên";
    } else if (restrictAge.contains("18")) {
      ageText = "18 tuổi trở lên";
    }

    if (ageText != null) {
      // Nội dung giống với iOS/Android
      final confirmMessage =
          "Tôi xác nhận mua vé cho người xem từ $ageText và hiểu rằng BETA sẽ không hoàn lại tiền nếu không chứng thực được độ tuổi của khán giả. Tham khảo quy định của Cục Điện Ảnh.";

      UDialog().showConfirm(
          body: RichText(
            text: TextSpan(
              style: const TextStyle(fontSize: 14, color: Colors.black87),
              children: [
                const TextSpan(text: "Tôi xác nhận mua vé cho người xem từ "),
                TextSpan(
                  text: ageText,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
                const TextSpan(
                    text:
                        " và hiểu rằng BETA sẽ không hoàn lại tiền nếu không chứng thực được độ tuổi của khán giả. Tham khảo "),
                TextSpan(
                  text: "quy định",
                  style: const TextStyle(
                    decoration: TextDecoration.underline,
                    color: Colors.blue,
                  ),
                  recognizer: TapGestureRecognizer()
                    ..onTap = () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => const PolicyScreen(policyType: PolicyType.payment),
                        ),
                      );
                    },
                ),
                const TextSpan(text: " của Cục Điện Ảnh."),
              ],
            ),
          ),
          showTitle: false,
          // Không có title như iOS/Android
          btnCancelOnPress: () {
            Navigator.pop(context);
          },
          btnOkOnPress: () {
            Navigator.pop(context);
            navigateToPayment();
          },
          btnCancelText: 'Hủy',
          btnOkText: 'Đồng ý');
    } else {
      navigateToPayment();
    }
  }

  void navigateToPayment() {
   /* Platform.isIOS ? navigateToIOSStylePayment() :*/ navigateToAndroidStylePayment();
  }

  void navigateToIOSStylePayment() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => IOSStyleWebViewPayment(
          film: widget.film,
          listSeat: _listSeat,
          selectedSeats: _selectedSeats,
          totalPrice: _totalPrice,
          cinemaId: widget.cinemaId,
          combo: generateComboString(),
          cinemaName: widget.cinemaName,
          showTime: widget.showTime,
          showId: widget.showTime?.showId,
          remainingTime: _remainingTime,
          timeStartBooking: DateTime.fromMillisecondsSinceEpoch((timeStartBooking * 1000).toInt()),
          onPaymentSuccess: (result) {
            print('✅ iOS Style Payment Success: $result');
            Navigator.of(context).popUntil((route) => route.isFirst);
          },
          onPaymentFailed: (error) {
            print('❌ iOS Style Payment Failed: $error');
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Payment failed: $error')),
            );
          },
          onPaymentWaiting: () {
            print('⏳ iOS Style Payment Waiting');
          },
          onPaymentMethodSelected: (method) {
            print('💳 iOS Style Payment Method Selected: $method');
          },
        ),
      ),
    );
  }

  String generateComboString() {
    final comboString = SeatComboHelper.generateComboString(_selectedSeats);

    // Debug thông tin ghế
    if (comboString.isNotEmpty) {
      SeatComboHelper.debugSeatInfo(_selectedSeats);
    }

    return comboString;
  }

  void navigateToAndroidStylePayment() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => AndroidStyleWebViewPayment(
          film: widget.film,
          listSeat: _listSeat,
          selectedSeats: _selectedSeats,
          totalPrice: _totalPrice,
          cinemaId: widget.cinemaId,
          combo: generateComboString(),
          cinemaName: widget.cinemaName,
          showTime: widget.showTime,
          showId: widget.showTime?.showId,
          remainingTime: _remainingTime,
          // ✅ Pass timer like iOS Style
          timeStartBooking: DateTime.fromMillisecondsSinceEpoch((timeStartBooking * 1000).toInt()),
          // ✅ Pass start time like iOS Style
          onPaymentSuccess: (result) {
            print('✅ Android Style Payment Success: $result');
            // Handle payment success
            Navigator.of(context).popUntil((route) => route.isFirst);
          },
          onPaymentFailed: (error) {
            print('❌ Android Style Payment Failed: $error');
            // Handle payment failure
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(content: Text('Payment failed: $error')),
            );
          },
          onPaymentWaiting: () {
            print('⏳ Android Style Payment Waiting');
            // Handle payment waiting
          },
          onPaymentMethodSelected: (method) {
            print('💳 Android Style Payment Method Selected: $method');
            // Handle payment method selection
          },
        ),
      ),
    );
  }

  String formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '$minutes:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  @override
  Widget build(BuildContext context) {
    final formatter = NumberFormat("#,###", "vi_VN");

    return Scaffold(
      appBar: appBar(
        title: 'Menu.BookByMovie'.tr(),
        titleColor: Colors.white,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _error != null
              ? Center(child: Text(_error!))
              : SafeArea(
                  child: Column(
                    children: [
                      // Film info header with background image - giống iOS
                      Container(
                        height: 120, // Tăng chiều cao như iOS
                        width: double.infinity,
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                              Colors.black.withAlpha(179), // 0.7 * 255 = 179
                              Colors.black.withAlpha(230), // 0.9 * 255 = 230
                            ],
                          ),
                        ),
                        child: Stack(
                          children: [
                            // Background image with opacity
                            if (widget.film?.MainPosterUrl != null)
                              Positioned.fill(
                                child: Opacity(
                                  opacity: 0.3, // Giữ nguyên độ mờ
                                  child: Image.network(
                                    '${ApiService.baseUrlImage}/${widget.film!.MainPosterUrl}',
                                    fit: BoxFit.cover,
                                    errorBuilder: (_, __, ___) => Container(
                                      color: Colors.grey.shade800,
                                    ),
                                  ),
                                ),
                              ),

                            // Film details centered
                            Positioned.fill(
                              child: Container(
                                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                                child: Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    // Film name - giống iOS với font lớn hơn
                                    Text(
                                      (isEnglish ? widget.film?.Name_F : widget.film?.Name) ?? '--',
                                      textAlign: TextAlign.center,
                                      style: const TextStyle(
                                        fontSize: 22, // Tăng kích thước font
                                        fontWeight: FontWeight.bold,
                                        color: Colors.white,
                                      ),
                                    ),
                                    const SizedBox(height: 10), // Tăng khoảng cách

                                    // Film details - giống iOS với font nhỏ hơn
                                    Text(
                                      '${(isEnglish ? _listSeat?.filmFormatNameF : _listSeat?.formatName) ?? ''} | ${Convert.date(_listSeat?.gioChieu ?? '')} ${Convert.hours(_listSeat?.gioChieu ?? '')} | ${(isEnglish ? 'Room' : 'Phòng')}: ${_listSeat?.phongChieu ?? ''}',
                                      textAlign: TextAlign.center,
                                      style: const TextStyle(
                                        fontSize: CFontSize.base,
                                        color: Colors.white,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Combined seat pricing information and legend - giống iOS
                      Container(
                        width: double.infinity,
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
                        decoration: BoxDecoration(
                          color: Colors.grey.shade100, // Màu nền sáng hơn như iOS
                          border: Border(
                            bottom: BorderSide(color: Colors.grey.shade300, width: 1), // Đường viền đậm hơn
                          ),
                        ),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start, // Căn đầu các phần tử
                          children: [
                            // Seat legend - giống iOS
                            Expanded(
                              flex: 4, // Tăng flex để có nhiều không gian hơn
                              child: Column(
                                spacing: CSpace.base,
                                crossAxisAlignment: CrossAxisAlignment.start, // Căn phải
                                children: [
                                  buildLegendItem('Ghế trống', 'assets/icon/cinema/ic_empty_vip_seat.svg'),
                                  buildLegendItem('Ghế đang được giữ', 'assets/icon/cinema/ic_process_vip_seat.svg'),
                                  buildLegendItem('Ghế đang chọn', 'assets/icon/cinema/ic_select_vip_seat.svg'),
                                  buildLegendItem('Ghế đã bán', 'assets/icon/cinema/ic_sold_vip_seat.svg'),
                                  buildLegendItem('Ghế đã đặt trước', 'assets/icon/cinema/ic_set_vip_seat.svg'),
                                ],
                              ),
                            ),
                            // Seat pricing information - giống iOS
                            Expanded(
                              flex: 3, // Tăng flex để có nhiều không gian hơn
                              child: _listSeat?.ticketTypes != null && _listSeat!.ticketTypes!.isNotEmpty
                                  ? Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                                      spacing: CSpace.base,
                                      children: [
                                        ...(buildTicketTypePriceLabels()),
                                      ],
                                    )
                                  : Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        buildPriceLabel(
                                          'Ghế thường',
                                          '0đ',
                                          svgAsset: 'assets/icon/cinema/ic_empty_normal_seat.svg',
                                        ),
                                        buildPriceLabel(
                                          'Ghế VIP',
                                          '0đ',
                                          svgAsset: 'assets/icon/cinema/ic_empty_vip_seat.svg',
                                        ),
                                        buildPriceLabel(
                                          'Ghế đôi',
                                          '0đ',
                                          svgAsset: 'assets/icon/cinema/ic_empty_couple_seat.svg',
                                        ),
                                      ],
                                    ),
                            ),
                          ],
                        ),
                      ),

                      // Screen indicator - giống iOS
                      Container(
                        width: double.infinity,
                        margin: const EdgeInsets.only(top: 4, bottom: 4), // Tăng margin như iOS
                        child: Column(
                          children: [
                            Container(
                              width: double.infinity,
                              height: 30,
                              decoration: BoxDecoration(
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withAlpha(77), // 0.3 * 255 = 77
                                    blurRadius: 15, // Tăng độ mờ
                                    offset: const Offset(0, 5),
                                    spreadRadius: 1, // Tăng độ lan
                                  ),
                                ],
                              ),
                              child: SvgPicture.asset(
                                'assets/icon/cinema/ic_screen.svg', fit: BoxFit.fitWidth,
                                // Tăng chiều cao
                              ),
                            ),
                            const Text(
                              'MÀN HÌNH CHIẾU',
                              style: TextStyle(
                                fontSize: CFontSize.xs - 1, // Tăng kích thước font
                                color: Colors.grey,
                                fontWeight: FontWeight.w600, // Đậm hơn
                                letterSpacing: 1.0, // Thêm khoảng cách chữ
                              ),
                            ),
                          ],
                        ),
                      ),

                      // Enhanced seat grid - Android performance + iOS UX
                      Expanded(
                        flex: 5, // Increased flex to make it much taller
                        child: SeatGrid(
                          seatPositions: _listSeat?.screen?.seatPosition ?? [],
                          selectedSeats: _selectedSeats,
                          onSeatSelected: selectSeat,
                          onSeatDeselected: deselectSeat,
                        ),
                      ),

                      // Timer and booking button - giống iOS
                      Container(
                        padding: const EdgeInsets.symmetric(horizontal: 0, vertical: 8), // Tăng padding
                        decoration: BoxDecoration(
                          color: Colors.white,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withAlpha(26), // 0.1 * 255 = 26
                              blurRadius: 6, // Tăng độ mờ
                              offset: const Offset(0, -3), // Điều chỉnh offset
                              spreadRadius: 1, // Tăng độ lan
                            ),
                          ],
                        ),
                        child: Column(
                          children: [
                            // Timer - giống iOS
                            Container(
                              width: double.infinity,
                              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4), // Tăng padding

                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                crossAxisAlignment: CrossAxisAlignment.center,
                                children: [
                                  const Text(
                                    'Thời gian còn lại: ',
                                    style: TextStyle(
                                        fontSize: 24,
                                        fontWeight: FontWeight.w700,
                                        color: Colors.black,
                                        fontFamily: 'Oswald'),
                                  ),
                                  Container(
                                    padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 0), // Tăng padding
                                    // decoration: BoxDecoration(
                                    //   color: _remainingTime < 60 ? Colors.red : Colors.green,
                                    //   borderRadius: BorderRadius.circular(6), // Tăng bo góc
                                    // ),
                                    child: Text(
                                      formatTime(_remainingTime),
                                      style: const TextStyle(
                                        color: Colors.black,
                                        fontFamily: 'Oswald',
                                        fontWeight: FontWeight.bold,
                                        fontSize: 26, // Tăng kích thước font
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),

                            // Layout liền mạch: Info trên nền trắng + Button xanh
                            if (_selectedSeats.isNotEmpty)
                              Padding(
                                padding: const EdgeInsets.only(top: 8),
                                child: Row(
                                  children: [
                                    // Phần thông tin trên nền btnBookingdetailWhite.svg
                                    Expanded(
                                      flex: 4,
                                      child: SizedBox(
                                        height: 90,
                                        child: Padding(
                                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                                          child: Row(
                                            children: [
                                              // Cột 1: Ghế đã chọn
                                              Expanded(
                                                flex: 3,
                                                child: Column(
                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                                                  children: [
                                                    const Text(
                                                      'Ghế đã chọn',
                                                      style: TextStyle(
                                                        fontSize: CFontSize.xl,
                                                        color: Colors.black,
                                                        fontFamily: 'Oswald',
                                                        fontWeight: FontWeight.w500,
                                                      ),
                                                    ),
                                                    Text(
                                                      _selectedSeats.map((seat) => seat.seatNumber).join(', '),
                                                      style: const TextStyle(
                                                        fontSize: CFontSize.xl,
                                                        color: Colors.black,
                                                        fontFamily: 'Oswald',
                                                        fontWeight: FontWeight.bold,
                                                      ),
                                                      maxLines: 1,
                                                      overflow: TextOverflow.ellipsis,
                                                    ),
                                                  ],
                                                ),
                                              ),

                                              // // Divider
                                              Container(
                                                width: 3,
                                                height: 50,
                                                color: Colors.grey.shade300,
                                                margin: const EdgeInsets.symmetric(horizontal: 8),
                                              ),

                                              // Cột 2: Tổng tiền
                                              Expanded(
                                                flex: 2,
                                                child: Column(
                                                  crossAxisAlignment: CrossAxisAlignment.start,
                                                  mainAxisAlignment: MainAxisAlignment.spaceAround,
                                                  children: [
                                                    const Text(
                                                      'Tổng tiền',
                                                      style: TextStyle(
                                                        fontSize: CFontSize.xl,
                                                        color: Colors.black,
                                                        fontFamily: 'Oswald',
                                                        fontWeight: FontWeight.w500,
                                                      ),
                                                    ),
                                                    Text(
                                                      '${formatter.format(_totalPrice)} đ',
                                                      style: const TextStyle(
                                                        fontSize: CFontSize.xl,
                                                        fontFamily: 'Oswald',
                                                        color: Colors.indigo,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),

                                    // Nút Tiếp tục nối liền
                                    Expanded(
                                      flex: 2,
                                      child: InkWell(
                                        onTap: proceedToPayment,
                                        child: SizedBox(
                                          height: 90,
                                          child: Stack(
                                            alignment: Alignment.center,
                                            children: [
                                              SvgPicture.asset(
                                                'assets/icon/cinema/btnBookingdetail.svg',
                                                width: double.infinity,
                                                height: 90,
                                                fit: BoxFit.fill,
                                              ),
                                              const Text(
                                                'Tiếp tục',
                                                style: TextStyle(
                                                  color: Colors.white,
                                                  fontSize: CFontSize.xl,
                                                  fontFamily: 'Oswald',
                                                  fontWeight: FontWeight.bold,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
    );
  }

  Widget buildLegendItem(String label, String svgAsset) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          SvgPicture.asset(svgAsset, width: 20, height: 20),
          const SizedBox(width: 3),
          Text(
            label,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Colors.grey.shade700,
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> buildTicketTypePriceLabels() {
    final formatter = NumberFormat('#,###', 'vi_VN');
    final List<Widget> items = [];

    // Group ticket types by seat type
    final Map<String, TicketType> seatTypeToTicket = {};

    for (var ticket in _listSeat!.ticketTypes!) {
      if (ticket.seatTypeId != null) {
        seatTypeToTicket[ticket.seatTypeId!] = ticket;
      }
    }

    // Normal seat
    if (seatTypeToTicket.containsKey('c0f1e9a8-c9f5-4b0d-8b10-f3108996e60b')) {
      final ticket = seatTypeToTicket['c0f1e9a8-c9f5-4b0d-8b10-f3108996e60b']!;
      final price = ticket.price ?? 0;
      items.add(buildPriceLabel(
        'Ghế thường',
        '${formatter.format(price)} đ',
        svgAsset: 'assets/icon/cinema/ic_empty_normal_seat.svg',
      ));
    }

    // VIP seat
    if (seatTypeToTicket.containsKey('9f2dda7f-d09e-4d58-a504-5a6311345aae')) {
      final ticket = seatTypeToTicket['9f2dda7f-d09e-4d58-a504-5a6311345aae']!;
      final price = ticket.price ?? 0;
      items.add(buildPriceLabel(
        'Ghế VIP',
        '${formatter.format(price)} đ',
        svgAsset: 'assets/icon/cinema/ic_empty_vip_seat.svg',
      ));
    }

    // Couple seat
    if (seatTypeToTicket.containsKey('9beee28c-8cae-41d0-bd01-b0b22108432c')) {
      final ticket = seatTypeToTicket['9beee28c-8cae-41d0-bd01-b0b22108432c']!;
      final price = (ticket.price ?? 0) * 2;
      items.add(buildPriceLabel(
        'Ghế đôi',
        '${formatter.format(price)} đ',
        svgAsset: 'assets/icon/cinema/ic_empty_couple_seat.svg',
      ));
    }

    // If no items were added, use default items
    if (items.isEmpty) {
      items.add(buildPriceLabel(
        'Ghế thường',
        '80,000đ',
        svgAsset: 'assets/icon/cinema/ic_empty_normal_seat.svg',
      ));
      items.add(buildPriceLabel(
        'Ghế VIP',
        '100,000đ',
        svgAsset: 'assets/icon/cinema/ic_empty_vip_seat.svg',
      ));
      items.add(buildPriceLabel(
        'Ghế đôi',
        '160,000đ',
        svgAsset: 'assets/icon/cinema/ic_empty_couple_seat.svg',
      ));
    }

    return items;
  }

  Widget buildPriceLabel(String seatType, String price, {String? svgAsset}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceAround,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          if (svgAsset != null)
            Padding(
              padding: const EdgeInsets.only(right: 12),
              child: SvgPicture.asset(
                svgAsset,
                width: 20,
                height: 20,
              ),
            ),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(seatType, style: TextStyle(fontSize: 16, color: Colors.grey.shade700)),
              Text(price, style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold, color: Colors.black)),
            ],
          ),
        ],
      ),
    );
  }
}

class TransactionHistoryModel {
  String? InvoiceId;
  String? FilmName;
  String? CinemaName;
  String? CinemaId;
  String? ShowId;
  String? DateShow;
  String? ShowTime;
  String? DateEntered;
  int? QuantityPoint;
  String? DateExpiredPoint;
  String? AccountId;
  String? SalesChannelId;
  String? SalesChannelCode;
  String? CardId;
  String? CardNumber;
  int? TotalPayment;
  String? AirpayLandingUrl;

  TransactionHistoryModel({
    this.InvoiceId,
    this.FilmName,
    this.CinemaName,
    this.CinemaId,
    this.ShowId,
    this.DateShow,
    this.ShowTime,
    this.DateEntered,
    this.QuantityPoint,
    this.DateExpiredPoint,
    this.AccountId,
    this.SalesChannelId,
    this.SalesChannelCode,
    this.CardId,
    this.CardNumber,
    this.TotalPayment,
    this.AirpayLandingUrl,
  });

  factory TransactionHistoryModel.fromJson(Map<String, dynamic> json) {
    return TransactionHistoryModel(
      InvoiceId: json['Invoice_Id'],
      FilmName: json['FilmName'],
      CinemaName: json['CinemaName'],
      CinemaId: json['CinemaId'],
      ShowId: json['ShowId'],
      DateShow: json['DateShow'],
      ShowTime: json['ShowTime'],
      DateEntered: json['DateEntered'],
      QuantityPoint: json['QuantityPoint'],
      DateExpiredPoint: json['DateExpiredPoint'],
      AccountId: json['AccountId'],
      SalesChannelId: json['SalesChannelId'],
      SalesChannelCode: json['SalesChannelCode'],
      CardId: json['CardId'],
      CardNumber: json['CardNumber'],
      TotalPayment: json['TotalPayment'],
      AirpayLandingUrl: json['AirpayLandingUrl'],
    );
  }
}

class TransactionHistoryDetailModel {
  String? InvoiceId;
  String? FilmName;
  String? CinemaName;
  String? CinemaId;
  String? ShowId;
  String? DateShow;
  String? ShowTime;
  String? DateEntered;
  double? QuantityPoint;
  String? DateExpiredPoint;
  String? AccountId;
  String? SalesChannelId;
  String? SalesChannelCode;
  String? CardId;
  String? CardNumber;
  double? TotalPayment;
  String? AirpayLandingUrl;
  String? Screen;
  int? TotalSeats;
  List<TicketTypeCopy>? TicketTypes;
  List<Payment>? PaymentModel;
  List<ComboModel>? ListCombo;
  String? QrCode;

  TransactionHistoryDetailModel({
    this.InvoiceId,
    this.FilmName,
    this.CinemaName,
    this.CinemaId,
    this.ShowId,
    this.DateShow,
    this.ShowTime,
    this.DateEntered,
    this.QuantityPoint,
    this.DateExpiredPoint,
    this.AccountId,
    this.SalesChannelId,
    this.SalesChannelCode,
    this.CardId,
    this.CardNumber,
    this.TotalPayment,
    this.AirpayLandingUrl,
    this.Screen,
    this.TotalSeats,
    this.TicketTypes,
    this.PaymentModel,
    this.ListCombo,
    this.QrCode,
  });

  factory TransactionHistoryDetailModel.fromJson(Map<String, dynamic> json) {
    return TransactionHistoryDetailModel(
      InvoiceId: json['Invoice_Id'],
      FilmName: json['FilmName'],
      CinemaName: json['CinemaName'],
      CinemaId: json['CinemaId'],
      ShowId: json['ShowId'],
      DateShow: json['DateShow'],
      ShowTime: json['ShowTime'],
      DateEntered: json['DateEntered'],
      QuantityPoint: json['QuantityPoint'],
      DateExpiredPoint: json['DateExpiredPoint'],
      AccountId: json['AccountId'],
      SalesChannelId: json['SalesChannelId'],
      SalesChannelCode: json['SalesChannelCode'],
      CardId: json['CardId'],
      CardNumber: json['CardNumber'],
      TotalPayment: json['TotalPayment'],
      AirpayLandingUrl: json['AirpayLandingUrl'],
      Screen: json['Screen'],
      TotalSeats: json['TotalSeats'],
      TicketTypes: json['TicketTypes'] != null
          ? List<TicketTypeCopy>.from(
              json['TicketTypes'].map((x) => TicketTypeCopy.fromJson(x)))
          : null,
      PaymentModel: json['PaymentModel'] != null
          ? List<Payment>.from(
              json['PaymentModel'].map((x) => Payment.fromJson(x)))
          : null,
      ListCombo: json['ListCombo'] != null
          ? List<ComboModel>.from(
              json['ListCombo'].map((x) => ComboModel.fromJson(x)))
          : null,
      QrCode: json['QrCode'],
    );
  }
}

class TicketTypeCopy {
  String? TicketTypeId;
  String? Name;
  List<String>? ListSeatName;

  TicketTypeCopy({
    this.TicketTypeId,
    this.Name,
    this.ListSeatName,
  });

  factory TicketTypeCopy.fromJson(Map<String, dynamic> json) {
    return TicketTypeCopy(
      TicketTypeId: json['TicketTypeId'],
      Name: json['Name'],
      ListSeatName: json['ListSeatName'] != null
          ? List<String>.from(json['ListSeatName'])
          : null,
    );
  }
}

class Payment {
  String? PaymentTypeName;
  String? PaymentTypeCode;
  int? Values;

  Payment({
    this.PaymentTypeName,
    this.PaymentTypeCode,
    this.Values,
  });

  factory Payment.fromJson(Map<String, dynamic> json) {
    return Payment(
      PaymentTypeName: json['PaymentTypeName'],
      PaymentTypeCode: json['PaymentTypeCode'],
      Values: json['Values'],
    );
  }
}

class ComboModel {
  String? ComboId;
  String? Name;
  int? Quantity;
  int? Price;

  ComboModel({
    this.ComboId,
    this.Name,
    this.Quantity,
    this.Price,
  });

  factory ComboModel.fromJson(Map<String, dynamic> json) {
    return ComboModel(
      ComboId: json['ComboId'],
      Name: json['Name'],
      Quantity: json['Quantity'],
      Price: json['Price'],
    );
  }
}

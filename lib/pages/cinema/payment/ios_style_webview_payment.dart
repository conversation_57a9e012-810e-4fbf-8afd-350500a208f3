import 'package:flutter/material.dart';
import 'package:flutter_app/utils/index.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:flutter_app/core/index.dart';
import 'package:flutter_app/pages/cinema/model/list_seat_model.dart';
import 'package:flutter_app/pages/cinema/model/seat_model.dart';
import 'package:flutter_app/pages/cinema/model/create_booking_model.dart';
import 'package:flutter_app/pages/cinema/model/seat_booking_model.dart';
import 'package:url_launcher/url_launcher.dart';
import 'dart:async';

import '../../../cubit/auth.dart';
import '../../Movie_schedule/model/Film_model.dart';
import '../../voucher/api/api_test.dart';
import '../model/cinema_model.dart';
import '../model/ticket_type.dart';

class IOSStyleWebViewPayment extends StatefulWidget {
  final FilmModel? film;
  final String? combo;
  final ListSeatModel? listSeat;
  final String? cinemaId;
  final String? cinemaName;
  final Function(String?) onPaymentSuccess;
  final Function(String?) onPaymentFailed;
  final Function() onPaymentWaiting;
  final Function(String) onPaymentMethodSelected;
  final int? totalPrice;
  final List<SeatModel>? selectedSeats;
  final ShowModel? showTime;
  final String? showId;
  final int? remainingTime;
  final DateTime? timeStartBooking;

  const IOSStyleWebViewPayment({
    super.key,
    this.film,
    this.combo,
    this.listSeat,
    this.cinemaId,
    this.cinemaName,
    required this.onPaymentSuccess,
    required this.onPaymentFailed,
    required this.onPaymentWaiting,
    required this.onPaymentMethodSelected,
    this.totalPrice,
    this.selectedSeats,
    this.showTime,
    this.showId,
    this.remainingTime,
    this.timeStartBooking,
  });

  @override
  State<IOSStyleWebViewPayment> createState() => _IOSStyleWebViewPaymentState();
}

class _IOSStyleWebViewPaymentState extends State<IOSStyleWebViewPayment> {
  InAppWebViewController? webViewController;
  String? _htmlContent;
  bool _isLoading = true;
  String? _currentUrl;
  bool _webViewCreated = false; // Flag to prevent recreation
  Timer? _countdownTimer;
  int _remainingSeconds = 0;
  String? _paymentMethod;

  // Payment tracking variables exactly like iOS
  String? _airPayOrderId;

  // For MoMo payment method exactly like iOS
  String? _momoOrderId;
  String? _resultCode;
  String? _requestId;
  String? _transId;
  String? _message;
  String? _responseTime;
  String? _payType;
  String? _extraData;
  String? _partnerCode;

  // For ZaloPay exactly like iOS
  String? _zaloPayAppId;
  String? _zaloPayTransId;
  String? _zaloPayPmcId;
  String? _zaloPayBankCode;
  String? _zaloPayAmount;
  String? _zaloPayDAmount;
  String? _zaloPayStatus;
  String? _zaloPayCheckSum;

  @override
  void initState() {
    super.initState();
    _remainingSeconds = widget.remainingTime ?? 600; // 10 minutes default
    _loadBookingPayment();
    _startCountdownTimer();
    // Note: Payment callbacks are handled via deep link service in main app
    // No need for NotificationCenter observers here since we use deep links
  }

  @override
  void dispose() {
    _stopCountdownTimer();
    webViewController = null; // Clear webview controller to prevent recreation errors
    _webViewCreated = false; // Reset flag
    super.dispose();
  }

  /// Start countdown timer exactly like iOS PaymentViewController
  void _startCountdownTimer() {
    _stopCountdownTimer();
    _countdownTimer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (!mounted) {
        timer.cancel();
        return;
      }

      setState(() {
        _remainingSeconds--;
      });

      if (_remainingSeconds <= 0) {
        timer.cancel();
        _handleTimeout();
      }
    });
  }

  void _stopCountdownTimer() {
    _countdownTimer?.cancel();
    _countdownTimer = null;
  }

  void _handleTimeout() {
    if (mounted) {
      widget.onPaymentFailed('Timeout: Session expired');
      Navigator.of(context).pop();
    }
  }

  /// Load booking payment exactly like iOS getPaymentWeb()
  Future<void> _loadBookingPayment() async {
    try {
      setState(() {
        _isLoading = true;
      });

      // Create booking model exactly like iOS
      final bookingModel = _createBookingModel();

      // Call API /booking exactly like iOS FilmProvider.rx.request(.booking(bookingModel))
      final response = await RepositoryProvider.of<Api>(context).film.booking(body: bookingModel!.toJson());
      final data = response?.data;
      setState(() {
        _htmlContent = data;
        _isLoading = false;
      });

      print('✅ iOS Style: Booking HTML loaded successfully');

    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      print('❌ iOS Style: Failed to load booking payment: $e');
      widget.onPaymentFailed('Failed to load payment: $e');
    }
  }

  /// Create booking model exactly like iOS CreateBookingModel
  CreateBookingModel? _createBookingModel() {
    final ticketTypes = widget.listSeat?.ticketTypes;
    if (widget.showTime?.showId == null || widget.selectedSeats!.isEmpty || ticketTypes == null) {
      return null ;
    }

    final normalTicket = ticketTypes.firstWhere((ticket) => ticket.isNormal, orElse: () => TicketType(price: 0));

    final vipTicket = ticketTypes.firstWhere((ticket) => ticket.isVip, orElse: () => TicketType(price: 0));

    final coupleTicket = ticketTypes.firstWhere((ticket) => ticket.isCouple, orElse: () => TicketType(price: 0));

    // Create seats list - match iOS logic exactly (no double counting for couple seats)
    final seats = <SeatBookingModel>[];
    final processedCoupleSeats = <String>{};

    for (var seat in widget.selectedSeats ?? []) {
      TicketType ticketType;
      String seatType = "STARDAR"; // Chú ý: iOS sử dụng "STARDAR" thay vì "STANDARD"

      // Xác định loại ghế và loại vé tương ứng
      if (seat.seatType?.isVip == true || seat.seatTypeEnum == SeatType.VIP) {
        ticketType = vipTicket;
        seatType = "VIP";
      } else if (seat.seatType?.isCouple == true || seat.seatTypeEnum == SeatType.COUPLE) {
        ticketType = coupleTicket;
        seatType = "DOUBLE";

        // For couple seats, only add once per pair (like iOS logic)
        final seatKey = seat.seatNumber ?? '';
        if (processedCoupleSeats.contains(seatKey)) {
          continue; // Skip if already processed
        }
        processedCoupleSeats.add(seatKey);
        if (seat.coupleSeat?.seatNumber != null) {
          processedCoupleSeats.add(seat.coupleSeat!.seatNumber!);
        }
      } else {
        ticketType = normalTicket;
      }

      seats.add(SeatBookingModel(
        seatIndex: seat.seatIndex ?? 0,
        seatName: seat.seatNumber ?? '',
        seatType: seatType,
        ticketTypeId : ticketType.ticketTypeId ?? '',
        price: ticketType.price ?? 0,
      ));

      print('🎫 Added seat to booking: ${seat.seatNumber} - Type: $seatType - Price: ${ticketType.price}');
    }

    // Calculate countdown exactly like iOS
    final expiredTime = widget.timeStartBooking?.add(const Duration(minutes: 10)) ??
                      DateTime.now().add(const Duration(minutes: 10));
    final countDown = '/Date(${expiredTime.millisecondsSinceEpoch})/';

    return CreateBookingModel(
      showId: widget.showId ?? '',
      seats: seats,
      countDown: countDown,
    );
  }

  /// Update booking info exactly like iOS updateBookingInfo()
  void _updateBookingInfo() {
    if (webViewController == null) return;

    try {
      // Get film info exactly like iOS
      final filmInfo = widget.film?.getFinalOptions() ?? '';
      final dateStr = widget.listSeat?.ngayChieu?.split('T')[0] ?? '';
      final timeStr = widget.listSeat?.gioChieu?.split('T')[1].substring(0, 5) ?? '';

      // Build JavaScript exactly like iOS (method string concatenation style)
      final jsGetBookingInfo = 'var bookingIf = {};' +
          ' bookingIf.FilmName = "${_escapeString(widget.film?.getName() ?? "")}";' +
          ' bookingIf.FilmInfo = "${_escapeString(filmInfo)}";' +
          ' bookingIf.CinemaName = "${_escapeString(widget.listSeat?.tenRap ?? "")}";' +
          ' bookingIf.DateShow = "$dateStr";' +
          ' bookingIf.ShowTime = "$timeStr";' +
          ' bookingIf.Combo = "${_escapeString(widget.combo ?? "")}";' +
          ' bookingIf.TotalMoney = "${_formatCurrency(widget.totalPrice ?? 0)}";' +
          ' bookingIf.Screen = "${_escapeString(widget.listSeat?.phongChieu ?? "")}";' +
          ' bookingIf.FilmPoster = "${ApiService.baseUrlImage}${widget.film?.MainPosterUrl ?? ""}";' +
          ' bookingIf.FilmFormatCode = "${_escapeString(widget.listSeat?.filmFormatCode ?? "")}";' +
          'getBookingInfo(bookingIf);';

      webViewController!.evaluateJavascript(source: jsGetBookingInfo);
      print('✅ iOS Style: getBookingInfo executed');

      // Set customer info exactly like iOS
      // TODO: Get user info from AuthC
      final user = context.read<AuthC>().state.user;

      // Validate customer data before building JavaScript
      final customerId = user?.accountId ?? "";
      final customerCard = user?.cardNumber ?? "";
      final jsCustomerInfo = 'var cusI = {};' +
          ' cusI.customerId = "$customerId";' + // Get from user context
          ' cusI.customerCard = "$customerCard";' + // Get from user context
          'getCustomerInfo(cusI);';

      webViewController!.evaluateJavascript(source: jsCustomerInfo);
      print('✅ iOS Style: getCustomerInfo $jsCustomerInfo');

    } catch (e) {
      print('❌ iOS Style: Error updating booking info: $e');
    }
  }

  String _escapeString(String input) {
    return input.replaceAll('"', '\\"').replaceAll("'", "\\'").replaceAll('\n', '\\n');
  }

  String _formatCurrency(int amount) {
    // Format currency exactly like iOS toCurrency()
    return amount.toString().replaceAllMapped(
      RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))'),
      (Match m) => '${m[1]},',
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: appBar(
        title: 'Thanh toán',
        titleColor: Colors.white,
        leading: IconButton(
          icon: const Icon(Icons.close, color: Colors.white),
          onPressed: () {
            _stopCountdownTimer();
            Navigator.pop(context);
          },
        ),
      ),
      body: _isLoading
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Đang tải trang thanh toán...'),
                ],
              ),
            )
          : _buildWebView(),
    );
  }

  String _formatTime(int seconds) {
    final minutes = seconds ~/ 60;
    final remainingSeconds = seconds % 60;
    return '${minutes.toString().padLeft(2, '0')}:${remainingSeconds.toString().padLeft(2, '0')}';
  }

  Widget _buildWebView() {
    if (_htmlContent == null) {
      return const Center(child: Text('No content to display'));
    }

    return InAppWebView(
      key: const ValueKey('ios_style_webview_payment'), // Add unique key to prevent recreation
      // Load HTML content with base URL exactly like iOS
      initialData: InAppWebViewInitialData(
        data: _htmlContent!,
        baseUrl: WebUri('${ApiService.baseUrl}/Booking'),
      ),
      initialSettings: InAppWebViewSettings(
        // iOS-like WebView settings
        javaScriptEnabled: true,
        domStorageEnabled: true,
        useWideViewPort: true,
        loadWithOverviewMode: true,
        supportZoom: true,
        builtInZoomControls: false,
        displayZoomControls: false,

        // Security and compatibility
        mixedContentMode: MixedContentMode.MIXED_CONTENT_ALWAYS_ALLOW,
        allowsInlineMediaPlayback: true,
        mediaPlaybackRequiresUserGesture: false,

        // Performance
        cacheEnabled: true,
        clearCache: false,
      ),
      onWebViewCreated: (controller) async {
        print('🔗 iOS Style WebView created');
        if (_webViewCreated) {
          print('⚠️ iOS Style WebView already created, skipping setup');
          return;
        }
        _webViewCreated = true;
        webViewController = controller;

        // Add JavaScript interface "scriptHandler" exactly like iOS WKScriptMessageHandler
        controller.addJavaScriptHandler(
          handlerName: "scriptHandler",
          callback: (args) {
            if (!mounted) return;
            final message = args.isNotEmpty ? args[0].toString() : '';
            print('📱 iOS Style WebView received message: $message');
            _handleJavaScriptMessage(message);
          }
        );

        // Add JavaScript interface "logger" for console logging
        controller.addJavaScriptHandler(
          handlerName: "logger",
          callback: (args) {
            if (!mounted) return;
            final logMessage = args.isNotEmpty ? args[0].toString() : '';
            print('📝 iOS Style WebView Logger: $logMessage');
          }
        );

      },
      onConsoleMessage: (controller, consoleMessage) {
        // Capture all console.log messages from JavaScript
        print('🖥️ iOS Style WebView Console [${consoleMessage.messageLevel}]: ${consoleMessage.message}');
      },
      onLoadStart: (controller, url) {
        print('🔄 iOS Style WebView started loading: $url');
        _updateCurrentUrl(url.toString());
      },
      onLoadStop: (controller, url) async {
        print('✅ iOS Style WebView finished loading: $url');

        // Check screen type and update booking info exactly like iOS didFinish
        try {
          final result = await controller.evaluateJavascript(source: "screenType;");
          final screenType = result?.toString().replaceAll('"', '');

          if (screenType == "payment") {
            print('📄 iOS Style: Payment screen detected, updating booking info');
            _updateBookingInfo();

            // Debug: Call getTicketTypeInBooking() and print results
            _debugGetTicketTypeInBooking();

            // Debug: Print parsed JSON values from HTML script
            _debugParsedJsonValues();
          }
        } catch (e) {
          print('❌ iOS Style: Error checking screen type: $e');
        }
      },
      shouldOverrideUrlLoading: (controller, navigationAction) async {
        final url = navigationAction.request.url.toString();
        print('🔗 iOS Style: Navigation to: $url');

        // Handle external payment apps exactly like iOS decidePolicyFor
        return await _handleUrlNavigation(url);
      },
    );
  }

  void _updateCurrentUrl(String url) {
    setState(() {
      _currentUrl = url;
    });
  }

  /// Handle URL navigation exactly like iOS decidePolicyFor navigationAction
  Future<NavigationActionPolicy> _handleUrlNavigation(String url) async {
    // Handle AirPay exactly like iOS
    if (url.contains('airpay.vn') && await canLaunchUrl(Uri.parse(url))) {
      _paymentMethod = 'airpay';
      _trackPayment('confirm');
      _launchExternalUrl(url);
      // Extract orderId exactly like iOS
      final uri = Uri.parse(url);
      _airPayOrderId = uri.queryParameters['order_id'];
      return NavigationActionPolicy.CANCEL;
    }

    // Handle Momo exactly like iOS - FIXED URL pattern
    if (url.contains('payment.momo') && await canLaunchUrl(Uri.parse(url))) {
      _paymentMethod = 'momo';
      _trackPayment('confirm');
      _launchExternalUrl(url);
      return NavigationActionPolicy.CANCEL;
    }

    // Handle ZaloPay exactly like iOS
    if (url.contains('gateway.zalopay.vn') && await canLaunchUrl(Uri.parse(url))) {
      _paymentMethod = 'zalopay';
      _trackPayment('confirm');
      _launchExternalUrl(url);
      return NavigationActionPolicy.CANCEL;
    }

    // Handle OnePay domestic exactly like iOS
    if (url.contains('mtf.onepay.vn/onecomm-pay')) {
      _paymentMethod = 'noidia';
      _trackPayment('confirm');
      print('💳 iOS Style: Navigating to OnePay domestic payment');
      return NavigationActionPolicy.ALLOW;
    }

    // Handle OnePay international exactly like iOS
    if (url.contains('mtf.onepay.vn/promotion/vpcpr.op')) {
      _paymentMethod = 'quocte';
      _trackPayment('confirm');
      print('💳 iOS Style: Navigating to OnePay international payment');
      return NavigationActionPolicy.ALLOW;
    }

    return NavigationActionPolicy.ALLOW;
  }

  void _launchExternalUrl(String url) async {
    try {
      await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
    } catch (e) {
      print('❌ iOS Style: Error launching external URL: $e');
    }
  }

  void _trackPayment(String type, [String? errorCode, String? errorMsg]) {
    // TODO: Implement tracking exactly like iOS tracking(type:errorCode:errorMsg:)
    // This should call the same tracking service as iOS with same parameters
    print('📊 iOS Style: Tracking payment - Type: $type, Method: $_paymentMethod, ErrorCode: $errorCode, ErrorMsg: $errorMsg');

    // Track payment events exactly like iOS Tracking.swift
    switch (type) {
      case 'confirm':
        // Track confirm_payment event
        break;
      case 'success':
        // Track pay_success event
        break;
      case 'fail':
        // Track pay_fail event
        break;
    }
  }

  /// Handle JavaScript messages exactly like iOS WKScriptMessageHandler
  void _handleJavaScriptMessage(String message) {
    print('📱 iOS Style: Processing message: $message');

    switch (message.replaceAll('"', '')) {
      case 'policy':
        // TODO: Navigate to policy screen
        print('📋 iOS Style: Show policy');
        break;

      case 'payment_success':
        _stopCountdownTimer();
        _trackPayment('success');
        print('✅ iOS Style: Payment success');
        _showPaymentSuccessAlert();
        break;

      case 'awaiting_payment':
        _stopCountdownTimer();
        print('⏳ iOS Style: Payment awaiting');
        // Check if current screen is payment screen exactly like iOS
        if (mounted) {
          _showPaymentAwaitingAlert();
        }
        break;

      case 'payment_failed':
        _stopCountdownTimer();
        _trackPayment('fail', message, 'Alert.PaymentFailed');
        print('❌ iOS Style: Payment failed');
        _showPaymentFailedAlert();
        break;

      case 'booking_seat_failed':
        _stopCountdownTimer();
        print('❌ iOS Style: Booking seat failed');
        _showBookingSeatFailedAlert();
        break;

      default:
        print('❓ iOS Style: Unknown message: $message');
        break;
    }
  }

  /// Show payment success alert exactly like iOS showAlert(message: "Alert.PaymentSuccess".localized)
  void _showPaymentSuccessAlert() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Thanh toán thành công'),
          content: const Text('Cảm ơn bạn đã đặt vé. Vé của bạn đã được gửi đến email đăng ký.'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
                _getTransactionDetail(); // Exactly like iOS getTransactionDetail()
              },
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  /// Show payment failed alert exactly like iOS showAlert(message: "Alert.PaymentFailed".localized)
  void _showPaymentFailedAlert() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Thanh toán thất bại'),
          content: const Text('Thanh toán không thành công. Vui lòng thử lại.'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
                _gotoHome(); // Exactly like iOS AppDelegate.shared.gotoHome()
              },
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  /// Show booking seat failed alert exactly like iOS showAlert(message: "Alert.BookingSeatFailed".localized)
  void _showBookingSeatFailedAlert() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Đặt ghế thất bại'),
          content: const Text('Không thể đặt ghế. Vui lòng thử lại.'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
                _gotoHome(); // Exactly like iOS AppDelegate.shared.gotoHome()
              },
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  /// Show payment awaiting alert exactly like iOS showAlert(message: "Alert.BookingWaiting".localized)
  void _showPaymentAwaitingAlert() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Đang chờ thanh toán'),
          content: const Text('Giao dịch của bạn đang được xử lý. Vui lòng kiểm tra lại trong lịch sử giao dịch.'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
                _navigateToTransactionHistory(); // Exactly like iOS navigate to transaction history
              },
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  /// Get transaction detail exactly like iOS getTransactionDetail()
  void _getTransactionDetail() {
    webViewController?.evaluateJavascript(source: "getTransactionId();").then((result) {
      final transactionId = result?.toString().replaceAll('"', '');

      if (transactionId != null && transactionId.isNotEmpty && transactionId != 'null') {
        print('✅ iOS Style: Got transaction ID: $transactionId');
        _navigateToTransactionDetail(transactionId);
      } else {
        print('❌ iOS Style: No transaction ID, showing error alert');
        _showErrorAlert();
      }
    }).catchError((error) {
      print('❌ iOS Style: Error getting transaction ID: $error');
      _showErrorAlert();
    });
  }

  /// Show error alert exactly like iOS showAlert(title: "Alert.Error".localized)
  void _showErrorAlert() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Lỗi'),
          content: const Text('Đã xảy ra lỗi. Vui lòng thử lại.'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog
                _gotoHome(); // Navigate to home
              },
              child: const Text('OK'),
            ),
          ],
        );
      },
    );
  }

  /// Navigate to transaction detail exactly like iOS TransactionDetailViewController
  void _navigateToTransactionDetail(String transactionId) {
    // TODO: Implement proper transaction detail navigation
    // For now, just go to home
    _gotoHome();
  }

  /// Navigate to transaction history exactly like iOS TransactionHistory
  void _navigateToTransactionHistory() {
    // TODO: Navigate to transaction history screen
    // For now, go to home
    _gotoHome();
  }

  /// Go to home exactly like iOS AppDelegate.shared.gotoHome()
  void _gotoHome() {
    Navigator.of(context).popUntil((route) => route.isFirst);
  }

  /// Debug function to call getTicketTypeInBooking() and print results
  Future<void> _debugGetTicketTypeInBooking() async {
    try {
      print('🔍 iOS Style: Debugging getTicketTypeInBooking()...');

      // Call getTicketTypeInBooking() function and get its return value directly
      final returnValue = await webViewController!.evaluateJavascript(source: "getTicketTypeInBooking();");
      print('✅ iOS Style: getTicketTypeInBooking() return value = $returnValue');


      print('📋 iOS Style: === Values after getTicketTypeInBooking() ===');

      // Get ticketTypeInBooking array (direct value)
      final ticketTypeResult = await webViewController!.evaluateJavascript(source: "ticketTypeInBooking;");
      print('📊 iOS Style: ticketTypeInBooking = $ticketTypeResult');

      // Get bookingInfor.seats (direct value)
      final seatsResult = await webViewController!.evaluateJavascript(source: "bookingInfor.seats;");
      print('🎫 iOS Style: bookingInfor.seats = $seatsResult');

      // Get bookingInfor money information (direct values)
      final totalMoneyResult = await webViewController!.evaluateJavascript(source: "bookingInfor.TotalMoney;");
      print('💰 iOS Style: bookingInfor.TotalMoney = $totalMoneyResult');

      final comboMoneyResult = await webViewController!.evaluateJavascript(source: "bookingInfor.ComboMoney;");
      print('🎁 iOS Style: bookingInfor.ComboMoney = $comboMoneyResult');

      final totalMoneyWithoutTicketResult = await webViewController!.evaluateJavascript(source: "bookingInfor.TotalMoneyWithoutTicket;");
      print('🎟️ iOS Style: bookingInfor.TotalMoneyWithoutTicket = $totalMoneyWithoutTicketResult');

      // Get totalMoneySeat variable (direct value)
      final totalMoneySeatResult = await webViewController!.evaluateJavascript(source: "totalMoneySeat;");
      print('💵 iOS Style: totalMoneySeat = $totalMoneySeatResult');

      // Get paymentInfor updates (direct values)
      final paymentTotalMoneyNeedPay = await webViewController!.evaluateJavascript(source: "paymentInfor.TotalMoneyNeedPay;");
      print('💳 iOS Style: paymentInfor.TotalMoneyNeedPay = $paymentTotalMoneyNeedPay');

      final paymentTotalDiscount = await webViewController!.evaluateJavascript(source: "paymentInfor.TotalDiscount;");
      print('🎯 iOS Style: paymentInfor.TotalDiscount = $paymentTotalDiscount');

      // Get UI text updates (direct values)
      final totalMoneyNameText = await webViewController!.evaluateJavascript(source: r'$(".total-money-name").text();');
      print('🏷️ iOS Style: .total-money-name text = $totalMoneyNameText');

      final moneyNeedPayText = await webViewController!.evaluateJavascript(source: r'$(".money-need-pay").text();');
      print('💸 iOS Style: .money-need-pay text = $moneyNeedPayText');

      // Test console.log (much simpler than webkit messageHandlers)
      await webViewController!.evaluateJavascript(source: '''
        console.log("🎯 Test from getTicketTypeInBooking debug");
        console.log("dataBooking.Seats: " + JSON.stringify(bookingInfor.seats));
        console.log("ticketTypeInBooking: " + JSON.stringify(ticketTypeInBooking));
        console.log("bookingInfor.TotalMoney: " + bookingInfor.TotalMoney);
      ''');

      // Print all the values that the function processes/updates (direct values, no JSON.stringify)s
    } catch (e) {
      print('❌ iOS Style: Error debugging getTicketTypeInBooking(): $e');
    }
  }

  /// Debug function to print listCombo after JSON.parse
  Future<void> _debugParsedJsonValues() async {
    try {
      // Get listCombo after JSON.parse (full JSON string to avoid truncation)
      final listComboResult = await webViewController!.evaluateJavascript(source: "JSON.stringify(listCombo)");
      print('📦 iOS Style: listCombo = $listComboResult');

    } catch (e) {
      print('❌ iOS Style: Error debugging listCombo: $e');
    }
  }

  /// Note: Payment callbacks are handled via DeepLinkService in main app
  /// The deep link service will handle MoMo/ZaloPay callbacks and execute
  /// JavaScript functions in the WebView when payment apps return to our app
  /// This approach is cleaner than having NotificationCenter observers here
}

// ticketTypeInBooking = []
// bookingInfor.seats (with NewPrice) = null
// bookingInfor.TotalMoney = null
// bookingInfor.ComboMoney = null
//  bookingInfor.TotalMoneyWithoutTicket = null
// totalMoneySeat = 0.0
// paymentInfor.TotalMoneyNeedPay = null
// paymentInfor.TotalDiscount = 0.0
//  .total-money-name text = 0đ0đ
// .money-need-pay text = 0đ

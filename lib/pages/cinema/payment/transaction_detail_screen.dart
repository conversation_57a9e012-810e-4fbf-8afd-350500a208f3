import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_app/constants/index.dart';
import 'package:flutter_app/pages/cinema/payment/transaction_model.dart' ;
import 'package:flutter_app/pages/voucher/api/api_test.dart';
import 'package:flutter_app/models/transaction_history_model.dart' as thm;
import 'package:flutter_app/service/index.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:intl/intl.dart';
import 'package:qr_flutter/qr_flutter.dart';
import 'package:easy_localization/easy_localization.dart';

import '../../../utils/index.dart';

class TransactionDetailScreen extends StatefulWidget {
  final String transactionId;
  final String? userId;
  final bool backToHome;
  final thm.TransactionHistoryModel? transactionItem; // For direct navigation from list

  const TransactionDetailScreen({
    Key? key,
    required this.transactionId,
    this.userId,
    this.backToHome = false,
    this.transactionItem,
  }) : super(key: key);

  @override
  _TransactionDetailScreenState createState() => _TransactionDetailScreenState();
}

class _TransactionDetailScreenState extends State<TransactionDetailScreen> {
  bool _isLoading = true;
  String? _error;
  TransactionHistoryDetailModel? _transaction;
  final NumberFormat _currencyFormatter = NumberFormat('#,###', 'vi_VN');

  @override
  void initState() {
    super.initState();
    _loadTransactionDetail();
  }

  Future<void> _loadTransactionDetail() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      // Use auth service for transaction detail like iOS/Android
      final api = RepositoryProvider.of<Api>(context).auth;
      final userId = widget.userId  ?? '';

      final response = await api.getTransactionDetail(userId, widget.transactionId);

      if (response != null ) {
        setState(() {
          _transaction = TransactionHistoryDetailModel.fromJson(response.data);
          _isLoading = false;
        });
      } else {
        setState(() {
          _isLoading = false;
          _error = response?.message ?? "Không thể tải thông tin giao dịch";
        });
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _error = "Lỗi: $e";
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
      canPop: !widget.backToHome,
      onPopInvoked: (didPop) {
        if (!didPop && widget.backToHome) {
          Navigator.of(context).popUntil((route) => route.isFirst);
        }
      },
      child: Scaffold(
        appBar: AppBar(
          title: Text(
            'Transaction.Detail'.tr(),
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.w600,
            ),
          ),
          backgroundColor: CColor.primary,
          foregroundColor: Colors.white,
          elevation: 0,
          leading: IconButton(
            icon: const Icon(Icons.arrow_back_ios, color: Colors.white),
            onPressed: () {
              if (widget.backToHome) {
                Navigator.of(context).popUntil((route) => route.isFirst);
              } else {
                Navigator.of(context).pop();
              }
            },
          ),
          actions: [
            if (_transaction != null)
              IconButton(
                icon: const Icon(Icons.share, color: Colors.white),
                onPressed: _shareTransaction,
              ),
          ],
        ),
        body: _isLoading
            ? Center(child: CircularProgressIndicator(color: CColor.primary))
            : _error != null
                ? _buildErrorState()
                : _buildTransactionDetail(),
      ),
    );
  }

  Widget _buildTransactionDetail() {
    if (_transaction == null) {
      return Center(child: Text('Transaction.NoTransactionInfo'.tr()));
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Transaction Header Card
          _buildTransactionHeader(),

          const SizedBox(height: 16),

          // Film Information Card
          _buildFilmInfoCard(),

          const SizedBox(height: 16),

          // Ticket Information Card
          _buildTicketInfoCard(),

          const SizedBox(height: 16),

          // Payment Information Card
          _buildPaymentInfoCard(),

          const SizedBox(height: 16),

          // QR Code/Barcode Card
          // if (_transaction!.no != null && _transaction!.no!.isNotEmpty)
          //   _buildQRCodeCard(),

          const SizedBox(height: 32),
        ],
      ),
    );
  }

  Widget _buildTransactionHeader() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [CColor.primary, CColor.primary.withOpacity(0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: CColor.primary.withOpacity(0.3),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.receipt_long, color: Colors.white, size: 24),
              const SizedBox(width: 8),
              Text(
                'Transaction.TransactionCode'.tr(),
                style: TextStyle(
                  color: Colors.white.withOpacity(0.9),
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            _transaction!.InvoiceId ?? 'N/A',
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
              letterSpacing: 1.2,
            ),
          ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              'Transaction.TransactionSuccess'.tr(),
              style: TextStyle(
                color: Colors.white,
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInfoSection({
    required String title,
    required String content,
    List<Widget>? children,
    bool isHighlighted = false,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: const TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.red,
          ),
        ),
        const SizedBox(height: 8),
        if (children != null) ...children,
        if (children == null)
          Text(
            content,
            style: TextStyle(
              fontSize: isHighlighted ? 18 : 16,
              fontWeight: isHighlighted ? FontWeight.bold : FontWeight.normal,
            ),
          ),
      ],
    );
  }

  Widget _buildInfoRow(
    String label,
    String value, {
    bool isBold = false,
    Color? valueColor,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 16,
                color: Colors.grey.shade700,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 16,
                fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
                color: valueColor,
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildTicketTypeRows() {
    final List<Widget> rows = [];

    if (_transaction?.TicketTypes == null) return rows;

    for (var ticketType in _transaction!.TicketTypes!) {
      if (ticketType.ListSeatName == null || ticketType.ListSeatName!.isEmpty) continue;

      rows.add(
        _buildInfoRow(
          '${ticketType.Name ?? "Vé"}:',
          '${ticketType.ListSeatName!.join(", ")} (${ticketType.ListSeatName!.length})',
        ),
      );
    }

    return rows;
  }

  List<Widget> _buildPaymentMethodRows() {
    final List<Widget> rows = [];
    final formatter = NumberFormat("#,###", "vi_VN");

    if (_transaction?.PaymentModel == null) return rows;

    for (var payment in _transaction!.PaymentModel!) {
      if (payment.Values == null || payment.Values == 0) continue;

      rows.add(
        _buildInfoRow(
          '${payment.PaymentTypeName ?? "Thanh toán"}:',
          '${formatter.format(payment.Values ?? 0)} đ',
        ),
      );
    }

    return rows;
  }

  String _formatDate(String? dateString) {
    if (dateString == null) return 'N/A';

    try {
      final date = DateTime.parse(dateString);
      return DateFormat('dd/MM/yyyy').format(date);
    } catch (e) {
      return dateString;
    }
  }

  String _formatTime(String? timeString) {
    if (timeString == null) return 'N/A';

    try {
      final time = DateTime.parse(timeString);
      return DateFormat('HH:mm').format(time);
    } catch (e) {
      return timeString;
    }
  }

  String _formatDateTime(String? dateTimeString) {
    if (dateTimeString == null) return 'N/A';

    try {
      final dateTime = DateTime.parse(dateTimeString);
      return DateFormat('dd/MM/yyyy HH:mm').format(dateTime);
    } catch (e) {
      return dateTimeString;
    }
  }

  String _formatCurrency(double amount) {
    return _currencyFormatter.format(amount);
  }

  // Share transaction functionality
  void _shareTransaction() {
    if (_transaction == null) return;

    final text = '''
${('Transaction.Detail'.tr())} Beta Cinemas
${'Transaction.TransactionCode'.tr()}: ${_transaction!.InvoiceId ?? 'N/A'}
${'Transaction.Movie'.tr()}: ${_transaction!.FilmName ?? 'N/A'}
${'Transaction.Cinema'.tr()}: ${_transaction!.CinemaName ?? 'N/A'}
${'Transaction.TotalAmount'.tr()}: ${_formatCurrency(_transaction!.TotalPayment ?? 0)}đ
${'Transaction.TransactionDate'.tr()}: ${_formatDateTime(_transaction!.DateEntered ?? '')}
''';

    Clipboard.setData(ClipboardData(text: text));
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(content: Text('Transaction.ShareSuccess'.tr())),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.error_outline, size: 80, color: Colors.grey[400]),
            const SizedBox(height: 16),
            Text(
              'Transaction.LoadError'.tr(),
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey[700],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              _error ?? 'Transaction.UnknownError'.tr(),
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: _loadTransactionDetail,
              icon: const Icon(Icons.refresh),
              label: Text('Transaction.Retry'.tr()),
              style: ElevatedButton.styleFrom(
                backgroundColor: CColor.primary,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilmInfoCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.movie, color: CColor.primary, size: 24),
              const SizedBox(width: 8),
              Text(
                'Transaction.MovieInfo'.tr(),
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildDetailRow('Transaction.Movie'.tr(), _transaction!.FilmName ?? 'N/A'),
          _buildDetailRow('Transaction.Cinema'.tr(), _transaction!.CinemaName ?? 'N/A'),
          // _buildDetailRow('Phòng chiếu', _transaction!.screenName ?? 'N/A'),
          _buildDetailRow('Transaction.ShowDate'.tr(), _formatDate(_transaction!.DateShow)),
          _buildDetailRow('Transaction.ShowTime'.tr(), _formatTime(_transaction!.ShowTime)),
        ],
      ),
    );
  }

  Widget _buildTicketInfoCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.confirmation_number, color: CColor.primary, size: 24),
              const SizedBox(width: 8),
              Text(
                'Transaction.TicketInfo'.tr(),
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ..._buildTicketTypeRows(),
          if (_transaction!.ListCombo != null && _transaction!.ListCombo!.isNotEmpty) ...[
            const Divider(height: 24),
            Text(
              'Transaction.Combo'.tr(),
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: Colors.grey[700],
              ),
            ),
            const SizedBox(height: 8),
            ..._buildComboRows(),
          ],
        ],
      ),
    );
  }

  Widget _buildPaymentInfoCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.payment, color: CColor.primary, size: 24),
              const SizedBox(width: 8),
              Text(
                'Transaction.PaymentInfo'.tr(),
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ..._buildPaymentMethodRows(),
          const Divider(height: 24),
          _buildDetailRow(
            'Transaction.TotalAmount'.tr(),
            '${_formatCurrency(_transaction!.TotalPayment ?? 0)}đ',
            isBold: true,
            valueColor: CColor.primary,
          ),
          if (_transaction!.QuantityPoint != null && _transaction!.QuantityPoint! > 0)
            _buildDetailRow(
              'Transaction.AccumulatedPoints'.tr(),
              '+${_transaction!.QuantityPoint}',
              valueColor: Colors.green,
            ),
          if (_transaction!.DateExpiredPoint != null)
            _buildDetailRow(
              'Transaction.PointExpiryDate'.tr(),
              _formatDate(_transaction!.DateExpiredPoint),
              valueColor: Colors.orange,
            ),
          _buildDetailRow(
            'Transaction.TransactionDate'.tr(),
            _formatDateTime(_transaction!.DateEntered ?? ''),
          ),
        ],
      ),
    );
  }

  Widget _buildQRCodeCard() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.qr_code, color: CColor.primary, size: 24),
              const SizedBox(width: 8),
              Text(
                'Transaction.ElectronicTicketCode'.tr(),
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.grey[800],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          // Container(
          //   padding: const EdgeInsets.all(16),
          //   decoration: BoxDecoration(
          //     color: Colors.grey[50],
          //     borderRadius: BorderRadius.circular(12),
          //     border: Border.all(color: Colors.grey[300]!),
          //   ),
          //   child: Column(
          //     children: [
          //       // QR Code
          //       Container(
          //         padding: const EdgeInsets.all(16),
          //         decoration: BoxDecoration(
          //           color: Colors.white,
          //           borderRadius: BorderRadius.circular(8),
          //         ),
          //         child: QrImageView(
          //           data: _transaction!.no ?? '',
          //           version: QrVersions.auto,
          //           size: 200.0,
          //           backgroundColor: Colors.white,
          //         ),
          //       ),
          //       const SizedBox(height: 16),
          //       // Barcode
          //       Container(
          //         padding: const EdgeInsets.all(16),
          //         decoration: BoxDecoration(
          //           color: Colors.white,
          //           borderRadius: BorderRadius.circular(8),
          //         ),
          //         child: BarcodeWidget(
          //           barcode: Barcode.code128(),
          //           data: _transaction!.no ?? '',
          //           width: 250,
          //           height: 80,
          //         ),
          //       ),
          //       const SizedBox(height: 12),
          //       Text(
          //         _transaction!.no ?? '',
          //         style: TextStyle(
          //           fontSize: 16,
          //           fontWeight: FontWeight.bold,
          //           letterSpacing: 2,
          //           color: Colors.grey[700],
          //         ),
          //       ),
          //     ],
          //   ),
          // ),
          // const SizedBox(height: 16),
          Text(
            'Transaction.PresentCodeAtCounter'.tr(),
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
              fontStyle: FontStyle.italic,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(
    String label,
    String value, {
    bool isBold = false,
    Color? valueColor,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 6),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey[600],
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
                color: valueColor ?? Colors.grey[800],
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<Widget> _buildComboRows() {
    if (_transaction!.ListCombo == null || _transaction!.ListCombo!.isEmpty) {
      return [
        Text(
          'Không có combo',
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[600],
            fontStyle: FontStyle.italic,
          ),
        ),
      ];
    }

    return _transaction!.ListCombo!.map((combo) {
      return _buildDetailRow(
        combo.Name ?? 'Combo',
        'x${combo.Quantity ?? 0}',
      );
    }).toList();
  }
}

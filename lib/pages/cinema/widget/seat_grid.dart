import 'package:flutter/material.dart';
import 'package:flutter_app/pages/cinema/model/seat_model.dart';
import 'package:flutter_svg/flutter_svg.dart';

/// ✅ Optimized SeatGrid with improved performance and couple seat logic
class SeatGrid extends StatelessWidget {
  final List<List<SeatModel>> seatPositions;
  final List<SeatModel> selectedSeats;
  final Function(SeatModel) onSeatSelected;
  final Function(SeatModel) onSeatDeselected;

  const SeatGrid({
    super.key,
    required this.seatPositions,
    required this.selectedSeats,
    required this.onSeatSelected,
    required this.onSeatDeselected,
  });

  @override
  Widget build(BuildContext context) {
    // ✅ Pre-process couple seats once for better performance
    final processedSeatPositions = _preprocessCoupleSeatPairing(seatPositions);

    // ✅ Calculate layout dimensions efficiently
    final layoutInfo = _calculateLayoutInfo(processedSeatPositions);

    return LayoutBuilder(
      builder: (context, constraints) {
        final seatSize = _calculateOptimalSeatSize(constraints.maxWidth, layoutInfo.maxSeatsInRow);

        return SingleChildScrollView(
          scrollDirection: Axis.vertical,
          physics: const ClampingScrollPhysics(),
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 4),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const SizedBox(height: 5),
                // ✅ Use indexed loop for better performance
                ...List.generate(
                  processedSeatPositions.length,
                      (index) => _buildRow(processedSeatPositions[index], seatSize),
                ),
                const SizedBox(height: 5),
              ],
            ),
          ),
        );
      },
    );
  }

  /// ✅ Layout information for efficient rendering
  _LayoutInfo _calculateLayoutInfo(List<List<SeatModel>> processedSeatPositions) {
    int maxSeatsInRow = 0;

    for (var row in processedSeatPositions) {
      final Set<int> countedSeatIndices = {};
      int seatsCount = 0;

      for (var seat in row) {
        if (seat.seatIndex != null && countedSeatIndices.contains(seat.seatIndex!)) {
          continue;
        }

        if (seat.seatTypeEnum == SeatType.COUPLE) {
          seatsCount += 2; // Couple seat counts as 2 seats width-wise
          if (seat.seatIndex != null) countedSeatIndices.add(seat.seatIndex!);
          if (seat.coupleSeat?.seatIndex != null) countedSeatIndices.add(seat.coupleSeat!.seatIndex!);
        } else if (!seat.isWay && !seat.isBroken && !seat.isNotUsed) {
          seatsCount += 1;
        }
      }
      maxSeatsInRow = seatsCount > maxSeatsInRow ? seatsCount : maxSeatsInRow;
    }

    return _LayoutInfo(maxSeatsInRow: maxSeatsInRow);
  }

  /// ✅ Calculate optimal seat size based on available width - FIXED OVERFLOW
  double _calculateOptimalSeatSize(double availableWidth, int maxSeatsInRow) {
    if (maxSeatsInRow <= 0) return 32.0;

    // Account for padding and margins
    const double horizontalPadding = 8.0; // 4 * 2 from symmetric padding
    const double marginPerSeat = 4.0; // Approximate margin per seat
    const double totalMargins = marginPerSeat * 2; // Left and right margins

    final effectiveWidth = availableWidth - horizontalPadding - (totalMargins * maxSeatsInRow);
    final calculatedSize = (effectiveWidth / maxSeatsInRow).floorToDouble();

    // Ensure minimum readable size but prevent overflow
    return calculatedSize.clamp(20.0, 42.0);
  }

  /// ✅ Optimized couple seat preprocessing with better logic
  List<List<SeatModel>> _preprocessCoupleSeatPairing(List<List<SeatModel>> originalSeatPositions) {
    final processedPositions = <List<SeatModel>>[];

    for (var row in originalSeatPositions) {
      final processedRow = <SeatModel>[];
      final Set<int> pairedSeatIndices = {};

      for (var seat in row) {
        // Skip if this seat has already been paired
        if (seat.seatIndex != null && pairedSeatIndices.contains(seat.seatIndex!)) {
          continue;
        }

        if (seat.seatTypeEnum == SeatType.COUPLE) {
          final pairedSeat = _processCoupleSeat(seat, row, pairedSeatIndices);
          if (pairedSeat != null) {
            processedRow.add(pairedSeat);
          }
        } else {
          // Regular seat - add as is
          processedRow.add(seat);
        }
      }

      processedPositions.add(processedRow);
    }

    return processedPositions;
  }

  /// ✅ Process couple seat with improved logic
  SeatModel? _processCoupleSeat(SeatModel seat, List<SeatModel> row, Set<int> pairedSeatIndices) {
    if (seat.seatNumber == null || seat.seatNumber!.isEmpty) return null;

    // Only process standard seat names (A1, B2, etc.)
    if (!RegExp(r'^[A-Z]\d+$').hasMatch(seat.seatNumber!)) {
      return null; // Skip non-standard seat names silently
    }

    final seatNumber = _extractSeatNumber(seat.seatNumber!);
    if (seatNumber == null) return null;

    // ✅ OPTIMIZED: Only odd-numbered seats initiate pairing to prevent duplicates
    if (seatNumber % 2 != 1) {
      return null; // Even seats are handled by their odd counterparts
    }

    final couplePair = _findCouplePair(seat, row);
    if (couplePair != null &&
        !couplePair.isBroken &&
        !couplePair.isWay &&
        couplePair.seatIndex != null) {

      // Mark both seats as paired
      if (seat.seatIndex != null) pairedSeatIndices.add(seat.seatIndex!);
      pairedSeatIndices.add(couplePair.seatIndex!);

      // Return the main seat with couple pair linked
      return seat.copyWith(coupleSeat: couplePair);
    }

    // If no valid pair found, don't show this couple seat
    return null;
  }

  /// ✅ Find couple pair for a seat (optimized)
  SeatModel? _findCouplePair(SeatModel seat, List<SeatModel> row) {
    if (seat.seatNumber == null) return null;

    final seatNumber = _extractSeatNumber(seat.seatNumber!);
    if (seatNumber == null || seatNumber % 2 != 1) return null;

    // Generate pair seat name (J1 → J2, J3 → J4, etc.)
    final rowLetter = seat.seatNumber!.substring(0, 1);
    final pairSeatName = '$rowLetter${seatNumber + 1}';

    // Find the pair seat efficiently
    for (var candidateSeat in row) {
      if (candidateSeat.seatNumber == pairSeatName &&
          candidateSeat.seatTypeEnum == SeatType.COUPLE &&
          !candidateSeat.isBroken &&
          !candidateSeat.isWay) {
        return candidateSeat;
      }
    }

    return null; // No valid pair found
  }

  /// ✅ Build a row of seats with optimized rendering - FIXED OVERFLOW
  Widget _buildRow(List<SeatModel> rowSeats, double seatSize) {
    final Set<int> renderedSeatIndices = {};

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 1.5, horizontal: 2),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        physics: const ClampingScrollPhysics(),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: rowSeats.map((seat) =>
              _buildSeatWithTracking(seat, seatSize, renderedSeatIndices)
          ).toList(),
        ),
      ),
    );
  }

  /// ✅ Build seat with duplicate tracking (optimized)
  Widget _buildSeatWithTracking(SeatModel seat, double baseSeatSize, Set<int> renderedSeatIndices) {
    // Skip if this seat has already been rendered as part of a couple seat
    if (seat.seatIndex != null && renderedSeatIndices.contains(seat.seatIndex!)) {
      return const SizedBox.shrink();
    }

    // For couple seats, mark both seats as rendered to prevent duplicates
    if (seat.seatTypeEnum == SeatType.COUPLE && seat.coupleSeat != null) {
      if (seat.seatIndex != null) renderedSeatIndices.add(seat.seatIndex!);
      if (seat.coupleSeat!.seatIndex != null) {
        renderedSeatIndices.add(seat.coupleSeat!.seatIndex!);
      }
    }

    return _buildSeat(seat, baseSeatSize);
  }

  /// ✅ Build individual seat widget (optimized)
  Widget _buildSeat(SeatModel seat, double baseSeatSize) {
    // Return empty space for non-seat elements
    if (seat.isWay || seat.isBroken || seat.isNotUsed || seat.isEntranceExit) {
      return SizedBox(width: baseSeatSize, height: baseSeatSize);
    }

    // ✅ Optimized selection check
    final isSelected = _isSeatSelected(seat);

    // ✅ Cache seat image and dimensions
    final seatImage = _getSeatImage(seat, isSelected);
    final seatDimensions = _getSeatDimensions(seat, baseSeatSize);

    return GestureDetector(
      onTap: () => _handleSeatTap(seat, isSelected),
      child: Container(
        margin: EdgeInsets.all(baseSeatSize * 0.02), // Further reduced margin to prevent overflow
        child: Stack(
          alignment: Alignment.center,
          children: [
            SvgPicture.asset(
              seatImage,
              width: seatDimensions.width,
              height: seatDimensions.height,
              fit: BoxFit.contain,
            ),
            Text(
              _getSeatLabel(seat),
              style: TextStyle(
                fontSize: baseSeatSize * 0.35, // Slightly smaller for better fit
                fontWeight: FontWeight.w600,
                color: _getSeatTextColor(seat, isSelected),
              ),
              textAlign: TextAlign.center,
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  /// ✅ Optimized seat selection check
  bool _isSeatSelected(SeatModel seat) {
    for (var selectedSeat in selectedSeats) {
      if (selectedSeat.seatNumber == seat.seatNumber ||
          (seat.coupleSeat != null && selectedSeat.seatNumber == seat.coupleSeat!.seatNumber)) {
        return true;
      }
    }
    return false;
  }

  /// ✅ Handle seat tap with proper logic
  void _handleSeatTap(SeatModel seat, bool isSelected) {
    if (seat.soldStatus == SeatSoldStatus.EMPTY && !isSelected) {
      onSeatSelected(seat);
    } else if (isSelected) {
      onSeatDeselected(seat);
    }
  }

  /// ✅ Get seat dimensions based on type
  _SeatDimensions _getSeatDimensions(SeatModel seat, double baseSeatSize) {
    final width = seat.seatTypeEnum == SeatType.COUPLE ? baseSeatSize * 1.8 : baseSeatSize;
    final height = baseSeatSize * 1.1;
    return _SeatDimensions(width: width, height: height);
  }

  /// ✅ Get seat text color based on state
  Color _getSeatTextColor(SeatModel seat, bool isSelected) {
    if (isSelected) return Colors.white;
    if (seat.soldStatus != SeatSoldStatus.EMPTY) return Colors.white;
    return Colors.black;
  }

  /// ✅ Get seat image with optimized logic
  String _getSeatImage(SeatModel seat, bool isSelected) {
    const String basePath = 'assets/icon/cinema';

    // ✅ Use maps for better performance and maintainability
    const Map<SeatType, String> emptySeats = {
      SeatType.NORMAL: '$basePath/ic_empty_normal_seat.svg',
      SeatType.VIP: '$basePath/ic_empty_vip_seat.svg',
      SeatType.COUPLE: '$basePath/ic_empty_couple_seat.svg',
    };

    const Map<SeatType, String> selectedSeats = {
      SeatType.NORMAL: '$basePath/ic_select_normal_seat.svg',
      SeatType.VIP: '$basePath/ic_select_vip_seat.svg',
      SeatType.COUPLE: '$basePath/ic_select_couple_seat.svg',
    };

    const Map<SeatType, String> processingSeats = {
      SeatType.NORMAL: '$basePath/ic_process_normal_seat.svg',
      SeatType.VIP: '$basePath/ic_process_vip_seat.svg',
      SeatType.COUPLE: '$basePath/ic_process_couple_seat.svg',
    };

    const Map<SeatType, String> bookedSeats = {
      SeatType.NORMAL: '$basePath/ic_set_normal_seat.svg',
      SeatType.VIP: '$basePath/ic_set_vip_seat.svg',
      SeatType.COUPLE: '$basePath/ic_set_couple_seat.svg',
    };

    const Map<SeatType, String> soldSeats = {
      SeatType.NORMAL: '$basePath/ic_sold_normal_seat.svg',
      SeatType.VIP: '$basePath/ic_sold_vip_seat.svg',
      SeatType.COUPLE: '$basePath/ic_sold_couple_seat.svg',
    };

    // ✅ Optimized seat image selection
    if (isSelected) {
      return selectedSeats[seat.seatTypeEnum] ?? emptySeats[SeatType.NORMAL]!;
    }

    switch (seat.soldStatus) {
      case SeatSoldStatus.EMPTY:
        return emptySeats[seat.seatTypeEnum] ?? emptySeats[SeatType.NORMAL]!;

      case SeatSoldStatus.SELECTING:
      case SeatSoldStatus.SELECTED:
      case SeatSoldStatus.WAITING:
        return processingSeats[seat.seatTypeEnum] ?? processingSeats[SeatType.NORMAL]!;

      case SeatSoldStatus.BOOKED:
        return bookedSeats[seat.seatTypeEnum] ?? bookedSeats[SeatType.NORMAL]!;

      default:
        return soldSeats[seat.seatTypeEnum] ?? soldSeats[SeatType.NORMAL]!;
    }
  }

  /// ✅ Get seat label with optimized couple seat display - FIXED REVERSE NAMING
  String _getSeatLabel(SeatModel seat) {
    if (seat.seatNumber == null || seat.seatNumber!.isEmpty) return '';

    // For couple seats, show both seat numbers if available
    if (seat.seatTypeEnum == SeatType.COUPLE && seat.coupleSeat != null) {
      final mainSeatNumber = seat.seatNumber!;
      final coupleSeatNumber = seat.coupleSeat!.seatNumber;

      if (coupleSeatNumber != null && coupleSeatNumber.isNotEmpty) {
        final mainNum = _extractSeatNumber(mainSeatNumber) ?? 0;
        final coupleNum = _extractSeatNumber(coupleSeatNumber) ?? 0;

        // Determine the correct order based on seat numbers
        final isMainSeatFirst = mainNum < coupleNum;
        final firstSeat = isMainSeatFirst ? mainSeatNumber : coupleSeatNumber;
        final secondSeat = isMainSeatFirst ? coupleSeatNumber : mainSeatNumber;
        final firstNum = isMainSeatFirst ? mainNum : coupleNum;
        final secondNum = isMainSeatFirst ? coupleNum : mainNum;

        // For couple seats, show compact format: "J1-2" instead of "J1 J2"
        // Check if they are consecutive numbers
        if ((secondNum - firstNum) == 1) {
          final rowLetter = firstSeat.substring(0, 1);
          return '$rowLetter$firstNum-$secondNum';
        }

        // Fallback to space-separated format in correct order
        return '$firstSeat $secondSeat';
      }
    }

    // For regular seats, show the full seat number
    return seat.seatNumber!;
  }

  /// ✅ Extract number from seat name (optimized)
  int? _extractSeatNumber(String seatName) {
    if (seatName.length <= 1) return null;
    return int.tryParse(seatName.substring(1));
  }
}

/// ✅ Helper classes for better type safety and performance
class _LayoutInfo {
  final int maxSeatsInRow;

  const _LayoutInfo({required this.maxSeatsInRow});
}

class _SeatDimensions {
  final double width;
  final double height;

  const _SeatDimensions({required this.width, required this.height});
}


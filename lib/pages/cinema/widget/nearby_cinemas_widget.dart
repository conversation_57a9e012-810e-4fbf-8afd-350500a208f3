import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_app/constants/index.dart';
import 'package:flutter_app/pages/cinema/model/cinema_model.dart';
import 'package:flutter_app/pages/cinema/_detail.dart';
import 'package:flutter_app/pages/other_tab/beta_cinema/_detail.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_app/cubit/index.dart';

import '../../../widgets/src/list_image.dart';
import '../../voucher/api/api_test.dart';

class NearbyCinemasWidget extends StatelessWidget {
  final List<Cinema> nearbyCinemas;
  final bool isBooking;

  const NearbyCinemasWidget({
    super.key,
    required this.nearbyCinemas,
    this.isBooking = false,
  });

  @override
  Widget build(BuildContext context) {
    if (nearbyCinemas.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: CSpace.lg),
          child: Text(
            'CinemaList.NearCinema'.tr().toUpperCase(),
            style: const TextStyle(fontSize: CFontSize.xl2, fontWeight: FontWeight.w500, fontFamily: 'Oswald'),
          ),
        ),
        SizedBox(
          height: 180,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: nearbyCinemas.length > 6 ? 6 : nearbyCinemas.length,
            itemBuilder: (context, index) {
              final cinema = nearbyCinemas[index];
              return _buildCinemaCard(context, cinema);
            },
          ),
        ),
        const SizedBox(height: CSpace.base),
      ],
    );
  }

  Widget _buildCinemaCard(BuildContext context, Cinema cinema) {
    return InkWell(
      onTap: () {
        !isBooking
            ? Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => BlocProvider(
                    create: (context) => BlocC<ShowFilmModel>(),
                    child: ChooseCinemasView(
                      cinema: cinema,
                    ),
                  ),
                ),
              )
            : Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => CinemaDetailScreen(
                    initialCinema: cinema,
                  ),
                ));
      },
      child: Container(
        width: CSpace.width * 0.4,
        margin: const EdgeInsets.symmetric(horizontal: CSpace.sm),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.grey.withAlpha(51), // 0.2 opacity
              blurRadius: 6,
            ),
          ],
        ),
        child: Column(
          children: [
            ClipRRect(
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(12),
              ),
              child: imageNetwork(
                url: '${ApiService.baseUrlImage}/${cinema.picture ?? ''}',
                height: 100,
                width: double.infinity,
                fit: BoxFit.cover,
              ),
            ),
            Padding(
              padding: const EdgeInsets.all(8.0),
              child: Column(
                children: [
                  Text(
                    cinema.name ?? '',
                    style: const TextStyle(fontWeight: FontWeight.w600, fontSize: CFontSize.base),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 2,
                    textAlign: TextAlign.center,
                  ),
                  if (cinema.formattedDistance != null)
                    Padding(
                      padding: const EdgeInsets.only(top: 6.0),
                      child: Text(
                        cinema.formattedDistance!,
                        style: TextStyle(color: Colors.blue.shade800, fontSize: 16),
                      ),
                    ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}

import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_app/pages/cinema/model/cinema_model.dart';
import 'package:intl/intl.dart';

/// A widget that displays a list of show times in a horizontal scrollable list
/// Similar to the iOS TimeListView implementation
class TimeListView extends StatelessWidget {
  /// The list of show times to display
  final List<ShowModel> showTimes;

  /// Callback when a show time is selected
  final Function(ShowModel) onTimeSelected;

  /// Whether to show the empty seats count
  final bool showEmptySeats;

  const TimeListView({
    required this.showTimes,
    required this.onTimeSelected,
    this.showEmptySeats = true,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    if (showTimes.isEmpty) {
      return const Padding(
        padding: EdgeInsets.symmetric(vertical: 8.0),
        child: Text(
          'Không có suất chiếu',
          style: TextStyle(
            color: Colors.grey,
            fontStyle: FontStyle.italic,
          ),
        ),
      );
    }

    // Sort show times by start time
    final sortedShowTimes = List<ShowModel>.from(showTimes)
      ..sort((a, b) {
        if (a.startTime == null) return 1;
        if (b.startTime == null) return -1;
        return a.startTime!.compareTo(b.startTime!);
      });

    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Row(
        children: sortedShowTimes.map((show) {
          // Skip past show times
          final isPast = show.startTime != null && show.startTime!.isBefore(DateTime.now().toLocal());
          if (isPast) return const SizedBox.shrink();

          return TimeListCell(
            show: show,
            onTap: () => onTimeSelected(show),
            showEmptySeats: showEmptySeats,
          );
        }).toList(),
      ),
    );
  }
}

/// A cell that displays a single show time
/// Similar to the iOS TimeListCollectionCell implementation
class TimeListCell extends StatelessWidget {
  /// The show time to display
  final ShowModel show;

  /// Callback when the cell is tapped
  final VoidCallback onTap;

  /// Whether to show the empty seats count
  final bool showEmptySeats;

  const TimeListCell({
    required this.show,
    required this.onTap,
    this.showEmptySeats = true,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    final startTime = show.startTime;
    final formattedTime = startTime != null ? DateFormat('HH:mm').format(startTime) : '--:--';

    final emptySeats = (show.totalSeat ?? 0) - (show.seatSolded ?? 0);

    return GestureDetector(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Chip(
              padding: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 3.0),
              label: Text(
                formattedTime,
                style: const TextStyle(
                  color: Colors.black,
                  fontWeight: FontWeight.bold,
                  fontSize: 17,
                ),
              ),
              backgroundColor: Colors.grey.shade300,
            ),
            if (showEmptySeats) ...[
              Text(
                '$emptySeats ${'FilmBooking.Empty'.tr()}',
                style: const TextStyle(
                  color: Colors.black,
                  fontSize: 15,
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}

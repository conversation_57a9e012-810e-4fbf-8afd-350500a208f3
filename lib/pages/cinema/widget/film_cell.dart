import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_app/pages/Movie_schedule/model/Film_model.dart';
import 'package:flutter_app/pages/cinema/model/cinema_model.dart';
import 'package:flutter_app/pages/cinema/widget/time_list_view.dart';
import 'package:flutter_app/pages/voucher/api/api_test.dart';

import '../../../core/index.dart';

class FilmTimeWidget extends StatelessWidget {
  /// The film to display
  // final ShowFilmInCinema film;
  final ShowFilmModel film;

  /// The cinema where the film is showing
  final Cinema cinema;

  /// Whether this is the top film (to show the top badge)
  final bool isTop1;
  final bool isEnglish;

  /// Callback when the play trailer button is tapped
  final void Function()? onPlayTrailer;

  /// Callback when a show time is selected
  final void Function(ShowModel show)? onShowSelected;

  const FilmTimeWidget({
    required this.film,
    required this.cinema,
    this.isTop1 = false,
    this.onPlayTrailer,
    this.onShowSelected,
    this.isEnglish = false,
    super.key,
  });

  Widget _buildAgeIcon(String? age) {
    switch (age) {
      case 'C13':
        return Image.asset('assets/c-13.png', width: 24, height: 24);
      case 'C16':
        return Image.asset('assets/c-16.png', width: 24, height: 24);
      case 'C18':
        return Image.asset('assets/c-18.png', width: 24, height: 24);
      case 'P':
        return Image.asset('assets/p.png', width: 24, height: 24);
      default:
        return const SizedBox.shrink();
    }
  }

  /// Builds a widget to display format names
  /// If there are multiple format names (separated by |), they will be displayed as chips
  Widget _buildFormatNames(String? formatNames) {
    if (formatNames == null || formatNames.isEmpty) {
      return const SizedBox.shrink();
    }

    // Split the format names by the | delimiter
    final formats = formatNames.split('|').map((e) => e.trim()).toList();

    if (formats.length == 1) {
      // If there's only one format, display it as text
      return Text(
        formats.first,
        style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
      );
    } else {
      // If there are multiple formats, display them as chips in a wrap
      return Wrap(
        spacing: 8,
        runSpacing: 4,
        children: formats
            .map((format) => Chip(
                  label: Text(
                    format,
                    style: const TextStyle(fontSize: 13),
                  ),
                  backgroundColor: Colors.red.shade50,
                ))
            .toList(),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final duration = film.duration ?? 0;
    final imageURL = '${ApiService.baseUrlImage}${Uri.decodeComponent(film.mainPosterUrl ?? '')}';
    final isHot = film.isHot ?? true;

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: const [BoxShadow(color: Colors.black12, blurRadius: 6, offset: Offset(0, 2))],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Film Header
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Film Poster with badges
                Stack(
                  children: [
                    // Film Poster
                    ClipRRect(
                      borderRadius: BorderRadius.circular(5),
                      child: Image.network(
                        imageURL,
                        width: 110,
                        height: 160,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return Container(
                            width: 90,
                            height: 135,
                            color: Colors.grey.shade200,
                            child: const Icon(Icons.movie_outlined, color: Colors.grey),
                          );
                        },
                      ),
                    ),
                    // Hot Badge
                    if (isHot)
                      Positioned(
                        top: 0,
                        right: 0,
                        child: Image.asset('assets/icon/<EMAIL>', width: 35, height: 35),
                      ),
                    // Age Rating Badge
                    Positioned(
                      top: 5,
                      left: 5,
                      child: _buildAgeIcon(film.filmRestrictAgeName),
                    ),
                    // Play Button Overlay
                    Positioned.fill(
                        child: Center(
                            child: GestureDetector(
                      onTap: onPlayTrailer,
                      child: Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: Colors.black.withAlpha(128),
                          shape: BoxShape.circle,
                        ),
                        child: Image.asset(
                          'assets/icon/opacity.png',
                          width: 24,
                          height: 24,
                        ),
                      ),
                    ))),
                  ],
                ),
                const SizedBox(width: 16),
                // Film Details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Film Name
                      Text(
                      (  isEnglish ? film.nameF :  film.name) ?? '',
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontFamily: 'Oswald',
                          fontSize: 20,
                          height: 1.2,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                      const SizedBox(height: 8),
                      // Film Genre
                      Text(
                        (  isEnglish ? film.filmGenreNameF :  film.filmGenreName) ?? '',
                        style: const TextStyle(
                          color: Colors.black87,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 4),
                      // Film Duration
                      Text(
                        '$duration phút',
                        style: const TextStyle(
                          color: Colors.black87,
                          fontSize: 16,
                        ),
                      ),
                    ],
                  ),
                ),
                // Top 1 Badge
                if (isTop1)
                  Image.asset('assets/icon/fill.png', width: 30, height: 45),
              ],
            ),
          ),
          ...?film.listFilm?.map(
            (show) => Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                if (film.listFilm?.indexOf(show) == film.listFilm!.length - 1)
                  const Padding(
                    padding: EdgeInsets.symmetric(horizontal: 16),
                    child: Divider(height: 1, color: Colors.grey),
                  ),
                //Format Names
                const VSpacer(8),
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 16),
                  child: _buildFormatNames( isEnglish ? show.filmFormatNameF :show.filmFormatName),
                ),
                // const VSpacer(4),
                // Show Times
                Padding(
                  padding: const EdgeInsets.only(left: 16, right: 8, bottom: 8),
                  child: TimeListView(
                    showTimes: List<ShowModel>.from(show.listShow ?? []),
                    onTimeSelected: (show) {
                      if (onShowSelected != null) {
                        onShowSelected!(show);
                      }
                    },
                  ),
                ),
              ],
            ),
          ),
          const VSpacer(4),

        ],
      ),
    );
  }
}

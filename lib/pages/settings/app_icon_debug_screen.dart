import 'package:flutter/material.dart';
import 'package:flutter_app/utils/index.dart';

/// App Icon Debug Screen - For testing Firebase Remote Config
/// Similar to iOS-devkhai debugging functionality
class AppIconDebugScreen extends StatefulWidget {
  const AppIconDebugScreen({Key? key}) : super(key: key);

  @override
  State<AppIconDebugScreen> createState() => _AppIconDebugScreenState();
}

class _AppIconDebugScreenState extends State<AppIconDebugScreen> {
  AppIconManager.AppIcon _currentIcon = AppIconManager.AppIcon.defaultIcon;
  String _remoteConfigValue = '';
  bool _isLoading = false;
  String _lastUpdateTime = '';

  @override
  void initState() {
    super.initState();
    _loadCurrentStatus();
  }

  Future<void> _loadCurrentStatus() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final currentIcon = await AppIconManager.getCurrentIcon();
      final remoteValue = await AppIconManager.getRemoteConfigIconName();
      
      if (mounted) {
        setState(() {
          _currentIcon = currentIcon;
          _remoteConfigValue = remoteValue;
          _lastUpdateTime = DateTime.now().toString();
          _isLoading = false;
        });
      }
    } catch (e) {
      print('Error loading current status: $e');
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  Future<void> _triggerRemoteConfigUpdate() async {
    setState(() {
      _isLoading = true;
    });

    try {
      print('🔄 Manually triggering Remote Config update...');
      await AppLifecycleManager.triggerIconUpdate();
      
      // Reload status after update
      await _loadCurrentStatus();
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Remote Config update triggered'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      print('Error triggering Remote Config update: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('App Icon Debug'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _buildContent(),
    );
  }

  Widget _buildContent() {
    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Current Status Card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Current Status',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  _buildStatusRow('Current Icon', AppIconManager.getIconDisplayName(_currentIcon)),
                  _buildStatusRow('Remote Config Value', _remoteConfigValue.isEmpty ? '(empty)' : _remoteConfigValue),
                  _buildStatusRow('Last Update', _lastUpdateTime.isEmpty ? 'Never' : _lastUpdateTime),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 20),
          
          // Action Buttons
          ElevatedButton.icon(
            onPressed: _isLoading ? null : _triggerRemoteConfigUpdate,
            icon: const Icon(Icons.cloud_sync),
            label: const Text('Trigger Remote Config Update'),
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.orange,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
          
          const SizedBox(height: 12),
          
          ElevatedButton.icon(
            onPressed: _isLoading ? null : _loadCurrentStatus,
            icon: const Icon(Icons.refresh),
            label: const Text('Refresh Status'),
            style: ElevatedButton.styleFrom(
              padding: const EdgeInsets.symmetric(vertical: 12),
            ),
          ),
          
          const SizedBox(height: 20),
          
          // Firebase Remote Config Info
          Card(
            color: Colors.blue.shade50,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Firebase Remote Config',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text('Key: AppIcon_IOS_Name'),
                  const Text('Values:'),
                  const Text('  • "" (empty) → Default icon'),
                  const Text('  • "heart" or "hearts" → Heart icon'),
                  const SizedBox(height: 8),
                  const Text(
                    'This matches exactly with iOS-devkhai implementation.',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          const SizedBox(height: 20),
          
          // Lifecycle Info
          Card(
            color: Colors.green.shade50,
            child: Padding(
              padding: const EdgeInsets.all(16.0),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text(
                    'Automatic Updates',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text('✅ App becomes active (resume from background)'),
                  const Text('✅ App launch'),
                  const Text('✅ Manual trigger (this screen)'),
                  const SizedBox(height: 8),
                  const Text(
                    'Exactly like iOS-devkhai applicationDidBecomeActive.',
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              '$label:',
              style: const TextStyle(
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                color: Colors.blue,
              ),
            ),
          ),
        ],
      ),
    );
  }
}

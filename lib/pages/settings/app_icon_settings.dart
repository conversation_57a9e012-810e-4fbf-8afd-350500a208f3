// import 'package:flutter/material.dart';
// import 'package:flutter_app/utils/index.dart';
// import 'package:easy_localization/easy_localization.dart';
//
// import '../../utils/src/app_icon_manager.dart';
//
// /// App Icon Settings Screen - Similar to iOS-devkhai functionality
// /// Allows users to change app icon dynamically (Android and iOS)
// class AppIconSettingsScreen extends StatefulWidget {
//   const AppIconSettingsScreen({Key? key}) : super(key: key);
//
//   @override
//   State<AppIconSettingsScreen> createState() => _AppIconSettingsScreenState();
// }
//
// class _AppIconSettingsScreenState extends State<AppIconSettingsScreen> {
//   AppIconManager.AppIcon _currentIcon = AppIconManager.AppIcon.defaultIcon;
//   bool _isSupported = false;
//   bool _isLoading = true;
//
//   @override
//   void initState() {
//     super.initState();
//     _initializeIconSettings();
//   }
//
//   Future<void> _initializeIconSettings() async {
//     try {
//       final isSupported = await AppIconManager.isSupported();
//       final currentIcon = await AppIconManager.getCurrentIcon();
//
//       if (mounted) {
//         setState(() {
//           _isSupported = isSupported;
//           _currentIcon = currentIcon;
//           _isLoading = false;
//         });
//       }
//     } catch (e) {
//       print('Error initializing icon settings: $e');
//       if (mounted) {
//         setState(() {
//           _isLoading = false;
//         });
//       }
//     }
//   }
//
//   Future<void> _changeIcon(AppIconManager.AppIcon icon) async {
//     if (!_isSupported) {
//       _showNotSupportedDialog();
//       return;
//     }
//
//     setState(() {
//       _isLoading = true;
//     });
//
//     try {
//       final success = await AppIconManager.setIcon(icon);
//
//       if (success && mounted) {
//         setState(() {
//           _currentIcon = icon;
//         });
//
//         _showSuccessSnackBar(icon);
//       } else {
//         _showErrorSnackBar();
//       }
//     } catch (e) {
//       print('Error changing icon: $e');
//       _showErrorSnackBar();
//     } finally {
//       if (mounted) {
//         setState(() {
//           _isLoading = false;
//         });
//       }
//     }
//   }
//
//   void _showNotSupportedDialog() {
//     showDialog(
//       context: context,
//       builder: (context) => AlertDialog(
//         title: const Text('Not Supported'),
//         content: const Text('Dynamic app icons are not supported on this device.'),
//         actions: [
//           TextButton(
//             onPressed: () => Navigator.pop(context),
//             child: const Text('OK'),
//           ),
//         ],
//       ),
//     );
//   }
//
//   void _showSuccessSnackBar(AppIconManager.AppIcon icon) {
//     ScaffoldMessenger.of(context).showSnackBar(
//       SnackBar(
//         content: Text('App icon changed to ${AppIconManager.getIconDisplayName(icon)}'),
//         backgroundColor: Colors.green,
//       ),
//     );
//   }
//
//   void _showErrorSnackBar() {
//     ScaffoldMessenger.of(context).showSnackBar(
//       const SnackBar(
//         content: Text('Failed to change app icon'),
//         backgroundColor: Colors.red,
//       ),
//     );
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(
//         title: const Text('App Icon'),
//         backgroundColor: Colors.transparent,
//         elevation: 0,
//       ),
//       body: _isLoading
//           ? const Center(child: CircularProgressIndicator())
//           : _buildIconList(),
//     );
//   }
//
//   Widget _buildIconList() {
//     if (!_isSupported) {
//       return const Center(
//         child: Padding(
//           padding: EdgeInsets.all(16.0),
//           child: Column(
//             mainAxisAlignment: MainAxisAlignment.center,
//             children: [
//               Icon(
//                 Icons.info_outline,
//                 size: 64,
//                 color: Colors.grey,
//               ),
//               SizedBox(height: 16),
//               Text(
//                 'Dynamic App Icons',
//                 style: TextStyle(
//                   fontSize: 20,
//                   fontWeight: FontWeight.bold,
//                 ),
//               ),
//               SizedBox(height: 8),
//               Text(
//                 'This feature is not supported on this device.',
//                 textAlign: TextAlign.center,
//                 style: TextStyle(
//                   color: Colors.grey,
//                 ),
//               ),
//             ],
//           ),
//         ),
//       );
//     }
//
//     final icons = AppIconManager.getAllIcons();
//
//     return ListView.builder(
//       padding: const EdgeInsets.all(16),
//       itemCount: icons.length,
//       itemBuilder: (context, index) {
//         final icon = icons[index];
//         final isSelected = icon == _currentIcon;
//
//         return Card(
//           margin: const EdgeInsets.only(bottom: 12),
//           child: ListTile(
//             leading: Container(
//               width: 50,
//               height: 50,
//               decoration: BoxDecoration(
//                 borderRadius: BorderRadius.circular(12),
//                 border: Border.all(
//                   color: isSelected ? Colors.blue : Colors.grey.shade300,
//                   width: 2,
//                 ),
//               ),
//               child: ClipRRect(
//                 borderRadius: BorderRadius.circular(10),
//                 child: _buildIconPreview(icon),
//               ),
//             ),
//             title: Text(
//               AppIconManager.getIconDisplayName(icon),
//               style: TextStyle(
//                 fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
//               ),
//             ),
//             subtitle: Text(
//               isSelected ? 'Current' : 'Tap to select',
//               style: TextStyle(
//                 color: isSelected ? Colors.blue : Colors.grey,
//               ),
//             ),
//             trailing: isSelected
//                 ? const Icon(
//                     Icons.check_circle,
//                     color: Colors.blue,
//                   )
//                 : null,
//             onTap: () => _changeIcon(icon),
//           ),
//         );
//       },
//     );
//   }
//
//   Widget _buildIconPreview(AppIconManager.AppIcon icon) {
//     final assetPath = AppIconManager.getIconAssetPath(icon);
//
//     return Image.asset(
//       assetPath,
//       fit: BoxFit.cover,
//       errorBuilder: (context, error, stackTrace) {
//         return Container(
//           color: Colors.grey.shade200,
//           child: Icon(
//             Icons.image_not_supported,
//             color: Colors.grey.shade400,
//           ),
//         );
//       },
//     );
//   }
// }
//
// /// App Icon Settings Tile for Settings Screen
// class AppIconSettingsTile extends StatefulWidget {
//   const AppIconSettingsTile({Key? key}) : super(key: key);
//
//   @override
//   State<AppIconSettingsTile> createState() => _AppIconSettingsTileState();
// }
//
// class _AppIconSettingsTileState extends State<AppIconSettingsTile> {
//   AppIconManager.AppIcon _currentIcon = AppIconManager.AppIcon.defaultIcon;
//   bool _isSupported = false;
//
//   @override
//   void initState() {
//     super.initState();
//     _loadCurrentIcon();
//   }
//
//   Future<void> _loadCurrentIcon() async {
//     try {
//       final isSupported = await AppIconManager.isSupported();
//       final currentIcon = await AppIconManager.getCurrentIcon();
//
//       if (mounted) {
//         setState(() {
//           _isSupported = isSupported;
//           _currentIcon = currentIcon;
//         });
//       }
//     } catch (e) {
//       print('Error loading current icon: $e');
//     }
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     if (!_isSupported) {
//       return const SizedBox.shrink(); // Hide if not supported
//     }
//
//     return ListTile(
//       leading: const Icon(Icons.apps),
//       title: const Text('App Icon'),
//       subtitle: Text(AppIconManager.getIconDisplayName(_currentIcon)),
//       trailing: const Icon(Icons.arrow_forward_ios),
//       onTap: () {
//         Navigator.push(
//           context,
//           MaterialPageRoute(
//             builder: (context) => const AppIconSettingsScreen(),
//           ),
//         ).then((_) => _loadCurrentIcon()); // Refresh when returning
//       },
//     );
//   }
// }

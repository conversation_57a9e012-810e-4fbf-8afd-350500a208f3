import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:flutter_facebook_auth/flutter_facebook_auth.dart';
import 'package:sign_in_with_apple/sign_in_with_apple.dart';
import 'dart:io' show Platform;

import '../../service/notification_service.dart';
import '../../utils/index.dart';
import '/constants/index.dart';
import '/core/index.dart';
import '/cubit/index.dart';
import '/models/index.dart';
import '/services/captcha_service.dart';
import '/widgets/index.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final _formKey = GlobalKey<FormState>();
  final _usernameController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isLoading = false;
  String? _errorMessage;
  String? _deviceId;

  @override
  Widget build(BuildContext context) {
    // Set screen size for responsive design
    CSpace.setScreenSize(context);

    return Scaffold(
      appBar: appBar(title: 'Auth.Login'.tr(), titleColor: Colors.white),
      body: Stack(alignment: Alignment.topCenter, children: [
        // Main content
        SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: CSpace.xl5),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                // Form section
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Form fields
                    WForm<MUser>(
                      key: _formKey,
                      list: _listFormItem,
                      onInit: (controllers) {
                        // Store controllers for validation
                      },
                    ),

                    // Error message if any
                    if (_errorMessage != null && _errorMessage!.isNotEmpty)
                      Container(
                        width: double.infinity,
                        margin: const EdgeInsets.only(bottom: CSpace.base),
                        padding: const EdgeInsets.all(CSpace.base),
                        decoration: BoxDecoration(
                          color: Colors.red.withAlpha(25),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Text(
                          _errorMessage!,
                          style: const TextStyle(
                            color: Colors.red,
                            fontSize: CFontSize.sm,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),

                    // Remember me and Forgot password row
                    WButton(
                      type: TextButton,
                      onPressed: () => context.pushNamed(CRoute.forgotPassword),
                      child: Text('${'pages.login.login.Forgot password'.tr()}?',
                          style: const TextStyle(
                            fontSize: CFontSize.base,
                            color: Colors.blue,
                            decoration: TextDecoration.underline,
                          )),
                    ),

                    // const VSpacer(CSpace.xl3),

                    // Login button with loading state
                    BlocConsumer<BlocC<MUser>, BlocS<MUser>>(
                      listenWhen: (previous, current) =>
                          current.status == AppStatus.success || current.status == AppStatus.fails,
                      listener: (context, state) async {
                        setState(() {
                          _isLoading = false;
                        });

                        if (state.status == AppStatus.success) {
                          await NotificationService.instance.initialize();
                          GoRouter.of(context).go(CRoute.home);
                        } else if (state.status == AppStatus.fails) {
                          setState(() {
                            _errorMessage = 'Login failed. Please try again.';
                          });
                        }
                      },
                      builder: (context, state) => Padding(
                          padding: const EdgeInsets.symmetric(vertical: CSpace.base),
                          child: SizedBox(
                            width: double.infinity,
                            height: 50,
                            child: DecoratedBox(
                              decoration: BoxDecoration(
                                gradient: const LinearGradient(
                                  colors: [
                                    Color(0xFFFF7143), // Cam đậm (deep orange)
                                    Color(0xFFFF9800), // Cam trung tính
                                  ],
                                  begin: Alignment.centerLeft,
                                  end: Alignment.centerRight,
                                ),
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: ElevatedButton(
                                key: const Key('loginButton'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.transparent,
                                  foregroundColor: Colors.white,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  elevation: 2,
                                ),
                                onPressed: _isLoading ? null : () => _login(context),
                                child: _isLoading
                                    ? const SizedBox(
                                        width: 24,
                                        height: 24,
                                        child: CircularProgressIndicator(
                                          strokeWidth: 2,
                                          valueColor: AlwaysStoppedAnimation<Color>(Colors.blue),
                                        ),
                                      )
                                    : Text('pages.login.login.Log in'.tr().toUpperCase(),style: const TextStyle(fontFamily: 'Oswald',fontSize: CFontSize.xl,fontWeight: FontWeight.bold),),
                              ),
                            ),
                          )),
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(vertical: CSpace.base),
                      child: SizedBox(
                        width: double.infinity,
                        height: 50,
                        child: DecoratedBox(
                          decoration: BoxDecoration(
                            gradient: const LinearGradient(
                              colors: [     Colors.indigoAccent,           // Màu bắt đầu
                              Colors.blue,
                              Colors.lightBlueAccent // Màu kết thúc
                              ],
                              begin: Alignment.centerLeft,
                              end: Alignment.centerRight,
                            ),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: ElevatedButton(
                            style: ElevatedButton.styleFrom(
                              backgroundColor: Colors.transparent,
                              shadowColor: Colors.transparent,
                              foregroundColor: Colors.white,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              elevation: 0,
                            ),
                            onPressed: _isLoading ? null : () => _loginWithFacebook(context),
                            child: Text(
                              'pages.login.login.log in Facebook'.tr().toUpperCase(),
                              style: const TextStyle(
                                fontSize: CFontSize.base,
                                fontWeight: FontWeight.bold,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      ),
                    ),
                    // Apple login button - tương tự iOS implementation
                    if (Platform.isIOS) // Only show on iOS like iOS repo
                      Padding(
                        padding: const EdgeInsets.symmetric(vertical: CSpace.base),
                        child: SizedBox(
                          width: double.infinity,
                          height: 50,
                          child: SignInWithAppleButton(
                            onPressed: _isLoading ? (){} :() => _loginWithApple(context),
                            text:   'pages.login.login.log in apple'.tr().toUpperCase(),
                            height: 50,
                            style: SignInWithAppleButtonStyle.black,
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),

                  ],
                ),


                // Social login section - tương tự Android và iOS
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: CSpace.base),
                  child: Row(
                    children: [
                      const Expanded(child: Divider(thickness: 1)),
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8),
                        child: Text(
                          'Or',
                          style: TextStyle(
                            fontSize: CFontSize.base,
                            color: Colors.grey[600],
                          ),
                        ),
                      ),
                      const Expanded(child: Divider(thickness: 1)),
                    ],
                  ),
                ),

                // Register option
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 0),
                  child: InkWell(
                    onTap: () => context.pushNamed(CRoute.register),
                    child: Text("pages.login.register.Do you already have an account?".tr(),
                        style: const TextStyle(color: Colors.black, fontSize: CFontSize.xl)),
                  ),
                ),
              ],
            ),
          ),
        ),

        // Loading overlay
        if (_isLoading)
          Container(
            color: Colors.black.withAlpha(75),
            child: const Center(
              child: WLoading(),
            ),
          ),
      ]),
    );
  }

  // Login function with captcha validation - tương tự Android LoginFragment.btnLogin
  void _login(BuildContext context) async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Show captcha dialog - tương tự Android BaseActivity.checkCaptcha
      final captchaToken = await CaptchaService.showCaptcha(context);

      if (captchaToken == null) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Captcha validation failed';
        });
        return;
      }

      // Validate form
      // if (_validateInputs()) {
      context.read<BlocC<MUser>>().submit(
            notification: false,
            format: MUser.fromJson,
            api: (value, page, size, sort) async {
              // Add device ID and captcha token
              // value['rememberMe'] = rememberMe;
              // if (_deviceId != null) {
              //   value['deviceId'] = _deviceId;ád
              // }

              // Make API call with captcha token
              return RepositoryProvider.of<Api>(context).auth.login(
                body: {...value, 'deviceId': _deviceId},
                // captchaToken: captchaToken,
              );
            },
            submit: (data) => context.read<AuthC>().save(data: data, context: context),
          );
      // } else {
      //   setState(() {

      _isLoading = false;
      //     _errorMessage = 'Please enter valid email and password';
      //   });
      // }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Login failed: $e';
      });
    }
  }

  // Facebook login function - tương tự Android và iOS implementation
  Future<void> _loginWithFacebook(BuildContext context) async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Track Facebook login begin - tương tự iOS Tracking.shared.authBegin(method: "facebook")
      print('🔵 Facebook login begin');

      // Logout any existing Facebook session first - tương tự iOS loginManager.logOut()
      await FacebookAuth.instance.logOut();

      // Perform Facebook login - tương tự iOS loginManager.logIn(permissions: ["public_profile", "email"])
      final LoginResult result = await FacebookAuth.instance.login(
        permissions: ['public_profile', 'email'],
      );

      if (result.status == LoginStatus.success) {
        // Get access token - tương tự iOS result?.token
        final AccessToken accessToken = result.accessToken!;

        // Get user data to check email permission - tương tự Android GraphRequest.newMeRequest
        final userData = await FacebookAuth.instance.getUserData(
          fields: "id,email,name",
        );

        print('📧 Facebook user data: $userData');

        // Check if email is available - tương tự iOS grantedPermissions.contains("email")
        if (userData['email'] == null || userData['email'].toString().isEmpty) {
          setState(() {
            _isLoading = false;
            _errorMessage = 'Không thể lấy email từ Facebook của bạn';
          });
          await FacebookAuth.instance.logOut();
          return;
        }

        // Call Facebook login API - tương tự Android presenter?.loginFacebook và iOS loginFB
        await _callFacebookLoginAPI(accessToken.token, context);
      } else if (result.status == LoginStatus.cancelled) {
        // User cancelled - tương tự iOS result.isCancelled
        print('🚫 Facebook login cancelled');
        setState(() {
          _isLoading = false;
        });
      } else {
        // Login failed - tương tự iOS error case
        print('❌ Facebook login failed: ${result.message}');
        setState(() {
          _isLoading = false;
          _errorMessage = 'Đăng nhập Facebook thất bại';
        });
      }
    } catch (e) {
      print('❌ Facebook login error: $e');
      setState(() {
        _isLoading = false;
        _errorMessage = 'Có lỗi xảy ra khi đăng nhập Facebook';
      });
    }
  }

  // Call Facebook login API with captcha - tương tự Android loginFacebook và iOS loginFB
  Future<void> _callFacebookLoginAPI(String token, BuildContext context) async {
    try {
// <<<<<<< HEAD
//       // Show captcha dialog for Facebook login - tương tự Android BaseActivity.checkCaptcha
//       final captchaToken = await CaptchaService.showCaptcha(context);
//
//       if (captchaToken == null) {
//         ScaffoldMessenger.of(context).showSnackBar(
//           const SnackBar(content: Text('Captcha validation failed')),
//         );
//         return;
//       }
//
//       context.read<BlocC<MUser>>().submit(
//             notification: false,
//             format: MUser.fromJson,
//             api: (value, page, size, sort) async {
//               // Call Facebook login API with captcha token
//               return RepositoryProvider.of<Api>(context).auth.loginFacebook(
//                     token: token,
//                     deviceId: _deviceId,
//                     // captchaToken: captchaToken,
//                   );
//             },
//             submit: (data) {
//               // Track successful login - tương tự iOS Tracking.shared.authComplete(method: "facebook")
//               print('✅ Facebook login successful');
//               context.read<AuthC>().save(data: data, context: context);
//             },
//           );
// =======
      final call =  await RepositoryProvider.of<Api>(context).auth.loginFacebook(
        token: token,
        deviceId: _deviceId,
      );
      if(call != null){
        context.read<AuthC>().save(data: call.data, context: context);
        context.pop();
      }
      // context.read<BlocC<MUser>>().submit(
      //       notification: false,
      //       format: MUser.fromJson,
      //       api: (value, page, size, sort) async {
      //         // Call Facebook login API - tương tự Android APIClient.shared.accountAPI.loginFacebook
      //         return RepositoryProvider.of<Api>(context).auth.loginFacebook(
      //               token: token,
      //               deviceId: _deviceId,
      //             );
      //       },
      //       submit: (data) {
      //         // Track successful login - tương tự iOS Tracking.shared.authComplete(method: "facebook")
      //         print('✅ Facebook login successful');
      //       },
      //     );
    } catch (e) {
      print('❌ Facebook API call failed: $e');
      setState(() {
        _isLoading = false;
        _errorMessage = 'Đăng nhập thất bại';
      });
    }
  }

  // Apple login function - tương tự iOS implementation
  Future<void> _loginWithApple(BuildContext context) async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      // Track Apple login begin - tương tự iOS Tracking.shared.authBegin(method: "apple")
      print('🍎 Apple login begin');

      // Check if Apple Sign In is available - tương tự iOS #available(iOS 13.0, *)
      if (!await SignInWithApple.isAvailable()) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Apple Sign In không khả dụng trên thiết bị này';
        });
        return;
      }

      // Perform Apple Sign In - tương tự iOS ASAuthorizationAppleIDProvider
      final credential = await SignInWithApple.getAppleIDCredential(
        scopes: [
          AppleIDAuthorizationScopes.email,
          AppleIDAuthorizationScopes.fullName,
        ],
      );

      print('🍎 Apple credential received: ${credential.identityToken}');

      // Extract user information - tương tự iOS appleIDCredential
      final String identityToken = credential.identityToken ?? '';
      final String userFirstName = credential.givenName ?? '';
      final String userLastName = credential.familyName ?? '';
      final String userEmail = credential.email ?? '';
      final String fullName = '$userFirstName $userLastName'.trim();

      print('🍎 Apple user info: $fullName, $userEmail');

      if (identityToken.isEmpty) {
        setState(() {
          _isLoading = false;
          _errorMessage = 'Không thể lấy thông tin từ Apple ID';
        });
        return;
      }

      // Call Apple login API - tương tự iOS loginApple
      await _callAppleLoginAPI(identityToken, fullName, userEmail, context);
    } on SignInWithAppleAuthorizationException catch (e) {
      print('❌ Apple authorization error: ${e.code} - ${e.message}');

      String errorMessage;
      switch (e.code) {
        case AuthorizationErrorCode.canceled:
          // User cancelled - don't show error, just stop loading
          return;
        case AuthorizationErrorCode.failed:
          errorMessage = 'Xác thực Apple ID thất bại. Vui lòng thử lại.';
          break;
        case AuthorizationErrorCode.invalidResponse:
          errorMessage = 'Phản hồi từ Apple không hợp lệ. Vui lòng thử lại.';
          break;
        case AuthorizationErrorCode.notHandled:
          errorMessage = 'Yêu cầu không được xử lý. Vui lòng thử lại.';
          break;
        case AuthorizationErrorCode.unknown:
          errorMessage = 'Lỗi không xác định. Vui lòng kiểm tra cài đặt Apple ID và thử lại.';
          break;
        default:
          errorMessage = 'Đăng nhập Apple thất bại. Vui lòng thử lại.';
      }

      setState(() {
        _errorMessage = errorMessage;
      });
    } catch (e) {
      print('❌ Apple login error: $e');
      setState(() {
        _errorMessage = 'Có lỗi xảy ra khi đăng nhập Apple. Vui lòng thử lại.';
      });
    } finally {
      // Đảm bảo _isLoading luôn được tắt
      setState(() {
        _isLoading = false;
      });
    }
  }

  // Call Apple login API with captcha - tương tự iOS loginApple
  Future<void> _callAppleLoginAPI(String token, String fullName, String email, BuildContext context) async {
    try {
      // Show captcha dialog for Apple login
      final captchaToken = await CaptchaService.showCaptcha(context);

      if (captchaToken == null) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Captcha validation failed')),
        );
        return;
      }

      context.read<BlocC<MUser>>().submit(
            notification: false,
            format: MUser.fromJson,
            api: (value, page, size, sort) async {
              // Call Apple login API with captcha token
              return RepositoryProvider.of<Api>(context).auth.loginApple(
                    token: token,
                    fullName: fullName,
                    email: email,
                    deviceId: _deviceId,
                    // captchaToken: captchaToken,
                  );
            },
            submit: (data) {
              // Track successful login - tương tự iOS Tracking.shared.authComplete(method: "apple")
              print('✅ Apple login successful');
              context.read<AuthC>().save(data: data, context: context);
            },
          );
    } catch (e) {
      print('❌ Apple API call failed: $e');
      setState(() {
        _isLoading = false;
        _errorMessage = 'Đăng nhập thất bại';
      });
    }
  }

  // Validate form inputs
  // bool _validateInputs() {
  //   // Basic validation
  //   if (_listFormItem.isEmpty) return false;
  //
  //   // Check if username and password are not empty
  //   final usernameItem = _listFormItem.firstWhere((item) => item.name == 'username', orElse: () => MFormItem());
  //   final passwordItem = _listFormItem.firstWhere((item) => item.name == 'password', orElse: () => MFormItem());
  //
  //   if (usernameItem.value == null || usernameItem.value.toString().isEmpty) {
  //     setState(() {
  //       _errorMessage = 'Email cannot be empty';
  //     });
  //     return false;
  //   }
  //
  //   if (passwordItem.value == null || passwordItem.value.toString().isEmpty) {
  //     setState(() {
  //       _errorMessage = 'Password cannot be empty';
  //     });
  //     return false;
  //   }
  //
  //   return true;
  // }

  @override
  void initState() {
    _init();
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _getDeviceInfo();
  }

  @override
  void dispose() {
    _usernameController.dispose();
    _passwordController.dispose();
    super.dispose();
  }

  // Get device ID for API request
  Future<void> _getDeviceInfo() async {
    try {
      final deviceInfo = DeviceInfoPlugin();
      if (mounted) {
        if (Theme.of(context).platform == TargetPlatform.iOS) {
          final iosInfo = await deviceInfo.iosInfo;
          setState(() {
            _deviceId = iosInfo.identifierForVendor;
          });
        } else if (Theme.of(context).platform == TargetPlatform.android) {
          final androidInfo = await deviceInfo.androidInfo;
          setState(() {
            _deviceId = androidInfo.id;
          });
        }
      }
    } catch (e) {
      // If we can't get device ID, we'll proceed without it
      print('Error getting device ID: $e');
    }
  }

  bool rememberMe = false;
  List<MFormItem> _listFormItem = [];

  Future<void> _init() async {
    _listFormItem = [
      MFormItem(
        name: 'username',
        hintText: 'Email hoặc tên đăng nhập',
        value: '',
        icon: 'assets/form/mail.svg',
        isWantBackgroundTran: false,
        // onChange: (value, controllers) {
        //   setState(() {
        //     _errorMessage = null;
        //   });
        // },
      ),
      MFormItem(
        name: 'password',
        value: '',
        hintText: 'pages.login.login.Password'.tr(),
        icon: 'assets/form/password.svg',
        isWantBackgroundTran: false,
        password: true,
        // onChange: (value, controllers) {
        //   setState(() {
        //     _errorMessage = null;
        //   });
        // },
      ),
    ];
  }
}

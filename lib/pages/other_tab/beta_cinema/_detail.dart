import 'package:easy_localization/easy_localization.dart';
import 'package:flutter/material.dart';
import 'package:flutter_app/pages/promotion/index.dart';
import 'package:flutter_app/pages/voucher/api/api_test.dart';
import 'package:flutter_app/service/location_service.dart';
import 'package:flutter_app/widgets/index.dart';
import 'package:fl_location/fl_location.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_inappwebview/flutter_inappwebview.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../../constants/index.dart';
import '../../../models/project/promotion.dart';
import '../../../utils/src/api.dart';
import '../../Movie_schedule/widget/news_and_deals_card.dart';
import '../../cinema/_detail.dart';
import '../../cinema/model/cinema_model.dart';
String localize(String key) {
  // Implement localization logic here
  Map<String, String> translations = {
    "CinemaDetail.Title": "Chi tiết rạp",
    "ticket_price": "Giá vé",
    "show": "Lịch chiếu",
    "call_now": "Gọi ngay",
    "Address": "Địa chỉ",
    "OpenInGoogleMap": "Mở bằng Google Maps",
    "OpenInAppleMap": "Mở bằng Bản đồ Apple",
    "RouteInMap.Title": "Chỉ đường",
    "Bt.Cancel": "Huỷ",
    // Thêm các key khác
  };
  return translations[key] ?? key;
}
// --- Hết phần giả định hàm dịch ---

class CinemaDetailScreen extends StatefulWidget {
  final Cinema? initialCinema;

  const CinemaDetailScreen({super.key, this.initialCinema});

  @override
  _CinemaDetailScreenState createState() => _CinemaDetailScreenState();
}

class _CinemaDetailScreenState extends State<CinemaDetailScreen> {
  Cinema? _cinema;
  List<PromotionItem> _promotions = [];
  bool _isLoading = true;
  GoogleMapController? _mapController;
  Set<Marker> _markers = {};
  final LocationService _locationService = LocationService();
  Location? _userPosition;
  bool _isLoadingLocation = false;

  // Cache for price content to avoid reloading
  String? _cachedPriceContent;
  bool _isPriceContentLoading = false;

  // Giả định các service gọi API
  // final CinemaProvider _cinemaProvider = CinemaProvider();
  // final EcmProvider _ecmProvider = EcmProvider();

  @override
  void initState() {
    super.initState();
    _cinema = widget.initialCinema;
    _fetchData();
    _getUserLocation();
  }

  Future<void> _getUserLocation() async {
    setState(() {
      _isLoadingLocation = true;
    });

    try {
      _userPosition = await _locationService.determinePosition(
        showError: false,
        context: context,
      );

      // Calculate distance if coordinates are available
      if (_userPosition != null && _cinema != null && _cinema!.hasValidCoordinates) {
        _cinema!.distance = _locationService.calculateDistance(
          _userPosition!.latitude,
          _userPosition!.longitude,
          _cinema!.latitudeAsDouble!,
          _cinema!.longitudeAsDouble!,
        );
        setState(() {}); // Update UI with distance
      }
    } catch (e) {
      print('Error getting location: $e');
    } finally {
      setState(() {
        _isLoadingLocation = false;
      });
    }
  }

  Future<void> _fetchData() async {
    setState(() {
      _isLoading = true;
      // FlutterEasyLoading.show(status: 'Loading...'); // Hiển thị loading
    });

    try {
      // Nếu cinema ban đầu không đủ thông tin chi tiết
      if (_cinema?.newsId == null && _cinema?.cinemaId != null) {
        // Giả định hàm getCinemaDetail trả về Future<CinemaModel>
        // final detailedCinema = await _cinemaProvider.getCinemaDetail(_cinema!.cinemaId!);
        // setState(() {
        //   _cinema = detailedCinema;
        // });
      }

      // Load promotions exactly like iOS getPromotionCategory() -> getListPromotion()
      await _loadPromotions();

      _updateUI(); // Cập nhật UI sau khi có dữ liệu
    } catch (e) {
      // Xử lý lỗi
      print("Error fetching data: $e");
      // FlutterEasyLoading.showError('Failed to load data');
    } finally {
      setState(() {
        _isLoading = false;
        // FlutterEasyLoading.dismiss(); // Ẩn loading
      });
    }
  }

  void _updateUI() {
    if (_cinema == null) return;

    // --- Cập nhật Map ---
    final lat = double.tryParse(_cinema?.latitude ?? '');
    final lng = double.tryParse(_cinema?.longitude ?? '');

    if (lat != null && lng != null) {
      final position = LatLng(lat, lng);
      _markers = {
        Marker(
          markerId: MarkerId(_cinema?.cinemaId ?? 'cinema_marker'),
          position: position,
          infoWindow: InfoWindow(
            title: _cinema?.name ?? '',
            snippet: _cinema?.address ?? '',
          ),
        )
      };
      _mapController?.animateCamera(
        CameraUpdate.newCameraPosition(
          CameraPosition(target: position, zoom: 17.0),
        ),
      );
    }
    setState(() {}); // Cập nhật lại UI với dữ liệu mới
  }

  void _onMapCreated(GoogleMapController controller) {
    _mapController = controller;
    _updateUI(); // Cập nhật marker khi map sẵn sàng
  }

  // --- Xử lý sự kiện nhấn nút ---
  void _onTicketPricePressed() {
    // Show price popup like Android CinemaPriceFragment
    _showPricePopup();
  }

  void _onSessionTimePressed() {
    // Navigate to cinema booking like Android BookByCinemaFragment
    _navigateToCinemaBooking();
  }

  // Show price popup like Android PopupFragment.getInstance(CinemaPriceFragment)
  void _showPricePopup() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context1) => Container(
        height: MediaQuery.of(context).size.height * 0.8,
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Giá vé',
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  IconButton(
                    onPressed: () => Navigator.pop(context),
                    icon: const Icon(Icons.close),
                  ),
                ],
              ),
            ),
            // WebView content - exactly like iOS CinemaPriceViewController
            Expanded(
              child: _buildPriceWebView(),
            ),
          ],
        ),
      ),
    );
  }

  // Navigate to cinema booking like Android BookByCinemaFragment
  void _navigateToCinemaBooking() {
    if (_cinema?.cinemaId != null) {
      Navigator.pushReplacement(context, MaterialPageRoute(builder: (context) => ChooseCinemasView(cinema: _cinema ?? Cinema(),),));
    }
  }

  // Build price webview with cached content
  Widget _buildPriceWebView() {
    // Load content only once when first opened
    if (_cachedPriceContent == null && !_isPriceContentLoading) {
      _isPriceContentLoading = true;
      _loadPriceContent().then((content) {
        if (mounted) {
          setState(() {
            _cachedPriceContent = content;
            _isPriceContentLoading = false;
          });
        }
      }).catchError((error) {
        if (mounted) {
          setState(() {
            _cachedPriceContent = '';
            _isPriceContentLoading = false;
          });
        }
      });
    }

    // Show loading while first load
    if (_isPriceContentLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    // Show cached content
    final htmlContent = _cachedPriceContent ?? '';
    if (htmlContent.isEmpty) {
      return const Center(
        child: Text(
          'Không có thông tin giá vé',
          style: TextStyle(fontSize: 16, color: Colors.grey),
        ),
      );
    }

    // Use InAppWebView exactly like iOS WKWebView.loadHTMLString()
    return InAppWebView(
      key: const ValueKey('cinema_price_webview'),
      initialData: InAppWebViewInitialData(
        data: htmlContent,
        baseUrl: WebUri(ApiService.baseUrl),
      ),
      initialSettings: InAppWebViewSettings(
        // iOS-like WebView settings for price display
        javaScriptEnabled: true,
        domStorageEnabled: true,
        useWideViewPort: true,
        loadWithOverviewMode: true,
        supportZoom: true,
        builtInZoomControls: false,
        displayZoomControls: false,

        // Text scaling like Android textZoom = 200
        textZoom: 120,

        // Security and compatibility
        mixedContentMode: MixedContentMode.MIXED_CONTENT_ALWAYS_ALLOW,
        allowsInlineMediaPlayback: true,
        mediaPlaybackRequiresUserGesture: false,

        // Performance
        cacheEnabled: true,
        clearCache: false,
      ),
      onWebViewCreated: (controller) {
        print('🔗 Cinema Price WebView created');
      },
      onLoadStart: (controller, url) {
        print('🔄 Cinema Price WebView started loading: $url');
      },
      onLoadStop: (controller, url) async {
        print('✅ Cinema Price WebView finished loading: $url');

        // Debug: Print HTML content length and first 200 characters
        print('📄 Cinema Price: HTML content length = ${htmlContent.length}');
        if (htmlContent.length > 200) {
          print('📄 Cinema Price: HTML preview = ${htmlContent.substring(0, 200)}...');
        } else {
          print('📄 Cinema Price: HTML content = $htmlContent');
        }

        // Check if content is actually loaded in webview
        try {
          final bodyContent = await controller.evaluateJavascript(source: "document.body.innerHTML;");
          print('📄 Cinema Price: WebView body content length = ${bodyContent?.toString().length ?? 0}');
        } catch (e) {
          print('❌ Cinema Price: Error checking webview content: $e');
        }
      },
      onLoadError: (controller, url, code, message) {
        print('❌ Cinema Price WebView load error: $message');
      },
      onConsoleMessage: (controller, consoleMessage) {
        print('🖥️ Cinema Price WebView Console: ${consoleMessage.message}');
      },
    );
  }



  // Load price content like Android CinemaPricePresenter.getPrivacyPolicy()
  Future<String> _loadPriceContent() async {
    final prefs = await SharedPreferences.getInstance();
    final isEnglish = prefs.getBool('isEnglish') ?? false;

    try {
      // Step 1: Get policy ID like Android getSecurityId("mobile:app:giave:$lang")
      final policyIdResult = await RepositoryProvider.of<Api>(context).other.getPolicyId(
        policyType: isEnglish ? 'mobile:app:giave:en' : "mobile:app:giave:vi",
      );

      if (policyIdResult != null ) {
        final policyId = policyIdResult.data['ParameterValue'];

        if (policyId != null) {
          // Step 2: Get policy content like Android getNewWithId(securityId)
          final contentResult = await RepositoryProvider.of<Api>(context).other.getPolicyContent(
            id: policyId,
          );

          if (contentResult != null) {
            final contentData = contentResult.data;

            // Extract HTML content from response
            if (contentData != null) {
              // Try different possible content fields
              String htmlContent = '';

              // Check for detailed content structure
              if (contentData['Noi_dung_chi_tiet'] != null &&
                  contentData['Noi_dung_chi_tiet'] is List &&
                  contentData['Noi_dung_chi_tiet'].isNotEmpty) {
                final paragraphData = contentData['Noi_dung_chi_tiet'][0]['ParagraphData'];
                if (paragraphData != null && paragraphData['ParagraphContent'] != null) {
                  htmlContent = paragraphData['ParagraphContent'];
                }
              }
              // Return HTML content or default message
              if (htmlContent.isNotEmpty) {
                return htmlContent;
              }
            }
          }
        }
      }

      // Fallback: Return default price content if API fails
      return '''
        <div style="padding: 20px; font-family: Arial, sans-serif;">
          <h2 style="color: #333; text-align: center;">Bảng Giá Vé Beta Cinemas</h2>
          <p style="text-align: center; color: #666;">Thông tin giá vé sẽ được cập nhật sớm nhất.</p>
          <div style="margin-top: 20px;">
            <p><strong>Lưu ý:</strong></p>
            <ul>
              <li>Giá vé có thể thay đổi theo từng rạp và thời gian chiếu</li>
              <li>Vui lòng liên hệ rạp để biết thông tin chi tiết</li>
            </ul>
          </div>
        </div>
      ''';

    } catch (e) {
      print('Error loading price content: $e');
      // Return error message as HTML
      return '''
        <div style="padding: 20px; text-align: center; color: #666;">
          <h3>Không thể tải thông tin giá vé</h3>
          <p>Vui lòng thử lại sau hoặc liên hệ rạp để biết thông tin chi tiết.</p>
          <p style="font-size: 12px; color: #999;">Lỗi: ${e.toString()}</p>
        </div>
      ''';
    }
  }

  void _onCallPressed() async {
    final phoneNumber = _cinema?.phoneNumber?.replaceAll(' ', '');
    if (phoneNumber != null) {
      final url = 'tel:$phoneNumber';
      if (await canLaunchUrl(Uri.parse(url))) {
        await launchUrl(Uri.parse((url)));
      } else {
        // Xử lý lỗi không thể gọi
        print('Could not launch $url');
      }
    }
  }

  void _onGuidePressed() async {
    final lat = double.tryParse(_cinema?.latitude ?? '');
    final lng = double.tryParse(_cinema?.longitude ?? '');

    if (lat == null || lng == null) return;

    final appleUrl = 'maps://?daddr=$lat,$lng&dirflg=d';
    final googleUrl = 'comgooglemaps://?daddr=$lat,$lng&directionsmode=driving';
    final googleWebUrl = 'https://www.google.com/maps/dir/?api=1&destination=$lat,$lng&travelmode=driving';

    bool canGoogle = await canLaunchUrl(Uri.parse(googleUrl));
    bool canApple = await canLaunchUrl(Uri.parse(appleUrl));

    if (canGoogle && canApple) {
      showModalBottomSheet(
        context: context,
        builder: (context) => SafeArea(
          child: Wrap(
            children: <Widget>[
              ListTile(
                leading: const Icon(Icons.map), // Thay icon phù hợp
                title: Text(localize('OpenInGoogleMap')),
                onTap: () async {
                  await launchUrl(Uri.parse(googleUrl));
                  Navigator.pop(context);
                },
              ),
              ListTile(
                leading: const Icon(Icons.map_outlined), // Thay icon phù hợp
                title: Text(localize('OpenInAppleMap')),
                onTap: () async {
                  await launchUrl(Uri.parse(appleUrl));
                  Navigator.pop(context);
                },
              ),
              ListTile(
                title: Text(localize('Bt.Cancel')),
                onTap: () => Navigator.pop(context),
              ),
            ],
          ),
        ),
      );
    } else if (canApple) {
      await launchUrl(Uri.parse(appleUrl));
    } else if (canGoogle) {
      await launchUrl(Uri.parse(googleUrl));
    } else {
      // Mở Google Maps trên web nếu không có app nào
      if (await canLaunchUrl(Uri.parse(googleWebUrl))) {
        await launchUrl(Uri.parse(googleWebUrl));
      } else {
        print('Could not launch any map application');
      }
    }
  }

  // Load promotions exactly like iOS getPromotionCategory() -> getListPromotion()
  Future<void> _loadPromotions() async {
    try {
      print('🔄 Cinema Detail: Loading promotions...');

      // Step 1: Get promotion category like iOS getNewEvent()
      final api = RepositoryProvider.of<Api>(context);
      final categoryResponse = await api.promotion.getNewEvent();

      if (categoryResponse != null && categoryResponse.data != null) {
        final categoryData = categoryResponse.data['content'] ;

        if (categoryData != null && categoryData.isNotEmpty) {
          final firstCategory = categoryData.first;
          final categoryId = firstCategory['CategoryId'];

          if (categoryId != null) {
            print('📂 Cinema Detail: Found category ID: $categoryId');

            // Step 2: Get promotions for category like iOS getNewForCategory()
            final promotionsResponse = await api.promotion.getItemsByCategory(
              categoryId: categoryId,
              pageNumber: 1,
               pageSize: 4,
            );

            if (promotionsResponse != null && promotionsResponse.data != null) {
              final promotionsData = promotionsResponse.data['content'] ;

              if (promotionsData != null) {
                // final promotionsList = promotionsData
                //     .map((json) => PromotionItem.fromJson(json))
                //     .toList();
                final promotionsList = promotionsData
                    .map<PromotionItem>((item) => PromotionItem.fromJson(item as Map<String, dynamic>))
                    .toList();
                setState(() {
                  _promotions = promotionsList;
                });

                print('✅ Cinema Detail: Loaded ${_promotions.length} promotions');
              }
            }
          }
        }
      }
    } catch (e) {
      print('❌ Cinema Detail: Error loading promotions: $e');
      setState(() {
        _promotions = [];
      });
    }
  }

  // --- Xử lý delegate từ NewsAndDealsCard - exactly like iOS NewsAndDealsViewDelegate ---
  void _onPromotionSelected(PromotionItem news) {
    // Navigate to news detail exactly like iOS newsView(_:didSelected:)
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => NewsDetailScreen(item: news)
      )
    );
    print("✅ Cinema Detail: Navigate to News Detail for: ${news.title}");
  }

  void _onShowAllPromotions() {
    // Navigate to news and deals list exactly like iOS newsViewDidShowAll()
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (_) => const PromotionPage(isHomePage: false,)
      )
    );
    print("✅ Cinema Detail: Navigate to News and Deals Screen");
  }

  Widget _buildActionButton(IconData icon, String label, VoidCallback onPressed) {
    return InkWell(
      onTap: onPressed,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: Colors.blueAccent),
          const SizedBox(height: 4),
          Text(label, style: const TextStyle(fontSize: 13)),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _isLoading && _cinema == null
          ? const Center(child: CircularProgressIndicator())
          : CustomScrollView(
              slivers: [
                SliverAppBar(
                  expandedHeight: 250,
                  pinned: true,
                  backgroundColor: Colors.transparent,
                  elevation: 0,
                  leading: IconButton(
                    icon: const Icon(Icons.arrow_back_ios_new, color: Colors.white),
                    onPressed: () => Navigator.of(context).pop(),
                    padding: EdgeInsets.zero,
                  ),
                  centerTitle: false,
                  title: Text(
                    "ListCinema.Title".tr(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: CFontSize.xl2,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  flexibleSpace: FlexibleSpaceBar(
                    background: Stack(
                      fit: StackFit.expand,
                      children: [
                        _cinema?.picture != null
                            ? imageNetwork(
                                url: '${ApiService.baseUrlImage}/${_cinema?.picture ?? ''}',
                                fit: BoxFit.cover,
                              )
                            : Container(color: Colors.grey[300]),
                        // Lớp phủ làm tối ảnh
                        Container(
                          decoration: const BoxDecoration(
                            gradient: LinearGradient(
                              colors: [Colors.black54, Colors.transparent],
                              end: Alignment.bottomCenter,
                              begin: Alignment.topCenter,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                SliverList(
                  delegate: SliverChildListDelegate([
                    // const SizedBox(height: 16),
                    Container(
                      color: Colors.black45,
                      padding: const EdgeInsets.symmetric(horizontal: CSpace.sm, vertical: CSpace.xl),
                      child: Center(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              _cinema?.name ?? "",
                              style: const TextStyle(fontSize: 24, fontWeight: FontWeight.bold, color: Colors.white),
                            ),
                            if (_cinema?.formattedDistance != null)
                              Padding(
                                padding: const EdgeInsets.only(top: 8.0),
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    const Icon(
                                      Icons.location_on,
                                      size: 16,
                                      color: Colors.red,
                                    ),
                                    const SizedBox(width: 4),
                                    Text(
                                      'Cách bạn ${_cinema!.formattedDistance!}',
                                      style: const TextStyle(
                                        fontSize: 14,
                                        color: Colors.white,
                                      ),
                                    ),
                                    if (_isLoadingLocation)
                                      Padding(
                                        padding: const EdgeInsets.only(left: 8.0),
                                        child: SizedBox(
                                          width: 12,
                                          height: 12,
                                          child: CircularProgressIndicator(
                                            strokeWidth: 2,
                                            color: Colors.white.withAlpha(150),
                                          ),
                                        ),
                                      ),
                                  ],
                                ),
                              ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(height: 16),
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16.0),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                        children: [
                          _buildActionButton(
                              Icons.confirmation_number, localize('ticket_price'), _onTicketPricePressed),
                          _buildActionButton(Icons.schedule, localize('show'), _onSessionTimePressed),
                          _buildActionButton(Icons.phone, localize('call_now'), _onCallPressed),
                        ],
                      ),
                    ),

                    const Divider(height: 32),

                    // --- Bản đồ ---
                    SizedBox(
                      height: 200,
                      child: GoogleMap(
                        onMapCreated: _onMapCreated,
                        initialCameraPosition: CameraPosition(
                          target: LatLng(
                            double.tryParse(_cinema?.latitude ?? '0') ?? 0,
                            double.tryParse(_cinema?.longitude ?? '0') ?? 0,
                          ),
                          zoom: 17.0,
                        ),
                        markers: _markers,
                        myLocationButtonEnabled: false,
                        zoomControlsEnabled: false,
                      ),
                    ),

                    // --- Địa chỉ ---
                    Padding(
                      padding: const EdgeInsets.all(CSpace.base),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(localize("Address"), style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16)),
                          const SizedBox(height: 4),
                          Text(_cinema?.address ?? '...', style: const TextStyle(fontSize: 15)),
                          TextButton.icon(
                            onPressed: _onGuidePressed,
                            icon: const Icon(Icons.directions),
                            label: Text(localize('RouteInMap.Title')),
                          ),
                        ],
                      ),
                    ),

                    const Divider(),

                    // --- Khuyến mãi - exactly like iOS NewAndDealsView ---
                    if (_promotions.isNotEmpty) ...[
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text("Promotion.Title".tr(), style: const TextStyle(fontWeight: FontWeight.bold, fontSize: CFontSize.xl)),
                            TextButton(
                              onPressed: _onShowAllPromotions,
                              child: Text("Promotion.ViewAll".tr(), style: const TextStyle(color: Colors.blue)),
                            ),
                          ],
                        ),
                      ),
                      // Use NewsAndDealsCard exactly like iOS NewAndDealsView
                      NewsAndDealsCard(
                        data: _promotions,
                        onItemSelected: _onPromotionSelected,
                        onShowAll: _onShowAllPromotions,
                      ),
                    ],

                    const SizedBox(height: 10),
                  ]),
                ),
              ],
            ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_app/core/index.dart';
import 'package:flutter_app/pages/other_tab/setting/faq_screen.dart';
import 'package:flutter_app/pages/other_tab/setting/policy_screen.dart';
import 'package:flutter_app/pages/other_tab/setting/version_screen.dart';
import 'package:flutter_app/pages/other_tab/setting/widget/check_box_widget.dart';
import 'package:flutter_app/service/language_service.dart';
import 'package:flutter_app/utils/index.dart';
import 'package:flutter_app/widgets/language_switcher.dart';
import 'package:go_router/go_router.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../../../constants/index.dart';

class SettingScreen extends StatefulWidget {
  const SettingScreen({super.key});

  @override
  State<SettingScreen> createState() => SettingScreenState();
}

class SettingScreenState extends State<SettingScreen> {
  bool _notificationsEnabled = true;
  bool _locationEnabled = false; // ✅ Add location setting like iOS/Android
  String _appVersion = '1.0.0';

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();

    // Get app version like iOS/Android
    final packageInfo = await PackageInfo.fromPlatform();

    // Check location permission status like iOS/Android
    final locationStatus = await Permission.location.status;

    setState(() {
      _notificationsEnabled = prefs.getBool('notificationsEnabled') ?? true;
      _locationEnabled = locationStatus.isGranted; // ✅ Load location setting like iOS/Android
      _appVersion = packageInfo.version; // ✅ Get real version like iOS/Android
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: appBar(title: 'Setting.Title'.tr(), titleColor: Colors.white),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildLanguageSection(),
          const SizedBox(height: 16),
          _buildOtherSection(),
        ],
      ),
    );
  }

  Widget _buildLanguageSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: Text(
            'Setting.Language'.tr(),
            style: Theme.of(context).textTheme.bodyLarge,
          ),
        ),
        // Option 1: Use the original checkbox style
        ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Column(
            children: [
              CheckboxCell(
                title: 'Setting.VietNam'.tr(),
                isChecked: context.locale == const Locale('vi'),
                isTop: true,
                onTap: () => _changeLanguage(false,context),
              ),
              const Divider(height: 1),
              CheckboxCell(
                title: 'Setting.English'.tr(),
                isChecked: context.locale == const Locale('en'),
                isBottom: true,
                onTap: () => _changeLanguage(true,context),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildOtherSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(bottom: 8),
          child: Text(
            'pages.login.register.Other'.tr(),
            style: Theme.of(context).textTheme.bodyLarge,
          ),
        ),
        ClipRRect(
          borderRadius: BorderRadius.circular(8),
          child: Column(
            children: [
              SwitchCell(
                title: 'Setting.Notify'.tr(),
                value: _notificationsEnabled,
                onChanged: (value) => _toggleNotifications(value),
              ),
              const Divider(height: 1),
              SwitchCell(
                title: 'Setting.Location'.tr(), // ✅ Add location setting like iOS/Android
                value: _locationEnabled,
                onChanged: (value) {
                  print('🔄 Location switch onChanged called with: $value');
                  // Simple test - just toggle state
                  setState(() {
                    _locationEnabled = value;
                  });
                  print('✅ Location state updated to: $_locationEnabled');

                  // Call async function separately to avoid blocking
                  _handleLocationPermissions(value);
                },
              ),
              const Divider(height: 1),
              SettingCell(
                title: 'Setting.FAQ'.tr(),
                onTap: () => Navigator.push(
                  context,
                  MaterialPageRoute(builder: (context) => const FAQScreen()),
                ),
              ),
              const Divider(height: 1),
              SettingCell(
                title: 'Setting.Version'.tr(),
                onTap: () => Navigator.push(context, MaterialPageRoute(builder: (context) => const VersionInfoScreen(),)),
                content: _appVersion,
              ),
              const Divider(height: 1),
              SettingCell(
                title: 'Setting.Policy'.tr(),
                onTap: () => _showPolicy('privacy'),
              ),
              const Divider(height: 1),
              SettingCell(
                title: 'Setting.PaymentPolicy'.tr(),
                onTap: () => _showPolicy('payment'),
              ),
              const Divider(height: 1),
              SettingCell(
                title: 'Setting.SecurePolicy'.tr(),
                onTap: () => _showPolicy('security'),
              ),
              const Divider(height: 1),
              SettingCell(
                title: 'Setting.CompanyInfo'.tr(),
                onTap: () => _showCompanyInfo(),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Future<void> _changeLanguage(bool isEnglish,BuildContext context) async {
    final languageService = LanguageService();
    final languageCode = isEnglish ? 'en' : 'vi';

    // Save language preference and update app locale
    await languageService.setLanguage(context, languageCode);

    // Also save to the old preference key for backward compatibility
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('isEnglish', isEnglish);

    // Show a dialog to confirm the language change
    if (mounted) {
      _showLanguageChangedDialog(isEnglish,context);
    }
  }

  void _showLanguageChangedDialog(bool isEnglish,BuildContext context1) {
    UDialog().showSuccess(
        text: isEnglish ? 'Language has been changed to English' : 'Ngôn ngữ đã được chuyển sang Tiếng Việt',
        title: 'Setting.Language'.tr()).then((value) => context1.goNamed(CRoute.splash),);
  }

  Future<void> _toggleNotifications(bool value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('notificationsEnabled', value);
    setState(() {
      _notificationsEnabled = value;
    });

    // Request notification permissions if enabling notifications
    if (value) {
      // This would typically use a plugin like firebase_messaging or flutter_local_notifications
      // to request notification permissions
      // For now, we'll just show a dialog to inform the user
      if (mounted) {
        showDialog(
          context: context,
          builder: (context) => AlertDialog(
            title: Text('Setting.Notify'.tr()),
            content: Text(
              context.locale.languageCode == 'en'
                  ? 'You will now receive notifications from the app.'
                  : 'Bạn sẽ nhận được thông báo từ ứng dụng.',
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text('Bt.OK'.tr()),
              ),
            ],
          ),
        );
      }
    }
  }

  /// Handle location permissions separately from UI update
  Future<void> _handleLocationPermissions(bool value) async {
    try {
      print('🔄 Handling location permissions for value: $value');

      // Save to preferences
      final prefs = await SharedPreferences.getInstance();
      await prefs.setBool('locationEnabled', value);

      // If enabling, check permissions
      if (value) {
        print('📍 Checking location permissions...');
        _checkLocationPermissions();
      }
    } catch (e) {
      print('❌ Error in _handleLocationPermissions: $e');
    }
  }

  /// Check location permissions separately
  Future<void> _checkLocationPermissions() async {
    try {
      final status = await Permission.location.status;
      print('📍 Current permission status: $status');

      switch (status) {
        case PermissionStatus.granted:
          print('✅ Permission already granted');
          break;

        case PermissionStatus.denied:
          print('⚠️ Permission denied, requesting...');
          final result = await Permission.location.request();
          print('📍 Permission request result: $result');
          if (!result.isGranted) {
            // If user denied, turn off the switch
            setState(() {
              _locationEnabled = false;
            });
            final prefs = await SharedPreferences.getInstance();
            await prefs.setBool('locationEnabled', false);
          }
          break;

        case PermissionStatus.permanentlyDenied:
          print('❌ Permission permanently denied, showing dialog');
          _showLocationPermissionDialog();
          // Turn off the switch
          setState(() {
            _locationEnabled = false;
          });
          final prefs = await SharedPreferences.getInstance();
          await prefs.setBool('locationEnabled', false);
          break;

        default:
          print('❓ Unknown permission status: $status');
          break;
      }
    } catch (e) {
      print('❌ Error checking permissions: $e');
    }
  }

  /// Show location permission dialog like iOS LocationManager.openLocationAlert
  void _showLocationPermissionDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Location.PermissionTitle'.tr()),
        content: Text('Location.PermissionMessage'.tr()),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text('Common.Cancel'.tr()),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              openAppSettings(); // Open device settings like iOS
            },
            child: Text('Common.Settings'.tr()),
          ),
        ],
      ),
    );
  }

  void _showPolicy(String type) {
    PolicyType policyType;

    switch (type) {
      case 'privacy':
        policyType = PolicyType.terms;
        break;
      case 'payment':
        policyType = PolicyType.payment;
        break;
      case 'security':
        policyType = PolicyType.security;
        break;
      default:
        policyType = PolicyType.terms;
    }

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PolicyScreen(policyType: policyType),
      ),
    );
  }

  void _showCompanyInfo() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const PolicyScreen(policyType: PolicyType.companyInfo),
      ),
    );
  }
}

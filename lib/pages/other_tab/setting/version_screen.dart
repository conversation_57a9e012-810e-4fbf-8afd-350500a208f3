import 'package:flutter/material.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:easy_localization/easy_localization.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../../core/index.dart';
import '../../../models/project/app_params.dart';
import '../../../service/version_manager.dart';
import '../../../widgets/update_dialog.dart';
import '../../../utils/index.dart';

class VersionInfoScreen extends StatefulWidget {
  const VersionInfoScreen({super.key});

  @override
  _VersionInfoScreenState createState() => _VersionInfoScreenState();
}

class _VersionInfoScreenState extends State<VersionInfoScreen> {
  String _currentVersion = '';
  String? _latestVersion;
  VersionStatus _status = VersionStatus.upToDate;
  bool _isLoading = false;
  String? _error;

  // Services
  late final _api = RepositoryProvider.of<Api>(context);
  final _versionManager = VersionManager();

  @override
  void initState() {
    super.initState();
    _getVersionInfo();
    _checkForUpdates();
  }

  Future<void> _getVersionInfo() async {
    final packageInfo = await PackageInfo.fromPlatform();
    setState(() {
      _currentVersion = packageInfo.version;
    });
  }

  Future<void> _checkForUpdates() async {
    setState(() {
      _isLoading = true;
      _error = null;
    });

    try {
      final result = await _versionManager.checkForUpdates(_api);

      setState(() {
        _status = result.status;
        _latestVersion = result.latestVersion;
        _error = result.error;
        _isLoading = false;
      });

      // Show update dialog if update is available and not dismissed
      if (result.hasUpdate && result.latestVersion != null) {
        final wasDismissed = await _versionManager.wasVersionCheckDismissed(result.latestVersion!);
        if (!wasDismissed && mounted) {
          _showUpdateDialog(result);
        }
      }
    } catch (e) {
      setState(() {
        _status = VersionStatus.checkFailed;
        _error = e.toString();
        _isLoading = false;
      });
    }
  }

  void _showUpdateDialog(VersionCheckResult result) {
    UpdateDialog.show(
      context,
      result,
      onUpdatePressed: () async {
        await _versionManager.openAppStore();
      },
      onLaterPressed: () async {
        if (result.latestVersion != null) {
          await _versionManager.dismissVersionCheck(result.latestVersion!);
        }
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: appBar(
        title: 'Version.Title'.tr(),
        titleColor: Colors.white,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            VersionInfoWidget(
              currentVersion: _currentVersion,
              latestVersion: _latestVersion,
              status: _status,
              onCheckPressed: _isLoading ? null : _checkForUpdates,
            ),
            const SizedBox(height: 16),
            if (_isLoading)
              const Card(
                child: Padding(
                  padding: EdgeInsets.all(16.0),
                  child: Row(
                    children: [
                      CircularProgressIndicator(),
                      SizedBox(width: 16),
                      Text('Version.Checking'),
                    ],
                  ),
                ),
              ),
            if (_error != null)
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Row(
                    children: [
                      const Icon(Icons.error_outline, color: Colors.red),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Text(
                          'Version.CheckError'.tr(args: [_error!]),
                          style: const TextStyle(color: Colors.red),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            const Spacer(),
            _buildSupportInfo(),
          ],
        ),
      ),
    );
  }

  Widget _buildSupportInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Version.SupportInfo'.tr(),
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Version.MinimumOS'.tr(args: ['iOS 12.0', 'Android 6.0']),
              style: const TextStyle(fontSize: 14),
            ),
            const SizedBox(height: 4),
            Text(
              'Version.LastUpdated'.tr(args: [DateFormat('dd/MM/yyyy').format(DateTime.now())]),
              style: const TextStyle(fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }
}

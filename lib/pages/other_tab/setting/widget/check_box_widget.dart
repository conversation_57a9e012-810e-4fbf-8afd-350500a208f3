import 'package:flutter/material.dart';

class CheckboxCell extends StatelessWidget {
  final String title;
  final bool isChecked;
  final bool isTop;
  final bool isBottom;
  final VoidCallback? onTap;

  const CheckboxCell({
    Key? key,
    required this.title,
    required this.isChecked,
    this.isTop = false,
    this.isBottom = false,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(isTop ? 8 : 0),
          topRight: Radius.circular(isTop ? 8 : 0),
          bottomLeft: Radius.circular(isBottom ? 8 : 0),
          bottomRight: Radius.circular(isBottom ? 8 : 0),
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    title,
                    style: const TextStyle(
                      fontSize: 18,
                      color: Color(0xFF494C62),
                    ),
                  ),
                ),
                if (isChecked)
                  const Icon(
                    Icons.check,
                    color: Colors.blue,
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class SwitchCell extends StatelessWidget {
  final String title;
  final bool value;
  final ValueChanged<bool> onChanged;

  const SwitchCell({
    Key? key,
    required this.title,
    required this.value,
    required this.onChanged,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Material(
        color: Colors.transparent,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Row(
            children: [
              Expanded(
                child: Text(
                  title,
                  style: const TextStyle(
                    fontSize: 18,
                    color: Color(0xFF494C62),
                  ),
                ),
              ),
              Switch(
                value: value,
                onChanged: (newValue) {
                  print('🔄 Switch tapped: $value -> $newValue');
                  onChanged(newValue);
                },
                activeColor: Colors.blue,
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class SettingCell extends StatelessWidget {
  final String title;
  final String? content;
  final bool isEnabled;
  final VoidCallback? onTap;

  const SettingCell({
    Key? key,
    required this.title,
    this.content,
    this.isEnabled = true,
    this.onTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: isEnabled ? onTap : null,
          child: Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          fontSize: 18,
                          color: isEnabled
                              ? const Color(0xFF494C62)
                              : Colors.grey,
                        ),
                      ),
                      if (content != null)
                        Padding(
                          padding: const EdgeInsets.only(top: 4),
                          child: Text(
                            content!,
                            style: const TextStyle(
                              fontSize: 14,
                              color: Colors.grey,
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
                const Icon(
                  Icons.chevron_right,
                  color: Colors.grey,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

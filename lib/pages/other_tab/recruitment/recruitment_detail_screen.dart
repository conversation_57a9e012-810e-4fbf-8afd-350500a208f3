import 'package:flutter/material.dart';
import 'package:flutter_app/core/index.dart';
import 'package:flutter_app/models/index.dart';
import 'package:flutter_app/pages/voucher/api/api_test.dart';
import 'package:flutter_app/utils/index.dart';
import 'package:flutter_html/flutter_html.dart';
import 'package:intl/intl.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';

class RecruitmentDetailScreen extends StatelessWidget {
  final PromotionItem item;

  const RecruitmentDetailScreen({super.key, required this.item});

  @override
  Widget build(BuildContext context) {
    // Format the date if available
    String formattedDate = '';
    if (item.publishDate != null) {
      try {
        // final date = DateTime.parse(item.publishDate!);
        formattedDate = Convert.date(item.publishDate ?? "");
      } catch (e) {
        formattedDate = item.publishDate!;
      }
    }

    return Scaffold(
      appBar: appBar(
        title: '<PERSON> tiết tuyển dụng',
        titleColor: Colors.white
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Featured image
            // if (item.imageUrl != null)
            //   Image.network(
            //     item.imageUrl!,
            //     width: double.infinity,
            //     height: 150,
            //     fit: BoxFit.cover,
            //     errorBuilder: (context, error, stackTrace) {
            //       return Container(
            //         width: double.infinity,
            //         height: 200,
            //         color: Colors.grey[300],
            //         child: const Icon(Icons.image_not_supported, size: 50),
            //       );
            //     },
            //   ),

            Padding(
              padding: const EdgeInsets.all(8),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title
                  Text(
                    item.title,
                    style: const TextStyle(
                      fontSize: 22,
                      fontWeight: FontWeight.bold,
                      color: Color(0xFF223849),
                    ),
                  ),
                  const SizedBox(height: 8),

                  // Date
                  if (formattedDate.isNotEmpty)
                    Text(
                      'Ngày đăng: $formattedDate',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[600],
                      ),
                    ),
                  const SizedBox(height: 16),

                  // Content
                  if (item.fullContent.isNotEmpty)
                    SizedBox(width:double.infinity,child: _buildHtmlContent(item.fullContent))
                  else if (item.summary.isNotEmpty)
                    Text(item.summary)
                  else
                    const Text('Không có nội dung chi tiết'),
                ],
              ),
            ),
          ],
        ),
      ),
      floatingActionButton: item.newsURI != null
          ? FloatingActionButton(
              onPressed: () => _launchUrl(item.newsURI!),
              child: const Icon(Icons.open_in_browser),
            )
          : null,
    );
  }

  Widget _buildHtmlContent(String htmlContent) {
    return LayoutBuilder(
      builder: (context,constraints) {
        return Html(
          data: htmlContent,
          style: {
            "body": Style(
              fontSize: FontSize(16.0),
              lineHeight: const LineHeight(1.5),
              fontFamily: "Oswald",
              // width: Width(constraints.maxWidth),
            ),
            "p": Style(
              margin: Margins(bottom: Margin(16)),
              fontFamily: "Oswald",
            ),
            "img": Style(
              width: Width(constraints.minWidth),

              alignment: Alignment.center,

            ),
          },
          onLinkTap: (url, _, ___) {
            if (url != null) {
              _launchUrl(url);
            }
          },
        );
      }
    );
  }

  Future<void> _launchUrl(String url) async {
    Share.share('${ApiService.baseURLWeb}/$url');
    // final Uri uri = Uri.parse('${ApiService.baseURLWeb}/$url');
    // if (!await launchUrl(uri, mode: LaunchMode.externalApplication)) {
    //   throw Exception('Could not launch $url');
    // }
  }
}

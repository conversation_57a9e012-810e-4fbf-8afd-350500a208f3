import 'package:flutter/material.dart';

class CFontSize {
  CFontSize._();

  static const double xs = 12.0;
  static const double sm = 14.0;
  static const double base = 16.0;
  static const double lg = 18.0;
  static const double xl = 20.0;
  static const double xl2 = 24.0;
  static const double xl3 = 30.0;
  static const double xl4 = 36.0;
}

class CSpace {
  CSpace._();

  static const xs = 4.0;
  static const sm = 6.0;
  static const base = 8.0;
  static const lg = 10.0;
  static const xl = 12.0;
  static const xl2 = 14.0;
  static const xl3 = 16.0;
  static const xl4 = 20.0;
  static const xl5 = 24.0;

  static double _width = 0;
  static double _height = 0;

  // Safe getters with fallback values
  static double get width => _width > 0 ? _width : 375.0;
  static double get height => _height > 0 ? _height : 812.0;

  // Check if screen size has been initialized
  static bool get isInitialized => _width > 0 && _height > 0;
  static void setScreenSize(BuildContext context) {
    try {
      // Check if MediaQuery is available in the widget tree
      final mediaQuery = MediaQuery.maybeOf(context);
      if (mediaQuery != null) {
        _width = mediaQuery.size.width;
        _height = mediaQuery.size.height;
        debugPrint('✅ CSpace: Screen size set to ${_width}x${_height}');
      } else {
        // Fallback to default values if MediaQuery is not available
        _width = 375.0; // iPhone default width
        _height = 812.0; // iPhone default height
        debugPrint('⚠️ CSpace: MediaQuery not available, using fallback size ${_width}x${_height}');
      }
    } catch (e) {
      // Additional safety net
      _width = 375.0;
      _height = 812.0;
      debugPrint('❌ CSpace: Error setting screen size: $e, using fallback ${_width}x${_height}');
    }
  }
}

class CHeight {
  CHeight._();

  static const xs = 28.0;
  static const sm = 32.0;
  static const base = 36.0;
  static const lg = 40.0;
  static const xl = 44.0;
  static const xl2 = 48.0;
  static const xl3 = 56.0;
  static const xl4 = 64.0;
}

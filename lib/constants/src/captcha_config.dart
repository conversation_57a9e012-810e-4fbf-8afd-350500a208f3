/// Configuration for reCAPTCHA
/// T<PERSON><PERSON>ng tự Android BuildConfig.RE_CAPTCHA_KEY và iOS ReCaptcha configuration
class CaptchaConfig {
  // TODO: Thay thế bằng site key thự<PERSON> tế từ Google reCAPTCHA Console
  // Đ<PERSON> lấy site key:
  // 1. T<PERSON>y cập https://www.google.com/recaptcha/admin
  // 2. Tạo site mới với domain của ứng dụng
  // 3. Chọn reCAPTCHA v2 "I'm not a robot" Checkbox
  // 4. Copy site key và thay thế giá trị dưới đây
  static const String siteKey = 'YOUR_RECAPTCHA_SITE_KEY_HERE';
  
  // Endpoint cho reCAPTCHA (có thể thay đổi nếu cần)
  static const String endpoint = 'https://www.google.com/recaptcha/api.js';
  
  // Timeout cho captcha validation (milliseconds)
  static const int timeoutMs = 30000; // 30 seconds
  
  // Validate site key configuration
  static bool get isConfigured => siteKey != 'YOUR_RECAPTCHA_SITE_KEY_HERE' && siteKey.isNotEmpty;
}

import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:package_info_plus/package_info_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

import '../models/project/app_params.dart';
import '../models/index.dart';
import '../core/index.dart';
import '../constants/index.dart';
import '../utils/src/api.dart';

/// Version Manager - handles version checking and update notifications
/// Matches iOS VersionInfoViewController and Android VersionFragment functionality
class VersionManager {
  static final VersionManager _instance = VersionManager._internal();
  factory VersionManager() => _instance;
  VersionManager._internal();

  static const String versionCheckKey = 'version_check_dismissed';
  static const String _lastVersionKey = 'last_checked_version';

  /// Check for app updates using app parameters API
  /// Matches iOS checkNewVersion() and Android getAppParamsViaApi()
  Future<VersionCheckResult> checkForUpdates(Api api) async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      final currentVersion = packageInfo.version;

      // Get app parameters from API
      final response = await api.other.getAppParams();

      if (response?.data == null) {
        return VersionCheckResult(
          status: VersionStatus.checkFailed,
          currentVersion: currentVersion,
          error: 'Failed to fetch app parameters',
        );
      }

      // Parse app parameters - match iOS/Android response structure
      // iOS: response.ListObject, Android: it.Data
      final List<dynamic> paramsData = response!.data['Data'] ?? response.data['content'] ?? [];
      print('📱 App params response data: $paramsData');

      final List<AppParams> appParams = paramsData
          .map((item) => AppParams.fromJson(item))
          .toList();

      print('📱 Parsed app params: ${appParams.length} items');
      for (var param in appParams) {
        print('📱 Param: ${param.paramsCode} = ${param.value}');
      }

      // Find version parameter
      final versionParam = appParams.firstWhere(
        (param) => param.isVersionParam,
        orElse: () => AppParams(),
      );

      if (versionParam.paramsCode == null) {
        return VersionCheckResult(
          status: VersionStatus.checkFailed,
          currentVersion: currentVersion,
          error: 'Version parameter not found',
        );
      }

      // Compare versions
      final latestVersion = versionParam.versionValue;
      final comparison = _compareVersions(latestVersion, currentVersion);

      if (comparison > 0) {
        // New version available
        final isMandatory = versionParam.isMandatoryUpdate;
        return VersionCheckResult(
          status: isMandatory ? VersionStatus.updateRequired : VersionStatus.updateAvailable,
          currentVersion: currentVersion,
          latestVersion: latestVersion,
          title: versionParam.paramsName,
          message: versionParam.paramsMessage,
          messageHighlight: versionParam.paramsMessageHighLight,
          canClose: versionParam.canClose ?? true,
        );
      } else {
        // Up to date
        return VersionCheckResult(
          status: VersionStatus.upToDate,
          currentVersion: currentVersion,
          latestVersion: latestVersion,
        );
      }
    } catch (e) {
      final packageInfo = await PackageInfo.fromPlatform();
      return VersionCheckResult(
        status: VersionStatus.checkFailed,
        currentVersion: packageInfo.version,
        error: e.toString(),
      );
    }
  }

  /// Check for updates using store API (fallback method)
  /// Matches iOS checkNewVersion() iTunes API call
  Future<VersionCheckResult> checkForUpdatesFromStore() async {
    try {
      final packageInfo = await PackageInfo.fromPlatform();
      final currentVersion = packageInfo.version;
      final bundleId = packageInfo.packageName;

      String? latestVersion;

      if (Platform.isIOS) {
        // Check iOS App Store
        latestVersion = await _checkiOSAppStore(bundleId);
      } else if (Platform.isAndroid) {
        // Check Google Play Store
        latestVersion = await _checkAndroidPlayStore(bundleId);
      }

      if (latestVersion == null) {
        return VersionCheckResult(
          status: VersionStatus.checkFailed,
          currentVersion: currentVersion,
          error: 'Failed to fetch version from store',
        );
      }

      final comparison = _compareVersions(latestVersion, currentVersion);

      if (comparison > 0) {
        return VersionCheckResult(
          status: VersionStatus.updateAvailable,
          currentVersion: currentVersion,
          latestVersion: latestVersion,
          canClose: true,
        );
      } else {
        return VersionCheckResult(
          status: VersionStatus.upToDate,
          currentVersion: currentVersion,
          latestVersion: latestVersion,
        );
      }
    } catch (e) {
      final packageInfo = await PackageInfo.fromPlatform();
      return VersionCheckResult(
        status: VersionStatus.checkFailed,
        currentVersion: packageInfo.version,
        error: e.toString(),
      );
    }
  }

  /// Check iOS App Store for latest version
  Future<String?> _checkiOSAppStore(String bundleId) async {
    try {
      final url = 'https://itunes.apple.com/lookup?bundleId=$bundleId';
      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        final json = jsonDecode(response.body);
        final results = json['results'] as List?;

        if (results != null && results.isNotEmpty) {
          return results.first['version'] as String?;
        }
      }
    } catch (e) {
      debugPrint('Error checking iOS App Store: $e');
    }
    return null;
  }

  /// Check Android Play Store for latest version
  Future<String?> _checkAndroidPlayStore(String packageName) async {
    try {
      // Note: This is a simplified version. In production, you might want to use
      // a more reliable method or your own API endpoint
      final url = 'https://play.google.com/store/apps/details?id=$packageName&hl=en';
      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        // Parse HTML to extract version (this is fragile and might break)
        // In production, consider using your own API or a more reliable method
        final regex = RegExp(r'Current Version.*?>([\d.]+)<');
        final match = regex.firstMatch(response.body);
        return match?.group(1);
      }
    } catch (e) {
      debugPrint('Error checking Android Play Store: $e');
    }
    return null;
  }

  /// Compare two version strings
  /// Returns: 1 if version1 > version2, 0 if equal, -1 if version1 < version2
  /// Matches Android versionCompare() function
  int _compareVersions(String version1, String version2) {
    final v1Parts = version1.split('.').map(int.parse).toList();
    final v2Parts = version2.split('.').map(int.parse).toList();

    final maxLength = v1Parts.length > v2Parts.length ? v1Parts.length : v2Parts.length;

    for (int i = 0; i < maxLength; i++) {
      final v1Part = i < v1Parts.length ? v1Parts[i] : 0;
      final v2Part = i < v2Parts.length ? v2Parts[i] : 0;

      if (v1Part > v2Part) return 1;
      if (v1Part < v2Part) return -1;
    }

    return 0;
  }

  /// Check if version check was dismissed for this version
  Future<bool> wasVersionCheckDismissed(String version) async {
    final prefs = await SharedPreferences.getInstance();
    final dismissedVersion = prefs.getString(versionCheckKey);
    return dismissedVersion == version;
  }

  /// Mark version check as dismissed
  Future<void> dismissVersionCheck(String version) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(versionCheckKey, version);
  }

  /// Open app store for update
  /// Matches iOS/Android openStore() functionality
  Future<void> openAppStore() async {
    String url;
    if (Platform.isIOS) {
      url = AppConfig.iosAppStoreUrl;
    } else if (Platform.isAndroid) {
      url = AppConfig.androidPlayStoreUrl;
    } else {
      debugPrint('Unsupported platform for app store');
      return;
    }

    final uri = Uri.parse(url);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
      debugPrint('Opened app store: $url');
    } else {
      debugPrint('Could not launch app store: $url');
    }
  }
}

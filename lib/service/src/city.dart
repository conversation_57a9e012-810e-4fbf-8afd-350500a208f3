import 'dart:convert';

import 'package:http/http.dart';

import '/models/index.dart';
import 'base_http.dart';

/// City API service - matches iOS CityAPI.swift and Android CityAPI.kt
class SCity {
  late String endpoint;
  late Map<String, String> headers;
  late Future<MApi?> Function({required Response result}) checkAuth;

  SCity(this.endpoint, this.headers, this.checkAuth);

  /// Get list of cities - matches iOS .listCity and Android getListCity()
  /// Endpoint: api/v1/erp/cities
  /// Method: GET
  /// Returns: List<CityModel>
  Future<MApi?> getListCity({Map<String, dynamic> filter = const {}}) async => checkAuth(
      result: await BaseHttp.get(
        url: '$endpoint/api/v1/erp/cities',
        headers: headers,
        queryParameters: { 'filter': jsonEncode(filter)},
      ));

  /// Get districts of a city - matches iOS .listDistrict(cityId) and Android getDistrictOfCity(cityId)
  /// Endpoint: api/v1/erp/cities/{cityId}/districts
  /// Method: GET
  /// Parameters: cityId - ID of the city
  /// Returns: List<CityModel> (districts use same model as cities)
  Future<MApi?> getDistrictOfCity({required String cityId}) async => checkAuth(
      result: await BaseHttp.get(
        url: '$endpoint/api/v1/erp/cities/$cityId/districts',
        headers: headers,
        queryParameters: {},
      ));
}

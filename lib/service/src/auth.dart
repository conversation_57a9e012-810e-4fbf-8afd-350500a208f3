import 'package:http/http.dart';

import '/models/index.dart';
import 'base_http.dart';

class SAuth {
  late String endpoint;
  late Map<String, String> headers;
  late Future<MApi?> Function({required Response result}) checkAuth;

  SAuth(this.endpoint, this.headers, this.checkAuth);

  Future<MApi?> login({required body}) async => checkAuth(
          result: await BaseHttp.post(
        // url: '$endpoint/authentication/jwt/login',
        url: '$endpoint/api/v1/erp/accounts/login',
        body: body,
        headers: headers,
      ));

  // Facebook login - tương tự Android APIClient.shared.accountAPI.loginFacebook và iOS AccountProvider.rx.request(.loginFB)
  Future<MApi?> loginFacebook({
    required String token,
    String? deviceId,
  }) async => checkAuth(
          result: await BaseHttp.post(
        url: '$endpoint/api/v1/erp/accounts/login-facebook',
        body: {
          'Token': token,
          'ReCaptchaToken': '', // Empty for now, can be added later if needed
          if (deviceId != null) 'DeviceId': deviceId,
        },
        headers: headers,
      ));

  // Apple login - tương tự iOS AccountProvider.rx.request(.loginApple)
  Future<MApi?> loginApple({
    required String token,
    required String fullName,
    required String email,
    String? deviceId,
  }) async => checkAuth(
          result: await BaseHttp.post(
        url: '$endpoint/api/v1/erp/accounts/login-apple',
        body: {
          'Token': token,
          'Name': fullName,
          'Email': email,
          'ReCaptchaToken': '', // Empty for now, can be added later if needed
          if (deviceId != null) 'DeviceId': deviceId,
        },
        headers: headers,
      ));

  Future<MApi?> zalo() async => checkAuth(
          result: await BaseHttp.get(
        url: '$endpoint/parameters/zalo',
        queryParameters: {},
        headers: headers,
      ));

  // Register user account - matches iOS AccountProvider.rx.request(.register)
  Future<MApi?> register({required body}) async => checkAuth(
          result: await BaseHttp.post(
        url: '$endpoint/api/v1/erp/accounts', // ✅ Match iOS endpoint
        body: body,
        headers: headers,
      ));

  // Forgot password - matches iOS AccountProvider.rx.request(.forgotPass) and Android AccountAPI.forgotPassword
  Future<MApi?> forgotPassword({required String email}) async => checkAuth(
      result: await BaseHttp.post(  // ✅ POST method like iOS/Android
          url: '$endpoint/api/v1/erp/accounts/recovery-password',  // ✅ Match iOS/Android endpoint
          body: {
            'UserName': email,  // ✅ Match iOS/Android parameter name
            // 'DeviceId': deviceId,  // Optional - iOS includes this, Android doesn't
          },
          headers: headers));

  Future<MApi?> verifyForgotPassword(
          {required String resetPasswordToken}) async =>
      checkAuth(
          result: await BaseHttp.put(
              url: '$endpoint/me/verify-forgot-password',
              body: {'resetPasswordToken': resetPasswordToken},
              headers: headers));

  Future<MApi?> resetPassword(
          {required String resetPasswordToken,
          required String password}) async =>
      checkAuth(
          result: await BaseHttp.put(
              url: '$endpoint/me/verify-forgot-password',
              body: {
                'resetPasswordToken': resetPasswordToken,
                'password': password
              },
              headers: headers));

  Future<MApi?> info({required String token, required String id}) async {
    headers['Authorization'] = 'Bearer $token';
    headers['X-User'] = id;
    return checkAuth(
        result: await BaseHttp.get(
      url: '$endpoint/api/v1/erp/accounts/%7B$id%7D',
      queryParameters: {},
      headers: headers,
    ));
  }

  // Update profile - matches iOS AccountProvider.rx.request(.updateProfile) and Android AccountAPI.updateProfile
  Future<MApi?> updateProfile({required String userId, required body}) async => checkAuth(
          result: await BaseHttp.put(
        url: '$endpoint/api/v1/erp/accounts/$userId',  // ✅ Match iOS/Android endpoint with userId
        body: body,
        headers: headers,
      ));
  // Update avatar - matches iOS/Android endpoint: api/v1/erp/accounts/{id}/avatar
  Future<MApi?> updateAvatar({required String userId, required body}) async => checkAuth(
          result: await BaseHttp.put(
        url: '$endpoint/api/v1/erp/accounts/$userId/avatar',  // ✅ Match iOS/Android endpoint
        body: body,
        headers: headers,
      ));

  // Change password - matches iOS AccountProvider.rx.request(.changePassword) and Android AccountAPI.changePassword
  Future<MApi?> updatePassword({required body}) async => checkAuth(
          result: await BaseHttp.put(
        url: '$endpoint/api/v1/erp/accounts/change-password',  // ✅ Match iOS/Android endpoint
        body: body,
        headers: headers,
      ));

  Future<MApi?> delete() async => checkAuth(
          result: await BaseHttp.delete(
        url: '$endpoint/me',
        headers: headers,
      ));

  // Get user profile by ID
  Future<MApi?> getProfileById(String userId) async => checkAuth(
          result: await BaseHttp.get(
        url: '$endpoint/api/v1/erp/accounts/$userId',
        headers: headers,
        queryParameters: {},
      ));

  // Get card class information
  Future<MApi?> getCardClass() async => checkAuth(
          result: await BaseHttp.get(
        url: '$endpoint/api/v1/erp/card-class',
        headers: headers,
        queryParameters: {},
      ));

  // Get user's card list
  Future<MApi?> getListCard(String userId) async => checkAuth(
          result: await BaseHttp.get(
        url: '$endpoint/api/v1/erp/accounts/$userId/cards',
        headers: headers,
        queryParameters: {},
      ));

  // Get user's point information
  Future<MApi?> getPoints(String userId) async => checkAuth(
          result: await BaseHttp.get(
        url: '$endpoint/api/v1/erp/accounts/$userId/points',
        headers: headers,
        queryParameters: {},
      ));

  // Get point history - tương tự iOS VoucherProvider.rx.request(.historyPoint)
  Future<MApi?> getPointHistory() async => checkAuth(
          result: await BaseHttp.get(
        url: '$endpoint/api/v2/erp/point/history',
        headers: headers,
        queryParameters: {},
      ));

  // Donate points - tương tự iOS VoucherProvider.rx.request(.donatePoint)
  Future<MApi?> donatePoints({
    required String email,
    required int point,
  }) async => checkAuth(
          result: await BaseHttp.post(
        url: '$endpoint/api/v2/erp/point/donate',
        headers: headers,
        body: {
          'email': email,
          'point': point,
        },
      ));

  // Search user for point donation - tương tự iOS AccountProvider.rx.request(.searchUser)
  Future<MApi?> searchUser(String keyword) async => checkAuth(
          result: await BaseHttp.get(
        url: '$endpoint/api/v1/erp/accounts/get-list-account',
        headers: headers,
        queryParameters: {
          'textSearch': keyword,
        },
      ));

  // Get transaction history - tương tự iOS AccountProvider.rx.request(.getTransactionHistory(userId))
  Future<MApi?> getTransactionHistory(String userId) async => checkAuth(
          result: await BaseHttp.get(
        url: '$endpoint/api/v1/erp/transaction-history/$userId',
        headers: headers,
        queryParameters: {
          'stringFilter': '', // iOS sends empty stringFilter
        },
      ));

  // Get transaction detail - tương tự iOS AccountProvider.rx.request(.getTransactionDetails(userId, tranId))
  Future<MApi?> getTransactionDetail(String userId, String transactionId) async => checkAuth(
          result: await BaseHttp.get(
        url: '$endpoint/api/v2/erp/transaction-history/$userId/$transactionId',
        headers: headers,
        queryParameters: {},
      ));

  // Register FCM token for push notifications - tương ứng với iOS registerFCMToken
  Future<MApi?> registerFCMToken({
    required String deviceId,
    required String accountId,
    required String deviceToken,
    required String deviceType,
  }) async => checkAuth(
          result: await BaseHttp.post(
        url: '$endpoint/api/v1/erp/notifications/register-device-token',
        headers: headers,
        body: {
          'DeviceId': deviceId,
          'AccountId': accountId,
          'DeviceToken': deviceToken,
          'DeviceType': deviceType,
        },
      ));

  // Unregister FCM token for push notifications - tương ứng với iOS unregisterFCMToken
  Future<MApi?> unregisterFCMToken({
    required String deviceId,
    required String accountId,
    required String deviceToken,
    required String deviceType,
  }) async => checkAuth(
          result: await BaseHttp.put(
        url: '$endpoint/api/v1/erp/notifications/unregister-device-token',
        headers: headers,
        body: {
          'DeviceId': deviceId,
          'AccountId': accountId,
          'DeviceToken': deviceToken,
          'DeviceType': deviceType,
        },
      ));

  // Legacy method - deprecated, use unregisterFCMToken instead
  Future<MApi?> unregisterDeviceToken(String deviceId, String accountId, String token) async => checkAuth(
          result: await BaseHttp.delete(
        url: '$endpoint/api/v1/erp/accounts/device-token?deviceId=$deviceId&accountId=$accountId&token=$token',
        headers: headers,
      ));
}

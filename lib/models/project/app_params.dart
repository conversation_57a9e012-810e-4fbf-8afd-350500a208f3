/// App Parameters Model - matches iOS AppParams and Android AppParamsModel
class AppParams {
  final String? paramsCode;
  final String? paramsMessage;
  final String? paramsMessageHighLight;
  final String? value;
  final bool? status;
  final bool? canClose;

  AppParams({
    this.paramsCode,
    this.paramsMessage,
    this.paramsMessageHighLight,
    this.value,
    this.status,
    this.canClose,
  });

  factory AppParams.fromJson(Map<String, dynamic> json) {
    return AppParams(
      paramsCode: json['ParamsCode'] as String?,
      paramsMessage: json['ParamsMessage'] as String?,
      paramsMessageHighLight: json['ParamsMessageHighLight'] as String?,
      value: json['Value'] as String?,
      status: json['Status'] as bool?,
      canClose: json['CanClose'] as bool?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'ParamsCode': paramsCode,
      'ParamsMessage': paramsMessage,
      'ParamsMessageHighLight': paramsMessageHighLight,
      'Value': value,
      'Status': status,
      'CanClose': canClose,
    };
  }

  /// Check if this is a version parameter
  bool get isVersionParam => paramsCode == 'app-version';

  /// Get the version value
  String get versionValue => value ?? '';

  /// Check if version update is mandatory (cannot be closed)
  bool get isMandatoryUpdate => !(canClose ?? true);
}

/// App Version Model - matches iOS Global.shared.appVersion
class AppVersion {
  final String? key;
  final String? value;
  final String? message;
  final String? messageHighLight;
  final bool? status;
  final bool? canClose;

  AppVersion({
    this.key,
    this.value,
    this.message,
    this.messageHighLight,
    this.status,
    this.canClose,
  });

  factory AppVersion.fromAppParams(AppParams params) {
    return AppVersion(
      key: params.value,
      value: params.value,
      message: params.paramsMessage,
      messageHighLight: params.paramsMessageHighLight,
      status: params.status,
      canClose: params.canClose,
    );
  }

  /// Check if version update is mandatory
  bool get isMandatory => !(canClose ?? true);

  /// Check if version checking is enabled
  bool get isEnabled => status ?? false;
}

/// Version Check Result
enum VersionStatus {
  upToDate,
  updateAvailable,
  updateRequired,
  checkFailed,
}

class VersionCheckResult {
  final VersionStatus status;
  final String? currentVersion;
  final String? latestVersion;
  final String? message;
  final String? messageHighlight;
  final bool canClose;
  final String? error;

  VersionCheckResult({
    required this.status,
    this.currentVersion,
    this.latestVersion,
    this.message,
    this.messageHighlight,
    this.canClose = true,
    this.error,
  });

  bool get hasUpdate => status == VersionStatus.updateAvailable || status == VersionStatus.updateRequired;
  bool get isMandatory => status == VersionStatus.updateRequired;
}

import '../pages/Movie_schedule/model/Film_model.dart';

/// Transaction History Model - tương tự iOS TransactionHistoryModel.swift
class TransactionHistoryModel {
  String? invoiceId;
  String? filmName;
  String? cinemaName;
  String? cinemaId;
  String? showId;
  String? dateShow;
  String? showTime;
  String? dateEntered;
  double? quantityPoint;
  String? dateExpiredPoint;
  String? accountId;
  String? salesChannelId;
  String? salesChannelCode;
  String? cardId;
  String? cardNumber;
  double? totalPayment;
  String? airpayLandingUrl;
  String? momoLandingUrl;  // tương tự iOS MomoLandingUrl
  String? zaloLandingUrl;  // tương tự iOS ZaloLandingUrl

  TransactionHistoryModel({
    this.invoiceId,
    this.filmName,
    this.cinemaName,
    this.cinemaId,
    this.showId,
    this.dateShow,
    this.showTime,
    this.dateEntered,
    this.quantityPoint,
    this.dateExpiredPoint,
    this.accountId,
    this.salesChannelId,
    this.salesChannelCode,
    this.cardId,
    this.cardNumber,
    this.totalPayment,
    this.airpayLandingUrl,
    this.momoLandingUrl,
    this.zaloLandingUrl,
  });

  factory TransactionHistoryModel.fromJson(Map<String, dynamic> json) {
    return TransactionHistoryModel(
      invoiceId: json['Invoice_Id'] as String?,
      filmName: json['FilmName'] as String?,
      cinemaName: json['CinemaName'] as String?,
      cinemaId: json['CinemaId']as String?,
      showId: json['ShowId'] as String?,
      dateShow: json['DateShow'] as String?,
      showTime: json['ShowTime'] as String?,
      dateEntered: json['DateEntered'] as String?,
      quantityPoint: (json['QuantityPoint'] as num?) as double?,
      dateExpiredPoint: json['DateExpiredPoint'],
      accountId: json['AccountId'],
      salesChannelId: json['SalesChannelId'],
      salesChannelCode: json['SalesChannelCode'],
      cardId: json['CardId'],
      cardNumber: json['CardNumber'],
      totalPayment: json['TotalPayment'],
      airpayLandingUrl: json['AirpayLandingUrl'],
      momoLandingUrl: json['MomoLandingUrl'],
      zaloLandingUrl: json['ZaloLandingUrl'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'Invoice_Id': invoiceId,
      'FilmName': filmName,
      'CinemaName': cinemaName,
      'CinemaId': cinemaId,
      'ShowId': showId,
      'DateShow': dateShow,
      'ShowTime': showTime,
      'DateEntered': dateEntered,
      'QuantityPoint': quantityPoint,
      'DateExpiredPoint': dateExpiredPoint,
      'AccountId': accountId,
      'SalesChannelId': salesChannelId,
      'SalesChannelCode': salesChannelCode,
      'CardId': cardId,
      'CardNumber': cardNumber,
      'TotalPayment': totalPayment,
      'AirpayLandingUrl': airpayLandingUrl,
      'MomoLandingUrl': momoLandingUrl,
      'ZaloLandingUrl': zaloLandingUrl,
    };
  }

  /// Format show time - tương tự iOS extension
  String get formattedShowTime {
    if (showTime == null || showTime!.isEmpty) return '';

    try {
      final date = DateTime.parse(showTime!);
      return '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year} | ${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
    } catch (e) {
      return showTime ?? '';
    }
  }

  /// Format currency - tương tự iOS extension
  String get formattedTotalPayment {
    if (totalPayment == null) return '0';

    // Format number with thousand separators
    final formatter = RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))');
    String result = totalPayment.toString().replaceAllMapped(formatter, (Match m) => '${m[1]},');
    return result;
  }

  /// Check if transaction is waiting - tương tự iOS logic
  bool get isWaiting {
    return invoiceId == "00000000-0000-0000-0000-000000000000";
  }

  /// Check if has payment URL - tương tự iOS logic
  bool get hasPaymentUrl {
    return (airpayLandingUrl?.isNotEmpty == true) ||
           (momoLandingUrl?.isNotEmpty == true) ||
           (zaloLandingUrl?.isNotEmpty == true);
  }
}

/// Transaction History Detail Model - tương tự iOS TransactionHistoryDetailModel.swift
class TransactionHistoryDetailModel {
  List<TicketType>? listTicketType;
  List<ComboItem>? listCombo;
  FilmModel? filmModel;
  List<PaymentModel>? paymentModel;
  String? invoiceId;
  String? filmName;
  String? cinemaName;
  String? screenName;
  String? cinemaId;
  String? showId;
  String? filmId;
  String? dateShow;
  String? showTime;
  String? dateEntered;
  String? seatName;
  String? transactionType;
  double? quantityPoint;
  String? dateExpiredPoint;
  double? spendingPoint;
  int? quantityCombo;
  int? quantitySeat;
  String? ticketTypeId;
  String? ticketTypeName;
  String? accountId;
  String? applicationId;
  String? salesChannelId;
  String? salesChannelCode;
  String? no; // tương tự iOS No property for barcode
  double? totalPayment; // tương tự iOS TotalPayment

  TransactionHistoryDetailModel({
    this.listTicketType,
    this.listCombo,
    this.filmModel,
    this.paymentModel,
    this.invoiceId,
    this.filmName,
    this.cinemaName,
    this.screenName,
    this.cinemaId,
    this.showId,
    this.filmId,
    this.dateShow,
    this.showTime,
    this.dateEntered,
    this.seatName,
    this.transactionType,
    this.quantityPoint,
    this.dateExpiredPoint,
    this.spendingPoint,
    this.quantityCombo,
    this.quantitySeat,
    this.ticketTypeId,
    this.ticketTypeName,
    this.accountId,
    this.applicationId,
    this.salesChannelId,
    this.salesChannelCode,
    this.no,
    this.totalPayment,
  });

  factory TransactionHistoryDetailModel.fromJson(Map<String, dynamic> json) {
    return TransactionHistoryDetailModel(
      listTicketType: json['ListTicketType'] != null
          ? (json['ListTicketType'] as List).map((e) => TicketType.fromJson(e)).toList()
          : null,
      listCombo: json['ListCombo'] != null
          ? (json['ListCombo'] as List).map((e) => ComboItem.fromJson(e)).toList()
          : null,
      filmModel: json['FilmModel'] != null ? FilmModel.fromJson(json['FilmModel']) : null,
      paymentModel: json['PaymentModel'] != null
          ? (json['PaymentModel'] as List).map((e) => PaymentModel.fromJson(e)).toList()
          : null,
      invoiceId: json['Invoice_Id'],
      filmName: json['FilmName'],
      cinemaName: json['CinemaName'],
      screenName: json['ScreenName'],
      cinemaId: json['CinemaId'],
      showId: json['ShowId'],
      filmId: json['FilmId'],
      dateShow: json['DateShow'],
      showTime: json['ShowTime'],
      dateEntered: json['DateEntered'],
      seatName: json['SeatName'],
      transactionType: json['TransactionType'],
      quantityPoint: (json['QuantityPoint'] as num?)?.toDouble(),
      dateExpiredPoint: json['DateExpiredPoint'],
      spendingPoint: (json['SpendingPoint'] as num?)?.toDouble(),
      quantityCombo: json['QuantityCombo'],
      quantitySeat: json['QuantitySeat'],
      ticketTypeId: json['TicketTypeId'],
      ticketTypeName: json['TicketTypeName'],
      accountId: json['AccountId'],
      applicationId: json['ApplicationId'],
      salesChannelId: json['SalesChannelId'],
      salesChannelCode: json['SalesChannelCode'],
      no: json['No'],
      totalPayment: (json['TotalPayment'] as num?)?.toDouble(),
    );
  }

  /// Format show time - tương tự iOS showTime computed property
  (String, String) get formattedShowTime {
    if (showTime == null || showTime!.isEmpty) return ('', '');

    try {
      final date = DateTime.parse(showTime!);
      final dateStr = '${date.day.toString().padLeft(2, '0')}/${date.month.toString().padLeft(2, '0')}/${date.year}';
      final timeStr = '${date.hour.toString().padLeft(2, '0')}:${date.minute.toString().padLeft(2, '0')}';
      return (dateStr, timeStr);
    } catch (e) {
      return (showTime ?? '', '');
    }
  }

  /// Format seat name - tương tự iOS seatName computed property
  (int, String) get formattedSeatName {
    if (seatName == null || seatName!.isEmpty) return (0, '');

    final seats = seatName!.split(',').map((s) => s.trim()).where((s) => s.isNotEmpty).toList();
    return (seats.length, seats.join(', '));
  }

  /// Format combos - tương tự iOS combos computed property
  (int, String) get formattedCombos {
    if (listCombo == null || listCombo!.isEmpty) return (0, '');

    final totalQuantity = listCombo!.fold<int>(0, (sum, combo) => sum + (combo.quantity ?? 0));
    final comboNames = listCombo!.map((combo) => '${combo.name ?? ''}(${combo.quantity ?? 0})').join(', ');

    return (totalQuantity, comboNames);
  }
}

/// Supporting models
class TicketType {
  String? id;
  String? name;
  int? price;
  int? quantity;
  String? ticketTypeId;
  List<String>? listSeatName;
  TicketType({this.id,  this.name, this.price, this.quantity, this.ticketTypeId, this.listSeatName});

  factory TicketType.fromJson(Map<String, dynamic> json) {
    return TicketType(
      id: json['Id'],
      ticketTypeId: json['TicketTypeId'], // tương tự iOS TicketTypeId
      name: json['Name'],
      price: json['Price'],
      quantity: json['Quantity'],
      listSeatName: json['ListSeatName'] != null
          ? List<String>.from(json['ListSeatName'])
          : [],
    );
  }
}

class ComboItem {
  String? id;
  String? name;
  int? price;
  int? quantity;

  ComboItem({this.id, this.name, this.price, this.quantity});

  factory ComboItem.fromJson(Map<String, dynamic> json) {
    return ComboItem(
      id: json['Id'],
      name: json['Name'],
      price: json['Price'],
      quantity: json['Quantity'],
    );
  }
}


class PaymentModel {
  String? paymentTypeCode;
  int? values;

  PaymentModel({this.paymentTypeCode, this.values});

  factory PaymentModel.fromJson(Map<String, dynamic> json) {
    return PaymentModel(
      paymentTypeCode: json['PaymentTypeCode'],
      values: json['Values'],
    );
  }
}

# Facebook Login Configuration Comparison

## 📋 Overview
So s<PERSON>h cấu hình Facebook Login giữa các môi trường (Dev vs Production) và các repository (iOS, Android, Flutter).

## 🔧 Current Configuration Status

### ❌ **Vấn đề hiện tại: "Invalid key hash"**
- **Lỗi**: `Invalid key hash` khi đăng nhập Facebook trên môi trường dev Flutter
- **<PERSON>uyên nhân**: Key hash không được cấu hình đúng cho môi trường dev
- **Ảnh hưởng**: Facebook login không hoạt động trên dev environment

---

## 📱 iOS Repository Configuration

### **Dev Environment** (`Booking/App/Dev/Info.plist`)
```xml
<key>FacebookAppID</key>
<string>367174740769877</string>
<key>FacebookClientToken</key>
<string>********************************</string>

<key>CFBundleURLSchemes</key>
<array>
    <string>fb367174740769877</string>
</array>

<!-- Bundle ID: com.beta.betacineplex.dev -->
```

### **Production Environment** (`Booking/App/Prod/Info.plist`)
```xml
<key>FacebookAppID</key>
<string>367174740769877</string>
<key>FacebookClientToken</key>
<string>********************************</string>

<key>CFBundleURLSchemes</key>
<array>
    <string>fb367174740769877</string>
</array>

<!-- Bundle ID: com.beta.betacineplex -->
```

### **Test Environment** (`Booking/App/Test/Info.plist`)
```xml
<key>FacebookAppID</key>
<string>367174740769877</string>
<key>FacebookClientToken</key>
<string>********************************</string>
```

---

## 🤖 Android Repository Configuration

### **Dev Environment** (`app/build.gradle`)
```gradle
dev {
    applicationIdSuffix = ".dev"
    resValue "string", "app_name", "Beta Cineplex Dev"
    resValue "string", "facebook_app_id", "367174740769877"
    resValue "string", "facebook_client_token", "********************************"
    resValue "string", "fb_login_protocol_scheme", "fb367174740769877"
    buildConfigField "String", "BASE_URL", '"http://dev.api.betacorp.vn/"'
}
```

### **Customer Environment** (`app/build.gradle`)
```gradle
customer {
    applicationIdSuffix = ".customer"
    resValue "string", "app_name", "Beta Cinemas Test"
    resValue "string", "facebook_app_id", "367174740769877"
    resValue "string", "facebook_client_token", "********************************"
    resValue "string", "fb_login_protocol_scheme", "fb367174740769877"
    buildConfigField "String", "BASE_URL", '"http://dev.api.betacorp.vn/"'
}
```

### **Production Environment** (`app/build.gradle`)
```gradle
prod {
    resValue "string", "app_name", "Beta Cinemas"
    resValue "string", "facebook_app_id", "367174740769877"
    resValue "string", "facebook_client_token", "********************************"
    resValue "string", "fb_login_protocol_scheme", "fb367174740769877"
    buildConfigField "String", "BASE_URL", '"https://api.betacorp.vn/"'
}
```

---

## 🦋 Flutter Repository Configuration

### **Current Configuration** (❌ **Có vấn đề**)

#### **iOS** (`ios/Runner/Info.plist`)
```xml
<key>FacebookAppID</key>
<string>367174740769877</string>
<key>FacebookClientToken</key>
<string>********************************</string>
<key>FacebookDisplayName</key>
<string>Betacineplex</string>

<key>CFBundleURLSchemes</key>
<array>
    <string>fb367174740769877</string>
</array>
```

#### **Android** (`android/app/src/main/res/values/strings.xml`)
```xml
<string name="facebook_app_id">367174740769877</string>
<string name="facebook_client_token">********************************</string>
<string name="fb_login_protocol_scheme">fb367174740769877</string>
<string name="facebook_display_name">Betacineplex</string>
```

#### **Environment Files**
- **Dev** (`.env.development`): `API_URL='http://dev.api.betacorp.vn/'`
- **Prod** (`.env.production`): `API_URL='https://api.betacorp.vn'`

---

## 🔍 Key Differences Analysis

### **1. Bundle ID / Package Name**
| Repository | Dev | Production |
|------------|-----|------------|
| **iOS** | `com.beta.betacineplex.dev` | `com.beta.betacineplex` |
| **Android** | `vn.zenity.betacineplex.dev` | `vn.zenity.betacineplex` |
| **Flutter** | `com.beta.betacineplex` | `com.beta.betacineplex` |

### **2. Facebook App Configuration**
| Setting | All Environments |
|---------|------------------|
| **App ID** | `367174740769877` |
| **Client Token** | `********************************` |
| **URL Scheme** | `fb367174740769877` |

### **3. API Endpoints**
| Repository | Dev | Production |
|------------|-----|------------|
| **iOS** | ❓ (Cần kiểm tra) | ❓ (Cần kiểm tra) |
| **Android** | `http://dev.api.betacorp.vn/` | `https://api.betacorp.vn/` |
| **Flutter** | `http://dev.api.betacorp.vn/` | `https://api.betacorp.vn` |

---

## ❌ Root Cause: "Invalid key hash" Error

### **Vấn đề chính:**
1. **Flutter không có cấu hình riêng cho dev environment**
2. **Key hash không được generate và cấu hình cho dev bundle ID**
3. **Facebook Developer Console chưa có key hash cho Flutter dev**

### **So sánh với iOS/Android:**
- **iOS/Android**: Có bundle ID riêng cho dev (`.dev` suffix)
- **Flutter**: Sử dụng cùng bundle ID cho cả dev và prod
- **Facebook**: Cần key hash riêng cho mỗi bundle ID

---

## 🔧 Recommended Solutions

### **Option 1: Separate Bundle IDs (Recommended)**
Tạo bundle ID riêng cho dev environment như iOS/Android:

#### **Android** (`android/app/build.gradle`)
```gradle
android {
    buildTypes {
        debug {
            applicationIdSuffix ".dev"
            // Other debug configurations
        }
        release {
            // Production configuration
        }
    }
}
```

#### **iOS** (`ios/Runner.xcodeproj/project.pbxproj`)
```
// Create separate schemes for dev and prod
// Similar to iOS repo structure
```

### **Option 2: Environment-specific Facebook Config**
Sử dụng flavors để cấu hình Facebook khác nhau:

#### **Flutter** (`lib/main.dart`)
```dart
void main() {
  const String environment = String.fromEnvironment('ENVIRONMENT', defaultValue: 'development');
  
  if (environment == 'development') {
    // Use dev Facebook config
  } else {
    // Use prod Facebook config
  }
  
  runApp(MyApp());
}
```

### **Option 3: Generate and Add Key Hashes**
Generate key hash cho Flutter và thêm vào Facebook Developer Console:

```bash
# Generate debug key hash
keytool -exportcert -alias androiddebugkey -keystore ~/.android/debug.keystore | openssl sha1 -binary | openssl base64

# Generate release key hash
keytool -exportcert -alias <your-key-alias> -keystore <path-to-keystore> | openssl sha1 -binary | openssl base64
```

---

## 📝 Action Items

### **Immediate (Fix current error):**
1. ✅ Generate key hash cho Flutter debug keystore
2. ✅ Add key hash vào Facebook Developer Console
3. ✅ Test Facebook login trên dev environment

### **Long-term (Proper setup):**
1. ✅ Implement separate bundle IDs cho dev/prod
2. ✅ Create environment-specific Facebook configurations
3. ✅ Update CI/CD để handle multiple environments
4. ✅ Document key hash generation process

### **Testing:**
1. ✅ Test Facebook login trên cả dev và prod
2. ✅ Verify key hash cho tất cả environments
3. ✅ Update integration tests để cover Facebook login

---

## 🔗 References
- [Facebook Android SDK Setup](https://developers.facebook.com/docs/android/getting-started)
- [Facebook iOS SDK Setup](https://developers.facebook.com/docs/ios/getting-started)
- [Flutter Facebook Auth Plugin](https://pub.dev/packages/flutter_facebook_auth)
- [Android Key Hash Generation](https://developers.facebook.com/docs/android/getting-started#release-key-hash)

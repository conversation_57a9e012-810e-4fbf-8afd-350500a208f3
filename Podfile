# Uncomment the next line to define a global platform for your project
platform :ios, '12.0'

def all_pod
    pod 'FBSDKCoreKit'
    pod 'FBSDKLoginKit'
    pod 'iCarousel'
    pod 'SwiftDate'
    pod 'Alamofire'
    pod 'PKHUD'
    pod 'IQKeyboardManagerSwift'
    pod 'AlamofireImage'
    pod 'ActiveLabel'
    pod 'UITableView+FDTemplateLayoutCell'
    pod 'VisualEffectView'
    pod 'KMNavigationBarTransition'
    pod 'GoogleMaps'
    pod 'GooglePlaces'
    pod 'Fabric'
    pod 'Crashlytics'
    pod 'RSBarcodes_Swift'
    pod 'PopupDialog'
    pod 'DRPLoadingSpinner', :git => 'https://github.com/hiennv92/DRPLoadingSpinner.git'
    pod 'ImageSlideshow/Alamofire'
    pod 'PullToRefreshKit'
    pod 'ReCaptcha/RxSwift'
    pod 'RxCocoa'
    pod 'SignalRSwift'
    pod 'ObjectMapper'
    pod 'Moya', '~> 14.0.0-alpha.1'
    pod 'Moya/RxSwift'
    pod 'RxSwift', '~> 5.0.1'
    pod 'SwiftSignalRClient'
    pod 'youtube-ios-player-helper'
    pod 'Mixpanel-swift'
    pod 'FirebaseAnalytics'
    pod 'FirebaseMessaging'
    pod 'FirebaseRemoteConfig'
    pod 'SDWebImage'
end

target 'Booking-dev' do
  # Comment the next line if you're not using Swift and don't want to use dynamic frameworks
  use_frameworks!
  inhibit_all_warnings!
  all_pod
end

target 'Booking-test' do
    # Comment the next line if you're not using Swift and don't want to use dynamic frameworks
    use_frameworks!
    inhibit_all_warnings!
    all_pod
end

target 'Booking-pro' do
    # Comment the next line if you're not using Swift and don't want to use dynamic frameworks
    use_frameworks!
    inhibit_all_warnings!
    all_pod
end

post_install do |installer|
  installer.generated_projects.each do |project|
    project.targets.each do |target|
      target.build_configurations.each do |config|
        config.build_settings['IPHONEOS_DEPLOYMENT_TARGET'] = '12.0'
        xcconfig_path = config.base_configuration_reference.real_path
        xcconfig = File.read(xcconfig_path)
        xcconfig_mod = xcconfig.gsub(/DT_TOOLCHAIN_DIR/, "TOOLCHAIN_DIR")
        File.open(xcconfig_path, "w") { |file| file << xcconfig_mod }
      end
    end
  end
      installer.pods_project.targets.each do |target|
          target.build_configurations.each do |config|
          xcconfig_path = config.base_configuration_reference.real_path
          xcconfig = File.read(xcconfig_path)
          xcconfig_mod = xcconfig.gsub(/DT_TOOLCHAIN_DIR/, "TOOLCHAIN_DIR")
          File.open(xcconfig_path, "w") { |file| file << xcconfig_mod }
          end
      end
  end

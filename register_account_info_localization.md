# Register & Account Info Localization Update

## Tổng Quan

Đã chuyển đổi các text constants trong `register.dart` và `_account_info.dart` thành dạng đa ngôn ngữ, bổ sung vào file `vi.json` và `en.json`.

## Files Đã Cập Nhật

### 1. **Register Screen** (`lib/pages/access/register.dart`)

#### **Text Constants Converted:**
- **Section Titles**: "Thông tin bắt buộc", "Thông tin bổ sung"
- **Field Labels**: "CMND/CCCD"
- **Validation Messages**: Error messages cho form validation
- **Error Messages**: Registration error messages

#### **Before & After:**
```dart
// Before
MFormItem(
  label: 'Thông tin bắt buộc'.toUpperCase(),
  type: EFormItemType.title,
),
MFormItem(
  name: 'personalId',
  label: 'CMND/CCCD',
  required: false,
),
onValidator: (value, listController) {
  if (value == null || value.isEmpty) {
    return 'Full name is required';
  }
  return null;
},

// After
MFormItem(
  label: 'Register.RequiredInfo'.tr().toUpperCase(),
  type: EFormItemType.title,
),
MFormItem(
  name: 'personalId',
  label: 'Register.PersonalId'.tr(),
  required: false,
),
onValidator: (value, listController) {
  if (value == null || value.isEmpty) {
    return 'Register.FullNameRequired'.tr();
  }
  return null;
},
```

### 2. **Account Info Screen** (`lib/pages/my_profile/_account_info.dart`)

#### **Text Constants Converted:**
- **Section Titles**: "Thông tin cơ bản", "Thông tin liên hệ"
- **Field Labels**: "Họ và tên", "Giới tính", "Ngày sinh", "Số điện thoại", etc.

#### **Before & After:**
```dart
// Before
_buildSectionTitle('Thông tin cơ bản'),
info(title: 'Họ và tên', content: widget.user.name),
info(title: 'Giới tính', content: widget.user.genderString ?? ''),
info(title: 'Ngày sinh', content: Convert.date(widget.user.birthdate ?? '')),
info(title: 'Số điện thoại', content: widget.user.phoneNumber?.toString() ?? ''),
info(title: 'Tỉnh/Thành phố', content: widget.user.addressCity ?? ''),

// After
_buildSectionTitle('AccountInfo.BasicInfo'.tr()),
info(title: 'AccountInfo.FullName'.tr(), content: widget.user.name),
info(title: 'AccountInfo.Gender'.tr(), content: widget.user.genderString ?? ''),
info(title: 'AccountInfo.BirthDate'.tr(), content: Convert.date(widget.user.birthdate ?? '')),
info(title: 'AccountInfo.PhoneNumber'.tr(), content: widget.user.phoneNumber?.toString() ?? ''),
info(title: 'AccountInfo.Province'.tr(), content: widget.user.addressCity ?? ''),
```

## New Translation Keys Added

### **Vietnamese (vi.json):**
```json
"Register": {
  "RequiredInfo": "Thông tin bắt buộc",
  "AdditionalInfo": "Thông tin bổ sung",
  "PersonalId": "CMND/CCCD",
  "TermsAgreement": "Bạn phải đồng ý với điều khoản sử dụng",
  "FullNameRequired": "Họ tên là bắt buộc",
  "InvalidEmailFormat": "Định dạng email không hợp lệ",
  "PasswordMinLength": "Mật khẩu phải có ít nhất 6 ký tự",
  "PasswordMismatch": "Xác nhận mật khẩu không khớp",
  "PhoneRequired": "Số điện thoại là bắt buộc",
  "RegistrationFailed": "Đăng ký thất bại",
  "RegistrationError": "Lỗi đăng ký"
},
"AccountInfo": {
  "BasicInfo": "Thông tin cơ bản",
  "ContactInfo": "Thông tin liên hệ",
  "FullName": "Họ và tên",
  "Gender": "Giới tính",
  "BirthDate": "Ngày sinh",
  "PhoneNumber": "Số điện thoại",
  "Province": "Tỉnh/Thành phố",
  "District": "Quận/Huyện",
  "Address": "Địa chỉ"
}
```

### **English (en.json):**
```json
"Register": {
  "RequiredInfo": "Required information",
  "AdditionalInfo": "Additional information",
  "PersonalId": "ID/Passport",
  "TermsAgreement": "You must agree to the terms of use",
  "FullNameRequired": "Full name is required",
  "InvalidEmailFormat": "Invalid email format",
  "PasswordMinLength": "Password must be at least 6 characters",
  "PasswordMismatch": "Password confirmation does not match",
  "PhoneRequired": "Phone number is required",
  "RegistrationFailed": "Registration failed",
  "RegistrationError": "Registration error"
},
"AccountInfo": {
  "BasicInfo": "Basic information",
  "ContactInfo": "Contact information",
  "FullName": "Full name",
  "Gender": "Gender",
  "BirthDate": "Birth date",
  "PhoneNumber": "Phone number",
  "Province": "Province/City",
  "District": "District",
  "Address": "Address"
}
```

## Summary of Changes

### **Files Modified:**
1. `lib/pages/access/register.dart` - **12 text constants** converted
2. `lib/pages/my_profile/_account_info.dart` - **10 text constants** converted
3. `assets/translations/vi.json` - **22 new Vietnamese keys** added
4. `assets/translations/en.json` - **22 new English keys** added

### **Key Benefits:**
1. **Consistent User Experience**: Form labels và error messages nhất quán giữa tiếng Việt và tiếng Anh
2. **Professional Validation**: Error messages được localized properly
3. **Complete Registration Flow**: Toàn bộ registration process hỗ trợ đa ngôn ngữ
4. **Account Management**: Account info screen hoàn toàn localized

### **Text Constants Converted:**

#### **Register Screen:**
- Section titles (Required Info, Additional Info)
- Field labels (Personal ID)
- Validation error messages (12 different validation rules)
- Registration error messages

#### **Account Info Screen:**
- Section titles (Basic Info, Contact Info)
- All field labels (Full Name, Gender, Birth Date, Phone, Province, District, Address)

## Usage Examples

### **Form Validation:**
```dart
// Vietnamese
"Họ tên là bắt buộc"
"Định dạng email không hợp lệ"
"Mật khẩu phải có ít nhất 6 ký tự"

// English
"Full name is required"
"Invalid email format"
"Password must be at least 6 characters"
```

### **Section Headers:**
```dart
// Vietnamese
"THÔNG TIN BẮT BUỘC"
"THÔNG TIN BỔ SUNG"
"Thông tin cơ bản"
"Thông tin liên hệ"

// English
"REQUIRED INFORMATION"
"ADDITIONAL INFORMATION"
"Basic information"
"Contact information"
```

## Ready for Use

Bây giờ cả **Register** và **Account Info** screens đều hỗ trợ đầy đủ đa ngôn ngữ:
- ✅ Form labels và section titles
- ✅ Validation error messages
- ✅ Registration error messages
- ✅ Account information display

Users có thể switch giữa tiếng Việt và tiếng Anh một cách seamless trong toàn bộ registration và account management flow! 🎉
